const {ObjectUtils} = require("./ObjectUtils");
const Decimal = require("decimal.js");
const EE = require("../../core/ee");
const { ObjectUtil } = require('../../common/ObjectUtil');

class NumberUtil {
    constructor() {
        this.retain = 2;
    }

    /**
     * 数字格式化方法
     * @param number
     * @param decimalPlaces
     * @return {number}
     */
    numberFormat(number, decimalPlaces) {
        const parsedNumber = parseFloat(number);

        // 检查输入是否为有效数值
        if (isNaN(parsedNumber)) {
            throw new Error('输入无效的数字');
        }

        // 使用toFixed方法将数字保留指定位数小数
        const formattedNumber = parsedNumber.toFixed(decimalPlaces);
        const result = parseFloat(formattedNumber);

        return result;
    }

    /**
     * 保留decimalPlaces位数
     * @param number
     * @return {number}
     */
    numberScale(number,decimalPlaces) {
        if (!number || !ObjectUtils.isNumberStr(""+number)) {
            return 0.00;
        }
        if (ObjectUtils.isNumber(decimalPlaces)){
           return  Number(new Decimal(number).toFixed(decimalPlaces));
        }else {
           return Number(new Decimal(number).toFixed(this.retain));
        }
    }

    /**
     * 保留指定位数，整数的时候不保留
     * @param num
     * @param decimalPlaces
     * @return {*|string}
     */
     formatNumber(num,decimalPlaces) {
        if (Number.isInteger(num)) {
            return num.toString();
        } else {
            const numStr = num.toString();
            const decimalIndex = numStr.indexOf('.');
            if (decimalIndex !== -1 && numStr.length - decimalIndex - 1 > decimalPlaces) {
                return parseFloat(num).toFixed(decimalPlaces);
            } else {
                return numStr;
            }
        }
    }

    /**
     *  // 使用正则表达式去除尾部多余的零，并且如果有小数点，只保留小数点前的部分
     * @param numStr
     * @returns {*}
     */
    removeExtraZerosAndDot(numStr) {
        return Number(numStr.replace(/(\.\d*?[1-9])?(\.0*|0*)$/, '$1'));
    }

    /**
     * 保留2位数
     * @param number
     * @return {number}
     */
    numberScale2(number) {
        if (ObjectUtils.isNumber(number)){
            return this.numberScale(number, 2);
        }else {
            return this.numberScale(Number(number), 2);
        }

    }

    /**
     * 保留4位数
     * @param number
     * @return {number}
     */
    numberScale4(number) {
        return this.numberScale(number, 4);
    }


    /**
     * 保留6位数
     * @param number
     * @return {number}
     */
    numberScale6(number) {
        return this.numberScale(number, 6);
    }

    add(a, b) {
        if (a === null || a === undefined || a ==='') a = 0;
        if (b === null || b === undefined || b ==='') b = 0;
        // return a + b;

        a = new Decimal(a);
        b = new Decimal(b);
        return a.plus(b).toNumber();
    }
    addParams(...args) {
        const sum = args.reduce((accumulator, current) => {
            if (current === null || current === undefined || current ==='' || current ==='/') current = 0;
            const decimalValue = new Decimal(current);
            return accumulator.plus(decimalValue);
        }, new Decimal(0));

        return sum.toNumber();
    }


    subtract(a, b) {
        if (a === null || a === undefined || a==='') a = 0;
        if (b === null || b === undefined || b==='') b = 0;
        // return a - b;

        a = new Decimal(a);
        b = new Decimal(b);
        return a.minus(b).toNumber();
    }

    multiply(a, b) {
        if (a === null || a === undefined || a==='') a = 0;
        if (b === null || b === undefined || b==='') b = 0;
        // return a * b;

        try{
            a = new Decimal(a);
            b = new Decimal(b);
        }catch (e) {
            return null;
        }

        return a.times(b).toNumber();
    }

    multiplyParams(...args) {
        const result = args.reduce((accumulator, currentValue) => {
            if (currentValue === null || currentValue === undefined || currentValue === '') {
                currentValue = 0;
            }

            const decimalValue = new Decimal(currentValue);
            return accumulator.times(decimalValue);
        }, new Decimal(1));
        return result.toNumber();
    }

    multiplyToString(a, b, decimalPlaces) {
        if (a === null || a === undefined || a=== '/'||a=== '') a = 0;
        if (b === null || b === undefined || b=== '/'||b=== '') b = 0;

        let result = new Decimal(a).times(b);
        if (decimalPlaces !== undefined) {
            result = result.toFixed(decimalPlaces);
        }
        if (result == -0.00){
            result = new Decimal(0).toFixed(decimalPlaces);
        }
        return result;
    }

    /**
     * 先乘法，再保留位数，不包括末尾的0
     * @param a
     * @param b
     * @param decimalPlaces
     */
    multiplyToStringExEnd0(a, b, decimalPlaces){
        return this.numberRemoveEnd0(this.multiplyToString(a, b, decimalPlaces)).toString();
    }

    /**
     * 去除末尾的0
     * @param data
     * @return {*}
     */
    numberRemoveEnd0(number){
        return parseFloat(number+"");
    }

    numberScaleAndRemoveEnd0(number, decimalPlaces){
        return parseFloat(this.numberScale(number, decimalPlaces)+"");
    }

    /**
     * 除100， 可用于求百分比
     *
     * @param data
     * @return
     */
    divide100(data){
        if (ObjectUtils.isEmpty(data)){
            data = 0;
        }
        return this.divide(data,100);

    }


    divide(a, b) {
        if (a === null || a === undefined || a === '') a = 0;
        if (b === null || b === undefined || b === '') b = 0;
        if (b === 0) return 0;
        //return a / b;

        a = new Decimal(a);
        b = new Decimal(b);
        return a.dividedBy(b).toNumber();
    }

    /**
     * 金额小写转大写
     * @param n
     * @returns {string|string}
     */
    numToCny(n) {
        const fraction = ['角', '分'];
        const digit = [
            '零', '壹', '贰', '叁', '肆',
            '伍', '陆', '柒', '捌', '玖'
        ];
        const unitMap = [
            ['元', '万', '亿'],
            ['', '拾', '佰', '仟']
        ];

        const zeroFilter = ['零仟零佰零拾', '零仟零佰', '零佰零拾', '零仟', '零佰', '零拾'];

        let num = Math.abs(n);
        let s = '';
        fraction.forEach((item, index) => {
            //这里保留五位小数  是因为 2097.12 * 10 * Math.pow(10, 1) = 209711.99999999997
            s += (digit[Math.floor(this.numberScale(n * 10 * Math.pow(10, index),5)) % 10] + item);
        });
        s = s || '整';
        num = Math.floor(num);
        for (let i = 0; i < unitMap[0].length && num > 0; i++) {
            let p = '';
            for (let j = 0; j < unitMap[1].length && num > 0; j++) {
                p = digit[num % 10] + unitMap[1][j] + p;
                num = Math.floor(num / 10);
            }
            s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unitMap[0][i] + s;

            for (let zeroObj of zeroFilter) {
                if (s.includes(zeroObj)) {
                    s = s.replace(zeroObj, "零");
                    break;
                }
            }
            if (s.includes("零万")) {
                s = s.replace("零万", "");
            }
        }

        return n < 0 ? `负${s}` : s;
    }







    /**
     * 数字转中文
     * @param num
     * @return {*}
     */
     numberToChinese(num) {
        const chineseNumber = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
        const units = ['', '十', '百', '千', '万', '亿'];
        const str = num.toString();
        const reg = /(?<=(^|[^一-十]))十/g;

        return str
            .replace(reg, '一十')
            .replace(/\d/g, match => chineseNumber[parseInt(match)])
            .split('')
            .map((char, index) => {
                const unitIndex = (str.length - index - 1) % 4;

                if (char === '一' && unitIndex === 1) {
                    return units[unitIndex];
                } else {
                    return char + units[unitIndex];
                }
            })
            .join('');
    }


    /**
     *
     * @param number    比较的值
     * @param lowerBound 下限
     * @param upperBound  上限
     * @return {boolean}
     */
    isBetween(number, lowerBound, upperBound) {
        return number > lowerBound && number <= upperBound;
    }

    /**
     *  如果=0 则返回默认字符串0.00
     * @param number
     */
    getDefault(number) {
        if(number===undefined || ObjectUtils.isEmpty(number) || number===0){
            return '0.00';
        }
        return number;
    }










    /**
     * 数字转中文 特殊业务需要用到
     * @param str
     * @return {[]}
     */
    numToChByBusiness(strArr) {

        /**
         *    "1~9,11~12,14,13.1,13.3~13.12"
         */
        let classLevel2 = [];
        for (const str of strArr) {
            const parts = str.split(',');
            for (const part of parts) {
                //包含~的元素
                if (part.includes('~')) {
                    let item = part.split('~');
                    //判断是否有小数
                    if (item[0].includes('.')) {
                        //13.3~13.12
                        for (const itemElement of item) {
                            //有第三节的
                            let key = '第' + this.numberToChinese(parseInt(item[0])) + '章';
                            const containsKey = classLevel2.some(obj => obj.hasOwnProperty(key));
                            if (!containsKey) {
                                classLevel2.push({ [key]: [] });
                            }
                            let start = item[0].split('.')[1];
                            let end = item[1].split('.')[1];
                            const numbers = Array.from({ length: parseInt(end) - parseInt(start) + 1 }, (_, index) => parseInt(start) + index);
                            let array = [];
                            const value = classLevel2.find(obj => obj.hasOwnProperty(key))[key];
                            for (const number of numbers) {
                                value.push(this.numberToChinese(parseInt(number)) + '、');
                            }
                        }

                    } else {
                        const numbers = Array.from({ length: parseInt(item[1]) - parseInt(item[0]) + 1 }, (_, index) => parseInt(item[0]) + index);
                        for (const number of numbers) {
                            classLevel2.push({ ['第' + this.numberToChinese(parseInt(number)) + '章']: [] });
                        }
                    }
                } else {
                    // 14,13.1
                    const item = part.split('.');
                    if (item.length > 1) {
                        //有第三节的
                        let key = '第' + this.numberToChinese(parseInt(item[0])) + '章';
                        const containsKey = classLevel2.some(obj => obj.hasOwnProperty(key));
                        if (!containsKey) {
                            classLevel2.push({ [key]: [] });
                        }
                        const value = classLevel2.find(obj => obj.hasOwnProperty(key))[key];
                        value.push(this.numberToChinese(parseInt(item[1])) + '、');
                    } else {
                        //只有第二章
                        classLevel2.push({ ['第' + this.numberToChinese(parseInt(item[0])) + '章']: [] });
                    }
                }
            }
        }

        if (!ObjectUtils.isEmpty(classLevel2)){
            classLevel2 = [... new Set(classLevel2)]
        }
        return classLevel2;
    }


    roundHalfUp(num) {
        if (!ObjectUtil.isNumber(num)) {
            throw new Error(`${num} is not a number`);
        }
        return new Decimal(num).toFixed(2);
    }

  roundHalfUpReturnNumber(num) {
    return Number(new Decimal(num).toFixed(2));
  }


    /**
     * 人材机明细区：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
     * @param number
     * @returns {number}
     */
    rcjDetailAmountFormat(number){
        let { service } = EE.app;
        let rcjSummaryAmount = service.globalConfigurationService.getDecimalPointConfig().rcjDetail.amount;
        if (!number) {
            return 0;
        }
        if (ObjectUtils.isNumber(rcjSummaryAmount)){
            return  Number(new Decimal(number).toFixed(rcjSummaryAmount));
        }else {
            return Number(new Decimal(number).toFixed(this.retain));
        }
    }

    /**
     * 金额、合计，金额类：小数点后2位，第3位四舍五入
     * @param number
     * @returns {number}
     */
    costPriceAmountFormat(number){
        if (!ObjectUtils.isNumberStr(""+number)) {
            return number;
        }
        let { service } = EE.app;
        let costPrice = service.globalConfigurationService.getDecimalPointConfig().costPrice;

        if (ObjectUtils.isNumber(costPrice)){
            return  Number(new Decimal(number).toFixed(costPrice));
        }else {
            return Number(new Decimal(number).toFixed(this.retain));
        }
    }

    /**
     * 人材机汇总：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
     * @param number
     * @returns {number}
     */
    rcjSummaryAmountFormat(number){
        let { service } = EE.app;
        let rcjSummaryAmount = service.globalConfigurationService.getDecimalPointConfig().rcjSummaryAmount;
        if (!number) {
            return 0;
        }
        if (ObjectUtils.isNumber(rcjSummaryAmount)){
            return  Number(new Decimal(number).toFixed(rcjSummaryAmount));
        }else {
            return Number(new Decimal(number).toFixed(this.retain));
        }
    }

    /**
     * 工程量，数量类：小数点后3位，第4位四舍五入
     * @param number
     * @returns {number}
     */
    qDDeAmountFormat(number){
        let { service } = EE.app;
        let qDDeAmount = service.globalConfigurationService.getDecimalPointConfig().qDDeAmount;
        if (!number) {
            return 0;
        }
        if (ObjectUtils.isNumber(qDDeAmount)){
            return  Number(new Decimal(number).toFixed(qDDeAmount));
        }else {
            return Number(new Decimal(number).toFixed(this.retain));
        }
    }

    /**
     * 人材机明细区 税率
     * @param number
     * @returns {number}
     */
    rateFormat(number){
        let { service } = EE.app;
        let rate = service.globalConfigurationService.getDecimalPointConfig().rcjDetail.taxRate;
        if (!number) {
            return 0;
        }
        if (ObjectUtils.isNumber(rate)){
            return  Number(new Decimal(number).toFixed(rate));
        }else {
            return Number(new Decimal(number).toFixed(this.retain));
        }
    }

    /**
     * 人材机明细区 除税系数
     * @param number
     * @returns {number}
     */
    taxRemovalFormat(number){
        let { service } = EE.app;
        let rate = service.globalConfigurationService.getDecimalPointConfig().rcjDetail.taxRemoval;
        if (!number) {
            return 0;
        }
        if (ObjectUtils.isNumber(rate)){
            return  Number(new Decimal(number).toFixed(rate));
        }else {
            return Number(new Decimal(number).toFixed(this.retain));
        }
    }

    /**
     * 其他项目数量
     * @param number
     * @returns {number}
     */
    qtxmAmountFormat(number){
        if (!ObjectUtils.isNumberStr(""+number)) {
            return number;
        }
        let { service } = EE.app;
        let rate = service.globalConfigurationService.getDecimalPointConfig().qtxm.amount;

        if (ObjectUtils.isNumber(rate)){
            return  Number(new Decimal(number).toFixed(rate));
        }else {
            return Number(new Decimal(number).toFixed(this.retain));
        }
    }

    /**
     * 安文费 税率
     * @param number
     * @returns {number}
     */
    awfRateFormat(number){
        let { service } = EE.app;
        let rate = service.globalConfigurationService.getDecimalPointConfig().feeFile.rate.awf;
        if (!number) {
            return 0;
        }
        if (ObjectUtils.isNumber(rate)){
            return  Number(new Decimal(number).toFixed(rate));
        }else {
            return Number(new Decimal(number).toFixed(4));
        }
    }

    /**
     * 规费 税率
     * @param number
     * @returns {number}
     */
    gfRateFormat(number){
        let { service } = EE.app;
        let rate = service.globalConfigurationService.getDecimalPointConfig().feeFile.rate.gf;
        if (!number) {
            return 0.00;
        }
        if (ObjectUtils.isNumber(rate)){
            return  Number(new Decimal(number).toFixed(rate));
        }else {
            return Number(new Decimal(number).toFixed(this.retain));
        }
    }

    /**
     * 管理费 税率
     * @param number
     * @returns {number}
     */
    glfRateFormat(number){
        let { service } = EE.app;
        let rate = service.globalConfigurationService.getDecimalPointConfig().feeFile.rate.glf;
        if (!number) {
            return 0.00;
        }
        if (ObjectUtils.isNumber(rate)){
            return  Number(new Decimal(number).toFixed(rate));
        }else {
            return Number(new Decimal(number).toFixed(this.retain));
        }
    }

    /**
     * 利润 税率
     * @param number
     * @returns {number}
     */
    lrRateFormat(number){
        let { service } = EE.app;
        let rate = service.globalConfigurationService.getDecimalPointConfig().feeFile.rate.lr;
        if (!number) {
            return 0.00;
        }
        if (ObjectUtils.isNumber(rate)){
            return  Number(new Decimal(number).toFixed(rate));
        }else {
            return Number(new Decimal(number).toFixed(this.retain));
        }
    }


    costCodeGrFormat(number){
        let { service } = EE.app;
        let rate = service.globalConfigurationService.getDecimalPointConfig().costCode.gr;
        if (!number) {
            return 0;
        }
        if (ObjectUtils.isNumber(rate)){
            return  Number(new Decimal(number).toFixed(rate));
        }else {
            return Number(new Decimal(number).toFixed(this.retain));
        }
    }
}

module.exports = {
    NumberUtil: new NumberUtil()
};
