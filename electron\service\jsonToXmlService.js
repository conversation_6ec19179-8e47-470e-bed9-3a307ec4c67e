const {ObjectUtils} = require("../utils/ObjectUtils");
const fs = require('fs');
const _ = require("lodash");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {NumberUtil} = require("../utils/NumberUtil");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {Service} = require('../../core');
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
let npm = require('getmac');
const UnitPriceConstant = require("../enum/UnitPriceConstant");
const RcjLevelMarkConstant = require("../enum/RcjLevelMarkConstant");
const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const CalculateBaseType = require("../enum/CalculateBaseType");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const CostTypeJrgEnum = require("../enum/CostTypeJrgEnum");
const CostTypeSbLbEnum = require("../enum/CostTypeSbLbEnum");
const Fixs = require("../fixs");

//获取mac地址
class JsonToXmlService extends Service {
    constructor(ctx) {

        super(ctx);
        let rcjArray=[];//人材机汇总接口返回值
        let RcjMap ={};
    }
    async printObj(obj, nodeName, prefix) {
        let start = "<" + nodeName;
        let end = "</" + nodeName + ">";
        let subMap = new Map();

        for (let prop in obj) {
            if (!obj.hasOwnProperty(prop)) {
                continue;
            }

            let value = obj[prop];

            if (ObjectUtils.isObject(value) || ObjectUtils.isArray(value)) {
                subMap.set(prop, value);
            } else {
                value = ObjectUtils.isEmpty(value) ? "" : value+"";
                start += " " + prop + '="' + await this.escapeXmlAttribute(value)+ '"';
            }
        }

        if (subMap.size === 0) {
            start += "/>";
        } else {
            start += ">";
        }
        this.res = this.res + prefix + start + "\n";
        for (const [key, o] of subMap.entries()) {
            if (ObjectUtils.isObject(o)) {
               await this.printObj(o, key, prefix + "  ");
            } else if (ObjectUtils.isArray(o)) {
                for (let i = 0; i < o.length; i++) {
                  await  this.printObj(o[i], key, prefix + "   ");
                }
            }
        }
        if (subMap.size === 0) {
            this.res = this.res;
        } else {
            this.res = this.res + prefix + end + "\n";
        }
        return this.res;
    }

    /**
     * 特殊字符转义
     * @param value
     * @returns {Promise<*>}
     */
    async escapeXmlAttribute(value) {
        if(ObjectUtils.isEmpty(value)){
            return ''
        }
        value = value+''
        return value.replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
    }

    /**
     *
     * 获取xml内容
     * @param args
     * @return {Promise<string>}
     */
    async generateXmlString(obj,type) {
        PricingFileWriteUtils.writeToMemory(obj);
        this.res = "";
        let json = await this.convertXMLData(obj,type);
        let xmlString = '<?xml version="1.0" encoding="UTF-8"?>' + "\n"  + await this.printObj(json, "JingJiBiao", "");
        return xmlString;
    }



    async generateXml(args) {
        let resObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
        //改为从文件读取
        let projectObj = await PricingFileFindUtils.getProjectObjByPath(resObj.path);
        await  new Fixs(projectObj,projectObj.version).fix();
        this.res = "";
        let json = await this.convertXMLData(projectObj,args.type);
        let xmlString = '<?xml version="1.0" encoding="UTF-8"?>' + "\n"  + await this.printObj(json, "JingJiBiao", "");


        let path = projectObj.path;
        let pathArray = path.split("\\");
        let fileName = pathArray[pathArray.length - 1];
        let suffix = 'xml';
        if(args.vender === 0){
            suffix ='xml'
        }
        let newFileName = projectObj.constructName+"."+suffix;
        let newPath = path.replace(fileName, newFileName);

        //获取线上项目
        // let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(projectObj.constructName);
        let result = dialog.showSaveDialogSync(null, {
            title: '保存文件',
            defaultPath: newPath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: [suffix]} // 可选的文件类型
            ]
        });

        if (!ObjectUtils.isEmpty(result)) {

            let filePath = result;
            try {
                fs.writeFileSync(filePath, xmlString);
                console.log('XML写入文件成功！');
            } catch (err) {
                console.log('XML写入文件失败：', err);
            }
            return true;
        }else {

            return false;

        }


    }

    /**
     * ysf格式转为河北标准格式XML对应JSON
     * @param projectObj
     */
    async convertXMLData(projectObj,type){


        let taxCalculation = await this.service.baseFeeFileService.taxCalculationParam(projectObj);

        let json = {};
        json.Xmbh = projectObj.constructCode;
        json.Xmmc = projectObj.constructName;
        json.Version = "3.0";//王浩让改成3.0
        json.Jsfs = taxCalculation.taxCalculationMethod == '1'?'一般计税':'简易计税';
        json.SoftName = "云算房智能造价平台（服务商：河北宏盛建通信息技术有限公司）";
        try {
            json.MachineKey = npm.default();
        }catch (e){
            console.log('获取Mac地址失败')
        }
        if(ObjectUtils.isEmpty(projectObj.securityFee)){
            json.IsXmAqwmf = 'false';//是否存在项目直接计取安全文明施工费情况，如果为True，安全文明施工费不再由单位工程汇总 【红成强调】
            json.Aqwmf = "0"; //只有在IsXmAqwmf为True的情况下才需要填写，其余情况为0 【红成强调】
        }else {
            json.IsXmAqwmf = 'true'
            json.Aqwmf = projectObj.securityFee
        }

        //通过造价分析获取   投标总价、招标控制价
        let zj = await this.countZj({constructId:projectObj.sequenceNbr,levelType:1})

        if(type === 0){
            await this.convertZhaoBiaoXx(json,projectObj);
        }else if(type === 1){
            await this.convertTouBiaoXx(json,projectObj,zj);
        }else if(type === 2){
            await this.convertZhaoBiaoKzjXx(json,projectObj,zj);
        }
        //对应不同的新建预算项目
        if(projectObj.biddingType!== 2){
            await this.convertDxgcxx(json,projectObj.singleProjects)
        }else {
            await this.convertDWGXDwgcxx(json,projectObj.unitProject);
        }

        this.rcjArray =[];
        return json;
    }

    async countZj(args){
       let zjfxArray = await this.service.unitProjectService.getCostAnalysisData(args);
        if (ObjectUtils.isEmpty(zjfxArray.costAnalysisConstructVOList)){
            return 0;
        }else {
            let total = 0;
            for (let i = 0; i < zjfxArray.costAnalysisConstructVOList.length; i++) {
                let costAnalysisConstructVO = zjfxArray.costAnalysisConstructVOList[i];
                total = NumberUtil.addParams(total,costAnalysisConstructVO.gczj,costAnalysisConstructVO.sbfsj)
            }
            return NumberUtil.numberScale2(total);
        }

    }
    async convertZhaoBiaoXx(json,projectObj){
        let ZhaoBiaoXx ={};
        let constructProjectJBXX = projectObj.constructProjectJBXX;
        if(!ObjectUtils.isEmpty(constructProjectJBXX)){
            for (let i = 0; i < constructProjectJBXX.length; i++) {
                switch (constructProjectJBXX[i].name) {
                    case '招标人(发包人)':
                        ZhaoBiaoXx.Zbr =constructProjectJBXX[i].remark;
                        break;
                    case '招标人(发包人)法人或其授权人':
                        ZhaoBiaoXx.ZbrDb =constructProjectJBXX[i].remark;
                        break;
                    case '工程造价咨询人':
                        ZhaoBiaoXx.Zxr =constructProjectJBXX[i].remark;
                        break;
                    case '工程造价咨询人法人或其授权人':
                        ZhaoBiaoXx.ZxrDb =constructProjectJBXX[i].remark;
                        break;
                    case '编制人':
                        ZhaoBiaoXx.Bzr =constructProjectJBXX[i].remark;
                        break;
                    case '编制时间':
                        ZhaoBiaoXx.BzRq =constructProjectJBXX[i].remark;
                        break;
                    case '核对人(复核人)':
                        ZhaoBiaoXx.Fhr =constructProjectJBXX[i].remark;
                        break;
                    case '核对(复核)时间':
                        ZhaoBiaoXx.FhRq =constructProjectJBXX[i].remark;
                        break;
                }
            }

        }

        json.ZhaoBiaoXx = ZhaoBiaoXx;



    }

    async convertZhaoBiaoKzjXx(json, projectObj,zj) {
        let ZhaoBiaoKzjXx = {};

        let constructProjectJBXX = projectObj.constructProjectJBXX;
        if(!ObjectUtils.isEmpty(constructProjectJBXX)){
            for (let i = 0; i < constructProjectJBXX.length; i++) {
                switch (constructProjectJBXX[i].name) {
                    case '招标人(发包人)':
                        ZhaoBiaoKzjXx.Zbr =constructProjectJBXX[i].remark;
                        break;
                    case '招标人(发包人)法人或其授权人':
                        ZhaoBiaoKzjXx.ZbrDb =constructProjectJBXX[i].remark;
                        break;
                    case '工程造价咨询人':
                        ZhaoBiaoKzjXx.Zxr =constructProjectJBXX[i].remark;
                        break;
                    case '工程造价咨询人法人或其授权人':
                        ZhaoBiaoKzjXx.ZxrDb =constructProjectJBXX[i].remark;
                        break;
                    case '编制人':
                        ZhaoBiaoKzjXx.Bzr =constructProjectJBXX[i].remark;
                        break;
                    case '编制时间':
                        ZhaoBiaoKzjXx.BzRq =constructProjectJBXX[i].remark;
                        break;
                    case '核对人(复核人)':
                        ZhaoBiaoKzjXx.Fhr =constructProjectJBXX[i].remark;
                        break;
                    case '核对(复核)时间':
                        ZhaoBiaoKzjXx.FhRq =constructProjectJBXX[i].remark;
                        break;

                }
            }

        }
        ZhaoBiaoKzjXx.Zbkzj  = zj;
        ZhaoBiaoKzjXx.Gq = '0';
        //参考规则表改为string
        ZhaoBiaoKzjXx.Zlcn = '';

        json.ZhaoBiaoKzjXx = ZhaoBiaoKzjXx;
    }

    //投标
    async convertTouBiaoXx(json, projectObj,zj) {
        let TouBiaoXx = {}
        let constructProjectJBXX = projectObj.constructProjectJBXX;

        if(!ObjectUtils.isEmpty(constructProjectJBXX)){
            for (let i = 0; i < constructProjectJBXX.length; i++) {
                switch (constructProjectJBXX[i].name) {
                    case '招标人(发包人)':
                        TouBiaoXx.Zbr =constructProjectJBXX[i].remark;
                        break;
                    case '投标人(承包人)':
                        TouBiaoXx.Tbr =constructProjectJBXX[i].remark;
                        break;
                    case '投标人(承包人)法人或其授权人':
                        TouBiaoXx.TbrDb =constructProjectJBXX[i].remark;
                        break;
                    case '投标人(承包人)法人或其授权人':
                        TouBiaoXx.TbrDb =constructProjectJBXX[i].remark;
                        break;
                    case '编制人':
                        TouBiaoXx.Bzr =constructProjectJBXX[i].remark;
                        break;
                    case '编制时间':
                        TouBiaoXx.BzRq =constructProjectJBXX[i].remark;
                        break;
                }
            }
        }


        TouBiaoXx.Tbzj  = zj;
        TouBiaoXx.Gq  = '0';
        TouBiaoXx.Bzj  = '0';
        //参考规则表改为string
        TouBiaoXx.Zlcn  = '';
        TouBiaoXx.Dblx  = '0';//担保类型 应填充默认值为0（计价软件没有字段映射）
        if(ObjectUtils.isEmpty(global.microDog)){
            TouBiaoXx.DogNum  = ''; //加密锁硬件序列号，通过BASE64转码保存，不得出现填写“未检测到加密锁号码”之类无效的信息
            TouBiaoXx.DogTbdw  = '';
        }else {
            TouBiaoXx.DogNum  = global.microDog.serial; //加密锁硬件序列号，通过BASE64转码保存，不得出现填写“未检测到加密锁号码”之类无效的信息
            TouBiaoXx.DogTbdw  = global.microDog.busName;
        }

        json.TouBiaoXx = TouBiaoXx;
    }



    //单位工程xx
    async convertDxgcxx(json,singleProjects){

        if(ObjectUtils.isEmpty(singleProjects)){
            return;
        }
        let dxgcxxArray =new Array();
        for (let i = 0; i < singleProjects.length; i++) {
            let singleProject = singleProjects[i];
            let Dxgcxx = {};
            Dxgcxx.Dxgcbh = singleProject.projectCode;
            Dxgcxx.Dxgcmc =singleProject.projectName;
            Dxgcxx.Je = singleProject.total;
            Dxgcxx.Gf = singleProject.gfee;
            Dxgcxx.Aqwmf =singleProject.safeFee ;
            Dxgcxx.SbfSJ = NumberUtil.getDefault(singleProject.sbfTax);

            if(!ObjectUtils.isEmpty(singleProject.subSingleProjects)){

                let  subSingleProjects =singleProject.subSingleProjects;
                let subDxgcxxArray =new Array();
                for (let j = 0; j < subSingleProjects.length; j++) {

                    let  subSingleProject = subSingleProjects[j];
                    let  DxgcxxSon = {};
                    DxgcxxSon.Dxgcbh = subSingleProject.projectCode;
                    DxgcxxSon.Dxgcmc =subSingleProject.projectName;
                    DxgcxxSon.Je = subSingleProject.total;
                    DxgcxxSon.Gf = subSingleProject.gfee;
                    DxgcxxSon.Aqwmf =subSingleProject.safeFee ;
                    DxgcxxSon.SbfSJ = NumberUtil.getDefault(subSingleProject.sbfTax);

                    if(ObjectUtils.isNotEmpty(subSingleProject.subSingleProjects)){
                        await this.convertDxgcxx(DxgcxxSon,subSingleProject.subSingleProjects);
                    }else {
                        await this.convertDwgcxx(DxgcxxSon,subSingleProject.unitProjects);
                    }


                    subDxgcxxArray.push(DxgcxxSon)
                }
                Dxgcxx.Dxgcxx = subDxgcxxArray;

            }else {
                await this.convertDwgcxx(Dxgcxx,singleProject.unitProjects);
            }

            dxgcxxArray.push(Dxgcxx);
        }
        json.Dxgcxx = dxgcxxArray;

    }

    // async  convertDxgcxx(singleProject, json = { Dxgcxx: [] }) {
    //     // 创建 Dxgcxx 对象并填充数据
    //     const Dxgcxx = {
    //         Dxgcbh: singleProject.projectCode,
    //         Dxgcmc: singleProject.projectName,
    //         Je: singleProject.total,
    //         Gf: singleProject.gfee,
    //         Aqwmf: singleProject.safeFee,
    //         SbfSJ: singleProject.sbfTax
    //     };
    //
    //     // 如果存在子项目，递归调用转换函数
    //     if (!ObjectUtils.isEmpty(singleProject.subSingleProjects)) {
    //         const subDxgcxxArray = await Promise.all(singleProject.subSingleProjects.map(async (subProject) =>
    //             await this.convertDxgcxx(subProject)
    //         ));
    //         Dxgcxx.subDxgcxxArray = subDxgcxxArray;
    //     }
    //
    //     // 如果有关联的项目，处理它们
    //     if (!ObjectUtils.isEmpty(singleProject.unitProjects)) {
    //         await this.convertDwgcxx(Dxgcxx, singleProject.unitProjects);
    //     }
    //
    //     // 将当前项目的 Dxgcxx 对象添加到结果数组中
    //     json.Dxgcxx.push(Dxgcxx);
    //
    //     // 返回更新后的 json 对象
    //     return json;
    // }

    /**
     * 转换专业类别
     * 映射规则
     12:
     单位工程清单专业 为“建筑工程” 取 1
     清单专业 为“装饰工程” 取 2
     清单专业 为“安装工程” 取 3
     清单专业 为“市政工程” 且定额册选择为“2012-SZGC-DEK” 取 4
     清单专业 为“园林绿化工程” 且定额册选择为“2013-YLLH-DEK” 取 5
     清单专业 为 “市政工程” 且定额册选择为“2013-SZSS-DEX” 取 8
     清单专业 为“仿古建筑工程” 且定额册选择“2013-FGJZ-DEG” 取 9
     清单专业 为“园林绿化工程” 且定额册选择为“2014-YLLHYH-DEY” 取10
     清单专业 为“仿古建筑工程” 且定额册选择为“2014-GJXSGC-DEG” 取 11
     其他取 15

     22：
     单位工程清单专业 为 “建筑工程” 取 1
     清单专业为 “装饰装修工程” 取 2
     清单专业为“安装工程” 取 3
     清单专业为“市政工程” 取 4
     其他取 15  后续定额册更新后 再次补充映射规则
     */
    convertZylb(constructMajorType,mainDeLibrary){

        switch (constructMajorType) {
            case '建筑工程':{
                return 1
                break;
            }
            case '装饰工程':{
                return 2
                break;
            }
            case '安装工程':{
                return 3
                break;
            }
            case '市政工程':{
                if("2013-SZSS-DEX"==mainDeLibrary){
                    return 8
                }else {
                    return 4
                }
                break;
            }
            case '园林绿化工程':{
                if("2013-YLLH-DEK"==mainDeLibrary){
                    return 5
                }else  if("2014-YLLHYH-DEY"==mainDeLibrary){
                    return 10
                }else {
                    return 15
                }
                break;
            }
            case '仿古建筑工程':{
                if("2013-FGJZ-DEG"==mainDeLibrary){
                    return 9
                }else  if("2014-GJXSGC-DEG"==mainDeLibrary){
                    return 11
                }else {
                    return 15
                }
                break;
            }
        }
    }



    /**
     * 单位工程
     * @param Dxgcxx
     * @param unitProjects
     * @returns {Promise<void>}
     */
    async convertDwgcxx(Dxgcxx,unitProjects){
        if(ObjectUtils.isEmpty(unitProjects)){
            return;
        }
        let dwgcxxArray = new Array();
        for (let i = 0; i < unitProjects.length; i++) {
            let unitProject = unitProjects[i];
            //人材机汇总查询
            this.rcjArray =await this.service.rcjProcess.queryConstructRcjByDeIdNew(2, 0, unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);
            // 获取单位下人材机list
            let rcjList =await PricingFileFindUtils.getRcjList(unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);


            this.RcjMap =await this.convertRcjMap(rcjList);


            let Dwgcxx = {};
            Dwgcxx.Dwgcbh = unitProject.upCode ;
            Dwgcxx.Dwgcmc = unitProject.upName ;
            Dwgcxx.Zylb =await this.convertZylb(unitProject.constructMajorType,unitProject.mainDeLibrary);
            Dwgcxx.SbfSj = NumberUtil.getDefault(unitProject.sbfsj);
            Dwgcxx.Aqwmf = unitProject.safeFee ;
            //Fywj
            await this.convertFywj(Dwgcxx,unitProject.unitCostSummarys,unitProject.unitCostCodePrices)
            //Qdxm
            await this.convertQdxm(Dwgcxx,unitProject);


            //Csxm
            await this.convertCsxm(Dwgcxx,unitProject);


            //Qtxm
            await this.convertQtxm(Dwgcxx,unitProject.otherProjects,unitProject.feeFiles);


            //Zlje
            await this.convertZlje(Dwgcxx,unitProject.otherProjectProvisionals);

            //签证和索赔
            //await this.convertVisaAndClaim(Dwgcxx,unitProject.otherProjectQzAndSuoPeis);

            //Clzg
            await this.convertClzg(Dwgcxx,unitProject.otherProjectClZgjs);
            //Sbzg
            await this.convertSbzg(Dwgcxx,unitProject.otherProjectSbZgjs);

            //Zygczg
            await this.convertZygczg(Dwgcxx,unitProject.otherProjectZygcZgjs);


            //Zcbfwf
            await this.convertZcbfwf(Dwgcxx,unitProject.otherProjectServiceCosts);


            //Jrg
            await this.convertJrg(Dwgcxx,unitProject.otherProjectDayWorks);


            //Rcjhz
            await this.convertRcjhz(Dwgcxx,unitProject);


            //Zzsjxshzb
            await this.convertZzsjxshzb(Dwgcxx,unitProject.unitCostCodePrices)


            //ZyClSb
            await this.convertZyClSb(Dwgcxx,unitProject);


            //ZbrClSb
            await this.convertZbrClSb(Dwgcxx,unitProject);


            //Aqwmsgf
            await this.convertAqwmsgf(Dwgcxx,unitProject);


            //Gf
            await this.convertGf(Dwgcxx,unitProject);




            dwgcxxArray.push(Dwgcxx)

        }
        Dxgcxx.Dwgcxx = dwgcxxArray;

    }

    //xxx单位工程信息
    async convertDWGXDwgcxx(Dxgcxx,unitProject){

        // 获取单位下人材机list
        let rcjList =await PricingFileFindUtils.getRcjList(unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);


        this.RcjMap =await this.convertRcjMap(rcjList);

        let Dwgcxx = {};
        Dwgcxx.Dwgcbh = unitProject.upCode ;
        Dwgcxx.Dwgcmc = unitProject.upName ;
        Dwgcxx.Zylb ==await this.convertZylb(unitProject.constructMajorType,unitProject.mainDeLibrary)
        Dwgcxx.SbfSj = NumberUtil.getDefault(unitProject.sbfsj);
        Dwgcxx.Aqwmf = unitProject.safeFee ;
        //Fywj
        await this.convertFywj(Dwgcxx,unitProject.unitCostSummarys,unitProject.unitCostCodePrices)

        //Qdxm
        await this.convertQdxm(Dwgcxx,unitProject);

        //Csxm
        await this.convertCsxm(Dwgcxx,unitProject);

        //Qtxm
        await this.convertQtxm(Dwgcxx,unitProject.otherProjects,unitProject.feeFiles);
        //Zlje
        await this.convertZlje(Dwgcxx,unitProject.otherProjectProvisionals);
        //签证和索赔
        //await this.convertVisaAndClaim(Dwgcxx,unitProject.otherProjectQzAndSuoPeis);
        //Clzg
        await this.convertClzg(Dwgcxx,unitProject.otherProjectClZgjs);
        //Sbzg
        await this.convertSbzg(Dwgcxx,unitProject.otherProjectSbZgjs);
        //Zygczg
        await this.convertZygczg(Dwgcxx,unitProject.otherProjectZygcZgjs);
        //Zcbfwf
        await this.convertZcbfwf(Dwgcxx,unitProject.otherProjectServiceCosts);
        //Jrg
        await this.convertJrg(Dwgcxx,unitProject.otherProjectDayWorks);
        //Rcjhz
        await this.convertRcjhz(Dwgcxx,unitProject);
        //Zzsjxshzb
        await this.convertZzsjxshzb(Dwgcxx,unitProject.unitCostCodePrices)
        //ZyClSb
        await this.convertZyClSb(Dwgcxx,unitProject);
        //ZbrClSb
        await this.convertZbrClSb(Dwgcxx,unitProject);
        //Aqwmsgf
        await this.convertAqwmsgf(Dwgcxx,unitProject);
        //Gf
        await this.convertGf(Dwgcxx,unitProject);


        Dxgcxx.Dwgcxx = Dwgcxx;

    }

    //单位工程费用汇总
    async convertFywj(Dwgcxx,unitCostSummarys,unitCostCodePrices){

        if(ObjectUtils.isEmpty(unitCostSummarys)){
            return;
        }
        let Fywj ={};
        let fywjMxArray = new Array();
        for (let i = 0; i < unitCostSummarys.length; i++) {

            let unitCostSummary = unitCostSummarys[i];
            let FywjMx ={};

            FywjMx.Xh = unitCostSummary.dispNo  ;
            FywjMx.Mc = unitCostSummary.name  ;
            FywjMx.Jsjc = unitCostSummary.calculateFormula  ;
            FywjMx.Fl = ObjectUtils.isEmpty(unitCostSummary.rate)?'100':unitCostSummary.rate  ;
            FywjMx.Je = NumberUtil.getDefault(unitCostSummary.price);
            FywjMx.Rgf = '0.00'  ;
            FywjMx.Clf = '0.00' ;
            FywjMx.Jxf = '0.00'  ;
            if('分部分项工程量清单计价合计' ==unitCostSummary.name){
                FywjMx.Rgf = NumberUtil.getDefault(unitCostCodePrices.find(item => item.code == 'RGF').price);
                FywjMx.Clf = NumberUtil.getDefault(unitCostCodePrices.find(item => item.code == 'CLF').price);
                FywjMx.Jxf = NumberUtil.getDefault(unitCostCodePrices.find(item => item.code == 'JXF').price);
            }
            if('措施项目清单计价合计' ==unitCostSummary.name){
                FywjMx.Rgf = NumberUtil.getDefault(NumberUtil.add(unitCostCodePrices.find(item => item.code == 'DJCS_RGF').price,unitCostCodePrices.find(item => item.code == 'QTZJCS_RGF').price));
                FywjMx.Clf = NumberUtil.getDefault(NumberUtil.add(unitCostCodePrices.find(item => item.code == 'DJCS_CLF').price,unitCostCodePrices.find(item => item.code == 'QTZJCS_CLF').price));
                FywjMx.Jxf = NumberUtil.getDefault(NumberUtil.add(unitCostCodePrices.find(item => item.code == 'DJCS_JXF').price,unitCostCodePrices.find(item => item.code == 'QTZJCS_JXF').price));
            }
            if('单价措施项目工程量清单计价合计' ==unitCostSummary.name){
                FywjMx.Rgf = NumberUtil.getDefault(unitCostCodePrices.find(item => item.code == 'DJCS_RGF').price);
                FywjMx.Clf = NumberUtil.getDefault(unitCostCodePrices.find(item => item.code == 'DJCS_CLF').price);
                FywjMx.Jxf = NumberUtil.getDefault(unitCostCodePrices.find(item => item.code == 'DJCS_JXF').price);
            }
            if('其他总价措施项目清单计价合计' ==unitCostSummary.name){
                FywjMx.Rgf = NumberUtil.getDefault(unitCostCodePrices.find(item => item.code == 'QTZJCS_RGF').price);
                FywjMx.Clf = NumberUtil.getDefault(unitCostCodePrices.find(item => item.code == 'QTZJCS_CLF').price);
                FywjMx.Jxf = NumberUtil.getDefault(unitCostCodePrices.find(item => item.code == 'QTZJCS_JXF').price);
            }
            FywjMx.Fyxlb = await this.fywjFyxlb(unitCostSummary);


            fywjMxArray.push(FywjMx)
        }
        Fywj.FywjMx = fywjMxArray;

        Dwgcxx.Fywj = Fywj;

    }

    //fywj单位工程费用汇总Fyxlb费用类别
    fywjFyxlb(unitCostSummary) {
        //红成说是改成数字编号 2023/11/2
        let res ;
        switch (unitCostSummary.type) {
            case '分部分项工程量清单合计':
                res = 1;
                break;
            case '措施项目清单合计':
                res = 2;
                break;
            case '单价措施项目费':
                res = 2.1;
                break;
            case '其他总价措施项目费':
                res = 2.2;
                break;
            case '其他项目清单合计':
                res = 3;
                break;
            // case '暂列金额':
            //     res = 3.1;
            //     break;
            // case '专业工程暂估价':
            //     res = 3.2;
            //     break;
            // case '总承包服务费':
            //     res = 3.3;
            //     break;
            // case '计日工':
            //     res = 3.4;
            //     break;
            case '规费':
                res = 4;
                break;
            case '税金':
                res = 5;
                break;
            case '工程造价':
                res = 6;
                break;
            case '其他费用（在工程造价内）':
                res = 7;
                break;
            case '其他费用（不在工程造价内）':
                res = 8;
                break;
            case '安全文明施工费':
                res = 9;
                break;
            // case '安全生产、文明施工基本费':
            //     res = 9.1;
            //     break;
            // case '安全生产、文明施工增加费':
            //     res = 9.2;
            //     break;
            case '销项税额':
                res = 10;
                break;
            case '附加税费':
                res = 11;
                break;
            case '进项税额':
                res = 12;
                break;
            case '税前工程造价':
                res = 13;
                break;
            case '增值税应纳税额':
                res = 14;
                break;
            default:
                res = 8;
                break;
        }
        return res;
    }

    //分部分项清单计价表
    async convertQdxm(Dwgcxx, unitProject) {
        //获取单位工程所有人材机
        let itemBillProjects = unitProject.itemBillProjects;
        let rcjArray = this.rcjArray;
        let Qdxm = {}
        if(ObjectUtils.isEmpty(itemBillProjects)){
            Dwgcxx.Qdxm =Qdxm;
            return
        }

        let dwgc = itemBillProjects.find(item => item.kind === '0');
        let bt = null;
        if (ObjectUtils.isNotEmpty(dwgc)) {
            //获取dwgc的所有标题(可能会出现没有标题只有清单的情况)
            bt = itemBillProjects.filter(item => item.parentId === dwgc.sequenceNbr);
        }

        if(ObjectUtils.isEmpty(bt)){
            return
        }

        const createItemBillProjects = async (QdBtArray,bt) =>{

            if(bt[0].kind === '03'){
                //(需要考虑没有分布的情况)此处就是清单
                await this.convertFbFxQdDeRcj(itemBillProjects,rcjArray,bt,Qdxm,unitProject)

            }else {
                //有分布
                for (let i = 0; i < bt.length; i++) {
                    let QdBt ={};
                    let btElement = bt[i];

                    QdBt.Xh = btElement.dispNo;
                    QdBt.Mc = btElement.name;
                    QdBt.Je = btElement.total;

                    // 标题下的第一层
                    let filter1 = itemBillProjects.filter(item => item.parentId === btElement.sequenceNbr);

                    if (!ObjectUtils.isEmpty(filter1)){
                        //判断当前层为标题还是清单
                        let filter2 = filter1.filter(item =>{

                            return (item.kind === '01' ||  item.kind === '02')
                        } );
                        if(!ObjectUtils.isEmpty(filter2)){
                            //标题
                            let  QdBtArray = new Array()
                            let QdBtArrayS = await createItemBillProjects(QdBtArray,filter2);
                            QdBt.QdBt = QdBtArrayS;

                        }else {
                            await this.convertFbFxQdDeRcj(itemBillProjects,rcjArray,filter1,QdBt,unitProject)
                        }

                    }
                    QdBtArray.push(QdBt)
                }

                return QdBtArray;
            }

        }

        let QdBtArray = new Array()
        Qdxm.QdBt = await createItemBillProjects(QdBtArray,bt);
        if (ObjectUtils.isEmpty(Qdxm.QdBt)){
            delete Qdxm.QdBt;
        }
        Dwgcxx.Qdxm =Qdxm;
    }


    //措施项目
    async convertCsxm(Dwgcxx, unitProject) {
        //获取单位工程所有人材机
        let rcjArray =this.rcjArray;

        let measureProjectTables = unitProject.measureProjectTables;
        let Csxm = {}
        if(ObjectUtils.isEmpty(measureProjectTables)){
            Dwgcxx.Csxm =Csxm;
            return
        }
        let csxm = measureProjectTables.find(item => item.kind === '0');
        //获取csxm下的所有标题（）
        let bt = measureProjectTables.filter(item => item.parentId === csxm.sequenceNbr);
        if(ObjectUtils.isEmpty(bt)){
            return
        }

        let ZjCs = {};
        let ZjCsBtArray = new Array();

        let DjCs = {}
        let DjCsBtArray = new Array();


        //其他总价措施标题
        let QtZjCsBt = {};

        QtZjCsBt.Mc = '其他总价措施';
        QtZjCsBt.Je = 0;
        //存放其他总价措施清单
        let QtZjCsQd = new Array();


        for (let i = 0; i < bt.length; i++) {
            let btElement = bt[i];
            let qdArray = measureProjectTables.filter(item => item.parentId === btElement.sequenceNbr);
            if(  btElement.constructionMeasureType === 3 ) {// 1单价措施 2安文费 3其他总价措施
                //3其他总价措施
                QtZjCsBt.Xmlb = '2' //其他总价措施项目
                QtZjCsBt.Je = NumberUtil.add(QtZjCsBt.Je,btElement.total);
                if(!ObjectUtils.isEmpty(qdArray)){
                    if(qdArray[0].kind ==='02'){
                        for (let j = 0; j < qdArray.length; j++) {

                            let fb = qdArray[j];

                            //查询单价措施项目下的清单
                            let qdS = measureProjectTables.filter(item => item.parentId === fb.sequenceNbr);
                            if (!ObjectUtils.isEmpty(qdS)){
                                for (let k = 0; k < qdS.length; k++) {
                                    QtZjCsQd.push(qdS[k]);
                                }

                            }
                        }
                    }else {
                        for (let j = 0; j < qdArray.length; j++) {
                            QtZjCsQd.push(qdArray[j]);
                        }

                    }
                }

            }else if(btElement.constructionMeasureType === 2){

                //2安文费
                let ZjCsBt = {};
                ZjCsBt.Xh = btElement.dispNo;
                ZjCsBt.Mc = btElement.name;
                ZjCsBt.Je = btElement.total;
                ZjCsBt.Xmlb ='1'; // 安文费是1
                let ZjCsMxArray = new Array();
                if(!ObjectUtils.isEmpty(qdArray)){
                    if(qdArray[0].kind ==='02'){
                        //分布

                        let awfQd = new Array();
                        for (let j = 0; j < qdArray.length; j++) {

                            let fb = qdArray[j];

                            //查询单价措施项目下的清单
                            let qdS = measureProjectTables.filter(item => item.parentId === fb.sequenceNbr);
                            if (!ObjectUtils.isEmpty(qdS)){
                                for (let k = 0; k < qdS.length; k++) {
                                    awfQd.push(qdS[k]);
                                }
                            }

                        }
                        await this.convertQTZJCsQdDeRcj(awfQd, unitProject, measureProjectTables, ZjCsMxArray, ZjCsBt);
                        // ZjCsBt.ZjCsBt = zjCsBtArray;
                    }else {
                        //清单
                        await this.convertQTZJCsQdDeRcj(qdArray, unitProject, measureProjectTables, ZjCsMxArray, ZjCsBt);
                    }
                }
                ZjCsBtArray.push(ZjCsBt);
            }else if(btElement.constructionMeasureType === 1){
                //单价措施
                let DjCsBt = {};
                DjCsBt.Xh = btElement.dispNo;
                DjCsBt.Mc = btElement.name;
                DjCsBt.Je = ObjectUtils.isEmpty(btElement.total)?'0':btElement.total;
                // DjCsBt.Xmlb = btElement.projectType;

                if(!ObjectUtils.isEmpty(qdArray)){
                    if(qdArray[0].kind ==='02'){
                        //分布
                        let djCsBtArray = new Array();

                        for (let j = 0; j < qdArray.length; j++) {
                            let DjCsBt1 = {};
                            let fb = qdArray[j];
                            DjCsBt1.Xh = fb.dispNo;
                            DjCsBt1.Mc = fb.name;
                            DjCsBt1.Je = fb.total;

                            //查询单价措施项目下的清单
                            let qdS = measureProjectTables.filter(item => item.parentId === fb.sequenceNbr);
                            if (!ObjectUtils.isEmpty(qdS)){
                                await this.convertDJCsQdDeRcj(measureProjectTables,rcjArray,qdS,DjCsBt1,unitProject)
                            }

                            djCsBtArray.push(DjCsBt1);
                        }
                        DjCsBt.DjCsBt = djCsBtArray;
                    }else {
                        //清单
                        await this.convertDJCsQdDeRcj(measureProjectTables,rcjArray,qdArray,DjCs,unitProject)
                    }
                }
                DjCsBtArray.push(DjCsBt)
                DjCs.DjCsBt = DjCsBtArray;


            }

        }

        if (!ObjectUtils.isEmpty(QtZjCsQd)){
            let ZjCsMxArray = new Array();
            await this.convertQTZJCsQdDeRcj(QtZjCsQd, unitProject, measureProjectTables, ZjCsMxArray, QtZjCsBt,true);
        }

        ZjCsBtArray = [QtZjCsBt,...ZjCsBtArray];//把总价措施加入
        ZjCs.ZjCsBt = ZjCsBtArray;
        Csxm.ZjCs = ZjCs;
        Csxm.DjCs =DjCs;
        Dwgcxx.Csxm =Csxm;


    }

    //其他总价措施项目列表
    async QTZJCsXmlb(fxCode){

        if(ObjectUtils.isEmpty(fxCode)){
            return 0;
        }
        let res;
        fxCode = fxCode.slice(0,9);
        switch (fxCode) {
            case '011707001' :
                res =  1;
                break;
            case '021007001' :
                res =  1;
                break;
            case '031302001' :
                res =  1;
                break;
            case '041109001' :
                res =  1;
                break;
            case '050405001' :
                res =  1;
                break;
            case '060305001' :
                res =  1;
                break;
            case '070306001' :
                res =  1;
                break;
            case '081311001' :
                res =  1;
                break;


            case '011707002' :
                res =  2;
                break;
            case '021007002' :
                res =  2;
                break;
            case '031302002' :
                res =  2;
                break;
            case '041109002' :
                res =  2;
                break;
            case '050405002' :
                res =  2;
                break;
            case '060305002' :
                res =  2;
                break;
            case '070306002' :
                res =  2;
                break;
            case '081311002' :
                res =  2;
                break;

            case '011707004' :
                res =  3;
                break;
            case '021007004' :
                res =  3;
                break;
            case '021007004' :
                res =  3;
                break;
            case '041109003' :
                res =  3;
                break;
            case '050405003' :
                res =  3;
                break;
            case '060305003' :
                res =  3;
                break;
            case '070306003' :
                res =  3;
                break;
            case '081311003' :
                res =  3;
                break;

            case '011707007' :
                res =  4;
                break;
            case '021007007' :
                res =  4;
                break;
            case '050405008' :
                res =  4;
                break;
            case '070306006' :
                res =  4;
                break;
            case '031302006' :
                res =  4;
                break;
            case '041109007' :
                res =  4;
                break;
            case '060305006' :
                res =  4;
                break;
            case '081311006' :
                res =  4;
                break;


            case '011707B01' :
                res =  5;
                break;
            case '021007B01' :
                res =  5;
                break;
            case '031302B01' :
                res =  5;
                break;
            case '041109B01' :
                res =  5;
                break;
            case '050405B01' :
                res =  5;
                break;
            case '070306B01' :
                res =  5;
                break;

            case '011707B02' :
                res =  6;
                break;
            case '021007B02' :
                res =  6;
                break;
            case '031302B02' :
                res =  6;
                break;
            case '041109B02' :
                res =  6;
                break;
            case '050405B02' :
                res =  6;
                break;
            case '070306B02' :
                res =  6;
                break;

            case '011707B03' :
                res =  7;
                break;
            case '021007B03' :
                res =  7;
                break;
            case '031302B03' :
                res =  7;
                break;
            case '041109B03' :
                res =  7;
                break;
            case '050405B03' :
                res =  7;
                break;
            case '070306B03' :
                res =  7;
                break;

            case '011707B04' :
                res =  8;
                break;
            case '021007B04' :
                res =  8;
                break;
            case '031302B04' :
                res =  8;
                break;
            case '041109B04' :
                res =  8;
                break;
            case '050405B04' :
                res =  8;
                break;
            case '070306B04' :
                res =  8;
                break;

            case '011707B05' :
                res =  9;
                break;
            case '021007B05' :
                res =  9;
                break;
            case '031302B05' :
                res =  9;
                break;
            case '041109B05' :
                res =  9;
                break;
            case '050405B05' :
                res =  9;
                break;
            case '070306B05' :
                res =  9;
                break;


            case '011707B06' :
                res =  10;
                break;
            case '021007B06' :
                res =  10;
                break;
            case '031302B06' :
                res =  10;
                break;
            case '041109B06' :
                res =  10;
                break;
            case '050405B06' :
                res =  10;
                break;
            case '070306B06' :
                res =  10;
                break;



            case '011707B07' :
                res =  11;
                break;
            case '041109B07' :
                res =  11;
                break;
            case '031302B08' :
                res =  11;
                break;
            case '070306B07' :
                res =  11;
                break;

            case '011707B08' :
                res =  12;
                break;
            case '031302B09' :
                res =  12;
                break;
            case '041109B08' :
                res =  12;
                break;
            case '070306B08' :
                res =  12;
                break;


            case '041109005' :
                res =  13;
                break;


            case '021007B08' :
                res =  14;
                break;
            case '050405B08' :
                res =  14;
                break;

            case '021007B07' :
                res =  15;
                break;
            case '050405B07' :
                res =  15;
                break;


            case '041109B09' :
                res = 18 ;
                break;

            default:
                res  = 0;
                break;
        }
        return res;
    }

    //总价措施基数
    async connertZjcsJs(value){
        if(CalculateBaseType.RGFSCJ_JXFSCJ == value){
            return '人工费市场价+机械费市场价'
        }

        if(CalculateBaseType.RGFDEJ_JXFDEJ == value){
            return '人工费定额价+机械费定额价'
        }
        return '';
    }

    //其他总价措施清单的人材机
    async convertQTZJCsQdDeRcj(qdArray, unitProject, measureProjectTables, ZjCsMxArray, ZjCsBt,zjcsMark) {
        let memUnit = await PricingFileFindUtils.getUnit(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr);
        for (let j = 0; j < qdArray.length; j++) {
            let ZjCsMx = {}
            let qd = qdArray[j];
            ZjCsMx.Xh = qd.dispNo;
            ZjCsMx.Xmbm = qd.fxCode;
            ZjCsMx.Mc = qd.name;
            ZjCsMx.Js = memUnit.zjcsCostMathCache==undefined?'0':await this.connertZjcsJs( memUnit.zjcsCostMathCache.csfyCalculateBaseCode);
            ZjCsMx.Fl = '0'//清单的费率0 xhc
            ZjCsMx.Rgf = NumberUtil.getDefault(qd.rfee);
            ZjCsMx.Clf = NumberUtil.getDefault(qd.cfee);
            ZjCsMx.Jxf = NumberUtil.getDefault(qd.jfee);
            ZjCsMx.Rgjj = '0';
            ZjCsMx.Cljj = '0';
            ZjCsMx.Jxjj = '0';
            //单价构成
            let djgc = ObjectUtils.isEmpty(memUnit.feeBuild) ? null : memUnit.feeBuild[qd.sequenceNbr];
            if (!ObjectUtils.isEmpty(djgc)) {


                let glf =djgc.find(f=>f.type==="管理费")
                if(ObjectUtils.isNotEmpty(glf)){
                    ZjCsMx.Glf = NumberUtil.getDefault(NumberUtil.numberScale2( glf.allPrice));
                }else {
                    ZjCsMx.Glf = '0';
                }
                ZjCsMx.Glffl = '0'//清单的费率0 xhc

                let lr =djgc.find(f=>f.type==="利润")
                if(ObjectUtils.isNotEmpty(lr)){
                    ZjCsMx.Lr = NumberUtil.numberScale2( lr.allPrice);
                }else {
                    ZjCsMx.Lr = '0';
                }
                ZjCsMx.Lrfl = '0'//清单的费率0 xhc

                let gf = djgc.find(f => f && f.type === "规费")
                if(ObjectUtils.isNotEmpty(gf)){
                    ZjCsMx.Gf = NumberUtil.getDefault(NumberUtil.numberScale2(NumberUtil.numberScale2( gf.allPrice)));
                }else {
                    ZjCsMx.Gf = '0';
                }

                let aqwmf = djgc.find(f => f && f.type === "安全文明施工费");
                if(ObjectUtils.isNotEmpty(aqwmf)){
                    ZjCsMx.Aqwmf = NumberUtil.getDefault(NumberUtil.numberScale2( aqwmf.allPrice));
                }else {
                    ZjCsMx.Aqwmf = '0';
                }

            }else {
                ZjCsMx.Glf='0'
                ZjCsMx.Glffl='0'
                ZjCsMx.Lr='0'
                ZjCsMx.Lrfl='0'
                ZjCsMx.Gf='0'
                ZjCsMx.Aqwmf='0'
            }
            ZjCsMx.Je = NumberUtil.getDefault(qd.total);
            if(zjcsMark){
                ZjCsMx.Xmlb = await this.QTZJCsXmlb(qd.fxCode);
            }else {
                ZjCsMx.Xmlb = qd.projectType;
            }

            ZjCsMx.Rgdj = '0';//xhc说清单的值取0
            ZjCsMx.Bz = qd.description;
            //当前清单下的定额
            let deArrayFilter = measureProjectTables.filter(item => item.parentId === qd.sequenceNbr);

            if (!ObjectUtils.isEmpty(deArrayFilter)) {
                let deArray = deArrayFilter.filter(item => item.kind === '04');
                if (!ObjectUtils.isEmpty(deArray)) {

                    let ZjCsDe = {};
                    let ZjCsDeArray = new Array();
                    for (let k = 0; k < deArray.length; k++) {
                        let de = deArray[k];
                        let ZjCsDezjMx = {};
                        ZjCsDezjMx.Debm = de.fxCode;
                        ZjCsDezjMx.DeGuid = '';
                        ZjCsDezjMx.YsDebm = de.fxCode;
                        ZjCsDezjMx.Dekbz = await this.convertDekbz(de.description);
                        ZjCsDezjMx.Mc = de.bdName;
                        ZjCsDezjMx.Dw = de.unit;
                        ZjCsDezjMx.Sl = de.quantity
                        ZjCsDezjMx.Dj = de.price;
                        ZjCsDezjMx.Hj = de.total;
                        ZjCsDezjMx.Rgf = de.rfee;
                        ZjCsDezjMx.Zcf = ObjectUtils.isEmpty(de.zcfee)?'0':de.zcfee;
                        ZjCsDezjMx.Sbf = '0';
                        ZjCsDezjMx.Fcf = ObjectUtils.isEmpty(de.cfee)?'0':de.cfee;
                        ZjCsDezjMx.Clf = NumberUtil.numberScale2(NumberUtil.add(de.zcfee,de.cfee));
                        ZjCsDezjMx.Jxf = ObjectUtils.isEmpty(de.jfee)?'0':de.jfee;
                        let deDjgc =memUnit.feeBuild[de.sequenceNbr];
                        if (!ObjectUtils.isEmpty(deDjgc)) {
                            ZjCsDezjMx.Rgjj = NumberUtil.numberScale2(deDjgc.find(f => f.type === UnitPriceConstant.RGF_TYPE).unitPrice);
                            ZjCsDezjMx.Zcjj = NumberUtil.numberScale2(deDjgc.find(f => f.type === UnitPriceConstant.ZCF_TYPE).unitPrice);
                        } else {
                            ZjCsDezjMx.Rgjj = '0';
                            ZjCsDezjMx.Zcjj = '0';
                        }

                        ZjCsDezjMx.Fcjj = '0';
                        ZjCsDezjMx.Sbjj = '0';
                        if (!ObjectUtils.isEmpty(deDjgc)) {
                            ZjCsDezjMx.Cljj = NumberUtil.numberScale2(deDjgc.find(f => f.type === UnitPriceConstant.CLF_TYPE).unitPrice);
                            ZjCsDezjMx.Jxjj = NumberUtil.numberScale2(deDjgc.find(f => f.type === UnitPriceConstant.JXF_TYPE).unitPrice);

                        } else {
                            ZjCsDezjMx.Cljj = '0';
                            ZjCsDezjMx.Jxjj = '0';
                        }
                        ZjCsDezjMx.Glf = de.managerFee;
                        if (!ObjectUtils.isEmpty(deDjgc)) {
                            ZjCsDezjMx.Glffl = deDjgc.find(f => f.type === UnitPriceConstant.GLF_TYPE).rate;
                            ZjCsDezjMx.Glf = NumberUtil.numberScale2(deDjgc.find(f => f.type === UnitPriceConstant.GLF_TYPE).allPrice);
                        } else {
                            ZjCsDezjMx.Glffl = '0';
                        }

                        if (!ObjectUtils.isEmpty(deDjgc)) {
                            ZjCsDezjMx.Lrfl =deDjgc.find(f => f.type === UnitPriceConstant.LR_TYPE).rate;
                            ZjCsDezjMx.Lr = NumberUtil.numberScale2(deDjgc.find(f => f.type === UnitPriceConstant.LR_TYPE).allPrice);
                        } else {
                            ZjCsDezjMx.Lrfl = '0';
                        }
                        ZjCsDezjMx.Rgdj = de.totalRgdj;

                        //查询定额下的人材机
                        // let deRcjArry = rcjArray.filter(item =>item.deId ===de.sequenceNbr );
                        let deRcjArry = this.RcjMap.get(de.sequenceNbr)
                        if (!ObjectUtils.isEmpty(deRcjArry)) {
                            let Csxdercjhl = {};
                            let CsxdercjhlMxArray = new Array();
                            for (let l = 0; l < deRcjArry.length; l++) {
                                let deRcjElement = deRcjArry[l];
                                let CsxdercjhlMx = {};
                                CsxdercjhlMx.RcjId = deRcjElement.standardId;
                                CsxdercjhlMx.Rcjhl = ObjectUtils.isEmpty(deRcjElement.resQty) ? 0 : deRcjElement.resQty;
                                CsxdercjhlMx.RcjDehj = NumberUtil.multiplyToString(deRcjElement.dePrice, deRcjElement.totalNumber, 2);//单位定额现合价 Decimal 必填）
                                CsxdercjhlMx.Rcjhj = NumberUtil.multiplyToString(deRcjElement.marketPrice, deRcjElement.totalNumber, 2);
                                CsxdercjhlMxArray.push(CsxdercjhlMx);
                            }
                            Csxdercjhl.CsxdercjhlMx = CsxdercjhlMxArray;
                            ZjCsDezjMx.Csxdercjhl = Csxdercjhl;
                        }
                        ZjCsDeArray.push(ZjCsDezjMx);
                    }
                    ZjCsDe.ZjCsDezjMx = ZjCsDeArray;

                    ZjCsMx.ZjCsDe = ZjCsDe;
                }
            }
            ZjCsMxArray.push(ZjCsMx);


        }
        if(!ObjectUtils.isEmpty(ZjCsBt.ZjCsMx)){
            ZjCsBt.ZjCsMx = [...ZjCsBt.ZjCsMx,...ZjCsMxArray] ;
        }else {
            ZjCsBt.ZjCsMx = ZjCsMxArray;
        }
    }

    /**
     * 分布分项清单的人材机
     * 类型
     * @param itemBillProjects
     * @param rcjArray
     * @param filter1
     * @param QdBt
     * @returns {Promise<void>}
     */
    async convertFbFxQdDeRcj(itemBillProjects,rcjArray,filter1,QdBt,unitProject){
        let QdmxArray = new Array();
        let filterQd = filter1.filter(item =>item .kind === '03');
        if(!ObjectUtils.isEmpty(filterQd)){
            let unitTmp = await PricingFileFindUtils.getUnit(unitProject.constructId, unitProject.spId,unitProject.sequenceNbr);
            //清单
            for (let j = 0; j < filterQd.length; j++) {
                let filter1Element = filterQd[j];
                let Qdmx = {};
                Qdmx.Xh = filter1Element.dispNo;
                Qdmx.Qdbm = filter1Element.bdCode;
                Qdmx.Mc = filter1Element.name;
                Qdmx.Xmtz = filter1Element.projectAttr;
                Qdmx.Gcnr = '';//工作内容
                Qdmx.Jsgz = '';//计算规则
                Qdmx.Dw = filter1Element.unit;
                Qdmx.Sl = filter1Element.quantity;
                Qdmx.Zhdj = NumberUtil.getDefault(filter1Element.price);
                Qdmx.Rgf = NumberUtil.getDefault(filter1Element.rfee);
                Qdmx.Zcf =  NumberUtil.getDefault(filter1Element.zcfee);
                Qdmx.Sbf = '0.00';
                Qdmx.Fcf = NumberUtil.getDefault(filter1Element.cfee);//辅材费用
                Qdmx.Clf = NumberUtil.getDefault(NumberUtil.numberScale2(NumberUtil.add(filter1Element.zcfee,filter1Element.cfee)));
                Qdmx.Jxf = ObjectUtils.isEmpty(filter1Element.jfee)?'0':filter1Element.jfee;
                Qdmx.Rgjj = '0.00';
                Qdmx.Zcjj = '0.00';
                Qdmx.Fcjj = '0.00';
                Qdmx.Sbjj = '0.00';
                Qdmx.Cljj = '0.00';
                Qdmx.Jxjj = '0.00';
                let feeBuildElement = ObjectUtils.isEmpty(unitTmp.feeBuild) ? null : unitTmp.feeBuild[filter1Element.sequenceNbr];
                if(!ObjectUtils.isEmpty(feeBuildElement)){

                    let glf =feeBuildElement.find(f=>f.type==="管理费")
                    if(ObjectUtils.isNotEmpty(glf)){
                        Qdmx.Glf = NumberUtil.getDefault(NumberUtil.numberScale2( glf.allPrice));
                    }else {
                        Qdmx.Glf = '0.00';
                    }

                    let lr =feeBuildElement.find(f=>f.type==="利润")
                    if(ObjectUtils.isNotEmpty(lr)){
                        Qdmx.Lr = NumberUtil.getDefault(NumberUtil.numberScale2( lr.allPrice));
                    }else {
                        Qdmx.Lr = '0.00';
                    }

                    let gf =feeBuildElement.find(f=>f.type==="规费")
                    if(ObjectUtils.isNotEmpty(gf)){
                        Qdmx.Gf =    NumberUtil.numberScale2( gf.allPrice);
                    }else {
                        Qdmx.Gf = '0.00';
                    }

                    let aqwmf = feeBuildElement.find(f=>f.type==="安全文明施工费");
                    if(ObjectUtils.isNotEmpty(aqwmf)){
                        Qdmx.Aqwmf = NumberUtil.numberScale2( aqwmf.allPrice);
                    }else {
                        Qdmx.Aqwmf = '0.00';
                    }

                }else {
                    Qdmx.Glf = '0.00';
                    Qdmx.Lr = '0.00';
                    Qdmx.Gf = '0.00';
                    Qdmx.Aqwmf ='0.00';
                }
                Qdmx.Zgj =   '0.00';
                Qdmx.Zhhj = NumberUtil.getDefault(filter1Element.total);
                Qdmx.Rgdj = NumberUtil.getDefault(filter1Element.rfeePrice);
                Qdmx.Bz = filter1Element.description;

                //定额
                let deFilter = itemBillProjects.filter(item => item.parentId === filter1Element.sequenceNbr);

                if(!ObjectUtils.isEmpty(deFilter)){
                    let Qdxdezj = {};
                    let deArray = new Array();
                    //存放当前清单下所有人才机
                    let qdRcjArray = new Array();
                    for (let k = 0; k < deFilter.length; k++) {
                        let deFilterElement = deFilter[k];
                        let QdxdezjMx ={};
                        QdxdezjMx.Debm = deFilterElement.bdCode;
                        QdxdezjMx.DeGuid = "";
                        QdxdezjMx.YsDebm = "";
                        QdxdezjMx.Dekbz  =await this.convertDekbz(deFilterElement.description);;
                        QdxdezjMx.Mc = deFilterElement.bdName;
                        QdxdezjMx.Dw = deFilterElement.unit;
                        QdxdezjMx.Sl = deFilterElement.quantity;
                        QdxdezjMx.Dj = deFilterElement.price;
                        QdxdezjMx.Hj = deFilterElement.total;
                        QdxdezjMx.Rgf = deFilterElement.rfee;
                        QdxdezjMx.Zcf = ObjectUtils.isEmpty(deFilterElement.zcfee)?'0':deFilterElement.zcfee
                        QdxdezjMx.Sbf = '0';
                        QdxdezjMx.Fcf = ObjectUtils.isEmpty(deFilterElement.cfee)?'0':deFilterElement.cfee;
                        QdxdezjMx.Clf = NumberUtil.numberScale2(NumberUtil.add(deFilterElement.zcfee,deFilterElement.cfee));
                        QdxdezjMx.Jxf = ObjectUtils.isEmpty(deFilterElement.jfee)?'0':deFilterElement.jfee;
                        QdxdezjMx.Rgjj =  '0';
                        QdxdezjMx.Zcjj =  '0';
                        QdxdezjMx.Fcjj =  '0';
                        QdxdezjMx.Sbjj =  '0';
                        QdxdezjMx.Cljj =  '0';
                        QdxdezjMx.Jxjj =  '0';
                        let deFeeBuild = ObjectUtils.isEmpty(unitTmp.feeBuild) ? null : unitTmp.feeBuild[deFilterElement.sequenceNbr];
                        if(ObjectUtils.isNotEmpty(deFeeBuild)){
                            let glf =deFeeBuild.find(f=>f.type==="管理费")
                            if(ObjectUtils.isNotEmpty(glf)){
                                QdxdezjMx.Glf = NumberUtil.numberScale2( glf.allPrice);
                                QdxdezjMx.Glffl = glf.rate;
                            }else {
                                QdxdezjMx.Glf = '0';
                                QdxdezjMx.Glffl = '';
                            }

                            let lr =deFeeBuild.find(f=>f.type==="利润")
                            if(ObjectUtils.isNotEmpty(lr)){
                                QdxdezjMx.Lr = NumberUtil.numberScale2( lr.allPrice);
                                QdxdezjMx.Lrfl =  lr.rate;
                            }else {
                                QdxdezjMx.Lr = '0';
                                QdxdezjMx.Lrfl = '';
                            }
                        }else {
                            QdxdezjMx.Glf = '0';
                            QdxdezjMx.Glffl = '';
                            QdxdezjMx.Lr = '0';
                            QdxdezjMx.Lrfl = '';
                        }
                        QdxdezjMx.Rgdj = deFilterElement.rfeePrice;
                        //查询定额下的人材机
                        // let deRcjArry = rcjArray.filter(item =>item.deId ===deFilterElement.sequenceNbr );
                        let deRcjArry = this.RcjMap.get(deFilterElement.sequenceNbr)
                        if(!ObjectUtils.isEmpty(deRcjArry)){
                            let Qdxdercjhl = {};
                            let QdxdercjhlMxArray = new Array();
                            for (let l = 0; l < deRcjArry.length; l++) {
                                let deRcjElement = deRcjArry[l];
                                let QdxdercjhlMx = {};
                                QdxdercjhlMx.RcjId =deRcjElement.standardId ;
                                QdxdercjhlMx.Rcjhl =ObjectUtils.isEmpty(deRcjElement.resQty)?0:deRcjElement.resQty;
                                QdxdercjhlMx.RcjDehj =NumberUtil.multiplyToString(deRcjElement.dePrice,deRcjElement.totalNumber,2);//单位定额现合价 Decimal 必填）
                                QdxdercjhlMx.Rcjhj   =NumberUtil.multiplyToString(deRcjElement.marketPrice,deRcjElement.totalNumber,2);
                                QdxdercjhlMxArray.push(QdxdercjhlMx);
                                qdRcjArray.push(deRcjElement);
                            }
                            Qdxdercjhl.QdxdercjhlMx  =QdxdercjhlMxArray;

                            QdxdezjMx.Qdxdercjhl = Qdxdercjhl;
                        }
                        deArray.push(QdxdezjMx);
                    }
                    Qdxdezj.QdxdezjMx = deArray;
                    Qdmx.Qdxdezj =Qdxdezj;

                    //Qdxrcjhl 此处是查询当前清单下所有定额下的人材机并根据编码分组求和
                    let Qdxrcjhl = {};
                    if(!ObjectUtils.isEmpty(qdRcjArray)){
                        let group = ArrayUtil.group(qdRcjArray,'materialCode');
                        let QdxdercjhlMxS = new Array();
                        for ( let [key, value] of group.entries()) {
                            let QdxdercjhlMx = {};
                            QdxdercjhlMx.RcjId =value[0].standardId ;
                            QdxdercjhlMx.Rcjhl =value.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add( accumulator , constructProjectRcj.resQty);
                            }, 0);
                            QdxdercjhlMx.RcjDehj =value.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add( accumulator , NumberUtil.multiply(constructProjectRcj.dePrice,constructProjectRcj.totalNumber) );
                            }, 0);
                            QdxdercjhlMx.Rcjhj =value.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add( accumulator , NumberUtil.multiply(constructProjectRcj.marketPrice,constructProjectRcj.totalNumber) );
                            }, 0);
                            QdxdercjhlMxS.push(QdxdercjhlMx);
                        }
                        Qdxrcjhl.QdxdercjhlMx = QdxdercjhlMxS;
                    }
                    Qdmx.Qdxrcjhl = Qdxrcjhl;
                }
                QdmxArray.push(Qdmx)
            }
            QdBt.Qdmx = QdmxArray;
        }
    }

    //单价措施清单的人材机
    async convertDJCsQdDeRcj(measureProjectTables,rcjArray,filter1,DjCsBt,unitProject){
        let memUnit = await PricingFileFindUtils.getUnit(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr);
        let djCsMxArray = new Array();
        let filterQd = filter1.filter(item =>item .kind === '03');
        if(!ObjectUtils.isEmpty(filterQd)){
            //清单
            for (let j = 0; j < filterQd.length; j++) {
                let filter1Element = filterQd[j];
                let DjCsMx = {};
                DjCsMx.Xh = filter1Element.dispNo;
                DjCsMx.Qdbm = filter1Element.bdCode;
                DjCsMx.Mc = filter1Element.bdName;
                DjCsMx.Xmtz = filter1Element.projectAttr;
                DjCsMx.Gcnr = '';//工作内容
                DjCsMx.Jsgz = '';//计算规则
                DjCsMx.Dw = filter1Element.unit;
                DjCsMx.Sl = filter1Element.quantity;
                DjCsMx.Zhdj = filter1Element.price;
                DjCsMx.Rgf = filter1Element.rfee;
                DjCsMx.Zcf = ObjectUtils.isEmpty(filter1Element.zcfee)?'0':filter1Element.zcfee;
                DjCsMx.Sbf = '0';
                DjCsMx.Fcf = ObjectUtils.isEmpty(filter1Element.zcfee)?'0':filter1Element.cfee;//辅材费用
                DjCsMx.Clf = NumberUtil.numberScale2(NumberUtil.add(filter1Element.zcfee,filter1Element.cfee));
                DjCsMx.Jxf = ObjectUtils.isEmpty(filter1Element.jfee)?'0':filter1Element.jfee;
                DjCsMx.Rgjj = '0';
                DjCsMx.Zcjj = '0';
                DjCsMx.Fcjj = '0';
                DjCsMx.Sbjj = '0';
                DjCsMx.Cljj = '0';
                DjCsMx.Jxjj = '0';
                DjCsMx.Glf = filter1Element.managerFee;
                DjCsMx.Lr = filter1Element.profitFee;


                let djgc = ObjectUtils.isEmpty(memUnit.feeBuild) ? null : memUnit.feeBuild[filter1Element.sequenceNbr];
                if (!ObjectUtils.isEmpty(djgc)) {


                    let glf =djgc.find(f=>f.type==="管理费")
                    if(ObjectUtils.isNotEmpty(glf)){
                        DjCsMx.Glf = NumberUtil.numberScale2( glf.allPrice);
                    }else {
                        DjCsMx.Glf = '0';
                    }

                    let lr =djgc.find(f=>f.type==="利润")
                    if(ObjectUtils.isNotEmpty(lr)){
                        DjCsMx.Lr = NumberUtil.numberScale2( lr.allPrice);
                    }else {
                        DjCsMx.Lr = '0';
                    }

                    let gf = djgc.find(f => f && f.type === "规费")
                    if(ObjectUtils.isNotEmpty(gf)){
                        DjCsMx.Gf =    NumberUtil.numberScale2( gf.allPrice);
                    }else {
                        DjCsMx.Gf = '';
                    }

                    let aqwmf = djgc.find(f => f && f.type === "安全文明施工费");
                    if(ObjectUtils.isNotEmpty(aqwmf)){
                        DjCsMx.Aqwmf = NumberUtil.numberScale2( aqwmf.allPrice);
                    }else {
                        DjCsMx.Aqwmf = '';
                    }

                }else {
                    DjCsMx.Glf='0'
                    DjCsMx.Lr='0'
                    DjCsMx.Gf='0'
                    DjCsMx.Aqwmf='0'
                }
                DjCsMx.Zgj =   '';
                DjCsMx.Zhhj = filter1Element.total;
                DjCsMx.Rgdj = filter1Element.rfeePrice;
                DjCsMx.Bz = filter1Element.description;

                //定额
                let deFilter = measureProjectTables.filter(item => item.parentId === filter1Element.sequenceNbr);

                if(!ObjectUtils.isEmpty(deFilter)){
                    let Csxdezj = {};
                    let deArray = new Array();
                    //存放当前清单下所有人才机
                    let qdRcjArray = new Array();
                    for (let k = 0; k < deFilter.length; k++) {
                        let deFilterElement = deFilter[k];
                        let CsxdezjMx ={};
                        CsxdezjMx.Debm = deFilterElement.bdCode;
                        CsxdezjMx.DeGuid = "";
                        CsxdezjMx.YsDebm = "";
                        CsxdezjMx.Dekbz  =await this.convertDekbz(deFilterElement.description);
                        CsxdezjMx.Mc = deFilterElement.bdName;
                        CsxdezjMx.Dw = deFilterElement.unit;
                        CsxdezjMx.Sl = deFilterElement.quantity;
                        CsxdezjMx.Dj = deFilterElement.price;
                        CsxdezjMx.Hj = deFilterElement.total;
                        CsxdezjMx.Rgf = deFilterElement.rfee;
                        CsxdezjMx.Zcf = ObjectUtils.isEmpty(deFilterElement.zcfee)?'0':deFilterElement.zcfee;
                        CsxdezjMx.Sbf = '0';
                        CsxdezjMx.Fcf = ObjectUtils.isEmpty(deFilterElement.cfee)?'0':deFilterElement.cfee;
                        CsxdezjMx.Clf = NumberUtil.numberScale2(NumberUtil.add(deFilterElement.zcfee,deFilterElement.cfee));
                        CsxdezjMx.Jxf = ObjectUtils.isEmpty(deFilterElement.zcfee)?'0':deFilterElement.jfee;
                        CsxdezjMx.Rgjj =  '0';
                        CsxdezjMx.Zcjj =  '0';
                        CsxdezjMx.Fcjj =  '0';
                        CsxdezjMx.Sbjj =  '0';
                        CsxdezjMx.Cljj =  '0';
                        CsxdezjMx.Jxjj =  '0';

                        let deFeeBuild = ObjectUtils.isEmpty(memUnit.feeBuild) ? null : memUnit.feeBuild[deFilterElement.sequenceNbr];
                        if(ObjectUtils.isNotEmpty(deFeeBuild)){

                            let glf =deFeeBuild.find(f=>f.type==="管理费")
                            if(ObjectUtils.isNotEmpty(glf)){
                                CsxdezjMx.Glf = NumberUtil.numberScale2( glf.allPrice);
                                CsxdezjMx.Glffl = glf.rate;
                            }else {
                                CsxdezjMx.Glf = '0';
                                CsxdezjMx.Glffl = '0';
                            }

                            let lr =deFeeBuild.find(f=>f.type==="利润")
                            if(ObjectUtils.isNotEmpty(lr)){
                                CsxdezjMx.Lr = NumberUtil.numberScale2( lr.allPrice);
                                CsxdezjMx.Lrfl =  lr.rate;
                            }else {
                                CsxdezjMx.Lr = '0';
                                CsxdezjMx.Lrfl = '0';
                            }
                        }else {
                           CsxdezjMx.Glf = '0';
                           CsxdezjMx.Glffl = '0';
                           CsxdezjMx.Lr = '0';
                           CsxdezjMx.Lrfl = '0';
                        }
                        let deRcjArry = this.RcjMap.get(deFilterElement.sequenceNbr)
                        if(!ObjectUtils.isEmpty(deRcjArry)){
                            let rgfRcj = deRcjArry.filter(item => (item.deId ==deFilterElement.sequenceNbr && item.type =='人工费' ))
                            if(ObjectUtils.isNotEmpty(rgfRcj) &&   ObjectUtils.isNotEmpty(rgfRcj[0].marketPrice)){
                                CsxdezjMx.Rgdj = rgfRcj[0].marketPrice
                            }else {
                                CsxdezjMx.Rgdj = '0'
                            }
                        }else {
                            CsxdezjMx.Rgdj = '0';
                        }

                        //查询定额下的人材机
                        // let deRcjArry = rcjArray.filter(item =>item.deId ===deFilterElement.sequenceNbr );
                        if(!ObjectUtils.isEmpty(deRcjArry)){
                            let Csxrcjhl = {};
                            let CsxrcjhlMxArray = new Array();
                            for (let l = 0; l < deRcjArry.length; l++) {
                                let deRcjElement = deRcjArry[l];
                                let CsxdercjhlMx = {};
                                CsxdercjhlMx.RcjId =deRcjElement.standardId ;
                                CsxdercjhlMx.Rcjhl =ObjectUtils.isEmpty(deRcjElement.resQty)?0:deRcjElement.resQty;
                                CsxdercjhlMx.RcjDehj =NumberUtil.multiplyToString(deRcjElement.dePrice,deRcjElement.totalNumber,2);//单位定额现合价 Decimal 必填）
                                CsxdercjhlMx.Rcjhj   =NumberUtil.multiplyToString(deRcjElement.marketPrice,deRcjElement.totalNumber,2);
                                CsxrcjhlMxArray.push(CsxdercjhlMx);
                                qdRcjArray.push(deRcjElement);
                            }
                            Csxrcjhl.CsxdercjhlMx  =CsxrcjhlMxArray;

                            CsxdezjMx.Csxrcjhl = Csxrcjhl;
                        }
                        deArray.push(CsxdezjMx);
                    }
                    Csxdezj.CsxdezjMx = deArray;
                    DjCsMx.Csxdezj =Csxdezj;

                    //Csxrcjhl 此处是查询当前清单下所有定额下的人材机并根据编码分组求和
                    let Csxrcjhl = {};
                    if(!ObjectUtils.isEmpty(qdRcjArray)){
                        let group = ArrayUtil.group(qdRcjArray,'materialCode');
                        let CsxdercjhlMxS = new Array();
                        for ( let [key, value] of group.entries()) {
                            let CsxdercjhlMx = {};
                            CsxdercjhlMx.RcjId =value[0].standardId ;
                            CsxdercjhlMx.Rcjhl =value.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add( accumulator , constructProjectRcj.resQty);
                            }, 0);
                            CsxdercjhlMx.RcjDehj =value.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add( accumulator , NumberUtil.multiply(constructProjectRcj.dePrice,constructProjectRcj.totalNumber) );
                            }, 0);
                            CsxdercjhlMx.Rcjhj =value.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add( accumulator , NumberUtil.multiply(constructProjectRcj.marketPrice,constructProjectRcj.totalNumber) );
                            }, 0);
                            CsxdercjhlMxS.push(CsxdercjhlMx);
                        }
                        Csxrcjhl.CsxdercjhlMx = CsxdercjhlMxS;
                    }
                    DjCsMx.Csxrcjhl = Csxrcjhl;
                }
                djCsMxArray.push(DjCsMx)
            }
            DjCsBt.DjCsMx = djCsMxArray;
        }
    }
    /**
     * 其他项目
     * @param Dwgcxx
     * @param otherProjects
     * @returns {Promise<void>}
     */
    async convertQtxm(Dwgcxx, otherProjects,feeFiles) {
        let Qtxm ={};
        if(ObjectUtils.isEmpty(otherProjects)){
            Dwgcxx.Qtxm =Qtxm;
            return ;
        }
        let mainFeeFiles = {};
        if(!ObjectUtils.isEmpty(feeFiles)){
            mainFeeFiles  = feeFiles.find(item => item.defaultFeeFlag === 1);
        }else {
            mainFeeFiles.anwenRateBase = 0;
        }


        let QtxmMxArray = new Array();
        for (let i = 0; i < otherProjects.length; i++) {
            let otherProject = otherProjects[i];
            let QtxmMx ={};
            QtxmMx.Xh = otherProject.dispNo ;
            QtxmMx.Mc = otherProject.extraName ;
            QtxmMx.Dw = otherProject.unit ;
            QtxmMx.Jsgs = otherProject.calculationBase ;
            QtxmMx.Je  = otherProject.total ;
            QtxmMx.Aqwmf  = NumberUtil.multiplyToString(mainFeeFiles.anwenRateBase/100,otherProject.total,2) ;
            QtxmMx.Aqwmffl = mainFeeFiles.anwenRateBase ;
            QtxmMx.Bz = otherProject.description ;
            QtxmMx.Xmlb = await this.handleQtxmXmlb(otherProject.type);
            QtxmMxArray.push(QtxmMx)
        }
        Qtxm.QtxmMx =QtxmMxArray ;
        Dwgcxx.Qtxm =Qtxm;
    }

    /**
     * 其他项目项目类别转换
     * @returns {Promise<string>}
     */
    async handleQtxmXmlb(type){

        let res =''
        switch (type) {
            case OtherProjectCalculationBaseConstant.zljr :{
                res = '1'
                break;
            }
            case OtherProjectCalculationBaseConstant.zgj :{
                res = '2'
                break;
            }
            case OtherProjectCalculationBaseConstant.clzgj :{
                res = '2.1'
                break;
            }
            case OtherProjectCalculationBaseConstant.sbzgj :{
                res = '2.2'
                break;
            }
            case OtherProjectCalculationBaseConstant.zygczgj :{
                res = '2.3'
                break;
            }
            case OtherProjectCalculationBaseConstant.jrg :{
                res = '3'
                break;
            }
            case OtherProjectCalculationBaseConstant.zcbfwf :{
                res = '4'
                break;
            }


        }
        return res
    }

    //暂列金额
    async convertZlje(Dwgcxx, otherProjectProvisionals) {
        let zljeMxArray = new Array();
        let Zlje ={};
        if(ObjectUtils.isEmpty(otherProjectProvisionals)){
            let ZljeMx = {};
            ZljeMx.Xh = '';
            ZljeMx.Mc = '';
            ZljeMx.Dw = '';
            ZljeMx.Zdje = '0.00';
            ZljeMx.Csxs = '0.00';
            ZljeMx.Jxse = '0.00';
            ZljeMx.Bz = '';
            zljeMxArray.push(ZljeMx)
            Dwgcxx.Zlje = zljeMxArray;
            return
        }
        for (let i = 0; i < otherProjectProvisionals.length; i++) {
            let otherProjectProvisional = otherProjectProvisionals[i];
            let ZljeMx = {};
            ZljeMx.Xh = otherProjectProvisional.dispNo;
            ZljeMx.Mc = otherProjectProvisional.name;
            ZljeMx.Dw = otherProjectProvisional.unit;
            ZljeMx.Zdje = otherProjectProvisional.provisionalSum;
            ZljeMx.Csxs = NumberUtil.getDefault(otherProjectProvisional.taxRemoval);
            ZljeMx.Jxse = NumberUtil.getDefault(otherProjectProvisional.jxTotal);
            ZljeMx.Bz = otherProjectProvisional.description;
            zljeMxArray.push(ZljeMx)
        }
        Zlje.ZljeMx =zljeMxArray;
        Dwgcxx.Zlje = Zlje;
    }

    /**
     * 材料暂估价
     * @param Dwgcxx
     * @param otherProjectClZgjs
     * @returns {Promise<void>}
     */
    async convertClzg(Dwgcxx, otherProjectClZgjs) {
        let clzgMxArray = new Array();
        let Clzg ={};
        if(ObjectUtils.isEmpty(otherProjectClZgjs)){
            let ClzgMx = {};
            ClzgMx.Xh = '';
            ClzgMx.RcjId = '';
            ClzgMx.Clbh  = '';
            ClzgMx.Mc = '';
            ClzgMx.Ggxh = '';
            ClzgMx.Dw  = '';
            ClzgMx.Sl  = '0.00';


            ClzgMx.Dj  = '0.00';
            clzgMxArray.push(ClzgMx)
            Dwgcxx.Clzg =clzgMxArray;
            return
        }

        for (let i = 0; i < otherProjectClZgjs.length; i++) {
            let otherProjectZgj = otherProjectClZgjs[i];
            let ClzgMx = {};

            ClzgMx.Xh = otherProjectZgj.dispNo;
            ClzgMx.RcjId = '';
            ClzgMx.Clbh  = otherProjectZgj.unit;
            ClzgMx.Mc = otherProjectZgj.name;
            ClzgMx.Ggxh = otherProjectZgj.attr;
            ClzgMx.Dw  = otherProjectZgj.unit;
            ClzgMx.Sl  = '';
            ClzgMx.Dj  = otherProjectZgj.price;
            clzgMxArray.push(ClzgMx)
        }
        Clzg.ZljeMx =clzgMxArray;
        Dwgcxx.Clzg = Clzg;
    }

    //设备暂估价
    async convertSbzg(Dwgcxx, otherProjectSbZgjs) {
        let SbzgMxArray = new Array();
        let Sbzg ={};
        if(ObjectUtils.isEmpty(otherProjectSbZgjs)){
            let SbzgMx = {};
            SbzgMx.Xh = '';
            SbzgMx.Rcjld = '';
            SbzgMx.Sbbh = '';
            SbzgMx.Mc  = '';
            SbzgMx.Ggxh  = '';
            SbzgMx.Dw = '';
            SbzgMx.Sl = '0.00';
            SbzgMx.Dj = '0.00';
            SbzgMx.Hj = '0.00';
            SbzgMx.Bz = '';
            SbzgMxArray.push(SbzgMx);
            Dwgcxx.Sbzg = SbzgMxArray;
            return
        }
    }

    /**
     * 专用工程暂估价
     * @param Dwgcxx
     * @param otherProjectZygcZgjs
     * @returns {Promise<void>}
     */
    async convertZygczg(Dwgcxx, otherProjectZygcZgjs) {
        let Zygczg = {}
        let zygczgMxArray =new Array();
        if(ObjectUtils.isEmpty(otherProjectZygcZgjs)){
            let ZygczgMx = {};
            ZygczgMx.Xh = '';
            ZygczgMx.Mc = '';
            ZygczgMx.Gcnr = '';
            ZygczgMx.Dw  = '';
            ZygczgMx.Je = '0.00';
            ZygczgMx.Csxs  = '0.00';
            ZygczgMx.Jxse = '0.00';
            ZygczgMx.Bz = '';
            zygczgMxArray.push(ZygczgMx);
            Dwgcxx.Zygczg =zygczgMxArray;
            return
        }

        for (let i = 0; i < otherProjectZygcZgjs.length; i++) {
            let otherProjectZygcZgj = otherProjectZygcZgjs[i];
            let ZygczgMx = {};
            ZygczgMx.Xh = otherProjectZygcZgj.dispNo ;
            ZygczgMx.Gcmc = otherProjectZygcZgj.name;
            ZygczgMx.Gcnr = otherProjectZygcZgj.content;
            ZygczgMx.Dw  = otherProjectZygcZgj.unit ;
            ZygczgMx.Je = otherProjectZygcZgj.total ;
            ZygczgMx.Csxs  = otherProjectZygcZgj.taxRemoval ;
            ZygczgMx.Jxse = otherProjectZygcZgj.jxTotal ;
            ZygczgMx.Bz = otherProjectZygcZgj.description ;
            zygczgMxArray.push(ZygczgMx);
        }
        Zygczg.ZygczgMx = zygczgMxArray;
        Dwgcxx.Zygczg =Zygczg;
    }

    /**
     * 总承包服务费
     * @param Dwgcxx
     * @param otherProjectServiceCosts
     * @returns {Promise<void>}
     */
    async convertZcbfwf(Dwgcxx, otherProjectServiceCosts) {
        let Zcbfwf ={};
        if(ObjectUtils.isEmpty(otherProjectServiceCosts)){
            Dwgcxx.Zcbfwf = Zcbfwf;
            return
        }
        let parentList = otherProjectServiceCosts.filter(item =>item.parentId === null);
        let sonList = otherProjectServiceCosts.filter(item =>item.parentId !== null);

        let  zcbfwfMxS = new Array()

        if(!ObjectUtils.isEmpty(sonList)){
            for (let i = 0; i < sonList.length; i++) {
                let filterElement = sonList[i];
                let ZcbfwfMx = {};
                ZcbfwfMx.Xh = filterElement.dispNo;
                ZcbfwfMx.Mc = filterElement.fxName;
                ZcbfwfMx.Xmjz = filterElement.xmje;
                ZcbfwfMx.Fl = filterElement.rate;
                ZcbfwfMx.Je = filterElement.fwje;
                zcbfwfMxS.push(ZcbfwfMx);
            }
        }


        if(ObjectUtils.isEmpty(parentList)){
            //如果没有标题只生成明细数据
            if(!ObjectUtils.isEmpty(sonList)){

                Zcbfwf.ZcbfwfMx =zcbfwfMxS;
                Dwgcxx.Zcbfwf = Zcbfwf;
                return ;
            }
        }



        let ZcbfwfBtArray = new Array();

        for (let i = 0; i < parentList.length; i++) {

            let element = parentList[i];

            let ZcbfwfBt = {};
            ZcbfwfBt.Xh = element.dispNo;
            ZcbfwfBt.Mc = element.fxName;
            ZcbfwfBt.Je = element.fwje;

            let zcbfwfMxArray = new Array();

            //获取当前标题下的子项
            let filter = sonList.filter(item => item.parentId === element.sequenceNbr );
            if(!ObjectUtils.isEmpty(filter)){
                for (let j = 0; j < filter.length; j++) {
                    let filterElement = filter[j];
                    let ZcbfwfMx = {};
                    ZcbfwfMx.Xh = filterElement.dispNo;
                    ZcbfwfMx.Mc = filterElement.fxName;
                    ZcbfwfMx.Xmjz = filterElement.xmje;
                    ZcbfwfMx.Fl = filterElement.rate;
                    ZcbfwfMx.Je = filterElement.fwje;
                    zcbfwfMxArray.push(ZcbfwfMx);
                }
                ZcbfwfBt.ZcbfwfMx = zcbfwfMxArray;

            }
            ZcbfwfBtArray.push(ZcbfwfBt)

        }
        Zcbfwf.ZcbfwfBt =ZcbfwfBtArray;
        Zcbfwf.ZcbfwfMx =zcbfwfMxS;
        Dwgcxx.Zcbfwf = Zcbfwf;

    }

    /**
     * 签证与索赔
     * @param Dwgcxx
     * @param otherProjectVisaAndClaim
     * @returns {Promise<void>}
     */
    async convertVisaAndClaim(Dwgcxx, otherProjectVisaAndClaim) {
        let VisaAndClaimArray = new Array();
        let VisaAndClaim ={};
        if(ObjectUtils.isEmpty(otherProjectVisaAndClaim)){
            let VisaAndClaimMx = {};
            VisaAndClaimMx.Xh = '';
            VisaAndClaimMx.Mc = '';
            VisaAndClaimMx.Dw = '';
            VisaAndClaimMx.Zdje = '0.00';
            VisaAndClaimMx.Csxs = '0.00';
            VisaAndClaimMx.Jxse = '0.00';
            VisaAndClaimMx.Bz = '';
            VisaAndClaimArray.push(VisaAndClaimMx)
            Dwgcxx.VisaAndClaim = VisaAndClaimArray;
            return
        }
        for (let i = 0; i < otherProjectVisaAndClaim.length; i++) {
            let VisaAndClaim = otherProjectVisaAndClaim[i];
            let VisaAndClaimMx = {};
            VisaAndClaimMx.Xh = VisaAndClaim.dispNo;
            VisaAndClaimMx.Mc = VisaAndClaim.project;
            VisaAndClaimMx.Dw = VisaAndClaim.type;
            VisaAndClaimMx.Zdje = VisaAndClaim.total;
            VisaAndClaimMx.Csxs = NumberUtil.getDefault(VisaAndClaim.price);
            VisaAndClaimMx.Jxse = NumberUtil.getDefault(VisaAndClaim.amount);
            VisaAndClaimMx.Bz = VisaAndClaim.zhPrice;
            VisaAndClaimArray.push(VisaAndClaimMx)
        }
        VisaAndClaim.VisaAndClaimMx = VisaAndClaimArray;
        Dwgcxx.VisaAndClaim = VisaAndClaim;
    }

    //计日工
    async convertJrg(Dwgcxx, otherProjectDayWorks) {
        let Jrg ={}
        if(ObjectUtils.isEmpty(otherProjectDayWorks)){
            Dwgcxx.jrg =Jrg;
            return;
        }
        let parentList = otherProjectDayWorks.filter(item =>item.parentId === null);
        let sonList = otherProjectDayWorks.filter(item =>item.parentId !== null);

        let JrgMxS  = new Array();
        if(ObjectUtils.isEmpty(parentList)){
            //如果没有标题只生成明细数据
            if(!ObjectUtils.isEmpty(sonList)){

                for (let i = 0; i < sonList.length; i++) {
                    let sonListElement = sonList[i];
                    let  JrgMx = {};
                    JrgMx.Xh = sonListElement.dispNo;
                    JrgMx.Mc = sonListElement.worksName;
                    JrgMx.Dw = sonListElement.unit;
                    JrgMx.Zdsl = sonListElement.tentativeQuantity;
                    JrgMx.Zhdj = sonListElement.price;
                    JrgMx.Zhhj = sonListElement.total;
                    JrgMx.Csxs = sonListElement.taxRemoval;
                    JrgMx.Jxse = sonListElement.jxTotal;
                    JrgMxS.push(JrgMx)
                }
                Dwgcxx.JrgMx = JrgMxS;
                return ;
            }
        }else {
            let JrgBtArray = new Array();
            for (let i = 0; i < parentList.length; i++) {
                let parentListElement = parentList[i];
                let JrgBt = {};
                JrgBt.Xh = parentListElement.dispNo;
                JrgBt.Mc = parentListElement.worksName;
                JrgBt.Je = parentListElement.total;
                JrgBt.LB = ''
                if(JrgBt.Mc!== undefined){
                    //遍历枚举
                    for(let key in CostTypeJrgEnum){
                        if(JrgBt.Mc === CostTypeJrgEnum[key].desc){
                            JrgBt.LB = CostTypeJrgEnum[key].code;
                        }
                    }
                }
                let JrgMxArray  = new Array();
                //获取当前标题下的子项
                let filter = sonList.filter(item => item.parentId === parentListElement.sequenceNbr );

                if(!ObjectUtils.isEmpty(filter)){
                    for (let j = 0; j < filter.length; j++) {
                        let filterElement = filter[j];
                        let  JrgMx = {};
                        JrgMx.Xh   = filterElement.dispNo;
                        JrgMx.Mc   = filterElement.worksName;
                        //规格型号
                        JrgMx.Ggxh = filterElement.specification;
                        JrgMx.Dw   = filterElement.unit;
                        JrgMx.Zdsl = filterElement.tentativeQuantity;
                        JrgMx.Zhdj = filterElement.price;
                        JrgMx.Zhhj = filterElement.total;
                        JrgMx.Csxs = filterElement.taxRemoval;
                        JrgMx.Jxse = filterElement.jxTotal;
                        JrgMxArray.push(JrgMx)

                    }
                    JrgBt.JrgMx = JrgMxArray;
                }
                JrgBtArray.push(JrgBt)
            }
            Jrg.JrgBt =JrgBtArray;
            Dwgcxx.Jrg =Jrg;
        }

    }



    //人材机汇总
    async convertRcjhz(Dwgcxx, unitProject) {

        let Rcjhz ={};

        let rcjArray =this.rcjArray;

        if(ObjectUtils.isEmpty(rcjArray)){
            Dwgcxx.Rcjhz =Rcjhz;
            return
        }
        //将二次解析的父级过滤掉
        let rcjArrayFilter = rcjArray.filter(i=>!(i.markSum ===1 && (i.levelMark ===RcjLevelMarkConstant.SINK_JX ||  i.levelMark ===RcjLevelMarkConstant.SINK_PB)));

        if(ObjectUtils.isEmpty(rcjArrayFilter)){
            Dwgcxx.Rcjhz =Rcjhz;
            return
        }
        let RcjhzMxArray = new Array();
        for (let i = 0; i < rcjArrayFilter.length; i++) {
            let rcj = rcjArrayFilter[i];
            let RcjhzMx = {};
            RcjhzMx.RcjId = rcj.standardId;
            RcjhzMx.RcjBm = rcj.materialCode;
            RcjhzMx.Mc = rcj.materialName;
            RcjhzMx.Ggxh = rcj.specification;
            RcjhzMx.Dw = rcj.unit;
            RcjhzMx.Sl = rcj.totalNumber;
            RcjhzMx.Dej = rcj.dePrice===0?'0.00':rcj.dePrice; // 含税定额价(预算价=定额价)
            RcjhzMx.CsDej =NumberUtil.multiplyToString(rcj.dePrice,1-rcj.taxRemoval,2); //除税定额价：含税定额价-(含税定额价*除税系数)
            RcjhzMx.Xxj = rcj.marketPrice;
            RcjhzMx.CsXxj = NumberUtil.multiplyToString(rcj.Xxj,1-rcj.taxRemoval,2);
            RcjhzMx.Csxs = rcj.taxRemoval===0?'0.00':rcj.taxRemoval;
            RcjhzMx.HsHj = rcj.total;
            RcjhzMx.CsHj = NumberUtil.multiplyToString(rcj.total,1-rcj.taxRemoval,2);
            RcjhzMx.Jxse = NumberUtil.numberScale2(NumberUtil.divide(rcj.jxTotal,rcj.totalNumber))===0?'0.00':NumberUtil.numberScale2(NumberUtil.divide(rcj.jxTotal,rcj.totalNumber));
            RcjhzMx.JxseHj = rcj.jxTotal;
            RcjhzMx.Cd = rcj.producer;
            RcjhzMx.Gycs = rcj.manufactor;
            RcjhzMx.Jhfs = '';
            RcjhzMx.Sddd = rcj.deliveryLocation;
            RcjhzMx.Bz = rcj.description;
            if(rcj.kind === 1){
                RcjhzMx.Rcjlb = 1;
            }else if (rcj.kind === 2 || rcj.kind === 6|| rcj.kind === 7|| rcj.kind === 8|| rcj.kind === 9|| rcj.kind === 10){
                RcjhzMx.Rcjlb = 2;
            }else if (rcj.kind === 5){
                RcjhzMx.Rcjlb = 3;
            }else if(rcj.kind === 4){
                RcjhzMx.Rcjlb = 4;
            }else if(rcj.kind === 3){
                RcjhzMx.Rcjlb = 5;
            }
            RcjhzMx.Jgbz = rcj.ifDonorMaterial==='1'?'1':'0';
            RcjhzMx.Zyclbz = rcj.kind === '5'?'1':'0';
            RcjhzMx.Zgjbz = rcj.ifProvisionalEstimate==='1'?'1':'0';
            RcjhzMx.IsChaiFen = rcj.markSum===1?'true':'false';
            RcjhzMx.RcjlbMx = this.judgeRcjlbMx(rcj.type);


            RcjhzMxArray.push(RcjhzMx)
        }
        Rcjhz.RcjhzMx = RcjhzMxArray;
        Dwgcxx.Rcjhz = Rcjhz;
    }


    //增值税进项税汇总表
    async convertZzsjxshzb(Dwgcxx, unitCostCodePrices) {
        let Zzsjxshzb ={};
        //查询 增值税进项税额计算汇总数据
        let find = unitCostCodePrices.filter(item => item.type === '5');
        let zzsjxshzbMxArray = new Array();
        for (let i = 0; i < find.length; i++) {
            let findElement = find[i];
            let ZzsjxshzbMx = {};
            ZzsjxshzbMx.Xh =i+1+"";
            ZzsjxshzbMx.Mc =findElement.name;
            ZzsjxshzbMx.Je =findElement.price===0?'0.00':findElement.price;

            zzsjxshzbMxArray.push(ZzsjxshzbMx)
        }
        Zzsjxshzb.ZzsjxshzbMx = zzsjxshzbMxArray;
        Dwgcxx.Zzsjxshzb = Zzsjxshzb;
    }

    /**
     * 主要材料设备
     * 红成  如果勾选了
     * @param Dwgcxx
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertZyClSb(Dwgcxx, unitProject) {
        let ZyClSb = {}
        if(ObjectUtils.isEmpty(this.rcjArray)){
            Dwgcxx.ZyClSb= ZyClSb;
            return;
        }
        let res =  this.rcjArray.filter(i=>i.mainMaterial ==1);//主要材料 设备表逻辑
        if(ObjectUtils.isEmpty(res)){
            Dwgcxx.ZyClSb= ZyClSb;
            return;
        }

        //将二次解析的父级过滤掉
        let rcjArrayFilter = res.filter(i=>!(i.markSum ===1 && (i.levelMark ===RcjLevelMarkConstant.SINK_PB ||  i.levelMark ===RcjLevelMarkConstant.SINK_JX)));
        let ZyClSbMxArray = new Array();
        for (let i = 0; i < rcjArrayFilter.length; i++) {
            let rcj = rcjArrayFilter[i];
            let ZyClSbMx = {};
            ZyClSbMx.Xh = rcj.dispNo;
            ZyClSbMx.RcjId  = rcj.standardId;
            ZyClSbMx.Clbh   = rcj.materialCode;
            ZyClSbMx.Mc     = rcj.materialName;
            ZyClSbMx.Ggxh   = rcj.specification;
            ZyClSbMx.Dw     = rcj.unit;
            ZyClSbMx.Sl     = rcj.totalNumber;
            ZyClSbMx.Dj     = rcj.dePrice;
            ZyClSbMx.Hj     = rcj.total;
            ZyClSbMx.Bz     = rcj.description;
            //主要材料设备类别lqs
            ZyClSbMx.Lb     = this.judgeRcjhzLb(rcj.ifDonorMaterial);
            for(let key in CostTypeSbLbEnum){
                if(rcj.materialCategory === CostTypeSbLbEnum[key].desc){
                    ZyClSbMx.Lb = CostTypeSbLbEnum[key].code;
                }
            }

            ZyClSbMxArray.push(ZyClSbMx);
        }
        ZyClSb.ZyClSbMx = ZyClSbMxArray;
        Dwgcxx.ZyClSb= ZyClSb;
    }

    //招标人材料设备明细表
    async convertZbrClSb(Dwgcxx, unitProject) {
        let ZbrClSb = {}
        let res =  this.rcjArray;
        if (ObjectUtils.isEmpty(res)){
            Dwgcxx.ZbrClSb = ZbrClSb;
            return;
        }
        let jgRcj = res.filter(item => item.ifDonorMaterial === 1);
        if (ObjectUtils.isEmpty(jgRcj)){
            Dwgcxx.ZbrClSb = ZbrClSb;
            return;
        }else {
            let ZbrClSbMxArray = new Array();
            for (let i = 0; i < jgRcj.length; i++) {
                let rcj = jgRcj[i];
                let ZbrClSbMx = {};
                ZbrClSbMx.Xh =    rcj.dispNo;
                ZbrClSbMx.RcjId  = rcj.standardId;
                ZbrClSbMx.Mc     = rcj.materialName;
                ZbrClSbMx.Ggxh   = rcj.specification;
                ZbrClSbMx.Dw     = rcj.unit;
                ZbrClSbMx.Sl     = rcj.totalNumber;
                ZbrClSbMx.Dj     = rcj.dePrice;
                ZbrClSbMx.Hj     = rcj.total;
                ZbrClSbMx.Zldj   = rcj.qualityGrade;
                ZbrClSbMx.Gysj   = '';
                ZbrClSbMx.Sddd   = rcj.deliveryLocation;
                ZbrClSbMx.Bz     = rcj.description;
                ZbrClSbMx.Lb     = rcj.kind === 4? 2: 1 ;
                ZbrClSbMxArray.push(ZbrClSbMx);
            }
            ZbrClSb.ZbrClSbMx = ZbrClSbMxArray;
            Dwgcxx.ZbrClSb = ZbrClSb;
        }


    }

    //安全文明施工费
    async convertAqwmsgf(Dwgcxx, unitProject) {
        let Aqwmsgf ={};

        let args ={};
        args.constructId = unitProject.constructId;
        args.singleId = unitProject.spId;
        args.unitId = unitProject.sequenceNbr;
        let safeFee = await this.service.safeFeeService.getSafeFee(args);
        if(ObjectUtils.isEmpty(safeFee)){
            Dwgcxx.Aqwmsgf = Aqwmsgf;
            return
        }

        let AqwmsgfMxArray = new Array();
        for (let i = 0; i < safeFee.length; i++) {
            let safeFeeElement = safeFee[i];
            let AqwmsgfMx ={};

            AqwmsgfMx.Xh = i+1+"";
            AqwmsgfMx.Mc = safeFeeElement.costMajorName;
            AqwmsgfMx.Qfjc = "分部分项工程费+措施费+其他项目合计+规费";
            AqwmsgfMx.Qfje =safeFeeElement.costFeeBase ;
            AqwmsgfMx.Fl = safeFeeElement.basicRate;
            AqwmsgfMx.Je = safeFeeElement.feeAmount;

            AqwmsgfMxArray.push(AqwmsgfMx);
        }
        Aqwmsgf.AqwmsgfMx = AqwmsgfMxArray;
        Dwgcxx.Aqwmsgf = Aqwmsgf;
    }

    //规费
    async convertGf(Dwgcxx, unitProject) {
        let Gf ={};
        let args ={};
        args.constructId = unitProject.constructId;
        args.singleId = unitProject.spId;
        args.unitId = unitProject.sequenceNbr;
        let gfeeFee = await this.service.gfeeService.getGfeeFee(args);

        if(ObjectUtils.isEmpty(gfeeFee)){
            Dwgcxx.Gf = Gf;
            return;
        }
        let GfMxArray = new Array();
        for (let i = 0; i < gfeeFee.length; i++) {
            let GfMx = {};
            let gfeeFeeElement = gfeeFee[i];
            GfMx.Xh = i+1+"";
            GfMx.Mc = gfeeFeeElement.costMajorName;
            GfMx.Qfjc = '人工预算价+机械预算价';
            GfMx.Qfje = gfeeFeeElement.costFeeBase;
            GfMx.Fl = gfeeFeeElement.gfeeRate;
            GfMx.Je = gfeeFeeElement.feeAmount;
            GfMxArray.push(GfMx)
        }
        Gf.GfMx = GfMxArray;
        Dwgcxx.Gf = Gf;

    }

    /**
     * 转换Dekbz
     * @returns {Promise<void>}
     */
    async convertDekbz(dekbz){

        if(ObjectUtils.isEmpty(dekbz)){
            return '';
        }else {
            let res = '';
            switch (dekbz) {
                case '河北省建筑工程消耗量定额（2012）' :
                   res= '12jz';
                   break;
                case '河北省装饰装修工程消耗量定额（2012）' :
                    res= '12zs';
                    break;
                case '河北省安装工程消耗量定额（2012）' :
                    res= '12az';
                    break;
                case '河北省市政工程消耗量定额（2012）' :
                    res= '12sz';
                    break;
                case '河北省仿古建筑工程消耗量定额（2013）' :
                    res= '13fg';
                    break;
                case '河北省园林绿化工程消耗量定额（2013）' :
                    res= '13yl';
                    break;
                case '河北省房屋修缮工程消耗量定额（土建分册）（2013）' :
                    res= '13xsjz';
                    break;
                case '河北省房屋修缮工程消耗量定额（安装分册）（2013）' :
                    res= '13xsaz';
                    break;
                case '河北省市政设施维修养护工程消耗量定额（2013）' :
                    res= '13szyh';
                    break;
                case '河北省城市园林绿化养护管理定额（2014）' :
                    res= '13ylyh';
                    break;
                case '古建（明清）修缮工程消耗量定额（2014）' :
                    res= '14gjxs';
                    break;
                case '京津翼城市地下综合管廊工程消耗量定额（2018）' :
                    res= '18gl';
                    break;
                case '城市轨道交通工程预算定额河北省消耗量定额（2015）' :
                    res= '15gd';
                    break;
                case '河北省人防工程预算定额（2015）' :
                    res= '15rf';
                    break;
                case '河北省装配式建筑工程定额（试行）（2018）' :
                    res= '18zp';
                    break;
                case '独立费' :
                    res= '独';
                    break;
                case '自编定额及企业定额等' :
                    res= '其他';
                    break;
                case '2022年《河北省建设工程消耗量标准》-建筑工程' :
                    res= '22jz';
                    break;
                case '2022年《河北省建设工程消耗量标准》-装饰装修工程' :
                    res= '22zs';
                    break;
                case '2022年《河北省建设工程消耗量标准》-安装工程' :
                    res= '22az';
                    break;
                case '2022年《河北省建设工程消耗量标准》-市政工程' :
                    res= '22sz';
                    break;
                default :
                    res = '';
            }
            return  res;
        }
    }

    /**
     * 用所有人材机构成
     * @param rcjList
     * @param rcjDetailList
     * @returns {Promise<void>}
     */
    async convertRcjMap(rcjList) {



        if(ObjectUtils.isEmpty(rcjList)){
            return new Map();
        }
        return  ArrayUtil.group(rcjList,'deId');


    }


    /**
     * Lb主要材料设备类别判断
     */
    judgeRcjhzLb(number) {

        return '1';


    }

    judgeRcjlbMx(number) {

        return '1';

    }


}

JsonToXmlService.toString = () => '[class JsonToXmlService]';
module.exports = JsonToXmlService;
