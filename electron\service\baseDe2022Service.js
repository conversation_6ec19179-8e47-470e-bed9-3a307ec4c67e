'use strict';

const {Service, Log} = require('../../core');
const {ObjectUtils} = require("../utils/ObjectUtils");
const {SqlUtils} = require("../utils/SqlUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {BaseDe2022} = require("../model/BaseDe");
const {TaxCalculationMethodEnum} = require("../enum/TaxCalculationMethodEnum");
const {In} = require("typeorm");
const { NumberUtil } = require('../utils/NumberUtil');

/**
 * 国标定额service
 */
class BaseDe2022Service extends Service {

    constructor(ctx) {
        super(ctx);
    }

    baseDe2022Dao = this.app.appDataSource.manager.getRepository(BaseDe2022);
    baseDeLibrary2022Service = this.service.baseDeLibrary2022Service;

    _dealLevelXList(levelXList, pos, classifyLevelKeyArray, levelAllList, sequenceNbr=1){
        if(ObjectUtils.isEmpty(levelXList)){
            return;
        }

        let subList = levelAllList[pos + 1];

        levelXList.forEach(item => {
            // 给前端统一code和name以及唯一标识
            this.modifyingAttributeValueNew(item, pos+1, classifyLevelKeyArray, sequenceNbr++);
            if(ObjectUtils.isEmpty(subList)){
                return;
            }
            let curSubList = subList.filter(subItem => {
                for (let i = 0; i <= pos; i++) {
                    if (item[classifyLevelKeyArray[i]] != subItem[classifyLevelKeyArray[i]]) {
                        return false;
                    }
                }
                return true;
            });
            item.childrenList = curSubList;

            if(ObjectUtils.isNotEmpty(curSubList) && pos + 1 < levelAllList.length){
                this._dealLevelXList(curSubList, pos+1, classifyLevelKeyArray, levelAllList, sequenceNbr)
            }
        });
    }

    /**
     * 获取定额分类目录树
     * @param libraryCode 清单册code
     * @returns {Promise<*[]>}
     */
    async listTreeByLibraryCode(libraryCode) {
        let selectSql = "SELECT\n" +
            "\tsequence_nbr as sequenceNbr,\n" +
            "\tlibrary_code as libraryCode,\n" +
            "\tlibrary_name as libraryName,\n" +
            "\tclasslevel01 as classifyLevel1,\n" +
            "\tclasslevel02 as classifyLevel2,\n" +
            "\tclasslevel03 as classifyLevel3,\n" +
            "\tclasslevel04 as classifyLevel4,\n" +
            "\tclasslevel05 as classifyLevel5,\n" +
            "\tclasslevel06 as classifyLevel6,\n" +
            "\tclasslevel07 as classifyLevel7\n" +
            "FROM\n" +
            "\tbase_de_chapter_2022\n" +
            "WHERE\n" +
            "\tlibrary_code = ?\n" +
            "ORDER BY\n" +
            "\tsort_no ASC";
        let deArrays = this.app.betterSqlite3DataSource.prepare(selectSql).all(libraryCode);
        // 去重取 classify_level1/classify_level2/classify_level3/classify_level4的list
        let classifyLevelKeyArray = ["classifyLevel1", "classifyLevel2", "classifyLevel3", "classifyLevel4", "classifyLevel5", "classifyLevel6", "classifyLevel7"];
        let levelAllList = [];
        for(let i = 0; i < classifyLevelKeyArray.length; i++){
            levelAllList[i] = this.distinctList(deArrays, ...classifyLevelKeyArray.slice(0, i+1));
        }
        let sequenceNbr = 1;
        this._dealLevelXList(levelAllList[0], 0, classifyLevelKeyArray, levelAllList, sequenceNbr)

        // 将顶层树节点改为定额册，与BS保持一致
        // 根据libraryCode查定额册名字
        let baseDeLibraryModel = await this.baseDeLibrary2022Service.getByLibraryCode(libraryCode);
        let topLibraryNode = {sequenceNbr: sequenceNbr++, name: baseDeLibraryModel.libraryName};
        topLibraryNode.childrenList = levelAllList[0];
        return topLibraryNode;
    }
    async listTreeByLibraryCodeBak(libraryCode) {
        // 定额arrays
        // let deArrays = (await this.baseDe2022Dao.find({
        //     where: {libraryCode: libraryCode},
        //     order: {sortNo: "ASC"}
        // }));
        // let selectSql = "SELECT\n" +
        //     "\tsequence_nbr as sequenceNbr,\n" +
        //     "\tlibrary_code as libraryCode,\n" +
        //     "\tlibrary_name as libraryName,\n" +
        //     "\tcslb_code as cslbCode,\n" +
        //     "\tqf_code as qf_code,\n" +
        //     "\tde_name as deName,\n" +
        //     "\tde_code as deCode,\n" +
        //     "\tclassify_level1 as classifyLevel1,\n" +
        //     "\tclassify_level2 as classifyLevel2,\n" +
        //     "\tclassify_level3 as classifyLevel3,\n" +
        //     "\tclassify_level4 as classifyLevel4\n" +
        //     "FROM\n" +
        //     "\t\"base_de_2022\" \n" +
        //     "WHERE\n" +
        //     "\tlibrary_code = ? \n" +
        //     "ORDER BY\n" +
        //     "\tsort_no ASC";
        let selectSql = "SELECT\n" +
            "\tsequence_nbr as sequenceNbr,\n" +
            "\tlibrary_code as libraryCode,\n" +
            "\tlibrary_name as libraryName,\n" +
            "\tclasslevel01 as classifyLevel1,\n" +
            "\tclasslevel02 as classifyLevel2,\n" +
            "\tclasslevel03 as classifyLevel3,\n" +
            "\tclasslevel04 as classifyLevel4\n" +
            "FROM\n" +
            "\tbase_de_chapter_2022\n" +
            "WHERE\n" +
            "\tlibrary_code = ?\n" +
            "ORDER BY\n" +
            "\tsort_no ASC";
        let deArrays = this.app.betterSqlite3DataSource.prepare(selectSql).all(libraryCode);
        // 去重取 classify_level1/classify_level2/classify_level3/classify_level4的list
        let level1AllList = this.distinctList(deArrays, "classifyLevel1");
        let level2AllList = this.distinctList(deArrays, "classifyLevel1", "classifyLevel2");
        let level3AllList = this.distinctList(deArrays, "classifyLevel1", "classifyLevel2", "classifyLevel3");
        let level4AllList = this.distinctList(deArrays, "classifyLevel1", "classifyLevel2", "classifyLevel3", "classifyLevel4");

        let sort = (ll1, ll2) => ll1.sortNo - ll2.sortNo;
        level1AllList = level1AllList.sort(sort);
        // 给前端作为唯一标识
        let sequenceNbr = 1;
        // level1
        level1AllList.forEach(level1Item => {
            // 给前端统一code和name以及唯一标识
            this.modifyingAttributeValue(level1Item, 1, sequenceNbr++);
            let leve2SubList = level2AllList.filter(level2AllItem => level2AllItem.classifyLevel1 === level1Item.classifyLevel1);
            level1Item.childrenList = leve2SubList;
            // level2
            leve2SubList.forEach(level2Item => {
                // 给前端统一code和name以及唯一标识
                this.modifyingAttributeValue(level2Item, 2, sequenceNbr++);
                let leve3SubList = level3AllList.filter(level3AllItem => (level3AllItem.classifyLevel2 === level2Item.classifyLevel2 && level3AllItem.classifyLevel1 === level2Item.classifyLevel1));
                level2Item.childrenList = leve3SubList;
                // level3
                leve3SubList.forEach(level3Item => {
                    // 给前端统一code和name以及唯一标识
                    this.modifyingAttributeValue(level3Item, 3, sequenceNbr++);
                    // level4
                    let level4SubList = level4AllList.filter(level4AllItem => (level4AllItem.classifyLevel3 === level3Item.classifyLevel3 && level4AllItem.classifyLevel2 === level3Item.classifyLevel2 && level4AllItem.classifyLevel1 === level3Item.classifyLevel1));
                    level3Item.childrenList = level4SubList;
                    level4SubList.forEach(level4Item => {
                        // 给前端统一code和name以及唯一标识
                        this.modifyingAttributeValue(level4Item, 4, sequenceNbr++);
                    })
                });
            });
        });

        //return level1AllList;

        // 将顶层树节点改为定额册，与BS保持一致
        // 根据libraryCode查定额册名字
        let baseDeLibraryModel = await this.baseDeLibrary2022Service.getByLibraryCode(libraryCode);
        let topLibraryNode = {sequenceNbr: sequenceNbr++, name: baseDeLibraryModel.libraryName};
        topLibraryNode.childrenList = level1AllList;
        return topLibraryNode;
    }

    /**
     * 修改属性值
     * @param baseDe 定额
     * @param level 清单专业level
     * @param sequenceNbr 重置后的sequenceNbr
     */
    modifyingAttributeValueNew(baseDe, level, classifyLevelKeyArray, sequenceNbr) {
        /*
         * 1.赋值name
         * 2.置空level下有误的属性值
         * 3.重置sequenceNbr
         */
        baseDe.sequenceNbr = sequenceNbr;
        baseDe.name = baseDe[classifyLevelKeyArray[level - 1]];
        for(let i = level; i < classifyLevelKeyArray.length; i++){
            baseDe[classifyLevelKeyArray[i]] = null;
        }
    }

    /**
     * 修改属性值
     * @param baseDe 定额
     * @param level 清单专业level
     * @param sequenceNbr 重置后的sequenceNbr
     */
    modifyingAttributeValue(baseDe, level, sequenceNbr) {
        /*
         * 1.赋值name
         * 2.置空level下有误的属性值
         * 3.重置sequenceNbr
         */
        baseDe.sequenceNbr = sequenceNbr;
        switch (level) {
            case 1:
                // level1
                baseDe.name = baseDe.classifyLevel1;
                baseDe.classifyLevel2 = null;
                baseDe.classifyLevel3 = null;
                baseDe.classifyLevel4 = null;
                break;
            case 2:
                // level2
                baseDe.name = baseDe.classifyLevel2;
                baseDe.classifyLevel3 = null;
                baseDe.classifyLevel4 = null;
                break;
            case 3:
                // level3
                baseDe.name = baseDe.classifyLevel3;
                baseDe.classifyLevel4 = null;
                break;
            case 4:
                // level4
                baseDe.name = baseDe.classifyLevel4;
                break;
            default:
                // ...
                break;
        }
    }

    /**
     * list去重
     * @param list 要分组的list
     * @param distinctColumn 分组字段名str
     * @returns {*[]} 新的list
     */
    distinctList(list, ...distinctColumn) {
        if (!Array.isArray(list)) {
            return [];
        }
        let groupArray = [];
        list.forEach(item => {
            // 是否为空
            for (let i = 0; i < distinctColumn.length; ++i) {
                if (ObjectUtils.isEmpty(item[distinctColumn[i]])) {
                    return;
                }
            }
            // 是否存在
            let isExist = groupArray.some(g => {
                let exist = true;
                for (let i = 0; i < distinctColumn.length; ++i) {
                    if (g[distinctColumn[i]] !== item[distinctColumn[i]]) {
                        exist = false;
                        break;
                    }
                }

                return exist;
            });
            // 不存在add
            if (!isExist) {
                groupArray.push(Object.assign({}, item));
            }
        })
        return groupArray;
    }


    async queryDeByBdCodeAndName(qdDeParam) {
        // 选中定额册下的数据
        if(ObjectUtils.isEmpty(qdDeParam.libraryCode)){
            throw new Error("定额册编码不可为空");
        }

        let paramsTmp = qdDeParam;



        let data = await this.selectDesWhenSelect(paramsTmp);
        let length = data.length;
        const startIndex = parseInt((qdDeParam.page-1)*qdDeParam.limit);
        const endIndex = startIndex + qdDeParam.limit;
        data = data.slice( startIndex,endIndex);
        return {"total":length,"data":data};

    }

    async selectDesWhenSelect(qdDeParam) {
        // 判断入参是否为空，不为空拼接sql
        let whereSql = "";
        let whereParams = {};
        // 清单册
        if (qdDeParam.libraryCode) {
            // 拼接and
            if (whereSql.length !== 0) whereSql += " and ";
            // 拼接sql和参数
            whereSql += "baseDe2022.libraryCode = :libraryCode"
            whereParams.libraryCode = qdDeParam.libraryCode;
        }
        // 清单名称
        if (qdDeParam.bdName) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "(baseDe2022.de_code like :bdName OR baseDe2022.de_name like :bdName)"
            whereParams.bdName = "%" + qdDeParam.bdName + "%";
        }
        // 一级分类
        if (qdDeParam.classifyLevel1) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe2022.classifyLevel1 = :classifyLevel1"
            whereParams.classifyLevel1 = qdDeParam.classifyLevel1;
        }
        // 二级分类
        if (qdDeParam.classifyLevel2) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe2022.classifyLevel2 = :classifyLevel2"
            whereParams.classifyLevel2 = qdDeParam.classifyLevel2;
        }
        // 三级分类
        if (qdDeParam.classifyLevel3) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe2022.classifyLevel3 = :classifyLevel3"
            whereParams.classifyLevel3 = qdDeParam.classifyLevel3;
        }
        // 四级分类
        if (qdDeParam.classifyLevel4) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe2022.classifyLevel4 = :classifyLevel4"
            whereParams.classifyLevel4 = qdDeParam.classifyLevel4;
        }
        // 五级分类
        if (qdDeParam.classifyLevel5) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe2022.classifyLevel5 = :classifyLevel5"
            whereParams.classifyLevel5 = qdDeParam.classifyLevel5;
        }
        // 六级分类
        if (qdDeParam.classifyLevel6) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe2022.classifyLevel6 = :classifyLevel6"
            whereParams.classifyLevel6 = qdDeParam.classifyLevel6;
        }
        // 七级分类
        if (qdDeParam.classifyLevel7) {
            if (whereSql.length !== 0) whereSql += " and ";
            whereSql += "baseDe2022.classifyLevel7 = :classifyLevel7"
            whereParams.classifyLevel7 = qdDeParam.classifyLevel7;
        }

        if (!whereSql) {
            console.log("error,参数为空");
        }

        let baseDes = await this.baseDe2022Dao
            .createQueryBuilder("baseDe2022")
            .where(whereSql, whereParams)
            .orderBy("baseDe2022.sortNo", "ASC")
            .getMany();
            //处理定额市场价
            if (ObjectUtils.isNotEmpty(baseDes)) {
                let unit = PricingFileFindUtils.getUnit(qdDeParam.constructId, qdDeParam.spId, qdDeParam.upId);
                //获取计税方式 '1'?'一般计税':'简易计税'
                let taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;
                let isSimple = (taxCalculationMethod == 0);
                for (const de of baseDes) {
                    de.priceBaseJournalTax=NumberUtil.numberScale2(de.priceBaseJournalTax);
                    de.priceBaseJournal=NumberUtil.numberScale2(de.priceBaseJournal);
                    de.price = isSimple ? de.priceBaseJournalTax :de.priceBaseJournal;
                }
            }
        return baseDes;
    }

    /**
     * 模糊查定额
     * @param qdDeParam 定额编码、名称、分类名
     * @see QdDeParam
     * @returns {Promise<ObjectLiteral[]>}
     */
    async queryDeByBdCodeAndNameOld(qdDeParam) {
        // 选中定额册下的数据情况下，直接返回
        if (((qdDeParam.classifyLevel1 && qdDeParam.classifyLevel1 !== "") ||
            (qdDeParam.classifyLevel2 && qdDeParam.classifyLevel2 !== "") ||
            (qdDeParam.classifyLevel3 && qdDeParam.classifyLevel3 !== "") ||
            (qdDeParam.classifyLevel4 && qdDeParam.classifyLevel4 !== "") ||
            (qdDeParam.libraryCode && qdDeParam.libraryCode !== "")) && (!qdDeParam.bdName || qdDeParam.bdName === "")) {
            let data = await this.selectDesWhenSelect(qdDeParam);
            let length = data.length;
            const startIndex = parseInt((qdDeParam.page - 1) * qdDeParam.limit);
            const endIndex = startIndex + qdDeParam.limit;
            data = data.slice(startIndex, endIndex);
            return {"total": length, "data": data};
        }

        let selectSql;
        let selectSqlCount22 = "select COUNT(1) as total from ( select d.de_name= ? as nameRes, d.de_code= ? as codeRes, d.library_code= ? as aa, d.*\n" +
            "      from base_de_2022 d\n" +
            "      where (de_name LIKE '%' || ? || '%' OR de_code LIKE '%' || ? || '%')\n" +
            "      order by nameRes desc, codeRes desc, aa desc, de_code, sort_no asc )";
        let selectSqlCount12 = "select COUNT(1) as total from ( select d.de_name= ? as nameRes, d.de_code= ? as codeRes, d.library_code= ? as aa, d.*\n" +
            "      from base_de d\n" +
            "      where (de_name LIKE '%' || ? || '%' OR de_code LIKE '%' || ? || '%')\n" +
            "      order by nameRes desc, codeRes desc, aa desc, de_code, sort_no asc )";
        if (qdDeParam.libraryCode.startsWith("2022")) {
            selectSql = "SELECT * FROM (SELECT * FROM base_de_2022 WHERE library_code = ? AND (de_name = ? OR de_code = ?)"
                + " UNION SELECT * FROM base_de_2022 WHERE library_code = ? AND (de_name LIKE ? OR de_code LIKE ?) ) AS combined_query ORDER BY sort_no ASC LIMIT ? , ?  ";
        } else {
            selectSql = "SELECT * FROM (SELECT * FROM base_de WHERE library_code = ? AND (de_name = ? OR de_code = ?)"
                + " UNION SELECT * FROM base_de WHERE library_code = ? AND (de_name LIKE ? OR de_code LIKE ?) ) AS combined_query ORDER BY sort_no ASC  LIMIT ? , ?  ";
        }

        let selectSql22 = "SELECT * FROM ("
            + "  SELECT * FROM base_de_2022 WHERE library_code != ? and  (de_name = ? OR de_code = ?) "
            + " UNION SELECT * FROM base_de_2022 WHERE library_code != ? and (de_name LIKE ? OR de_code LIKE ?) ) AS combined_query ORDER BY sort_no ASC LIMIT ? , ?  ";

        let selectSql12 = "SELECT * FROM ("
            + " SELECT * FROM base_de WHERE library_code != ? and  (de_name = ? OR de_code = ?) "
            + " UNION SELECT * FROM base_de WHERE library_code != ? and (de_name LIKE ? OR de_code LIKE ?) ) AS combined_query ORDER BY sort_no ASC LIMIT ? , ?  ";


        //查询当前单位工程的主定额册对应的 libraryCode
        let unit = PricingFileFindUtils.getUnit(qdDeParam.constructId, qdDeParam.spId, qdDeParam.upId);
        let sqlResCount22 = this.app.betterSqlite3DataSource.prepare(selectSqlCount22).all(qdDeParam.bdName, qdDeParam.bdName, qdDeParam.libraryCode,
            qdDeParam.bdName, qdDeParam.bdName);
        let sqlResCount12 = this.app.betterSqlite3DataSource.prepare(selectSqlCount12).all(qdDeParam.bdName, qdDeParam.bdName, qdDeParam.libraryCode,
            qdDeParam.bdName, qdDeParam.bdName);
        //查询数据  22 中包含12的数据
        // let sqlRes = this.app.betterSqlite3DataSource.prepare(selectSql).all(qdDeParam.libraryCode,qdDeParam.bdName, qdDeParam.bdName, qdDeParam.bdName, qdDeParam.bdName,qdDeParam.libraryCode,
        //     qdDeParam.bdName+"%",qdDeParam.bdName+"%",qdDeParam.bdName+"%",qdDeParam.bdName+"%", parseInt((qdDeParam.page - 1) * qdDeParam.limit), qdDeParam.limit);
        // let convertRes = SqlUtils.convertToModel(sqlRes);
        //查询当前册子数据
        let sqlRes = this.app.betterSqlite3DataSource.prepare(selectSql).all(qdDeParam.libraryCode, qdDeParam.bdName, qdDeParam.bdName, qdDeParam.libraryCode,
            qdDeParam.bdName + "%", qdDeParam.bdName + "%", parseInt((qdDeParam.page - 1) * qdDeParam.limit), qdDeParam.limit);
        let convertRes = SqlUtils.convertToModel(sqlRes);
        // convertRes.sort((a, b) => a.sortNo - b.sortNo);
        if (convertRes.length < qdDeParam.limit) {
            //查22的
            sqlRes = this.app.betterSqlite3DataSource.prepare(selectSql22).all(qdDeParam.libraryCode, qdDeParam.bdName, qdDeParam.bdName, qdDeParam.libraryCode,
                qdDeParam.bdName + "%", qdDeParam.bdName + "%", parseInt((qdDeParam.page - 1) * qdDeParam.limit), (qdDeParam.limit - convertRes.length));
            let convertToModel = SqlUtils.convertToModel(sqlRes);
            // convertToModel.sort((a, b) => a.sortNo - b.sortNo);
            convertRes = convertRes.concat(convertToModel);
            if (convertRes.length < qdDeParam.limit) {
                //查12的
                sqlRes = this.app.betterSqlite3DataSource.prepare(selectSql12).all(qdDeParam.libraryCode, qdDeParam.bdName, qdDeParam.bdName, qdDeParam.libraryCode,
                    qdDeParam.bdName + "%", qdDeParam.bdName + "%", parseInt((qdDeParam.page - 1) * qdDeParam.limit), (qdDeParam.limit - convertRes.length));
                let convertToModel = SqlUtils.convertToModel(sqlRes);
                // convertToModel.sort((a, b) => a.sortNo - b.sortNo);
                convertRes = convertRes.concat(convertToModel);
            }
        }

        //判断是否简易计税
        //获取计税方式 '1'?'一般计税':'简易计税'
        let taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;
        let isSimple = (taxCalculationMethod == 0);
        for (const de of convertRes) {
            de.price = isSimple ? de.priceBaseJournalTax : de.priceBaseJournal;
        }



        return {"total": sqlResCount22[0].total + sqlResCount12[0].total, "data": convertRes};
    }

    // async selectDesWhenSelect(qdDeParam) {
    //     // 判断入参是否为空，不为空拼接sql
    //     let whereSql = "";
    //     let whereParams = {};
    //     // 清单册
    //     if (qdDeParam.libraryCode) {
    //         // 拼接and
    //         if (whereSql.length !== 0) whereSql += " and ";
    //         // 拼接sql和参数
    //         whereSql += "baseDe.libraryCode = :libraryCode"
    //         whereParams.libraryCode = qdDeParam.libraryCode;
    //     }
    //     // 清单名称
    //     if (qdDeParam.bdName) {
    //         if (whereSql.length !== 0) whereSql += " and ";
    //         whereSql += "(baseDe.de_code like :bdName OR baseDe.de_name like :bdName)"
    //         whereParams.bdName = "%" + qdDeParam.bdName + "%";
    //     }
    //     // 一级分类
    //     if (qdDeParam.classifyLevel1) {
    //         if (whereSql.length !== 0) whereSql += " and ";
    //         whereSql += "baseDe.classifyLevel1 = :classifyLevel1"
    //         whereParams.classifyLevel1 = qdDeParam.classifyLevel1;
    //     }
    //     // 二级分类
    //     if (qdDeParam.classifyLevel2) {
    //         if (whereSql.length !== 0) whereSql += " and ";
    //         whereSql += "baseDe.classifyLevel2 = :classifyLevel2"
    //         whereParams.classifyLevel2 = qdDeParam.classifyLevel2;
    //     }
    //     // 三级分类
    //     if (qdDeParam.classifyLevel3) {
    //         if (whereSql.length !== 0) whereSql += " and ";
    //         whereSql += "baseDe.classifyLevel3 = :classifyLevel3"
    //         whereParams.classifyLevel3 = qdDeParam.classifyLevel3;
    //     }
    //     // 四级分类
    //     if (qdDeParam.classifyLevel4) {
    //         if (whereSql.length !== 0) whereSql += " and ";
    //         whereSql += "baseDe.classifyLevel4 = :classifyLevel4"
    //         whereParams.classifyLevel4 = qdDeParam.classifyLevel4;
    //     }
    //     // 五级分类
    //     if (qdDeParam.classifyLevel5) {
    //         if (whereSql.length !== 0) whereSql += " and ";
    //         whereSql += "baseDe.classifyLevel5 = :classifyLevel5"
    //         whereParams.classifyLevel5 = qdDeParam.classifyLevel5;
    //     }
    //     // 六级分类
    //     if (qdDeParam.classifyLevel6) {
    //         if (whereSql.length !== 0) whereSql += " and ";
    //         whereSql += "baseDe.classifyLevel6 = :classifyLevel6"
    //         whereParams.classifyLevel6 = qdDeParam.classifyLevel6;
    //     }
    //     // 七级分类
    //     if (qdDeParam.classifyLevel7) {
    //         if (whereSql.length !== 0) whereSql += " and ";
    //         whereSql += "baseDe.classifyLevel7 = :classifyLevel7"
    //         whereParams.classifyLevel7 = qdDeParam.classifyLevel7;
    //     }
    //     if (!whereSql) {
    //         console.log("error,参数为空");
    //     }
    //
    //     let baseDes = await this.baseDe2022Dao
    //         .createQueryBuilder("baseDe")
    //         .where(whereSql, whereParams)
    //         .orderBy("baseDe.sortNo", "ASC")
    //         .getMany();
    //     //处理定额市场价
    //     if (ObjectUtils.isNotEmpty(baseDes)) {
    //         let unit = PricingFileFindUtils.getUnit(qdDeParam.constructId, qdDeParam.spId, qdDeParam.upId);
    //         //获取计税方式 '1'?'一般计税':'简易计税'
    //         let taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;
    //         let isSimple = (taxCalculationMethod == 0);
    //         for (const de of baseDes) {
    //             de.price = isSimple ? de.priceBaseJournalTax : de.priceBaseJournal;
    //         }
    //     }
    //     return baseDes;
    // }

    /**
     * 定位清单
     * @param sequenceNbr 清单主键
     * @returns {Promise<*>}
     */
    async queryDeById(sequenceNbr) {
        return await this.baseDe2022Dao.findOneBy({sequenceNbr: sequenceNbr});
    }

    /**
     * selectOne
     * @param sequenceNbr sequenceNbr
     * @return {Promise<BaseDe>}
     */
    async selectOne(sequenceNbr) {
        return await this.baseDe2022Dao.findOneBy({sequenceNbr: sequenceNbr});
    }


    /**
     * selectDeByLibraryCode
     * @param sequenceNbr sequenceNbr
     * @return {Promise<BaseDe>}
     */
    async selectDeByLibraryCode(libraryCode,list) {
        return await this.baseDe2022Dao.findBy({libraryCode: libraryCode, deCode: In(list)});
    }



    /*
        /!**
         * 查询河北省装饰装修工程消耗量定额（2012）下挂的定额
         * @param sequenceNbr sequenceNbr
         * @return {Promise<BaseDe>}
         *!/
        async selectDecorationDe(libraryCode) {
            let deList = await this.baseDe2022Dao.find({
                where: {libraryCode: libraryCode}
            });
            let array = ['B.8垂直运输及超高增加费', 'B.9其它可竞技措施项', '装饰工程绿建补充'];
            return deList.filter(k => !array.includes(k.classifyLevel1))
        }*/

    /**
     * 模糊查询定额（快速组价用）
     * @param args
     */
    selectDeByBdName(args) {
        let selectSqlCount = "select COUNT(1) as total from ( select d.de_name= ? as nameRes, d.de_code= ? as codeRes, d.library_code= ? as aa, d.*\n" +
            "      from base_de_2022 d\n" +
            "      where (de_name LIKE '%' || ? || '%' OR de_code LIKE '%' || ? || '%')\n" +
            "      order by nameRes desc, codeRes desc, aa desc, de_code, sort_no asc )";

        let selectSql = "select d.de_name= ? as nameRes, d.de_code= ? as codeRes, d.library_code= ? as aa,  d.sequence_nbr as quota_id , d.*\n" +
            "      from base_de_2022 d\n" +
            "      where (de_name LIKE '%' || ? || '%' OR de_code LIKE '%' || ? || '%')\n" +
            "      order by nameRes desc, codeRes desc, aa desc, de_code, sort_no asc LIMIT ? ,?";
        //查看编码的like里 是否有主定额册的  现在是只要没有精准  就返回两个字段like的 数据  而是否主定额册则是乱的

        //查询当前单位工程的主定额册对应的 libraryCode
        let unit = PricingFileFindUtils.getUnit(args.constructId, args.spId, args.upId);
        let sqlResCount = this.app.betterSqlite3DataSource.prepare(selectSqlCount).all(args.bdName, args.bdName, unit.mainDeLibrary,
            args.bdName, args.bdName);
        let sqlRes = this.app.betterSqlite3DataSource.prepare(selectSql).all(args.bdName, args.bdName, unit.mainDeLibrary,
            args.bdName, args.bdName,
            parseInt((args.page - 1) * args.limit),
            args.limit);
        let convertRes = SqlUtils.convertToModel(sqlRes);
        return {"total": sqlResCount[0].total, "data": convertRes};
    }
}

BaseDe2022Service.toString = () => '[class BaseDe2022Service]';
module.exports = BaseDe2022Service;
