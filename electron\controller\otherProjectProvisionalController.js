const {ResponseData} = require("../utils/ResponseData");
const {Controller} = require("../../core");
class OtherProjectProvisionalController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 暂列金 操作
     * @param arg 清单册code
     * @returns {Promise<*>}
     */
    async otherProjectProvisional(arg) {

        const result = await this.service.otherProjectProvisionalService.otherProjectProvisional(arg);

        if (arg.operateType !==1){

            await this.service.management.sycnTrigger("unitDeChange");
            await this.service.management.trigger("itemChange");
        }
        return ResponseData.success(result);
    }

}

OtherProjectProvisionalController.toString = () => '[class OtherProjectProvisionalController]';
module.exports = OtherProjectProvisionalController;