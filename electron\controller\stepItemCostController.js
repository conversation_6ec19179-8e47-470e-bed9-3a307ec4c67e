const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const _ = require("lodash");

/**
 * 措施项目controller
 */
class StepItemCostController extends Controller {


    /**
     * 获取措施模板
     * @param args
     * @returns {*}
     */
    async getMeasureTemplates(args){

        return ResponseData.success(await this.service.stepItemCostService.getMeasureTemplates(args));
    }

    /**
     * 通过措施模板名称获得清单列表
     * @param args
     * @returns {*}
     */
    async getBaseListByTemplate(args){
        return ResponseData.success(await this.service.stepItemCostService.getBaseListByTemplate(args));
    }

    /**
     * 应用措施模板
     * @param args
     * @returns {*}
     */
    async applyMeasureTemplate(args){
        let {constructId, singleId, unitId,templateName} = args;


        return ResponseData.success( await this.service.stepItemCostService.initItemCost(constructId, singleId, unitId,templateName));
    }



    /**
     * 保存措施模板
     * @param args
     * @returns {*}
     */
    saveMeasureTemplate(args)
    {
        return ResponseData.success(this.service.stepItemCostService.saveMeasureTemplate(args));
    }

    /**
     * 获取措施项目类型列表
     * @returns {ResponseData}
     */
    getMeasureTypes() {
        let returnArry = this.service.stepItemCostService.DEFAULT_MEASURE_TYPES;
        returnArry = returnArry.filter(f=>f !== "安全生产、文明施工费");
        return ResponseData.success(returnArry);
    }

    /**
     * 初始化
     * @param args
     * @returns {ResponseData}
     */
    async initStepItemCost(args) {
        let {constructId, singleId, unitId} = args;
        await this.service.stepItemCostService.initItemCost(constructId, singleId, unitId);
        return ResponseData.success(true);
    }

    /**
     * 复制行
     * @param args
     * @returns {ResponseData}
     */
    copy(args) {
        let {sequenceNbrs} = args;
        if(!_.isArray(sequenceNbrs)){
            return ResponseData.fail("参数异常sequenceNbrs 应为数组");
        }
        const result = this.service.stepItemCostService.copyLine(sequenceNbrs);
        return ResponseData.success(result);
    }
    cut(args) {
        let {sequenceNbrs} = args;
        if(!_.isArray(sequenceNbrs)){
            return ResponseData.fail("参数异常sequenceNbrs 应为数组");
        }
        const result = this.service.stepItemCostService.cut(sequenceNbrs);
        return ResponseData.success(result);
    }

    async pasteLine(args){
        let {constructId, singleId, unitId,pointLine} = args;
        if(_.isEmpty(pointLine)){
            return ResponseData.fail("请选择要粘贴的行");
        }
        const result = await this.service.stepItemCostService.pasteLine(constructId, singleId, unitId,pointLine);

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");
        return ResponseData.success(result);
    }
    searchForsequenceNbr(args) {
        let {constructId, singleId, unitId,sequenceNbr} = args;
        const result = this.service.stepItemCostService.searchForsequenceNbr(constructId, singleId, unitId, sequenceNbr);
        return ResponseData.success(result);
    }
    /**
     * 批量删除
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async batchDelete(args) {
        let {constructId, singleId, unitId, sequenceNbrs} = args;
        if(!_.isArray(sequenceNbrs)){
            return ResponseData.fail("参数异常sequenceNbrs 应为数组");
        }
        try {
            const result = await this.service.stepItemCostService.batchDelete(constructId, singleId, unitId, sequenceNbrs);
            await this.service.management.sycnTrigger("unitDeChange");
            return ResponseData.success(result);
        } catch (e) {
            return ResponseData.fail(e.message);
        }


    }


    /**
     * 结构新增(空行数据)
     * @param args
     * @returns {ResponseData}
     */
    async save(args) {
        let {constructId, singleId, unitId, pointLine, newLine} = args;
        const result =  await this.service.stepItemCostService.save(constructId, singleId, unitId, pointLine, newLine);
        //状态数据处理
        this.service.baseBranchProjectOptionService.setItemDtaStatus( PricingFileFindUtils.getCSXM(constructId, singleId, unitId).getAllNodes());
        return ResponseData.success(result);
    }

    /**
     * 行内数据修改
     * @param args
     */
    async update(args) {
        let {constructId, singleId, unitWorkId, pointLineId,  column,value,params} = args;
        let res;
        if (params) {
            for (const key in params) {
                res = await this.service.stepItemCostService.upDateOnList(constructId, singleId, unitWorkId, pointLineId, {
                    column:key,value:params[key]
                });
            }
        }else {
            let upDateInfo={
                column,value
            }
             res = await this.service.stepItemCostService.upDateOnList(constructId, singleId, unitWorkId, pointLineId, upDateInfo);
        }

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.unitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: singleId,
            unitId: unitWorkId,
        });
        return ResponseData.success(res);
    }

    /**
     * 特征及项目编辑
     */
    updateQdFeature(args) {
        let {constructId, singleId, unitId, pointLine, updateStr} = args;
        this.service.stepItemCostService.updateQdFeature(constructId, singleId, unitId, pointLine, updateStr);
    }

    /**
     * 从清单定额索引中点击插入
     */
    async fillFromIndexPage(args) {
        let {constructId, singleId, unitId, pointLine, kind, indexId, unit, rcjFlag,libraryCode} = args;
        let res = await this.service.stepItemCostService.fillDataFromIndexPage(constructId, singleId, unitId, pointLine, kind, indexId, unit, rcjFlag,null,null,null,libraryCode);
        //状态数据处理
        this.service.baseBranchProjectOptionService.setItemDtaStatus( PricingFileFindUtils.getCSXM(constructId, singleId, unitId).getAllNodes());
        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.unitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
        });
        return ResponseData.success(res);
    }

    /**
     * 从清单定额索引中点替换
     */
    async replaceFromIndexPage(args) {
        let {constructId, singleId, unitWorkId, unitId, selectId, replaceId, type, conversionCoefficient, kind, unit,libraryCode} = args;
        if (!unitWorkId) {
            unitWorkId = unitId;
        }
        let res = await this.service.stepItemCostService.replaceFromIndexPage(constructId, singleId, unitWorkId, selectId, replaceId, type, conversionCoefficient, kind, unit,libraryCode);
        await this.service.management.sycnTrigger("unitDeChange");
        return ResponseData.success(res);
    }

    /**
     * 锁定解锁
     * @param args
     * @return {ResponseData}
     */
    lockQd(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        this.service.stepItemCostService.lockQd(constructId, singleId, unitId, pointLine.sequenceNbr);

        return ResponseData.success(true);
    }
    unLockQd(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        this.service.stepItemCostService.unLockQd(constructId, singleId, unitId, pointLine.sequenceNbr);

        return ResponseData.success(true);
    }
    lockAll(args) {
        let {constructId, singleId, unitId} = args;
        let all = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);

        if (!all || all.length == 0) {
            return ResponseData.success(true);
        } else {
            for (let i = 0 ; i < all.length ; ++i) {
                if (all[i].kind === "03") {
                    all[i].isLocked = 1;
                    this.service.baseBranchProjectOptionService.handleDeAddQdStatus(all,all[i],1);
                }
            }
        }
        return ResponseData.success(true);
    }
    unLockAll(args) {
        let {constructId, singleId, unitId} = args;
        let all = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);

        if (!all || all.length == 0) {
            return ResponseData.success(true);
        } else {
            for (let i = 0 ; i < all.length ; ++i) {
                if (all[i].kind === "03") {
                    delete all[i].isLocked;
                    this.service.baseBranchProjectOptionService.handleDeAddQdStatus(all,all[i],2);
                }
            }
        }
        return ResponseData.success(true);
    }
    searchPonitAndChild(args) {
        return ResponseData.success(this.service.stepItemCostService.searchPonitAndChild(args));
    }
    /**
     * 分页查询
     * @param args
     */
    page(args) {
        let {constructId, singleId, unitId, pageNum, pageSize, sequenceNbr,isAllFlag,colorList} = args;
        //获取单位数据
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let res = this.service.stepItemCostService.pageSearch(constructId, singleId, unitId, sequenceNbr,pageNum, pageSize,isAllFlag,unit.screenCondition,colorList);
        res.data.forEach(e => this.service.itemBillProjectOptionService.dataDeal(e,1));
        return ResponseData.success(res);
    }

    /**
     * 展开
     */
    open(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        const result = this.service.stepItemCostService.open(constructId, singleId, unitId, pointLine);
        return ResponseData.success(true);
    }

    /**
     * 折叠
     */
    close(args) {
        let {constructId, singleId, unitId, pointLine} = args;
        const result = this.service.stepItemCostService.close(constructId, singleId, unitId, pointLine);
        return ResponseData.success(true);
    }

    /**
     * 删除
     * @param args
     * @returns {ResponseData}
     */
    async remove(args) {
        let {constructId, singleId, unitWorkId, pointLine, isBlock} = args;
        try {
            const result = await this.service.stepItemCostService.removeLine(constructId, singleId, unitWorkId, pointLine, isBlock);
            //状态数据处理
            this.service.baseBranchProjectOptionService.setItemDtaStatus(PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId).getAllNodes());
            await this.service.management.sycnTrigger("unitDeChange");
            await this.service.unitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitWorkId,
            });
            return ResponseData.success(result);
        } catch (e) {
            return ResponseData.fail(e.message);
        }

    }


    /**
     * 措施项目数据行颜色设置  批量设置
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updateDataColorCsxmColl(args) {
        let {constructId, singleId, unitWorkId, idList, column,value} = args;
        for(const pointLineId of idList){
            let upDateInfo={
                column,value
            }
            let res = await this.service.stepItemCostService.upDateOnList(constructId, singleId, unitWorkId, pointLineId, upDateInfo);
        }
        await this.service.itemBillProjectOptionService.updateUnitColorList(constructId, singleId, unitWorkId,"csxm");
        return ResponseData.success(true);
    }

}
StepItemCostController.toString = () => '[class StepItemCostController]';
module.exports = StepItemCostController;
