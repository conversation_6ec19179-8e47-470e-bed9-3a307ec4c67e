<template>
  <div class="operate">
      <div class="tab-wrap" @click="menuClick">
          <div class="menu" v-for="i of tabList" :key="i.type" :class="{ active: useMenu === i.type }" :data-type="i.type">
              <icon-font class="icon-font" :type="i.icon" :data-type="i.type" ></icon-font>
              <span :data-type="i.type">{{ i.name }}</span>
          </div>
      </div>
  </div>
</template>
<script setup>
import {
  getCurrentInstance,
  watch,
  ref,
  computed,
  reactive,
  onMounted, markRaw,
} from 'vue';
import infoMode from "@/plugins/infoMode.js";
import { projectDetailStore } from '@/store/projectDetail';
const store = projectDetailStore();
let useMenu = ref();
const emit = defineEmits(['showLoadMould']);
const tabList = markRaw([
  {
    icon: 'icon-gengxinzhaobiaoshu',
    name: '更新招标书',
    type: 'create',
  },
  {
    icon: 'icon-daochuXML',
    name: '导出XML',
    type: 'xml',
  },
]);
const menuClick = e => {
  const type = e.target.dataset.type;
  console.log(type);
  if (type == 'xml') {
    useMenu.value = type;
    infoMode.show({
      iconType: 'icon-tishineirong',
      infoText: '是否进行项目自检？',
      descText: '请注意：导出xml建议进行项目自检，避免出现不必要的错误',
      descTextStyle: { color: 'red' },
      confirm: () => {
        infoMode.hide();
        store.SET_CHECK_VISIBLE(true);
        // emit('showLoadMould',type);
      },
      close: () => {
        infoMode.hide();
        //isShowXML.value = true;
        //saveExportXml();
      },
    });
  }else if(type == 'create') {
    emit('showLoadMould',type);
  }
};
</script>
<style lang="scss" scoped>
.operate {
  box-sizing: border-box;
  border-bottom: 1px solid #d6d6d6;
  background: #f3f6f9;
  width: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
  //height: 54px;
  height: 36px;
  //line-height: 54px;
  user-select: none;
  &:hover {
    overflow-x: auto;
  }
  &-scroll {
    display: flex;
    min-width: fit-content; /* 设置最小宽度为子元素的总宽度 */
    padding: 0 5px;
  }
  &-item {
    display: flex;
    flex-direction: column;
    justify-content: center; /* 垂直居中 */
    min-width: 50px;
    padding: 0 10px;
    text-align: center;
    height: 54px;
    cursor: pointer;
  }
  .tab-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    .icon-font {
      font-size: 19px;
      margin-right: 5px;
    }
    .menu {
      height: 100%;
      position: relative;
      padding: 5px 5px;
      border-radius: 4px;
      margin: 0 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      transition: all 0.3s;
      //&:last-child {
      //  &::after {
      //    display: none;
      //  }
      //}
      &::after {
        position: absolute;
        content: '';
        height: 100%;
        width: 1px;
        background-color: #dedede;
        right: -8px;
        top: 0;
      }
      &:hover,
      &.active {
        background: rgba(226, 227, 231, 0.79);
        opacity: 1;
      }
    }
  }
}
</style>
