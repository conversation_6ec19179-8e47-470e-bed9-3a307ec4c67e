const {YEAR_2022} = require("../enum/ConstantUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");

/**
 * 插入参数准备
 */
class InsertOptions {
    constructor({constructId, singleId, unitId,pointLine, newLine, indexId, libraryCode, rcjFlag, unit, option, skip, overwriteColumn}) {
        this.pointLine = pointLine;//插入的目标行 在哪个行底下插入   如果没有 则是第一行
        this.newLine = newLine;//插入的新行的内容
        this.kind = newLine.kind;//新行的类型
        this.indexId = indexId || "";//索引列过来的 id
        this.libraryCode = libraryCode || "2022"; //选择的是22 还是12 定额
        this.is2022 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        this.rcjFlag = rcjFlag || 0;//是否是人材机
        this.unit = unit || "";//多单位的情况 会传入
        this.option = option || "save";//插入的方式 save  只做保存
        //跳过的步骤  rcj  跳过人材机的添加    quantity  跳过工程量的默认处理     jiqu：跳过费用定额自动计取   huizong：跳过费用代码自动计算
        this.skip = { rcj: false, quantity: false, jiqu: false, huizong: false };
        if (skip) {
            this.skip = {...this.skip, ...skip};
        }
        this.overwriteColumn = overwriteColumn || true;// 是否覆盖列  元数据有值的 情况下是否覆盖
    }

    isInsert() {
        return this.pointLine.kind != this.newLine.kind || ObjectUtils.isNotEmpty(this.pointLine.bdCode);
    }

    isJiqu() {
        return this.skip.huizong;
    }

    isHuizong() {
        return this.skip.huizong;
    }
    //跳过人材机的添加
    skipAddRcj() {
        this.skip.rcj = true;
    }

    //跳过工程量的默认处理
    skipAddQuantity() {
        this.skip.quantity = true;
    }

    //跳过人材机 单价构成汇总计算
    skipCalculation() {
        this.skip.calculation = true;
    }

    build() {

    }

}

class RemoveOptions {
    constructor({pointLine, isBlock, skipCheck}) {
        this.pointLine = pointLine || {};//删除的行
        this.isBlock = isBlock;//是否关联删除
        this.skipCheck = skipCheck || false;//是否跳过验证

    }
}

module.exports = {InsertOptions, RemoveOptions};
