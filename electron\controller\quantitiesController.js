const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");

class QuantitiesController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    quantitiesService = this.service.quantitiesService;

    getList(param) {
        let {constructId, singleId, unitId, type, lineId}  = param;
        let pointLine = this._findPointLine(constructId, singleId, unitId, type, lineId)
        let result = this.quantitiesService.getQuantitiesList(pointLine);
        return ResponseData.success(result);
    }

    /**
     * 工程量明细 新增
     * @param param
     * @returns {ResponseData}
     */
    insert(param) {
        let {constructId, singleId, unitId, type, lineId, selectId}  = param;
        let pointLine = this._findPointLine(constructId, singleId, unitId, type, lineId)
        let newLine = this.quantitiesService.add(pointLine, selectId);
        return ResponseData.success(newLine);
    }

    /**
     * 工程量明细处修改数据
     * @param param
     * @returns {Promise<ResponseData>}
     */
    async updateQuantityData(param) {
        let {constructId, singleId, unitId, type, quotaListId, pointLine} = param;
        let {sequenceNbr, mathFormula, mathIllustrate, mathResult, variables, accumulateFlag} = pointLine;
        let quotaLine = this._findPointLine(constructId, singleId, unitId, type, quotaListId);
        this.quantitiesService.upDate(quotaLine, sequenceNbr, mathFormula, mathIllustrate, mathResult, variables, accumulateFlag, type, constructId, singleId, unitId);
        await this.service.management.sycnTrigger("unitDeChange");
        return ResponseData.success(true);
    }

    /**
     * 工程量明细
     * @param param
     * @returns {Promise<ResponseData>}
     */
    async delete(param) {
        let {constructId, singleId, unitId, type, lineId, deleteId} = param;
        let pointLine = this._findPointLine(constructId, singleId, unitId, type, lineId);
        this.quantitiesService.delete(pointLine, deleteId);
        await this.service.management.sycnTrigger("unitDeChange");
        return ResponseData.success(true);
    }

    move(param){
        let {constructId, singleId, unitId, type, lineInfo, direction}  = param;
        let pointLine = this._findPointLine(constructId, singleId, unitId, type, lineInfo.quotaListId);
        let qtId = lineInfo.sequenceNbr;
        if (direction === 1) { // 0上 1下
            this.quantitiesService.moveDown(pointLine, qtId);
        } else {
            this.quantitiesService.moveUp(pointLine, qtId);
        }
        return ResponseData.success(true);
    }

    async paste(param) {
        let {constructId, singleId, unitId, type, lineInfo, pasteId} = param;
        let pointLine = this._findPointLine(constructId, singleId, unitId, type, lineInfo.quotaListId);
        let qtId = lineInfo.sequenceNbr;

        this.quantitiesService.paste(pointLine, qtId, pasteId);
        await this.service.management.sycnTrigger("unitDeChange");

        return ResponseData.success(true);
    }

    clearAll(param) {
        let {constructId, singleId, unitId, type, quotaListId}  = param;
        let quotaLine = this._findPointLine(constructId, singleId, unitId, type, quotaListId);
        this.quantitiesService.initDatas(quotaLine);

        return ResponseData.success(true);
    }

    /**
     * 根据类型找选中行
     * @param type      类型      1 fbfx ； 2 csxm
     * @param lineId    选中行id
     * @private
     */
    _findPointLine(constructId, singleId, unitId ,type, lineId) {
        let allDatas;
        let pointLine;
        if (type === 1) {
            allDatas = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        } else {
            allDatas = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        }
      /*  for (let i = 0 ; i < allDatas.length ; ++i) {
            if (allDatas[i].sequenceNbr === lineId) {
                pointLine = allDatas[i];
                break;
            }
        }*/

        return allDatas.getNodeById(lineId);
    }

}

QuantitiesController.toString = () => '[class QuantitiesController]';
module.exports = QuantitiesController;




