

const {YSConSoleHandler} = require("./YSConSoleHandler");
const ConstantUtil = require("../enum/ConstantUtil");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {JSConSoleHandler} = require("../../packages/jieSuanProject/console_handle/JSConSoleHandler");
const {YSSHConSoleHandler} = require("../../packages/shenHeYuSuanProject/console_handle/YSSHConSoleHandler");
const {GSConSoleHandler} = require("../../packages/PreliminaryEstimate/console_handle/GSConSoleHandler");
const {GLJConSoleHandler} = require("../../packages/gongLiaoJiProject/console_handle/GLJConSoleHandler");
const {ObjectUtils} = require("../utils/ObjectUtils");
const EE = require("../../core/ee");
const Fixs = require("../fixs");
const Electron = require('../../core/electron');
const systemPath = require('path');
const fs = require('fs');
const {dialog,app} = require('electron')
class ConsoleStrategy{
    constructor({path}) {
        this.path = path;
        this.handler = null;
    }

    getHandler(path) {
        //获取文件后缀名
        let pathSuffix = path.match(/[^.]+$/)[0];
        let handle = null;
        switch (pathSuffix) {
            //预算 投标项目
            case "YSF": {
                handle = new YSConSoleHandler({path});
                break;
            }
            //预算 招标项目
            case "YSFZ": {
                handle = new YSConSoleHandler({path});
                break;
            }
            //预算 单位工程
            case "YSFD": {
                handle = new YSConSoleHandler({path});
                break;
            }
            //预算 工料机
            case "YSFG": {
                handle = new GLJConSoleHandler({path});
                break;
            }
            //结算
            case "YJS": {
                handle = new JSConSoleHandler({path});
                break;
            }
            //预算审核
            case "YSH": {
                handle = new YSSHConSoleHandler({path});
                break;
            }
            //概算
            case "YGS": {
                handle = new GSConSoleHandler({path});
                break;
            }
            //工料机
            case "YSFG": {
                handle = new GLJConSoleHandler({path});
                break;
            }
        }
        return handle;
    }

    /**
     * 打开本地项目
     */
    async openLocalObj() {
        let obj =await PricingFileFindUtils.getProjectObjByPath(this.path);

        // 处理其他项目总表中 2.1  2.2  2.3 数据中的默认值问题   没有版本号的问题解决后可以注释掉这一步
        await this.handelOtherProjectChildData(obj);

        await  new Fixs(obj,obj.version).fix();
        // //设置文件版本号
        // obj.version = app.getVersion();
        let res =await this.judgePath(obj.path,this.path)
        if(res){
            let handler = this.getHandler(obj.path);
            let pathFlag= false;
            if(obj.path.match(/[^.]+$/)?.[0]!=this.path.match(/[^.]+$/)?.[0]){
                //记录文件原始体路径 后边需要备份和删除
                pathFlag = true
            }
            obj.path = this.path;
            let win = await handler.openLocalObj(obj);
            await EE.app.service.projectConfigProcess.refreshConstruct(obj);
            if(pathFlag){
                await  this.backupAndDel ();
            }

            return win;
        }else {
            //给前端发送消息不打开文件
            dialog.showMessageBoxSync({
                type: 'info',
                message: `文件已被篡改，无法打开`,
                buttons: ['确定']
            });
            // const win = Electron.getMainWindow();
            // win.webContents.send('judgeSuffix', '文件已被篡改，无法打开');
        }

    }

    async handelOtherProjectChildData(project) {
        let unitProjects = PricingFileFindUtils.getUnitListByConstructObj(project);
        for (let i = 0; i < unitProjects.length; i++) {
            let unit = unitProjects[i];
            let otherProjects = unit.otherProjects;
            if (otherProjects) {
                for (const item of otherProjects) {
                    if (ObjectUtils.isNotEmpty(item.type) && ['CLZGJ', 'ZYGCZGJ', 'SBZGJ'].includes(item.type)) {
                        item.markSafa = 0;
                        item.markSj = 0;
                    }
                }
            }
        }
    }

    /**
     * 打开内存项目，用于复制后打开
     */
    async openFromMemory(obj) {
        await  new Fixs(obj,obj.version).fix();
        // //设置文件版本号
        // obj.version = app.getVersion();
        let res =await this.judgePath(obj.path,this.path)
        if(res){
            let handler = this.getHandler(obj.path);
            let pathFlag= false;
            if(obj.path.match(/[^.]+$/)?.[0]!=this.path.match(/[^.]+$/)?.[0]){
                //记录文件原始体路径 后边需要备份和删除
                pathFlag = true
            }
            obj.path = this.path;
            let win = await handler.openFromMemory(obj);
            await EE.app.service.projectConfigProcess.refreshConstruct(obj);
            if(pathFlag){
                await  this.backupAndDel ();
            }

            return win;
        }else {
            //给前端发送消息不打开文件
            dialog.showMessageBoxSync({
                type: 'info',
                message: `文件已被篡改，无法打开`,
                buttons: ['确定']
            });
            // const win = Electron.getMainWindow();
            // win.webContents.send('judgeSuffix', '文件已被篡改，无法打开');
        }

    }


    async backupAndDel() {
        // 获取默认存储路径
        let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(null);
        // 获取文件名和扩展名
        const filename = systemPath.basename(this.path);
        const {name, ext} = systemPath.parse(filename);

        // 构建目标文件路径
        let targetPath = systemPath.join(defaultStoragePath, filename);

        // 检查文件是否已存在，如果存在则添加后缀
        let counter = 1;
        while (fs.existsSync(targetPath)) {
            const newName = `${name}(${counter})${ext}`;
            targetPath = systemPath.join(defaultStoragePath, newName);
            counter++;
        }
        //给前端发送消息不打开文件
        const win = Electron.getMainWindow();
        dialog.showMessageBoxSync({
          type: 'info',
          message: `文件版本更新，历史文件已备份至${defaultStoragePath}处`,
          buttons: ['确定']
        });
        // win.webContents.send('judgeSuffix', `文件版本更新，历史文件已备份至${defaultStoragePath}处`);
        // 复制文件
        fs.copyFileSync(this.path, targetPath);
        //删除文件
        fs.unlinkSync(this.path);
    }

    /**
     *    a. 若用户手动于桌面将文件后缀修改后（修改后其后缀也需在合法范围内，如将“.YSFZ”修改为".YSF"），或涉及到历史文件打开，双击打开时将提示用户“文件版本更新，历史文件已备份至XXX处”（其中xxx为默认数据存储路径），打开后当前打开的项目文件后缀更新成对应规则的后缀格式，需处理的情形：
     *           i. 源文件为“.YSF”修改为“.YSFZ”或“.YSFD”
     *           ii. 源文件为“.YSFZ”修改为“.YSF”或“.YSFD”
     *           iii. 源文件为“.YSFD”修改为“.YSFZ”或“.YSF”
     *           iv. 此处当前仅列明预算相关文件处理，后续待结算审核、预算审核开发完成后需补充对应文件后缀及处理
     *
     * @param filePath json文件中的path
     * @param path 路径path
     */
    async judgePath(filePath,path){
        const allowedSuffixes = [ConstantUtil.YUSUAN_FILE_SUFFIX_Z,ConstantUtil.YUSUAN_FILE_SUFFIX_D,ConstantUtil.YUSUAN_FILE_SUFFIX,ConstantUtil.YUSUAN_FILE_SUFFIX_G];

        // 提取 path 和 filePath 的后缀
        const pathSuffix = path.match(/[^.]+$/)?.[0]; // 使用可选链操作符避免空值错误
        try {
            const filePathSuffix = filePath.match(/[^.]+$/)?.[0];

            // 判断后缀是否在允许的列表中
            const isPathSuffixValid = allowedSuffixes.includes(pathSuffix);
            const isFilePathSuffixValid = allowedSuffixes.includes(filePathSuffix);

            // 如果两者的后缀都在允许的列表中且一致，返回
            if (isPathSuffixValid && isFilePathSuffixValid ) {
                return true;
            } else {
                if (ConstantUtil.YUSUAN_FILE_SUFFIX_JS == pathSuffix && ConstantUtil.YUSUAN_FILE_SUFFIX_JS == filePathSuffix){
                    return true;
                }
                if (ConstantUtil.YUSUAN_FILE_SUFFIX_GS == pathSuffix && ConstantUtil.YUSUAN_FILE_SUFFIX_GS == filePathSuffix){
                    return true;
                }
                if (ConstantUtil.YUSUAN_FILE_SUFFIX_SH == pathSuffix && ConstantUtil.YUSUAN_FILE_SUFFIX_SH == filePathSuffix){
                    return true;
                }
                return false;
            }
        } catch (e){
            return false
        }
    }
}
module.exports =  ConsoleStrategy;
