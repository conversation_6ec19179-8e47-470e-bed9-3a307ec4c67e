<!--
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-08-22 10:54:45
 * @LastEditors: wangru
 * @LastEditTime: 2025-06-05 17:14:41
-->
<template>
  <common-modal
    className="dialog-comm"
    v-model:modelValue="show"
    :title="title"
    :mask="false"
    :lockView="false"
    :lockScroll="false"
    width="1000px"
    @cancel="cancel"
    @close="cancel"
  >
    <div class="associate-sub-quotas">
      <div class="left">
        <div class="variable-table">
          <s-table
            size="small"
            class="s-table"
            bordered
            :columns="variableGridOptions.columns"
            :scroll="{ y: 220 }"
            :pagination="false"
            rowKey="id"
            :data-source="variableGridOptions.data"
            @closeEditor="updateVariableCoefficientColl"
          >
            <template #bodyCell="{ column, text }">
              <template v-if="column.key === 'value'">
                {{ text }}
              </template>
              <template v-else>{{ text }}</template>
            </template>
            <template #cellEditor="{
                column,
                modelValue,
                save,
                closeEditor,
                record: row,
                editorRef,
                getPopupContainer,
              }">
              <template v-if="column.key === 'value'">
                <a-input
                  :bordered="false"
                  :value="modelValue.value"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row[column.field],
                      );
                      closeEditor();
                    }
                  "
                >
                </a-input>
              </template>
            </template>
          </s-table>
        </div>

        <div class="guide">
          <div class="title">子目指引</div>
          <div class="guide-content">
            <div
              class="list"
              v-for="item of guideList"
              :key="item"
              :class="{ active: item === guideValue }"
              @click="guideClick(item)"
            >
              {{ item }}
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <s-table
          size="small"
          ref="stableRef"
          class="s-table"
          bordered
          :defaultExpandAllRows="true"
          :expandedRowKeys="expandedRowKeys"
          :expandIconColumnIndex="3"
          :custom-cell="customCell"
          :rowSelection="{
            selectedRowKeys: state.selectedRowKeys,
            getCheckboxProps: record => {
              return {
                disabled: [1, 2].includes(record?.levelType)||!record?.deCode,
              };
            },
            onChange: onSelectChange,
          }"
          :columns="tableOptions.columns"
          :scroll="{ y: 435 }"
          :pagination="false"
          rowKey="sequenceNbr"
          :data-source="tableOptions.data"
          @expand="expand"
          @keydown="vxeTableKeydown"
        >
          <template #bodyCell="{
              text,
              record: row,
              index,
              column,
              key,
              openEditor,
              closeEditor,
            }">
            <div v-if="column.dataIndex === 'associate'">
              <span v-if="[1, 2].includes(row?.levelType)">{{ row.name }}</span>
            </div>
            <template v-if="column.key === 'quantityExpression'">
              {{ row.quantity }}
            </template>
          </template>
          <template #cellEditor="{
              column,
              modelValue,
              save,
              closeEditor,
              record: row,
              editorRef,
              getPopupContainer,
            }">
            <template v-if="column.key === 'quantityExpression'">
              <a-input
                :bordered="false"
                :value="modelValue.value"
                :get-popup-container="getPopupContainer"
                @update:value="
                  v => {
                    modelValue.value = v;
                  }
                "
                @blur="
                  () => {
                    quantityEditClosedEvent(
                      { row, column },
                      modelValue.value,
                      row[column.field],
                    );
                    closeEditor();
                  }
                "
              >
              </a-input>
            </template>
          </template>
        </s-table>
      </div>
    </div>
    <div class="btn-list">
      <div class="checkbox">
        <a-checkbox-group
          v-model:value="settingValue"
          :options="plainOptions"
          @change="checkboxGroupChange"
        />
      </div>
      <a-button
        type="primary"
        ghost
        @click="cancel"
        style="margin-right: 12px"
      >取消</a-button>
      <a-button
        type="primary"
        @click="batchSaveChildrenDeListColl"
      >确定</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import { computed, reactive, ref, watch } from 'vue';
import api from '@/api/projectDetail';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import { useVxeTableKeyDown } from '@/hooks/useVxeTableKeyDown';
const { vxeTableKeydown } = useVxeTableKeyDown();
const projectStore = projectDetailStore();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  currentInfo: {
    type: Object,
    default: () => null,
  },
  type: {
    type: Number,
    default: () => null,
  },
});
const emits = defineEmits(['update:visible', 'successHandler', 'quotasCancel']);
const state = reactive({
  selectedRowKeys: [],
  selectedRows: [],
});
const expandedRowKeys = ref([]);
const updateDeList = ref([]);
let originalDataList = ref([]);

const show = computed({
  get: () => {
    return props.visible;
  },
  set: val => {
    emits('update:visible', val);
  },
});
let guideValue = ref('显示所有');

const title = computed(() => {
  return props.currentInfo?.bdCode + '的关联子目';
});
const cancel = () => {
  emits('quotasCancel');
};
const customCell = ({ column, record: row }) => {
  let className = '';
  if (['associate'].includes(column.dataIndex)) {
    className += `Virtual-pdLeft-s${row?.levelType} `;
  }
  return { class: className };
};
let variableGridOptions = reactive({
  columns: [
    { dataIndex: 'id', width: 50, title: '序号' },
    { dataIndex: 'variableName', title: '变量描述' },
    {
      dataIndex: 'value',
      title: '值',
      key: 'value',
      editable: 'cellEditorSlot',
    },
  ],
  data: [],
});

let tableOptions = reactive({
  columns: [
    {
      title: '序号',
      dataIndex: 'dispNo',
      width: 50,
    },
    {
      title: '关联',
      dataIndex: 'jobContent',
      slot: true,
      autoHeight: true,
      width: 50,
      customRender: ({ record }) => {
        if (record?.children?.length > 0) {
          return {
            props: {
              colSpan: 3,
            },
          };
        }
        return;
      },
    },
    {
      title: '项目编码',
      dataIndex: 'deCode',
      autoHeight: true,
      width: 100,
    },
    {
      title: '名称',
      dataIndex: 'deName',
      autoHeight: true,
      width: 220,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 60,
    },
    {
      title: '工程量',
      dataIndex: 'quantityExpression',
      key: 'quantityExpression',
      editable: 'cellEditorSlot',
    },
  ],
  data: [],
});

const guideClick = value => {
  guideValue.value = value;
  queryChildrenDeColl();
};
let guideList = ref([]);

let settingValue = ref();
const plainOptions = ref([
  {
    label: '不再显示该窗口',
    value: 1,
  },
  {
    label: '当前项已关联子目',
    value: 2,
  },
]);

const getSubitemGuidanceColl = () => {
  let apiData = {
    parentDe: JSON.parse(JSON.stringify(props.currentInfo)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  console.log('apiDAta', apiData);
  api.getSubitemGuidanceColl(apiData).then(res => {
    console.log('获取子目列表', res);
    if (res.status === 200 && res.result) {
      guideList.value = res.result;
    }
  });
};

const queryVariableCoefficientColl = () => {
  let apiData = {
    parentDe: JSON.parse(JSON.stringify(props.currentInfo)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  console.log('apiDAta', apiData);
  api.queryVariableCoefficientColl(apiData).then(res => {
    console.log('获取父定额规则', res);
    if (res.status === 200 && res.result) {
      variableGridOptions.data = res.result;
      queryChildrenDeColl();
    }
  });
};

const updateVariableCoefficientColl = () => {
  let apiData = {
    queryVariableCoefficient: JSON.parse(
      JSON.stringify(variableGridOptions.data)
    ),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.updateVariableCoefficientColl(apiData).then(res => {
    console.log('修改父级规则数据', res);
    if (res.status === 200 && res.result) {
      variableGridOptions.data = res.result;
      queryChildrenDeColl();
    }
  });
};

const queryChildrenDeColl = () => {
  let apiData = {
    parentDe: JSON.parse(JSON.stringify(props.currentInfo)),
    deCoefficient: JSON.parse(JSON.stringify(variableGridOptions.data)),
    relationContent: guideValue.value,
    updateDeList: JSON.parse(JSON.stringify(updateDeList.value)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  console.log('apiData6666666', apiData);
  expandedRowKeys.value = [];
  api.queryChildrenDeColl(apiData).then(res => {
    console.log('获取父定额规则', res);
    if (res.status === 200 && res.result) {
      tableOptions.data = res.result;
      originalDataList.value = JSON.parse(JSON.stringify(res.result));
      expandedRowKeys.value.push(res.result[0].sequenceNbr);
      res.result[0].children.forEach((item, index) => {
        if (index === 0) {
          expandedRowKeys.value.push(item.sequenceNbr);
        }
      });
      console.log('expandedRowKeys', expandedRowKeys.value);
    }
  });
};
const sortIdsByTreeOrder = (tree, rows) => {
  const order = [];
  const idToIndex = new Map();
  const ids = rows.map(item => item.sequenceNbr);
  function dfs(node) {
    if (!node) return;
    if (ids.includes(node.sequenceNbr)) {
      order.push(node.sequenceNbr);
    }
    if (node.children) {
      for (const child of node.children) {
        dfs(child);
      }
    }
  }
  dfs(tree);
  order.forEach((id, index) => {
    idToIndex.set(id, index);
  });
  rows.sort(
    (a, b) => idToIndex.get(a.sequenceNbr) - idToIndex.get(b.sequenceNbr)
  );
  return rows;
};
const batchSaveChildrenDeListColl = () => {
  const deArray = sortIdsByTreeOrder(
    tableOptions.data[0],
    JSON.parse(JSON.stringify(state.selectedRows))
  );
  console.log('🚀 ~ batchSaveChildrenDeListColl ~ deArray:', deArray);
  let apiData = {
    deArray,
    zmQuantity: variableGridOptions.data.filter(
      x => x.variableCode === 'GCL'
    )[0].value,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(props.currentInfo)),
    deCoefficient: JSON.parse(JSON.stringify(variableGridOptions.data)),
    kind: '04',
    indexId: null,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
    libraryCode: props.currentInfo.libraryCode,
    fbfxOrCsxm: props.type === 1 ? 'fbfx' : 'csxm',
  };
  api.batchSaveChildrenDeListColl(apiData).then(res => {
    console.log('保存成功', res);
    if (res.status === 200 && res.result) {
      cancel();
      emits('successHandler', res.result);
    }
  });
};

const onSelectChange = (selectedRowKeys, selectedRows) => {
  console.log('selectedRowKeys changed: ', selectedRowKeys, selectedRows);
  state.selectedRowKeys = selectedRowKeys;
  state.selectedRows = selectedRows;
};

const expand = (expanded, record) => {
  console.log('expanded1111111111', expanded, record);
  if (expanded) {
    expandedRowKeys.value.push(record.sequenceNbr);
  } else {
    let index = expandedRowKeys.value.findIndex(x => x === record.sequenceNbr);
    expandedRowKeys.value.splice(index, 1);
  }
};

const checkboxGroupChange = checkedValue => {
  console.log('checkedValue', checkedValue);
  if (checkedValue.includes(1)) {
    projectConvenientSetColl();
  }
  if (checkedValue.includes(2)) {
    findNodePathById();
  }
  if (!checkedValue.includes(2)) {
    tableOptions.data = originalDataList.value;
  }
};

// 筛选子定额选中数据
function findNodePathById() {
  let allList = JSON.parse(JSON.stringify(originalDataList.value));
  if (guideValue.value === '显示所有') {
    tableOptions.data = allList.map(list => {
      list.children = list.children.filter(item => {
        console.log('item', item, guideValue.value);
        item.children = item.children.filter(child => {
          return state.selectedRowKeys.includes(child.sequenceNbr);
        });
        return item.children.length > 0;
      });
      return list.children.length > 0 ? list : null;
    });
  } else {
    tableOptions.data = allList.map(list => {
      list.children = list.children.map(item => {
        console.log('item', item, guideValue.value);
        if (item.jobContent === guideValue.value) {
          item.children = item.children.filter(child => {
            return state.selectedRowKeys.includes(child.sequenceNbr);
          });
        }
        return item;
      });
      return list;
    });
  }
}

const projectConvenientSetColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    column: 'deGlTcFlag',
    value: false,
  };
  api.projectConvenientSetColl(apiData).then(res => {
    if (res.result) {
      message.success('设置成功');
    }
  });
};

const editClosedEvent = ({ row, column }, newValue, oldValue) => {
  //测评分支先不允许输入字母 只可输入数字表达式
  const reg = /[^0-9|\-|\*|\+|\/|\.|(|)|（|）]/g;
  let flag = reg.test(newValue);
  if (flag) return;
  row.value = eval(newValue);
};

const quantityEditClosedEvent = ({ row, column }, newValue, oldValue) => {
  console.log('1111111111', newValue, row);
  row.quantityExpression = newValue;
  row.quantity = row.quantityExpression;
  let status = updateDeList.value.some(x => x.sequenceNbr === row.sequenceNbr);
  if (status) {
    updateDeList.value.forEach(item => {
      if (item.sequenceNbr === row.sequenceNbr) {
        item = row;
      }
    });
  } else {
    updateDeList.value.push(row);
  }
  queryChildrenDeColl();
};

watch(
  () => props.visible,
  () => {
    console.log('watch进来不');
    if (props.visible) {
      guideValue.value = '显示所有';
      state.selectedRowKeys = [];
      state.selectedRows = [];
      updateDeList.value = [];
      getSubitemGuidanceColl();
      queryVariableCoefficientColl();
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style lang="scss" scoped>
.btn-list {
  margin-top: 25px;
  display: flex;
  .checkbox {
    flex: 1;
  }
}
.s-table {
  ::v-deep(.surely-table) {
    border-radius: 0;
  }
}
.variable-table {
  height: 220px;
}
.guide {
  .title {
    font-size: 14px;
    line-height: 24px;
    padding: 10px 0 5px;
    color: #000;
  }
  .guide-content {
    padding: 10px 6px;
    border: 1px solid #b9b9b9;
    overflow-y: auto;
    height: 220px;
    .list {
      padding: 0 12px;
      font-size: 14px;
      line-height: 22px;
      color: #000;
      cursor: pointer;
      &.active {
        background: rgba(192, 217, 255, 0.39);
      }
    }
  }
}
.associate-sub-quotas {
  display: flex;
  .left {
    width: 330px;
    margin-right: 10px;
  }
  .right {
    width: calc(100% - 340px);
    ::v-deep(.surely-table-cell-content) {
      height: 22px !important;
      line-height: 22px !important;
      padding: 0 8px;
    }
  }
}
.ant-input {
  height: 100%;
  font-size: 12px;
  resize: none;
  padding: 0;
}
</style>
