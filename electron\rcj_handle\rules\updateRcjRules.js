const { PricingFileFindUtils } = require('../../utils/PricingFileFindUtils');
const { NumberUtil } = require('../../utils/NumberUtil');
const EE = require('../../../core/ee');
const { ObjectUtils } = require('../../utils/ObjectUtils');
const RcjLevelMarkConstant = require("../../enum/RcjLevelMarkConstant");
const {ConvertUtil} = require("../../utils/ConvertUtils");
const ConstructProjectRcjService = require("../../service/rcj/constructProjectRcjService");
const ConstantUtil = require("../../enum/ConstantUtil");
const TaxCalculationMethodEnum = require("../../enum/TaxCalculationMethodEnum");
const CalculationTool = require("../../unit_price_composition/compute/CalculationTool");


/**
 * 修改甲供数量
 * @param de
 * @param rcj
 * @param ctx
 */
function updateDonorMaterialNumber(de,rcj,ctx,isParent) {

	let {constructProjectRcjs,rcjDetailList}= ctx;
	if (ObjectUtils.isEmpty(de))return;
	//甲供数量处理
	let coefficient = NumberUtil.multiply(rcj.resQty,de.quantity);
	if (rcj.ifDonorMaterial == 1 && rcj.donorMaterialNumber == rcj.totalNumber) {
		if (!isParent){
			let parentRcj = constructProjectRcjs.find(i => i.sequenceNbr === rcj.rcjId);
			coefficient = NumberUtil.multiply(parentRcj.resQty,coefficient);
		}
		rcj.donorMaterialNumber = NumberUtil.numberScale4(coefficient);
	}
	if (rcj.levelMark == RcjLevelMarkConstant.SINK_JX || rcj.levelMark == RcjLevelMarkConstant.SINK_PB) {
		let rcjDetails = rcjDetailList.filter(i => i.rcjId === rcj.sequenceNbr);
		if (!ObjectUtils.isEmpty(rcjDetails)) {
			for (let rcjDetail of rcjDetails) {
				//甲供数量处理
				if (rcjDetail.ifDonorMaterial == 1 && rcjDetail.donorMaterialNumber == rcjDetail.totalNumber) {
					rcjDetail.donorMaterialNumber = NumberUtil.numberScale4(NumberUtil.multiply(coefficient, rcjDetail.resQty));
				}
			}
		}
	}
}

/**
 * 更新5要素相同 市场价和暂估价标示
 * @param constructProjectRcj
 * @param list
 * @param sourcePrice
 */
function changeSimilarlyRcj(constructProjectRcj,list,type,isSimple){
	let changeDeList = [];
	if (!ObjectUtils.isEmpty(list)) {
		for (let listElement of list) {
			if (constructProjectRcj.sequenceNbr !== listElement.sequenceNbr) {
				let specificationBooble = false;
				if (ObjectUtils.isEmpty(constructProjectRcj.specification) && ObjectUtils.isEmpty(listElement.specification)){
					specificationBooble = true;
				}
				if (constructProjectRcj.materialName === listElement.materialName
					&& constructProjectRcj.materialCode === listElement.materialCode
					&& (constructProjectRcj.specification === listElement.specification||specificationBooble)
					&& constructProjectRcj.unit === listElement.unit
					&& constructProjectRcj.dePrice === listElement.dePrice
					&& constructProjectRcj.markSum === listElement.markSum) {

					listElement.marketPrice = constructProjectRcj.marketPrice;
					//添加12定额后，修改定额的市场价，另一条定额的人材机明细合价没有自动计算 BUG修复
					listElement.total = NumberUtil.multiplyToString(listElement.marketPrice, listElement.totalNumber,2);

					//if (!ObjectUtils.isEmpty(constructProjectRcj.sourcePrice)) {
						listElement.sourcePrice = constructProjectRcj.sourcePrice;
					//}
					listElement.ifProvisionalEstimate = constructProjectRcj.ifProvisionalEstimate;
					listElement.highlight = constructProjectRcj.highlight;
					if (ObjectUtils.isNotEmpty(type)){
						if (type == 1){
							listElement.priceMarket = constructProjectRcj.priceMarket;
							listElement.priceMarketTax = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(listElement.priceMarket,NumberUtil.add(1+NumberUtil.divide100(listElement.taxRate))));
						}else {
							listElement.priceMarketTax = constructProjectRcj.priceMarketTax;
							listElement.priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.divide(listElement.priceMarketTax,NumberUtil.add(1+NumberUtil.divide100(listElement.taxRate))));
						}
						listElement.marketPrice = isSimple?listElement.priceMarketTax:listElement.priceMarket;
						//添加12定额后，修改定额的市场价，另一条定额的人材机明细合价没有自动计算 BUG修复
						listElement.total = NumberUtil.multiplyToString(listElement.marketPrice, listElement.totalNumber,2);
					}
					changeDeList.push(listElement.deId);
					// listElement.total = NumberUtil.multiply(listElement.marketPrice,
					// 	listElement.totalNumber).toFixed(2);
				}
			}
		}
	}
	return changeDeList;
}


let updateRcjRulesDetails = {
	//不含税市场价
	priceMarket: async ({rcj,ctx,isParent}) => {
		//修改不含税市场价后，含税市场价=不含税市场价*（1+税率%）；
		let { service } = EE.app;
		let {taxRate} = rcj;
		let {isSimple,unit,constructId, singleId, unitId} = ctx;
		rcj.priceMarket = NumberUtil.costPriceAmountFormat(rcj.priceMarket);
		rcj.priceMarketTax = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(rcj.priceMarket,NumberUtil.add(1+NumberUtil.divide100(taxRate))));
		rcj.marketPrice = isSimple?rcj.priceMarketTax:rcj.priceMarket;
		if (rcj.priceMarketTax == rcj.priceBaseJournalTax){
			rcj.sourcePrice = null;
		}else {
			rcj.sourcePrice = "自行询价";
		}

		//处理承包人 相同价格
		if (rcj.isCbrRcj == 1){
			const baseParams = {
				constructId:constructId,
				singleId:singleId,
				unitId:unitId,
				rcj:rcj,
				priceKey:"priceMarket"
			};
			service.rcjProcess.cbrRcjListPrice(baseParams);
		}

		ctx.alike_material = true;
		//修改 价格 系列
		ctx.series_price = true;
		// let changeRcjList = changeSimilarlyRcj(rcj,isParent?constructProjectRcjs:rcjDetailList,1,isSimple);
		// ctx.changeDeList.push(...changeRcjList)
		let fbfxDe = unit.itemBillProjects.filter(k =>k.rcjFlag == 1 && (k.bdCode == rcj.materialCode ||k.fxCode == rcj.materialCode))
		if (ObjectUtils.isNotEmpty(fbfxDe)){
			for (let item of fbfxDe) {
				let de = unit.itemBillProjects.getNodeById(item.sequenceNbr);
				de.zjfPrice = rcj.marketPrice
				 let calculationTool = new CalculationTool({constructId, singleId, unitId, allData: PricingFileFindUtils.getFbFx(constructId, singleId, unitId)});
				 calculationTool.calculationChian({sequenceNbr: de.sequenceNbr});
			}
		}
		let csxmDe = unit.measureProjectTables.filter(k =>k.rcjFlag == 1 && (k.bdCode == rcj.materialCode ||k.fxCode == rcj.materialCode))
		if (ObjectUtils.isNotEmpty(csxmDe)){
			for (let item of csxmDe) {
				item.zjfPrice = rcj.marketPrice
				let calculationTool = new CalculationTool({constructId, singleId, unitId, allData: PricingFileFindUtils.getCSXM(constructId, singleId, unitId)});
				calculationTool.calculationChian({sequenceNbr: item.sequenceNbr});
			}

		}

	},
	//含税市场价
	priceMarketTax: async ({rcj,ctx,isParent}) => {
		//修改含税市场价后，不含税市场价=含税市场价/（1+税率%）；
		let { service } = EE.app;
		let {taxRate} = rcj;
		let {isSimple,constructProjectRcjs,rcjDetailList,constructId, singleId, unitId} = ctx;
		rcj.priceMarketTax = NumberUtil.costPriceAmountFormat(rcj.priceMarketTax);
		rcj.priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.divide(rcj.priceMarketTax,NumberUtil.add(1+NumberUtil.divide100(taxRate))));
		rcj.marketPrice = isSimple?rcj.priceMarketTax:rcj.priceMarket;
		if (rcj.priceBaseJournal == rcj.priceMarket){
			rcj.sourcePrice = null;
		}else {
			rcj.sourcePrice = "自行询价";
		}
		ctx.alike_material = true;
		//修改 价格 系列
		ctx.series_price = true;

		//处理承包人 相同价格
		if (rcj.isCbrRcj == 1){
			const baseParams = {
				constructId:constructId,
				singleId:singleId,
				unitId:unitId,
				rcj:rcj,
				priceKey:"priceMarketTax"
			};
			service.rcjProcess.cbrRcjListPrice(baseParams);
		}
		// let changeRcjList = changeSimilarlyRcj(rcj,isParent?constructProjectRcjs:rcjDetailList,2,isSimple);
		// ctx.changeDeList.push(...changeRcjList)
	},
	//税率
	taxRate: ({rcj,ctx}) => {
		//修改税率后，通过含税市场价=不含税市场价*（1+税率%），
		let {taxRate} = rcj;
		let {isSimple} = ctx;

		if (isSimple){
			rcj.priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(100,
				NumberUtil.divide(rcj.priceMarketTax,NumberUtil.add(100,taxRate))));
		}else {
			rcj.priceMarketTax = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(
				0.01,NumberUtil.multiply(rcj.priceMarket,NumberUtil.add(100,taxRate))));
		}
		//rcj.priceMarketTax = NumberUtil.numberScale2(NumberUtil.multiply(rcj.priceMarket,NumberUtil.add(1+NumberUtil.divide100(taxRate))));
		rcj.marketPrice = isSimple?rcj.priceMarketTax:rcj.priceMarket;

		if (rcj.priceMarketTax == rcj.priceBaseJournalTax){
			rcj.sourcePrice = null;
		}else {
			rcj.sourcePrice = "自行询价";
		}
	},
	//市场价
	marketPrice:async ({rcj,ctx,updateRule,isParent}) => {
		//判断当前人材机数据是 22还是12
		let { service } = EE.app;
		const is2022Rcj = PricingFileFindUtils.is22Unit(ctx.unit);
		rcj.marketPrice = NumberUtil.costPriceAmountFormat(rcj.marketPrice);
		let {isSimple,unit,taxCalculationMethod,constructId, singleId, unitId} = ctx;
		let {constructProjectRcjs,rcjDetailList}= ctx;
		ctx.alike_material = true;
		//修改 价格 系列
		ctx.series_price = true;
		if (is2022Rcj){
			isSimple?rcj.priceMarketTax = rcj.marketPrice:rcj.priceMarket = rcj.marketPrice;
			if (rcj.priceMarketTax != rcj.priceBaseJournalTax){
				rcj.sourcePrice = "自行询价";
			}else {
				rcj.sourcePrice = null;
			}
			let fn  =null;
			if (isSimple){
				fn  =updateRule["priceMarketTax"];
			}else {
				fn  =updateRule["priceMarket"];
			}
			if(fn){
				fn({rcj,ctx,isParent});
			}
		}else {
			rcj.sourcePrice = rcj.marketPrice != rcj.dePrice?"自行询价":"";
			rcj.highlight = null;
			// rcj.total = NumberUtil.multiplyToString(rcj.marketPrice, rcj.totalNumber,2);
			// let changeRcjList;
			// if (isParent){
			// 	let rcjList = constructProjectRcjs.concat(rcjDetailList);
			// 	changeRcjList = changeSimilarlyRcj(rcj,rcjList,null,null);
			// }else {
			// 	let rcjList = constructProjectRcjs.concat(rcjDetailList);
			// 	changeRcjList=changeSimilarlyRcj(rcj,rcjList,null,null);
			//
			// }
			//
			// ctx.changeDeList.push(...changeRcjList);
			// await service.constructProjectRcjService.changeSimilarlyRcj(rcj,constructProjectRcjs,rcj.sourcePrice,is2022Rcj,taxCalculationMethod);
			// await service.constructProjectRcjService.changeSimilarlyRcj(rcj,rcjDetailList,rcj.sourcePrice,is2022Rcj,taxCalculationMethod,true,unit);
		}

		//处理承包人 相同价格
		if (rcj.isCbrRcj == 1){
			const baseParams = {
				constructId:constructId,
				singleId:singleId,
				unitId:unitId,
				rcj:rcj,
				priceKey:"marketPrice"
			};
			await service.rcjProcess.cbrRcjListPrice(baseParams);
		}

		let fbfxDe = unit.itemBillProjects.filter(k =>k.rcjFlag == 1 && (k.bdCode == rcj.materialCode ||k.fxCode == rcj.materialCode))
		if (ObjectUtils.isNotEmpty(fbfxDe)){
			for (let item of fbfxDe) {
				item.zjfPrice = rcj.marketPrice
				let calculationTool = new CalculationTool({constructId, singleId, unitId, allData: PricingFileFindUtils.getFbFx(constructId, singleId, unitId)});
				calculationTool.calculationChian({sequenceNbr: item.sequenceNbr});
			}




		}
		let csxmDe = unit.measureProjectTables.filter(k =>k.rcjFlag == 1 && (k.bdCode == rcj.materialCode ||k.fxCode == rcj.materialCode))
		if (ObjectUtils.isNotEmpty(csxmDe)){
			for (let item of csxmDe) {
				item.zjfPrice = rcj.marketPrice
				let calculationTool = new CalculationTool({constructId, singleId, unitId, allData: PricingFileFindUtils.getCSXM(constructId, singleId, unitId)});
				calculationTool.calculationChian({sequenceNbr: item.sequenceNbr});
			}

		}

	},

	//名称
	materialName: async({rcj,ctx}) => {
		let {constructProjectRcjs,rcjDetailList}= ctx;
		//原来逻辑 刷新甲供数量
		//刷新甲供数量
		ctx.jiagong_num = true;
		ctx.alike_material = true;
		////修改市场价
		//         await this.service.rcjProcess.marketPriceAlreadyHaveRcj(rcj,constructProjectRcjs,rcjDetailList);
	},

	specification: async({rcj,ctx}) => {
		if (rcj.specification == ""){
			rcj.specification = null;
		}
		//原来逻辑 刷新甲供数量
		ctx.jiagong_num = true;
		ctx.updateCode = true;
		ctx.alike_material = true;
	},
	kind: async({rcj,ctx}) => {
		let {kind} = rcj;
		let { service } = EE.app;
		let {constructProjectRcjs,rcjDetailList}= ctx;
		//材料
		if (kind == 2 || kind == 5){
			rcj.taxRemoval = rcj.taxRemovalBackUp;
			//设备
		}else if (kind == 4){
			//rcj.taxRemoval = ConstructProjectRcjService.sbfTaxRemoval;
			//rcj.taxRemoval = 11.28;
		}
		rcj.type = service.baseRcjService.getRcjTypeEnumDescByCode(kind);
		//刷新甲供数量
		ctx.jiagong_num = true;

	},
	//是否是暂估(0:不是，1：是)
	ifProvisionalEstimate: async({rcj,ctx,isParent}) => {
		// let { service } = EE.app;
		// //判断当前人材机数据是 22还是12
		// const is2022Rcj = PricingFileFindUtils.is22Unit(ctx.unit);
		// let {constructProjectRcjs,rcjDetailList,taxCalculationMethod}= ctx;


		ctx.alike_material = true;
		//人材机 暂估价表处理
		ctx.zg_material = true;

		// if (isParent){
		// 	changeSimilarlyRcj(rcj,constructProjectRcjs,null,null);
		// }else {
		// 	changeSimilarlyRcj(rcj,rcjDetailList,null,null);
		// }

		// if (type===1){
		// 	await service.constructProjectRcjService.changeSimilarlyRcj(rcj,constructProjectRcjs,null,is2022Rcj,taxCalculationMethod);
		// }else {
		// 	await service.constructProjectRcjService.changeSimilarlyRcj(rcj,rcjDetailList,null,is2022Rcj,taxCalculationMethod);
		// }
	},

	unit: async({rcj,ctx,de,oldValue,isParent,updateRule}) => {
		//修改rcj单位
		/*let orgUnitNum = Number.parseInt(oldValue);
		if (Number.isNaN(orgUnitNum)) {
			orgUnitNum = 1;
		}
		let nowUnitNum = Number.parseInt(rcj.unit);
		if (Number.isNaN(nowUnitNum)) {
			nowUnitNum = 1;
		}
		if (orgUnitNum !== nowUnitNum) {
			nowUnitNum = nowUnitNum ? nowUnitNum : 1;
			let resQty= NumberUtil.divide(
				rcj.resQty,
				nowUnitNum
			);
			rcj.resQty = resQty;
			let fn = updateRule["resQty"];
			if(fn){
				fn({rcj,ctx,de,isParent});
			}
		}*/
		//刷新甲供数量
		ctx.jiagong_num = true;
		ctx.updateCode = true;
		ctx.alike_material = true;
	},


	totalNumber: async ({rcj,ctx,de,isParent}) => {
		let {constructProjectRcjs}= ctx;
		let { service } = EE.app;
		let coefficient = NumberUtil.divide(rcj.totalNumber,de.quantity);
		rcj.changeResQtyCunsumerIds = rcj.resQty //定额类型判断使用
		rcj.beforQty = rcj.resQty;
		if (isParent){
			//rcj.resQty = NumberUtil.formatNumber(coefficient,4);
			rcj.resQty = NumberUtil.rcjDetailAmountFormat(coefficient);
			rcj.consumerResQty = rcj.resQty;
		}else {
			let parentRcj = constructProjectRcjs.find(i => i.sequenceNbr === rcj.rcjId);
			rcj.resQty = NumberUtil.rcjDetailAmountFormat(NumberUtil.multiply(parentRcj.resQty,coefficient));
		}
		updateDonorMaterialNumber(de,rcj,ctx,isParent);
		//修改合计数量会重新计算消耗量
		service.rcjProcess.updateRcjSyncDeConversionInfo(ctx.constructId, ctx.singleId, ctx.unitId,de.sequenceNbr,rcj,"updateQty",null,null,1);
	},

	resQty: ({rcj,ctx,de,isParent})=>{
		let { unit } = ctx;
		let { service } = EE.app;
		rcj.changeResQtyCunsumerIds = rcj.resQty //定额类型判断使用
		//小数点处理
		rcj.resQty = NumberUtil.rcjDetailAmountFormat(rcj.resQty);

		//todo 修改消耗量
		rcj.beforQty=rcj.consumerResQty?rcj.consumerResQty:rcj.initResQty;
		rcj.lastResQty = rcj.resQty;
		rcj.consumerResQty = rcj.resQty;
		rcj.consumerResQty = rcj.resQty;

		//todo 不知道此方法是否还有用
		service.conversionDeService.freezeRuleIds(rcj)
		if (rcj.kind3dType == 1) {
			rcj.resQtyChangeType = 1;
		}
		//todo 不知道此方法是否还有用
		if (!ObjectUtils.isEmpty(unit.conversionInfoList)) {
			let rules = unit.conversionInfoList.filter(f => f.deId === rcj.deId);
			if (rules && rules.length > 0) {
				if (!rcj.ruleDeActive) {
					rcj.ruleDeActive = {};
				}
				for (let i = 0; i < rules.length; ++i) {
					rcj.ruleDeActive[rules[i].ruleId] = true;
				}
			}
		}
		updateDonorMaterialNumber(de,rcj,ctx,isParent);
		//同步换算信息
		//处理人材机换算信息
		if(ObjectUtils.isEmpty(rcj.rcjId)){
			service.rcjProcess.updateRcjSyncDeConversionInfo(ctx.constructId, ctx.singleId, ctx.unitId,de.sequenceNbr,rcj,"updateQty",null,null,1);
		}

	},

	isLock: ({ rcj,ctx ,isParent}) => {
		let {rcjDetailList}= ctx;
		if (isParent){
			if(!ObjectUtils.isEmpty(rcjDetailList)) {
				//获取二级材料
				let list = rcjDetailList.filter(k => k.rcjId === rcj.sequenceNbr);
				for (let item of list) {
					item.isLock = rcj.isLock;
				}
			}
		}
	},
};


let parentUpdateRcjRulesDetails  = {
	...updateRcjRulesDetails
};



let childUpdateRcjRulesDetails  = {
	...updateRcjRulesDetails
};


module.exports = {parentUpdateRcjRulesDetails,childUpdateRcjRulesDetails,updateRcjRulesDetails};
