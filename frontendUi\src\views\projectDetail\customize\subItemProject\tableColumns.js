/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-07-03 11:24:30
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-15 15:09:08
 */
import { ref } from 'vue';
import { useSubItem } from '@/hooks/useSubItemStable.js';
import { hangZCSB } from './hangZCSB.js';

const { isSpecificationEdit } = hangZCSB({});
const typeList = ref([
  {
    name: '材料费',
    value: 2,
  },
  {
    name: '主材费',
    value: 5,
  },
  {
    name: '设备费',
    value: 4,
  },
]);
const feeTest = [
  {
    title: '设备费单价(定额价)',
    field: 'sbfPriceDe',
    visible: false,
    classType: 2,
  },{
    title: '设备费合价(定额价)',
    field: 'sbfTotalDe',
    classType: 2,
  },{
    title: '人工费单价(定额价)',
    field: 'rfeeDe',
    classType: 2,
  },{
    title: '人工费合价(定额价)',
    field: 'totalRfeeDe',
    classType: 2,
  },{
    title: '材料费单价(定额价)',
    field: 'cfeeDe',
    classType: 2,
  },{
    title: '材料费合价(定额价)',
    field: 'totalCfeeDe',
    classType: 2,
  },{
    title: '机械费单价(定额价)',
    field: 'jfeeDe',
    classType: 2,
  },{
    title: '机械费合价(定额价)',
    field: 'totalJfeeDe',
    classType: 2,
  },{
    title: '主材费单价(定额价)',
    field: 'zcfeeDe',
    classType: 2,
  },{
    title: '主材费合价(定额价)',
    field: 'totalZcfeeDe',
    classType: 2,
  },{
    title: '工日',
    field: 'grPrice',
    classType: 2,
  },{
    title: '工日合计',
    field: 'totalGrPrice',
    classType: 2,
  },
]
const getTableColumns = (emits, type) => {
  let { editClosedEvent } = useSubItem({
    emits,
    pageType: type,
  });
  let codeMap = {
    fbfx: 'bdCode',
    csxm: 'fxCode',
  };

  const tableColumns = ref([
    // 常用项

    {
      title: '序号',
      field: 'dispNo',
      dataIndex: 'dispNo',
      width: 50,
      align: 'center',
      autoHeight: true,
      classType: 1,
      fixed: 'left',
    },
    {
      title: '项目编码',
      field: codeMap[type],
      dataIndex: codeMap[type],
      align: 'left',
      headerAlign: 'center',
      width: 170,
      editRender: { autofocus: '.vxe-input--inner' },
      edit: true,
      classType: 1,
      fixed: 'left',
      editableTrigger: 'click',
      editable: 'cellEditorSlot',
    },
    {
      title: '类型',
      field: 'type',
      dataIndex: 'type',
      width: 60,
      align: 'center',
      slot: true,
      classType: 1,
      fixed: 'left',
      tooltip: true,
      editable: ({ record: row }) => {
        if (typeList.value.map(item => item.name).includes(row.type)) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '项目名称',
      field: 'name',
      dataIndex: 'name',
      width: 200,
      align: 'center',
      slot: true,
      autoHeight: true,
      editRender: { autofocus: '.vxe-textarea--inner' },
      classType: 1,
      tooltip: true,
      fixed: 'left',
      editable: ({ record: row }) => {
        // console.log(record)
        if (!['00', '07'].includes(row.kind)) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '项目特征',
      field: 'projectAttr',
      width: 180,
      slot: true,
      autoHeight: true,
      editRender: { autofocus: '.vxe-textarea--inner' },
      classType: 1,
      editable: ({ record: row }) => {
        if (['03'].includes(row.kind)) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '规格型号',
      field: 'specification',
      width: 80,
      slot: true,
      autoHeight: true,
      classType: 1,
      editRender: { autofocus: '.vxe-input--inner' },
      editable: ({ record: row }) => {
        if (isSpecificationEdit(row)) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '单位',
      field: 'unit',
      width: 65,
      slot: true,
      classType: 1,
      editRender: { autofocus: '.custom--inner' },
      editable: ({ record: row }) => {
        // console.log(record)
        if (
          ['03', '04', 94, 95].includes(row.kind) &&
          row[codeMap[type]] &&
          !['07'].includes(row.kind)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '工程量表达式',
      field: 'quantityExpression',
      width: 100,
      classType: 1,
      slot: true,
      autoHeight: true,
      editRender: { autofocus: '.vxe-input--inner' },
      editable: ({ record: row }) => {
        if (['03', '04'].includes(row.kind) && row.isCostDe !== 1) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '工程量',
      field: 'quantity',
      width: 80,
      slot: true,
      editRender: { autofocus: '.vxe-input--inner' },
      classType: 1,
      editable: ({ record: row }) => {
        if (['03', '04', 94, 95].includes(row.kind) && row.isCostDe !== 1) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },

    {
      title: '锁定综合单价',
      field: 'lockPriceFlag',
      width: 100,
      classType: 1,
    },
    {
      title: '单价',
      field: 'zjfPrice',
      width: 80,
      classType: 1,
      slot: true,
      editable: ({ record: row }) => {
        if (
          [94, 95].includes(row.kind) ||
          (row.kind == '04' && row.rcjFlag && Number(row.isChangeAva) !== 0)
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '合价',
      field: 'zjfTotal',
      width: 80,
      slot: true,
      classType: 1,
    },
    {
      title: '综合单价',
      field: 'price',
      width: 80,
      classType: 1,
      slot: true,
    },
    {
      title: '综合合价',
      field: 'total',
      width: 80,
      classType: 1,
    },

    {
      title: '单价构成文件',
      field: 'qfCode',
      width: 80,
      editRender: {},
      slot: true,
      ellipsis: true,
      classType: 1,
      editable: ({ record: row }) => {
        if (
          ['04'].includes(row.kind) &&
          row[codeMap[type]] &&
          !row.zjcsClassCode
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '取费文件',
      field: 'costMajorName',
      width: 80,
      editRender: { autofocus: '.vxe-select' },
      slot: true,
      classType: 1,
      visible: false,
      ellipsis: true,
      editable: ({ record: row }) => {
        if (
          ['04'].includes(row.kind) &&
          row[codeMap[type]] &&
          !row.zjcsClassCode
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '施工组织措施类别',
      field: 'measureType',
      width: 80,
      editRender: { autofocus: '.vxe-select' },
      slot: true,
      classType: 1,
      ellipsis: true,
      editable: ({ record: row }) => {
        if (
          row.kind === '04' &&
          row[codeMap[type]] &&
          !row.zjcsClassCode &&
          ![2, 5].includes(Number(row.isCostDe))
        ) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    },
    {
      title: '防寒子目',
      field: 'coldResistantSuborder',
      slot: true,
      classType: 1,
      visible: false,
    },
    {
      title: '主要清单',
      field: 'ifMainQd',
      slot: true,
      classType: 1,
      visible: false,
    },
    {
      title: '是否暂估',
      field: 'ifProvisionalEstimate',
      width: 100,
      classType: 1,
      slot: true,
      visible: false,
    },
    {
      title: '备注',
      field: 'description',
      slot: true,
      editRender: { autofocus: '.vxe-textarea--inner' },
      classType: 1,
      ellipsis: true,
      editable: ({ record: row }) => {
        if ([94, 95].includes(row.kind)) {
          return false;
        } else {
          return true;
        }
      },
      valueParser: ({ newValue, oldValue, record: row, column }) => {
        if (newValue === oldValue) return newValue;
        editClosedEvent({ row, column }, newValue, oldValue);
        return newValue;
      },
    },
    // 费用细项
    {
      title: '人工费单价',
      field: 'rfee',
      classType: 2,
      visible: false,
    },
    {
      title: '人工费合价',
      field: 'totalRfee',
      visible: false,
      classType: 2,
    },
    {
      title: '材料费单价',
      field: 'cfee',
      visible: false,
      classType: 2,
    },
    {
      title: '材料费合价',
      field: 'totalCfee',
      visible: false,
      classType: 2,
    },
    {
      title: '机械费单价',
      field: 'jfee',
      visible: false,
      classType: 2,
    },
    {
      title: '机械费合价',
      field: 'totalJfee',
      visible: false,
      classType: 2,
    },
    {
      title: '主材费单价',
      field: 'zcfee',
      visible: false,
      classType: 2,
    },
    {
      title: '主材费合价',
      field: 'totalZcfee',
      visible: false,
      classType: 2,
    },
    {
      title: '设备费单价',
      field: 'sbfPrice',
      visible: false,
      classType: 2,
    },
    {
      title: '设备费合价',
      field: 'sbfTotal',
      visible: false,
      classType: 2,
    },
    {
      title: '暂估单价',
      field: 'zgfPrice',
      visible: false,
      classType: 2,
    },
    {
      title: '暂估合价',
      field: 'zgfTotal',
      visible: false,
      classType: 2,
    },
    {
      title: '管理费单价',
      field: 'managerFee',
      visible: false,
      classType: 2,
    },
    {
      title: '管理费合价',
      field: 'totalManagerFee',
      visible: false,
      classType: 2,
    },
    {
      title: '利润单价',
      field: 'profitFee',
      classType: 2,
      visible: false,
    },
    {
      title: '利润合价',
      field: 'totalProfitFee',
      visible: false,
      classType: 2,
    },
    {
      title: '规费单价',
      field: 'gfPrice',
      visible: false,
      classType: 2,
    },
    {
      title: '规费合价',
      field: 'gfTotal',
      visible: false,
      classType: 2,
    },
    //此处缺少直接费单价和直接费合价
    {
      title: '生产工具使用费合价',
      field: 'scgjsyfTotal',
      visible: false,
      classType: 2,
    },
    {
      title: '生产工具使用费单价',
      field: 'scgjsyfPrice',
      visible: false,
      classType: 2,
    },
    {
      title: '繁华地段管理增加费单价',
      field: 'fhddglzjfPrice',
      visible: false,
      classType: 2,
    },
    {
      title: '繁华地段管理增加费合价',
      field: 'fhddglzjfTotal',
      visible: false,
      classType: 2,
    },
    {
      title: '冬季防寒费单价',
      field: 'gjfhfPrice',
      classType: 2,
      visible: false,
    },
    {
      title: '冬季防寒费合价',
      field: 'gjfhfTotal',
      visible: false,
      classType: 2,
    },
    {
      title: '山地管护增加费单价',
      field: 'sdghzjfPrice',
      visible: false,
      classType: 2,
    },
    {
      title: '山地管护增加费合价',
      field: 'sdghzjfTotal',
      visible: false,
      classType: 2,
    },

    {
      title: '绿色施工安全防护措施费单价',
      field: 'lssgaqfhcsfPrice',
      visible: false,
      classType: 2,
    },
    {
      title: '绿色施工安全防护措施费合价',
      field: 'lssgaqfhcsfTotal',
      visible: false,
      classType: 2,
    },
    {
      title: '最高限价',
      slot: true,
      field: 'ceilingPrice',
      editRender: { autofocus: '.vxe-textarea--inner' },
      classType: 2,
      visible: false,
      editable: 'cellEditorSlot',
    },
    {
      title: '进项税额单价',
      field: 'jxsePrice',
      visible: false,
      classType: 2,
    },
    {
      title: '进项税额合价',
      field: 'jxseTotal',
      visible: false,
      classType: 2,
    },
    {
      title: '销项税额单价',
      field: 'xxsePrice',
      visible: false,
      classType: 2,
    },
    {
      title: '销项税额合价',
      field: 'xxseTotal',
      visible: false,
      classType: 2,
    },
    {
      title: '增值税应纳税额单价',
      field: 'zzsynsePrice',
      visible: false,
      classType: 2,
    },
    {
      title: '增值税应纳税额合价',
      field: 'zzsynseTotal',
      visible: false,
      classType: 2,
    },
    {
      title: '附加税费单价',
      field: 'fjsePrice',
      visible: false,
      classType: 2,
    },
    {
      title: '附加税费合价',
      field: 'fjseTotal',
      visible: false,
      classType: 2,
    },
    {
      title: '税前工程造价单价',
      field: 'sqgczjPrice',
      visible: false,
      classType: 2,
    },
    {
      title: '税前工程造价合价',
      field: 'sqgczjTotal',
      visible: false,
      classType: 2,
    },
    {
      title: '风险费用单价',
      field: 'fxfyPrice',
      visible: false,
      classType: 2,
    },
    {
      title: '风险费用合价',
      field: 'fxfyTotal',
      visible: false,
      classType: 2,
    },
    {
      title: '税金单价',
      field: 'sjPrice',
      visible: false,
      classType: 2,
    },
    {
      title: '税金合价',
      field: 'sjTotal',
      classType: 2,
      visible: false,
    },
  ]);
  if (type === 'csxm') {
    let index = tableColumns.value.findIndex(a => a.field === 'measureType');
    tableColumns.value.splice(index + 1, 0, {
      title: '措施类别',
      field: 'itemCategory',
      editRender: { autofocus: '.vxe-select' },
      slot: true,
      classType: 1,
      editable: ({ record: row }) => {
        if (row.kind === '01' && row.constructionMeasureType !== 2) {
          return 'cellEditorSlot';
        } else {
          return false;
        }
      },
    });
    // 措施项目 底下才有
    let newObject = {
      title: '调整系数',
      field: 'adjustmentCoefficient',
      visible: true,
      editRender: { autofocus: '.vxe-textarea--inner' },
      classType: 1,
      editable: ({ record: row }) => {
        if (row.kind === '03' && row.zjcsClassCode !== '0') {
          return 'cellEditorSlot';
        }
        return false;
      },
    };
    tableColumns.value.splice(7, 0, newObject);
  }
  // return [...tableColumns.value, ...feeTest];
  return [...tableColumns.value];

};
export default getTableColumns;
