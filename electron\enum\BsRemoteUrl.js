/**
 *
 */
class BsRemoteUrl {

    /**
     * 打开线上项目URL
     */
    static openOnlineProject = 'http://***********/pricingApi/resource-api/pricing-bs-resource/1/ysf/file/output/json/';


    /**
     * 打开线上项目列表
     */
    static openOnlineProjectList = 'http://***********/pricingApi/resource-api/pricing-bs-resource/1/controlBoard/csProjectList';


    //load-price 开发ip
    static development = "http://*************:5843";

    //load-price 生产 域名
    static production = "https://jijia.yunsuanfang.com/loadprice";

    //使用什么环境
    static loadPrice = this.development;

    /**
     * 载价人材机数据
     */
    static loadPriceAcquire = this.loadPrice+"/1/loadPrice/acquire";
    //static loadPriceAcquire = "http://127.0.0.1:5843/1/loadPrice/acquire";

    /**
     * 载价人材机数据(载价平均价)
     */
    static loadPriceAcquireAvg = this.loadPrice+"/1/loadPrice/acquireAvg";
    //static loadPriceAcquireAvg = "http://*************:5843/1/loadPrice/acquireAvg";

    /**
     * 逐条载价 查询人材机
     */
    static loadPriceGetZtzj = this.loadPrice + "/1/loadPrice/getZtzj";
    //static loadPriceGetZtzj = "http://127.0.0.1:5843/1/loadPrice/getZtzj";

    /**
     * 逐条载价 查询人材机(载价平均价)
     */
    //static loadPriceGetZtzjAvg = this.loadPrice + "/1/loadPrice/getZtzjAvg";
    /*static loadPriceGetZtzjAvg = "http://*************:5843/1/loadPrice/getZtzjAvg";*/

    /**
     * 清单匹配组价方案查询
     */
    static mergePriceQuery = this.loadPrice+"/1/mergePrice/merge";


    /**
     * 清单项目特征方案查询
     */
    static qdProjectAttrQuery = this.loadPrice+"/1/mergePrice/mergeAttr";

    /**
     * 清单明细区组价方案查询
     */
    static qdDetailMergeQuery = this.loadPrice + "/1/mergePrice/solution";
    //static qdDetailMergeQuery = 'http://127.0.0.1:5843/1/mergePrice/solution';


    /**
     * 软件使用有效期接口
     * @type {string}
     */
    static softwareIndate = 'http://***********:7071/api/portal-resources/1/feedBackDays/queryByTelNumber/';



    /**
     * 算房宝材价动态
     * @type {string}
     */
    static sfb_periodical = 'https://api.sfb.yunsuanfang.com/1/portal/periodical/130100';


    /**
     * 获取信息价 地区列表 城市下面带着地区
     * @type {string}
     */
    static sfb_Dim_Region = 'https://api.sfb.yunsuanfang.com/1/material/dimRegion';

}

module.exports = BsRemoteUrl;
