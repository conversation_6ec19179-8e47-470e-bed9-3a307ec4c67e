<!--
 * @Descripttion: 报表头部的操作
 * @Author: sunchen
 * @Date: 2023-05-23 14:43:34
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-12-25 17:18:02
-->
<template>
  <div class="tab-wrap" @click="menuClick">
    <div
      class="menu"
      v-for="i of tabList"
      :key="i.type"
      :class="{ active: useMenu === i.type }"
      :data-type="i.type"
    >
      <icon-font
        class="icon-font"
        :type="i.icon"
        :data-type="i.type"
      ></icon-font>
      <span :data-type="i.type">{{ i.name }}</span>
    </div>
    <slot name="default"></slot>
  </div>
</template>

<script setup>
import { markRaw, ref } from 'vue';

const emit = defineEmits(['menuClick']);
let useMenu = ref();
const tabList = markRaw([
  {
    icon: 'icon-daochuExcel',
    name: '导出Excel',
    type: 'excel',
  },
  {
    icon: 'icon-daochuPDF',
    name: '导出PDF',
    type: 'pdf',
  },
  {
    icon: 'icon-daochuXML',
    name: '导出XML',
    type: 'xml',
  },
  {
    icon: 'icon-baocunbaobiao',
    name: '保存报表',
    type: 'save',
  },
  {
    icon: 'icon-daorubaobiao',
    name: '导入报表',
    type: 'import',
  },
  {
    icon: 'icon-xitongbaobiao',
    name: '系统报表',
    type: 'systemReportForm',
  },
]);

const menuClick = e => {
  const type = e.target.dataset.type;
  if (type) {
    useMenu.value = type;
    emit('menuClick', type);
  }
};
</script>
<style lang="scss" scoped>
.tab-wrap {
  width: 100%;
  display: flex;
  align-items: center;
  height: 42px;
  border-bottom: 2px solid #dcdfe6;
  padding-left: 10px;
  background-color: #fff;
  .icon-font {
    font-size: 14px;
    margin-right: 3px;
  }
  .menu {
    position: relative;
    padding: 5px 4px;
    border-radius: 4px;
    margin: 0 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.3s;
    font-size: 12px;
    &:last-child {
      &::after {
        display: none;
      }
    }
    &::after {
      position: absolute;
      content: '';
      height: 100%;
      width: 1px;
      background-color: #dedede;
      right: -8px;
      top: 0;
    }
    &:hover,
    &.active {
      background: rgba(226, 227, 231, 0.79);
      opacity: 1;
    }
  }
}
</style>
