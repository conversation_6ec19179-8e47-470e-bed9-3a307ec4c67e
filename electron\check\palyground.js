const {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Extend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    zbProjectPrivateCheckList,
    tbProjectPrivateCheckList,
    unitProjectPrivateCheckList,
    qd_code_repeat_bc,
    qd_unit_inconsistent_bc,
    qd_price_inconsistent_bc,
    qd_unit_inconsistent,
    resultTitle
} = require("./checklist");
const {checkContext} = require("./context");
const {qd, de} = require("../enum/StepItemCostLevelConstant");
const EE = require('../../core/ee');
const {ObjectUtils} = require("../utils/ObjectUtils");
const _ = require('lodash');
const ConstantUtil = require("../enum/ConstantUtil");
const {ConstructOperationUtil} = require("../utils/ConstructOperationUtil");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
class CheckPlayground {
    constructor(currentProject, unitSequenceNbrArray) {
        //工程项目 id
        this.constructId = currentProject.sequenceNbr;
        // 项目类型 默认招标
        this.biddingType = currentProject.biddingType;
        //项目数据 全量
        this.currentProject = currentProject;
        //检查项function列表
        this.checkList = {};
        //结果集
        this.resultMap = [];
        //当前需要检查的项 可选择的共有26项
        this.selectCheck = [];
        this.unitProjects = [];
        this.standardQdList = [];

        this.unitSequenceNbrArray = unitSequenceNbrArray;
    }

    selectCheckKey(selectList) {
        if (!Array.isArray(selectList)) throw Error("选择检查项必须为数组");
        if (selectList.length === 0) throw Error("选择检查项不能设置为空");
        this.selectCheck = selectList;
        return this;
    }

    filterCheck(allChecklist) {
        this.selectCheck.forEach(key => {
            this.checkList[key] = allChecklist[key];
        });
    }

    /*
    * 构建执行上下文
    * */
    build() {
        if (this.selectCheck.length === 0) throw Error("未选择检查项");
        if (!this.currentProject) throw Error("未设置项目数据");
        //拿到公共的 checklist 根据biddingType 获取每个项目独有的checklist
        //数据准备阶段
        switch (this.biddingType) {
            //招标
            case 0: {
                this.filterCheck(zbProjectPrivateCheckList());
                break;
            }
            // 投标
            case 1: {
                this.filterCheck(tbProjectPrivateCheckList());
                break;
            }
            // 单位工程项目
            case 2: {
                this.filterCheck(unitProjectPrivateCheckList());
                break;
            }
        }
        this.standardQdList=[];
        this.getAllUnit();
        return this;
    }
    async checkFfExistQdUnitDifferent(){
        let result = await qd_unit_inconsistent.call(this);
        let {count} =result;
        return count>0;
    }
    /*执行验证*/
    async check() {
        let checkList = Object.assign({}, this.checkList);
        for (const key in checkList) {
            let fn = checkList[key];
           await this.getResult(fn);
        }

        for (let i = 0; i < this.resultMap.length; i++) {
            let item = this.resultMap[i];
            if(item.checkType==94){
                this.resultMap[i].checkType=1;
            }
            if(item.checkType==95){
                this.resultMap[i].checkType=2;
            }
            if(item.checkType==96){
                this.resultMap[i].checkType=3;
            }
        }
        checkContext.addResult(this.constructId, this);
    }

    async getResult(fn) {
        let result = await fn.call(this);
        if (result) {
            let {count,error} =result;
            let index = fn.name.toUpperCase();
            let item = await this.buildResult(index);
            this.resultMap.push({...item, childrenList: error?error:result, count: count?count:result.length});
            if(item.checkType==1){
                await this.getResult(qd_code_repeat_bc);
            }
            if(item.checkType==2){
                await this.getResult(qd_unit_inconsistent_bc);
            }
            if(item.checkType==3){
                await this.getResult(qd_price_inconsistent_bc);
            }
        }
    }

    async buildResult(index) {
        let item = CheckListKey[index];
        if (!item) {
            item = ExtendCheckListKey[index];
        }
        return {
            uniqueStr:item.checkType,
            checkType: item.checkType,
            name: resultTitle[item.checkType] ? resultTitle[item.checkType] : item.checkTypeName,
            childrenList: [],
            count: 0
        };
    }

    getAllUnit() {
        // const {singleProjects, unitProject} = this.currentProject;
        // let unitProjects = [];
        // if (singleProjects) {
        //     unitProjects = singleProjects.flatMap(singleProject => singleProject.unitProjects);
        // }
        // if (unitProject) {
        //     unitProjects.push(unitProject);
        // }
        let unitProjects = PricingFileFindUtils.getUnitListByConstructObj(this.currentProject);
        this.unitProjects =  ConvertUtil.deepCopy(unitProjects.filter(item => this.unitSequenceNbrArray.includes(item.sequenceNbr)));
    }
    //获取所有分部
    getAllFb() {
        let itemBillProjectsArr = this.unitProjects.flatMap(unit => {
            let resultArry =[];
            let itemBillProjects = unit.itemBillProjects.filter(item => item.kind == BranchProjectLevelConstant.fb||item.kind == BranchProjectLevelConstant.zfb);
            if(itemBillProjects){
                itemBillProjects =itemBillProjects.map(item => {
                    item.moduleType = "fbfx";
                    item.bizType = "fbfx";
                    return {...item, parent: null, prev: null, next: null, children: null};
                });
                this.settingPublic(itemBillProjects, unit);
                resultArry=resultArry.concat(itemBillProjects);
            }
            return resultArry;
        });
        let measureProjectTablesArr = this.unitProjects.flatMap(unit => {
            let resultArry =[];
            let measureProjectTables = unit.measureProjectTables.filter(item => item.kind == BranchProjectLevelConstant.fb||item.kind == BranchProjectLevelConstant.zfb);
            if(measureProjectTables){
                measureProjectTables =measureProjectTables.map(item => {
                    item.moduleType = "csxm";
                    item.bizType = "csxm";
                    return {...item, parent: null, prev: null, next: null, children: null};
                });
                this.settingPublic(measureProjectTables, unit);
                resultArry = resultArry.concat(measureProjectTables);
            }
            return resultArry;
        });
        return [...itemBillProjectsArr, ...measureProjectTablesArr]
    }
    getAllQD() {
        if (this.standardQdList.length > 0) return this.standardQdList;
        let itemBillProjectsArr=[];
        let measureProjectTablesArr =[];
        for (let i = 0; i < this.unitProjects.length; i++) {
            let unit = this.unitProjects[i];
            let itemBillProjects = unit.itemBillProjects.filter(item => item.kind == qd);
            this.settingPublic(itemBillProjects, unit);
            itemBillProjects =itemBillProjects.map(item => {
                item.bizType = "fbfx";
                return {...item, parent: null, prev: null, next: null, children: null};
            });
            itemBillProjectsArr=itemBillProjectsArr.concat(itemBillProjects);
            let measureProjectTables = unit.measureProjectTables.filter(item => item.kind == qd);
            this.settingPublic(measureProjectTables, unit);
            measureProjectTables =measureProjectTables.map(item => {
                item.bizType = "csxm";
                return {...item, parent: null, prev: null, next: null, children: null};
            });
            measureProjectTablesArr=measureProjectTablesArr.concat(measureProjectTables);
        }
        this.standardQdList = [...itemBillProjectsArr, ...measureProjectTablesArr];
        return   this.standardQdList;
    }

    getAllDE() {
        let itemBillProjectsArr = this.unitProjects.flatMap(unit => {
            let itemBillProjects = unit.itemBillProjects.filter(item => item.kind == de);
            itemBillProjects =itemBillProjects.map(item => {
                item.moduleType = "fbfx";
                item.bizType = "fbfx";
                return {...item, parent: null, prev: null, next: null, children: null};
            });
            this.settingPublic(itemBillProjects, unit);
            return itemBillProjects;
        });
        let measureProjectTablesArr = this.unitProjects.flatMap(unit => {
            let measureProjectTables = unit.measureProjectTables.filter(item => item.kind == de);
            measureProjectTables =measureProjectTables.map(item => {
                item.moduleType = "csxm";
                item.bizType = "csxm";
                return {...item, parent: null, prev: null, next: null, children: null};
            });
            this.settingPublic(measureProjectTables, unit);
            return measureProjectTables;
        });
        return [...itemBillProjectsArr, ...measureProjectTablesArr];
    }

    /**
     * 获取所有单位人材机汇总数据
     * @returns {any[]}
     */
    getAllRrjSummary() {
        let {service} = EE.app;
        let allRrjSummary = new Array();
        for (let i = 0; i < this.unitProjects.length; i++) {
            let unitProject = this.unitProjects[i];
            let unitConstructProjectRcjQuery = unitProject.constructProjectRcjs;
            if (!ObjectUtils.isEmpty(unitConstructProjectRcjQuery)) {

                //筛掉字段为零的
                let filterParent = unitConstructProjectRcjQuery.filter(item=>item.totalNumber !=0 );
                allRrjSummary = allRrjSummary.concat(filterParent);
                // 三个id需要设置
                this.settingPublic(filterParent, unitProject);
                for (let j = 0; j < filterParent.length; j++) {
                    let unitConstructProjectRcjQueryElement = filterParent[j];

                    if(!ObjectUtils.isEmpty(unitConstructProjectRcjQueryElement.rcjDetailsDTOs)){
                        let filter = unitConstructProjectRcjQueryElement.rcjDetailsDTOs.filter(item=>item.totalNumber !=0 );

                        // 三个id需要设置
                        this.settingPublic(filter, unitProject);
                        allRrjSummary = allRrjSummary.concat(filter);
                    }

                }

            }

        }
        return allRrjSummary;
    }

    /**
     * 设置三个id及备注
     * @param obj
     * @param unitProject
     * @returns {*}
     */
    settingPublic(obj, unitProject) {
        const {singleProjects} = this.currentProject;
        if(this.currentProject.biddingType===2 ){
            for (let j = 0; j < obj.length; j++) {
                obj[j].constructId = unitProject.constructId;
                obj[j].spId = unitProject.spId;
                obj[j].upId = unitProject.sequenceNbr;
                obj[j].description = this.currentProject.constructName;

            }
        }else {
            // bug fix:22001 工程项目下面直接右键新建单位后，点击项目自检功能，有报错 单项工程下只有一个单位工程的情况 spId 不存在
            let singlesPath="";
            if(ObjectUtils.isEmpty(this.currentProject.unitProjectArray)){
                 singlesPath = this.generateSingleProjectsPath(unitProject.constructId, unitProject.spId);
            }else {
                singlesPath = this.currentProject.constructName;
            }
            for (let j = 0; j < obj.length; j++) {
                obj[j].constructId = unitProject.constructId;
                obj[j].spId = unitProject.spId;
                obj[j].upId = unitProject.sequenceNbr;
                obj[j].description = singlesPath + '/' + unitProject.upName;

            }
        }

    }

    generateSingleProjectsPath(constructId, singleId){
        let map = ConstructOperationUtil.flatConstructTreeToMapById(constructId);
        let singleArray = [];
        let nodeId = singleId;
        while(true){
            let node = map.get(nodeId);
            if(node.levelType != ConstantUtil.SINGLE_LEVEL_TYPE){
                break;
            }

            singleArray.unshift(node);

            nodeId = node.parentId;
        }

        return singleArray.map(item => item.projectName).join("/");
    }

    /**
     * 获取所有单位的计日工
     */
    getAllDayWorks() {
        let unitProjects = this.unitProjects;
        let dayWorks = [];
        for (let i = 0; i < unitProjects.length; i++) {
            let otherProjectDayWorks = unitProjects[i].otherProjectDayWorks;

            if (!ObjectUtils.isEmpty(otherProjectDayWorks)) {
                // 三个id需要设置
                this.settingPublic(otherProjectDayWorks, unitProjects[i]);

                for (let j = 0; j < otherProjectDayWorks.length; j++) {
                    dayWorks.push(otherProjectDayWorks[j])
                }
            }

        }
        return dayWorks
    }

    /**
     * 获取所有单位的总承包服务费
     */
    getAllProjectServiceCosts() {
        let unitProjects = this.unitProjects;
        let projectServiceCosts = [];
        for (let i = 0; i < unitProjects.length; i++) {
            let otherProjectServiceCosts = unitProjects[i].otherProjectServiceCosts;

            if (!ObjectUtils.isEmpty(otherProjectServiceCosts)) {
                // 三个id需要设置
                this.settingPublic(otherProjectServiceCosts, unitProjects[i]);
                for (let j = 0; j < otherProjectServiceCosts.length; j++) {
                    projectServiceCosts.push(otherProjectServiceCosts[j]);
                }
            }


        }

        return projectServiceCosts;
    }

    //标准清除
    clean() {

    }
}

module.exports = CheckPlayground;
/*demo
   let check = new CheckPlayground(0,{name:"test"}).selectCheckKey([1,2,3,4,5,6,7,8,9]).build();
    await check.check();
*/
