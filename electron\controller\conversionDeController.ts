// @ts-nocheck
import {StandardConvertMod} from "../../electron/enum/ConversionSourceEnum";
import {CommonDto, Controller} from "../../core";

const { ResponseData } = require("../utils/ResponseData");
const { PricingFileFindUtils } = require("../utils/PricingFileFindUtils");

export type BatchOperationalConversionRuleItemDto = {
	agencyCode?: string;
	chapterStatus?: boolean;
	dataFormat?: number;
	deCode?: string;
	ruleInfo?: string;
	clpb?: any;
	defaultValue: number | string;
	defaultValueMax?: string;
	defaultValueMin?: string;
	deId?: string;
	deName?: string;
	description?: string;
	extend1?: string;
	extend2?: string;
	extend3?: string;
	fbFxDeId: string;
	fileDetailsId?: string;
	height?: number;
	kind: string;
	libraryCode?: string;
	math: string;
	nowChange?: boolean;
	old_selected?: boolean;
	productCode?: string;
	rcjId?: string;
	recDate?: string;
	recStatus?: string;
	recUserCode?: string;
	relation: string;
	relationCode?: string;
	relationDeCode?: string;
	relationDeId?: string;
	relationGroupCnt?: number;
	relationGroupCode?: string;
	relationGroupId?: unknown;
	relationGroupName?: string;
	relationGroupRule?: string;
	rowSpan?: number;
	ruleRange?: string;
	selected?: number;
	selectedRule: string;
	sequenceNbr: string;
	sortNo?: unknown;
	topGroupType?: string;
	type: string;
	index: number;
	// 是否是统一换算规则
	isUniteRule?: boolean;
	currentRcjCode?: string;
	currentRcjLibraryCode?: string;
};
type BatchOperationalConversionRuleDto = CommonDto & {
	rules: BatchOperationalConversionRuleItemDto[];
};
// 标准换算
class ConversionDeController extends Controller {
	constructor(ctx: unknown) {
		super(ctx);
	}
	static toString() {
		return "[class ConversionDeController]";
	}

	// 清除换算记录
	async cleanRules(dto: CommonDto) {
		await this.service.conversionInfoService.clearConversionInfo(dto.constructId, dto.singleId, dto.unitId, dto.fbFxDeId);
		return ResponseData.success(true);
	}

	// 清除换算记录
	async cleanRulesOld(dto: CommonDto) {
		await this.service.conversionDeProcess.cleanRules(
			dto.standardDeId,
			dto.fbFxDeId,
			dto.constructId,
			dto.singleId,
			dto.unitId
		);
		await this.service.autoCostMathService.autoCostMath({
			constructId: dto.constructId,
			singleId: dto.singleId,
			unitId: dto.unitId
		});
		//重新计算费用汇总
		await this.service.unitCostCodePriceService.countCostCodePrice({
			constructId: dto.constructId,
			singleId: dto.singleId,
			unitId: dto.unitId
		});
		await this.service.management.sycnTrigger("unitDeChange");
		await this.service.management.trigger("itemChange");
		return ResponseData.success(true);
	}

	/**
	 * 获取标准换算数据
	 * @param dto
	 */
	async conversionRuleList(dto: CommonDto) {
		const result = await this.service.conversionDeProcess.conversionRuleList(dto);
		return ResponseData.success(result);
	}

	/**
	 * 更新换算信息
	 * @param dto
	 */
	async updateDeConversionInfo(dto: CommonDto) {
		const { fbFxDeId, constructId, singleId, unitId ,selectId,operateAction} = dto;
		const result = await this.service.conversionInfoService.updateDeConversionInfo(
			constructId,
			singleId,
			unitId,
			fbFxDeId,
			selectId,	//当前行id
			operateAction	//up上移,down下移,delete删除
		);
		return ResponseData.success(result);
	}


	/**
	 * 获取换算信息数据
	 * @param dto
	 */
	getDefDonversion(dto: CommonDto & { deId: string }) {
		const res = this.service.conversionDeService.getDefDonversion(dto);
		return ResponseData.success(res);
	}

	/**
	 * 标准换算右侧统一系数处理
	 * @param dto
	 */
	async updateDefDonversion(
		dto: CommonDto & { deId: string; donversions: string }
	) {
		const { constructId, singleId, unitId, deId, donversions } = dto;
		await this.service.conversionDeService.upDateDefault(
			constructId,
			singleId,
			unitId,
			deId,
			donversions
		);
		await this.service.management.sycnTrigger("unitDeChange");
		await this.service.management.trigger("itemChange");
		return ResponseData.success(true);
	}

	/**
	 * 标准换算 数据更新操作
	 * @param dto
	 */
	async batchOperationalConversionRule(dto: BatchOperationalConversionRuleDto) {
		// TODO 先kind1 再 kind3 再取消kind1  kind3失效
		const { fbFxDeId, constructId, singleId, unitId, rules } = dto;
		await this.service.conversionDeService.conversionRule(
			constructId,
			singleId,
			unitId,
			fbFxDeId,
			dto.rules
		);
		// TODO 人材机（合计数量及合价） 单价构成计算 费用代码计算 费用汇总数据
		await Promise.all([
			this.service.management.sycnTrigger("unitDeChange"),
			this.service.management.trigger("itemChange"),
		]);
		return ResponseData.success(true);
	}

	// 定额标准换算勾选
	async operationalConversionRule(
		dto: CommonDto & { clpb: any; baseRuleDetails: any }
	) {
		const { fbFxDeId, baseRuleDetails, constructId, singleId, unitId, clpb } =
			dto;
		baseRuleDetails.clpb = clpb; //材料配比
		const result = await this.service.conversionDeService.conversionRule(
			constructId,
			singleId,
			unitId,
			fbFxDeId,
			baseRuleDetails
		);
		await this.service.management.sycnTrigger("unitDeChange");
		await this.service.management.trigger("itemChange");
		return ResponseData.success(result);
	}

	/**
	 * 换算信息列表数据
	 * @param dto
	 */
	conversionInfoList(dto: CommonDto) {
		const { fbFxDeId, constructId, singleId, unitId } = dto;
		const result = this.service.conversionInfoService.getDeConversionInfo(
			constructId,
			singleId,
			unitId,
			fbFxDeId
		);
		return ResponseData.success(result);
	}

	getGroupNames(dto: { libraryCode: string }) {
		const { libraryCode,constructId, singleId, unitId } = dto;
		const res = this.service.conversionDeProcess.getGroupNames(libraryCode,constructId, singleId, unitId);
		return ResponseData.success(res);
	}

	getGroupDetail(dto: { groupName: string; libraryCode: string }) {
		const { groupName, libraryCode ,constructId, singleId, unitId} = dto;
		const res = this.service.conversionDeProcess.getGroupDetail(
			groupName,
			libraryCode,
			constructId,
			singleId,
			unitId
		);
		return ResponseData.success(res);
	}

	// 切换当前换算标准 是默认值计算 还是 当前输入值计算
	async switchConversionMod(dto: {
		deId: string;
		standardConvertMod: StandardConvertMod;
	}) {
		const res = await this.service.conversionDeService.switchConversionMod(dto);
		return ResponseData.success(res);
	}

	// 切换主材换算模式 参与或不参与换算
	async switchConversionMainMatMod(dto: { deId: string }) {
		const res =
			await this.service.conversionDeService.switchConversionMainMatMod(dto);
		return ResponseData.success(res);
	}
}
module.exports = ConversionDeController;
