<!--
 * @@Descripttion:
 * @Author: wangru
 * @Date: 2023-06-21 10:53:30
 * @LastEditors: wangru
 * @LastEditTime: 2024-10-23 17:33:10
-->
<template>
  <vxe-modal
    v-bind="getBindValue"
    :before-hide-method="beforeHideMethod"
  >
    <template
      v-if="slots.header"
      #header
    >
      <slot name="header"></slot>
    </template>
    <template
      v-if="slots.corner"
      #corner
    >
      <slot name="corner"></slot>
    </template>
    <a-spin
      :spinning="loadingModal"
      :tip="loadingTip"
      wrapperClassName="spin-noMask dialog-modal"
    >
      <!-- :show-zoom="props.maxOrMin" -->

      <template
        v-if="slots.default"
        #default
      >
        <slot name="default"></slot>
      </template>

      <template
        v-if="slots.footer"
        #footer
      >
        <slot name="footer"></slot>
      </template>
    </a-spin>
  </vxe-modal>
</template>
<script>
export default {
  name: 'commonModal',
};
</script>
<script setup>
import { propTypes } from '@/utils/propTypes';
import { computed, useAttrs, useSlots, ref, watchEffect } from 'vue';
const slots = useSlots();

const props = defineProps({
  id: propTypes.string.def('model_1'),
  modelValue: propTypes.bool.def(false),
  fullscreen: propTypes.bool.def(false),
  loading: propTypes.bool.def(false),
  title: propTypes.string.def('弹窗'),
  width: propTypes.string.def('800'),
  isNoClose: propTypes.bool.def(false),
  position: propTypes.object.def({
    top: '10vh',
  }),
  loadingModal: propTypes.bool.def(false),
  loadingTip: propTypes.string.def('数据加载中,请稍后...'),
  mask: propTypes.bool.def(true), //遮罩层是否展示
  lockView: propTypes.bool.def(true), // 是否锁住页面，不允许窗口之外的任何操作
  lockScroll: propTypes.bool.def(true), // 是否锁住滚动条，不允许页面滚动
  // maxOrMin: propTypes.bool.def(false), //是否有放大缩小窗口功能
  destroyOnClose: propTypes.bool.def(false), // 是否销毁弹框内容
});

const getBindValue = computed(() => {
  const attrs = useAttrs();
  const obj = { ...attrs, ...props };
  return obj;
});
const beforeHideMethod = () => {
  console.log('beforeHideMethod', props.isNoClose);
  if (props.isNoClose) {
    return new Error();
  } else {
    emits('update:modelValue', false);
  }
};
const emits = defineEmits(['update:modelValue']);
const close = () => {
  emits('update:modelValue', false);
};
</script>

<style lang="scss"></style>
