<!--
 * @Descripttion: 设置汇总范围
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: wangru
 * @LastEditTime: 2024-12-23 10:10:55
-->
<template>
  <common-modal
    className="dialog-comm setAggreScope-dialog"
    @close="cancel()"
    v-model:modelValue="dialogVisible"
    title="设置汇总范围"
    width="550px"
    height="550px"
    :mask="true"
    show-zoom
    resize
    :lock-view="false"
    destroy-on-close
    :loading="loading"
    :loading-config="{
      text: '正在加载中...',
    }"
  >
    <div class="group-content-wrap">
      <vxe-table
        ref="vexTable"
        :column-config="{ resizable: true }"
        :data="TableData"
        height="80%"
        :row-config="{
            isCurrent: true,
            keyField: 'id',
          }"
        :scroll-y="{ scrollToTopOnChange: true, enabled: true, gt: 20 }"
        :tree-config="{
            transform: true,
            rowField: 'id',
            parentField: 'parentId',
            line: true, 
            expandAll: true,
            iconOpen: 'vxe-icon-square-minus',
            iconClose: 'vxe-icon-square-plus'
          }"
        :show-overflow="true"
        :checkbox-config="{
          checkStrictly:false,
          checkField: 'merge',
        }"
      >
        <vxe-column
          field="sort"
          width="40"
          title=" "
        >
          <template #default="{ row, $rowIndex }">
            {{ $rowIndex + 1 }}
          </template>
        </vxe-column>
        <vxe-column
          field="name"
          min-width="300"
          title="工程名称"
          align="left"
          tree-node
        >
          <template #default="{ row, rowIndex }">
            <span style="margin-left:8px">{{ row.name }}</span>
          </template>
        </vxe-column>
        <vxe-column
          field="merge"
          min-width="60"
          title="调整"
          :cell-render="{}"
        >
          <template #default="{ row, rowIndex }">
            <a-checkbox
              v-model:checked="row.merge"
              :indeterminate="recursionFun(row)"
              @change="handleClearMergeMer(row)"
            >
            </a-checkbox>
            <!-- <vxe-checkbox
              v-model="row.merge"
              name="调整"
              @change="handleClearMergeMer(row, rowIndex)"
              :indeterminate="row.children?.some(a=>a.merge)&&!row.children?.every(a=>a.merge)"
            ></vxe-checkbox> -->
          </template>
        </vxe-column>
      </vxe-table>

      <p class="footer-btnList">
        <a-button
          :disabled="!TableData.find(a => a.merge && a.levelType === 3)"
          v-for="i in btnList"
          @click="btnFun(i)"
          size="small"
        >{{i.label}}</a-button>
      </p>
      <p class="footer-box">
        <a-button
          type="primary"
          ghost
          @click="cancel()"
        >取消</a-button>
        <a-button
          type="primary"
          style="margin-left: 14px"
          @click="handleOk()"
        >确定</a-button>
      </p>

    </div>
  </common-modal>
</template>
<script setup>
import { ref, nextTick, onMounted, computed, reactive } from 'vue';
import csProject from '@/api/csProject';
import api from '@/api/projectDetail.js';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import { useRoute } from 'vue-router';
import { projectDetailStore } from '@/store/projectDetail.js';
import { message } from 'ant-design-vue';
import { constructLevelTreeStructureList } from '@/api/csProject';
const route = useRoute();
const emits = defineEmits(['refresh']);
const props = defineProps(['unitIdList', 'isFirstOpen']);
let dialogVisible = ref(false);
const store = projectDetailStore();
let TableData = ref([]);
const vexTable = ref(null);
let loading = ref(false);
const btnList = reactive([
  {
    label: '选择同名工程',
    id: 'selectSameName',
  },
  {
    label: '取消同名工程',
    id: 'cancelSameName',
  },
  {
    label: '选择同专业工程',
    id: 'selectSameMajor',
  },
  {
    label: '取消同专业工程',
    id: 'cancelSameMajor',
  },
]);
const btnFun = item => {
  let selectList = TableData.value.filter(a => a.merge && a.levelType === 3);
  let target = vexTable.value.getCurrentRecord(); //当前选中目标
  //选中按照勾选的selectList   取消按照鼠标target
  if (selectList?.length === 0) {
    return;
  }
  switch (item.id) {
    case 'selectSameName':
      1;
      //选中同名
      let nameList = selectList.map(a => {
        return a.name;
      });
      TableData.value.map(a => {
        if (nameList.includes(a.name) && !a.merge) {
          a.merge = true;
          handleClearMergeMer(a);
        }
      });
      break;
    case 'cancelSameName':
      if (
        !TableData.value.find(
          a => target.name === a.name && target.id !== a.id && a.merge
        )
      )
        return;
      TableData.value.map(a => {
        if (target.name === a.name && a.merge) {
          a.merge = false;
          handleClearMergeMer(a);
        }
      });
      //取消同名
      break;
    case 'selectSameMajor':
      //选择同专业
      let majorList = selectList.map(a => {
        return a.constructMajorType;
      });
      TableData.value.map(a => {
        if (majorList.includes(a.constructMajorType) && !a.merge) {
          a.merge = true;
          handleClearMergeMer(a);
        }
      });
      break;
    case 'cancelSameMajor':
      //取消同专业
      if (
        !TableData.value.find(
          a =>
            target.constructMajorType === a.constructMajorType &&
            target.id !== a.id &&
            a.merge
        )
      )
        return;
      TableData.value.map(a => {
        if (target.constructMajorType === a.constructMajorType && a.merge) {
          a.merge = false;
          handleClearMergeMer(a);
        }
      });
      break;
  }
};
const cancel = (refresh = false) => {
  dialogVisible.value = false;
};

const open = k => {
  dialogVisible.value = true;
  getTableList();
};
let initTreeData = ref([]);
const getTableList = () => {
  // constructLevelTreeStructureList(route.query.constructSequenceNbr).then(
  //   res => {
  console.log('设置汇总范围-treeData.value', store.currentTreeInfo);
  // debugger;
  initTreeData.value = xeUtils.toTreeArray([{ ...store.currentTreeInfo }]);
  TableData.value = xeUtils.toTreeArray([{ ...store.currentTreeInfo }]);
  TableData.value.map(a => {
    if (
      !props.isFirstOpen &&
      props.unitIdList.length > 0 &&
      props.unitIdList.includes(a.id)
    ) {
      //不是第一次打开的话就按照历史记录展示
      a.merge = true;
      handleClearMergeMer(a);
    }
    if (props.isFirstOpen && props.unitIdList.length === 0) {
      //第一次打开每一项设置为选中
      a.merge = true;
      handleClearMergeMer(a);
    }
  });
  nextTick(() => {
    vexTable.value.setAllTreeExpand(true);
  });
  //   }
  // );
};
const recursionFun = row => {
  //设置半选状态--子级非全选且下级有选中状态
  let list = row?.children?.length > 0 ? xeUtils.toTreeArray([row]) : [];
  let flag = list.some(a => a.id !== row.id && a.merge);
  // console.log('recursionFun', row, list, flag);
  return row.levelType === 3 ? false : !list.every(a => a.merge) && flag;
};
const setChildMergeValue = target => {
  //设置子级选中状态
  if (target?.children?.length > 0 && target.levelType < 3) {
    target.children.map(a => {
      a.merge = target.merge;
      setChildMergeValue(a);
    });
  }
};
const setParentMergeValue = row => {
  //设置父级选中状态
  if (row.parentId) {
    let parent = TableData.value.find(a => a.id === row.parentId);
    if (parent?.children.every(a => a.merge)) {
      parent.merge = true;
    } else if (parent) {
      parent.merge = false;
    }
    parent ? setParentMergeValue(parent) : '';
  }
};
const handleClearMergeMer = row => {
  console.log(TableData.value, row);
  setChildMergeValue(row);
  // debugger;
  setParentMergeValue(row);

  // if (row.mergeTarget === null && !row.cyhb) {
  //   row.mergeTarget = false;
  // }
};

// 确认
const handleOk = () => {
  let selectList = TableData.value.filter(a => a.merge && a.levelType === 3);
  let unitList = selectList.map(a => {
    return a.id;
  });
  let allUnit = TableData.value.filter(a => a.levelType === 3);
  let isChange = selectList.length !== allUnit.length;
  console.log('选中行', selectList, unitList);
  // if (unitList?.length > 0) {
  emits('refresh', unitList, isChange);
  // }
};
defineExpose({
  open,
  cancel,
});
</script>

<style lang="scss">
.setAggreScope-dialog {
  //禁用浏览器默认选中
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  .vxe-modal--box {
    overflow: hidden !important;
  }
  .vxe-modal--content {
    padding: 17px 22px !important;
  }
  .content-table {
    height: 80%;
    padding: 0 0px;
  }
  .group-content-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .footer-btnList {
    display: flex;
    margin-top: 10px;
    justify-content: space-between;
  }
  .footer-box {
    width: 150px;
    margin-left: calc(100% - 150px);
  }
  .vxe-table .index-bg {
    background-color: #ffffff;
  }
  .vxe-table--body {
    border-collapse: collapse;
    // border: 2px solid #ffffff;
  }
}
</style>
