/*
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2024-01-03 15:38:07
 * @LastEditors: renmingming
 * @LastEditTime: 2024-07-12 16:51:33
 */
import { projectDetailStore } from '@/store/projectDetail';
import { nextTick, ref } from 'vue';

export const useReversePosition = () => {
  let projectStore = projectDetailStore();

  const headerMenuJump = () => {
    projectStore.headerMenu.triggerMenu();
  }
  // 左侧树跳转
  const treeJump = id => {
    //需要区分单位工程
    let row;
    if (projectStore.projectType !== 'DW') {
      console.log('zzzzzzzzzzzzzzz', id, projectStore.projectTree)
      row = projectStore.projectTree.find(item => item.id === id);
      setTimeout(() => {
        projectStore.asideTreeRef.setCheckRow(row);
      },900)
    } else {
      row = projectStore.currentTreeInfo; //单位工程左侧没有项目树
      // projectStore.SET_CURRENT_TREE_INFO(row);
    }
  };

  const tabMenuJump = (value, callback = null) => {
    projectStore.tabMenuRef.tabChangeByValue(value, callback);
  };

  const asideMenuJump = value => {
    projectStore.asideMenuRef.currentSelectedMenu(value);
  };

  const detailMaterialJump = value => {
    console.log('人材机明细ref', projectStore.materialRef);
    if (!projectStore.materialRef) {
      setTimeout(() => {
        detailMaterialJump(value);
      }, 800);
    }
    projectStore.materialRef?.currentMaterialInfo(value);
  };

  const componentTablePosition = (id, tabMenuName, childrenTabName) => {
    // 分部分项定位方法posRow
    console.log('positionFun1', projectStore.subItemProjectAutoPosition);
    console.log('tabMenuName', tabMenuName, projectStore.childComponentRef);
    let posFn = null;
    switch (tabMenuName) {
      case '分部分项':
        projectStore.positionId = id;
        projectStore.SET_POSITION_ID(id);
        console.log('projectStore.positionId', projectStore.positionId);
        posFn = projectStore.subItemProjectAutoPosition;
        break;
      case '措施项目':
        projectStore.positionId = id;
        projectStore.SET_POSITION_ID(id);
        posFn = projectStore.measuresItemProjectAutoPosition;
        break;
      case '其他项目':
        posFn = projectStore.otherProjectAutoPosition;
        break;
      case '人材机汇总':
        posFn = projectStore.summaryProjectAutoPosition;
    }
    if (
      childrenTabName &&
      (tabMenuName === '分部分项' || tabMenuName === '措施项目')
    ) {
      detailMaterialJump(childrenTabName);
    }
    if (
      tabMenuName === '其他项目' &&
      (childrenTabName === 'jrg' || childrenTabName === 'zcbfwf')
    ) {
      console.log('计日工数据');
      asideMenuJump(childrenTabName === 'jrg' ? '计日工' : '总承包服务费');
    }
    if (childrenTabName === 'jrg') {
      posFn = projectStore.dailyWorkProjectAutoPosition;
    } else if (childrenTabName === 'zcbfwf') {
      posFn = projectStore.serviceProjectAutoPosition;
    }

    console.log('posFn', posFn);
    if (!posFn && tabMenuName !== '分部分项' && tabMenuName !== '措施项目') {
      setTimeout(() => {
        componentTablePosition(id, tabMenuName, childrenTabName);
      }, 800);
      return;
    }
    setTimeout(() => {
      posFn?.posRow(id);
    }, 500)
  };

  const linkagePosition = ({ treeId, tabMenuName, rowId, childrenTabName }) => {
    // projectStore.subItemProjectAutoPosition = null; // 分部自动切换方法
    // projectStore.measuresItemProjectAutoPosition = null; // 措施自动
    // projectStore.dailyWorkProjectAutoPosition = null; // 计日工自动
    // projectStore.serviceProjectAutoPosition = null; // 总承包服务费自动
    // projectStore.summaryProjectAutoPosition = null; // 人材机汇总自动
    projectStore.isAutoPosition = true;
    console.log('1111111111111111222222222',treeId, tabMenuName, rowId, childrenTabName)
    headerMenuJump();
    treeJump(treeId);
    setTimeout(() => {
      tabMenuJump(tabMenuName, () => {
        setTimeout(() => {
          componentTablePosition(rowId, tabMenuName, childrenTabName);
        }, 200);
      });
    }, 1000);
  };

  const dataSearchPosition = ({ treeId, tabMenuName, type }) => {
    treeJump(treeId);
    setTimeout(() => {
      tabMenuJump(tabMenuName, () => {
        setTimeout(() => {
          componentTableSearch(type);
        }, 200);
      });
    }, 500);
  };

  const componentTableSearch = type => {
    projectStore.SET_COMBINED_SEARCH_LIST(type);
  };

  return {
    treeJump,
    tabMenuJump,
    componentTablePosition,
    linkagePosition,
    dataSearchPosition,
    componentTableSearch,
  };
};
