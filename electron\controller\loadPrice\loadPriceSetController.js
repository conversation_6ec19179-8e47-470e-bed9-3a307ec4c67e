
const {ResponseData} = require("../../utils/ResponseData");
const {Controller} = require("../../../core");

/**
 * 载价条件设置
 */
class LoadPriceSetController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 获取载价设置初始化数据
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async queryLoadPriceAreaDate(arg){
        const result = await this.service.loadPriceSetService.queryLoadPriceAreaDate(arg);
        return ResponseData.success(result);
    }

    /**
     * 查询载价报告 -扇形图
     * @returns {Promise<ResponseData>}
     */
    async queryLoadPriceReportRcj(args){

        let result = await this.service.loadPriceSetService.queryLoadPriceReportRcj(args);

        return ResponseData.success(result);
    }


    /**
     * 查询载价报告 -柱状图
     * @returns {Promise<ResponseData>}
     */
    async queryLoadPriceReportTarget(args){

        let result = await this.service.loadPriceSetService.queryLoadPriceReportTarget(args);

        return ResponseData.success(result);
    }

    /**
     * 鼠标右键清除载价
     * @param args
     * @returns {Promise<ResponseData>}
     */
    /*async clearLoadPrice(args){
        //type 1 = 工程项目  2 单位工程
        let {constructId, singleId,unitId,type,sequenceNbrList} = args;

        return ResponseData.success(true);
    }*/

    /**
     * 点击批量载价
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async loadingPrice(args) {
        let result = await this.service.loadPriceSetService.loadingPrice(args);
        if (result.length > 0) {
            return ResponseData.success(result);
        }else {  //没有数据  返回fail
            return ResponseData.fail(result);
        }
    }

    /**
     * 返回载价编辑弹窗数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async loadPriceEditPage(args) {
        const result = await this.service.loadPriceSetService.loadPriceEditPage(args);
        return ResponseData.success(result);
    }

    /**
     * 双击价格选择弹窗的数据行 更新待载价格
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updateLoadPrice(args) {
        await this.service.loadPriceSetService.updateLoadPrice(args);
        return ResponseData.success(true);
    }

    /**
     * 取消勾选及类型 更新载入前后的人材机总价 及执行载价的条数
     * @param args
     * @returns {Promise<void>}
     */
    async cancelCheckOrType(args) {
        await this.service.loadPriceSetService.cancelCheckOrType(args);
        return ResponseData.success(true);
    }

    /**
     * 价格选择弹窗接口数据查询
     * @param args
     * @returns {Promise<void>}
     */
    async priceAvailableChoice(args) {
        let {constructId, singleId,unitId,type,rcjId} = args;

    }

    /**
     * 批量应用接口更新市场价、价格来源
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async applyLoadingPriceInRcjDetails(args) {
        await this.service.loadPriceSetService.applyLoadingPriceInRcjDetails(args);
        return ResponseData.success(true);
    }

    /**
     * 查询载价报告明细列表
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async loadPriceList(args){

        let list =  await this.service.loadPriceSetService.loadPriceList(args);

        return ResponseData.success(list);

    }


    /**
     * 查询载价状态
     * @param args
     * @return {Promise<ResponseData>}
     */
    async loadPriceStatus(args){

        const result = await this.service.loadPriceSetService.loadPriceStatus(args);
        return ResponseData.success(result);

    }


    /**
     * 智能询价
     * @param args
     * @return {Promise<ResponseData>}
     */
    async smartLoadPrice(args){

        const result = await this.service.loadPriceSetService.smartLoadPrice(args);
        return ResponseData.success(result);

    }


    /**
     * 智能询价后应用
     * @param args
     * @return {Promise<ResponseData>}
     */
    async smartLoadPriceUse(args){

        const result = await this.service.loadPriceSetService.smartLoadPriceUse(args);

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");
        return ResponseData.success(result);

    }

    /**
     * 鼠标右键清除载价
     * @param args
     * @return {Promise<ResponseData>}
     */
    async clearLoadPriceUse(args){

        const result = await this.service.loadPriceSetService.clearLoadPriceUse(args);


        return ResponseData.success(result);

    }


    /**
     * 鼠标右键查询人材机关联定额
     * @param args
     * @return {Promise<ResponseData>}
     */
    async getRcjDe(args){

        const result = await this.service.loadPriceSetService.getRcjDe(args);

        return ResponseData.success(result);

    }


    /**
     * 鼠标右键查询人材机关联定额树结构
     * @param args
     * @return {Promise<ResponseData>}
     */
    async getConstructIdTree(args){

        const result = await this.service.loadPriceSetService.getConstructIdTree(args);

        return ResponseData.success(result);

    }


    /**
     * 鼠标右键 查询定额是否存在 以及 是分部分项还是措施项目
     * @param args
     * @return {Promise<ResponseData>}
     */
    async existDe(args){

        const result = await this.service.loadPriceSetService.existDe(args);

        return ResponseData.success(result);

    }


    /**
     * 首页获取算房宝首页材价动态
     * @param args
     * @return {Promise<ResponseData>}
     */
    async getSfbPeriodical(){

        const result = await this.service.loadPriceSetService.getSfbPeriodical();

        return ResponseData.success(result);

    }


    /**
     * 获取逐条载价 人材机类型树
     * @param
     * @return {Promise<ResponseData>}
     */
    async getRcjTypeTree(){

        const result = await this.service.loadPriceSetService.getRcjTypeTree();

        return ResponseData.success(result);

    }


    /**
     * 逐条载价 查询人材机
     * @returns {Promise<void>}
     */
    async getZtzjRcj(args){
        const result = await this.service.loadPriceSetService.getZtzjRcj(args);

        return ResponseData.success(result);
    }

    /**
     * 逐条载价 查询人材机(载价平均价)
     * @returns {Promise<void>}
     */
    async getZtzjRcjAvg(args){
        const result = await this.service.loadPriceSetService.getZtzjRcjAvg(args);

        return ResponseData.success(result);
    }


    /**
     * 逐条载价 应用
     * @returns {Promise<void>}
     */
    async useZtzjRcj(args){
        const result = await this.service.loadPriceSetService.useZtzjRcj(args);

        return ResponseData.success(result);
    }


    /**
     * 获取信息价 地区列表 城市下面带着地区
     * @returns {Promise<void>}
     */
    async getDimRegion(){
        const result = await this.service.loadPriceSetService.getDimRegion();

        return ResponseData.success(result);
    }

    /**
     * 载价平均价
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async loadingAvgPrice(args) {
        let result = await this.service.loadPriceSetService.loadingAvgPrice(args);
        if (result.length > 0) {
            return ResponseData.success(result);
        }else {  //没有数据  返回fail
            return ResponseData.fail(result);
        }
    }

    /**
     * 保存加权规则
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async saveAvgRule(args){
        const result = await this.service.loadPriceSetService.saveAvgRule(args);

        return ResponseData.success(result);
    }

    /**
     * 获取加权规则
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getAvgRule(args){
        const result = await this.service.loadPriceSetService.getAvgRule(args);

        return ResponseData.success(result);
    }

    /**
     * 获取项目 应用的载价地区 和期刊设置
     * @returns {Promise<void>}
     */
    async getLoadPriceCache(args){
        const result = await this.service.loadPriceSetService.getLoadPriceCache(args);

        return ResponseData.success(result);

    }

    /**
     * 删除分部分项、措施项目中空数据
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async deleteEmptyDate(args){
        const result = await this.service.loadPriceSetService.deleteEmptyDate(args);
        return ResponseData.success(result);
    }
}

LoadPriceSetController.toString = () => '[class LoadPriceSetController]';
module.exports = LoadPriceSetController;
