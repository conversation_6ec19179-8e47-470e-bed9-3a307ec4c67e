<!--
 * @@Descripttion:
 * @Author: wangru
 * @Date: 2024-04-07 10:31:58
 * @LastEditors: wangru
 * @LastEditTime: 2025-04-08 15:55:27
-->
<template>
  <div class="table-content">
    <p class="title"><span class="text">进项税额明细</span></p>
    <div class="content">
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true, isCurrent: true }"
        :tree-config="{
          childrenField: 'children',
          transform: false,
        }"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'cell',
          beforeEditMethod({ row, column }) {
            if (column.field === 'rate' && !row['isEditRate']) {
              //除税系数没有值不可以修改
              return false;
            }
            return true;
          },
        }"
        @edit-closed="editClosedEvent"
        @cell-click="useCellClickEvent"
        :cell-class-name="selectedClassName"
        class="table-edit-common table-content"
        :data="tableData"
        height="auto"
        keep-source
        ref="jxseTable"
      >
        <vxe-column
          field=""
          width="50"
          title=""
          fixed="left"
        >
          <template #default="{ row, $rowIndex }">
            {{ $rowIndex + 1 }}
          </template>
        </vxe-column>
        <vxe-column
          field="dispNo"
          width="60"
          title="序号"
          tree-node
        ></vxe-column>
        <vxe-column
          field="name"
          min-width="150"
          title="名称"
        ></vxe-column>
        <vxe-column
          field="calculateFormula"
          min-width="180"
          title="计算基数"
        ></vxe-column>
        <vxe-column
          field="rate"
          min-width="80"
          title="除税系数(%)"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            {{ row.rate }}
          </template>
          <template #edit="{ row }">
            <vxe-input
              :clearable="false"
              v-model.trim="row.rate"
              :maxlength="10"
              type="text"
              @blur="(row.rate = pureNumber(row.rate, 4)), clear()"
            >
            </vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          field="price"
          min-width="70"
          title="金额"
        ></vxe-column>
        <vxe-column
          field="remark"
          min-width="180"
          title="备注"
          :edit-render="{ autofocus: '.vxe-input--inner' }"
        >
          <template #default="{ row }">
            <span>{{ row.remark }}</span>
          </template>
          <template #edit="{ row }">
            <vxe-input
              :clearable="false"
              :maxlength="2000"
              v-model.trim="row.remark"
              type="text"
              @blur="clear()"
            ></vxe-input>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import feePro from '@/api/feePro';
import jieSuanApi from '@/api/jieSuanApi';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
import { pureNumber } from '@/utils/index';
import { message } from 'ant-design-vue';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const emits = defineEmits(['getUpList']);
const store = projectDetailStore();
let tableData = ref([]);
let jxseTable = ref();
onMounted(() => {
  getTableData();
});
watch(
  () => [store.tabSelectName, store.currentTreeInfo],
  ([val, oldVal], [newY, oldy]) => {
    if (
      store.tabSelectName === '费用汇总' &&
      store.currentTreeInfo.levelType === 3
    ) {
      getTableData();
    }
  }
);
const getTableData = async () => {
  const formdata = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  let isDifference = false;
  if (store.type === 'jieSuan') {
    jieSuanApi.findUnitProjectById(formdata).then(res => {
      isDifference = res.isDifference;
    });
  }
  console.log('---------进项税额明细弹框传参', formdata);
  await feePro.getInputTaxDetails(formdata).then(res => {
    if (res.status === 200) {
      res.result?.map(
        item => (item.isEditRate = item.rate > 0 || item.rate == 0)
      );
      tableData.value = res.result;
      if (
        store.type === 'jiesuan' &&
        !isDifference &&
        !store.currentTreeInfo.originalFlag
      ) {
        tableData.value = tableData.value.filter((item, index) => {
          return index <= 13;
        });
      }
      console.log('findUnitProjectById----进项税额明细弹框结果', isDifference);
      console.log('---------进项税额明细弹框结果', res.result);
      // jxseTable.value.reloadData(tableData.value);
    }
  });
};
const clear = () => {
  //清除编辑状态
  const $table = jxseTable.value;
  $table.clearEdit();
};
const editClosedEvent = ({ row, column }) => {
  const $table = jxseTable.value;
  const field = column.field;
  let value = row[field];
  // 判断单元格值没有修改
  if (!$table.isUpdateByRow(row, field)) {
    return;
  }
  if (field === 'rate' && row[field] !== '') {
    row[field] = parseFloat(value);
  }
  console.log('进项税额明细', row, field);
  upDate(row, field);
};
const upDate = (row, field) => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
    sequenceNbr: row.sequenceNbr,
  };
  apiData[field] = row[field];
  console.log('进项税额明细修改接口传参', apiData);
  feePro.updateInputTaxDetails(apiData).then(res => {
    if (res.status === 200) {
      message.success('修改成功');
      getTableData();
      emits('getUpList');
    }
  });
};
</script>
<style lang="scss" scoped>
.table-content {
  .title {
    width: 100%;
    background-color: #e7e7e7;
    height: 35px;
    text-align: left;
    margin: 2px 0 2px;
    .text {
      display: inline-block;
      width: 128px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      background-color: #f8fbff;
      border-top: 2px solid #4786ff;
    }
  }
  .content {
    width: 85%;
    height: calc(100% - 45px);
  }
}
</style>
