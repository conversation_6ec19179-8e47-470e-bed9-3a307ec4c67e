<template>
  <div class="common-aside" ref="commonAside">
    <a-tree
      class="common-aside-tree"
      :class="{ leftIsExpand: !leftIsExpand }"
      :draggable="!clickInsideContent && !isDraggable"
      block-node
      :tree-data="gData"
      v-model:selectedKeys="selectedKeys"
      v-model:expandedKeys="expandList"
      :fieldNames="{ title: 'name', key: 'id', children: 'children' }"
      @select="treeSelect"
      @dragenter="onDragEnter"
      @drop="onDrop"
      @dragover="onDragOver"
      @dragstart="dragstart"
      @rightClick="rightClick"
    >
      <template #switcherIcon="row">
        <div style="display: flex">
          <caret-down-outlined :class="row.switcherCls" />
          <!-- <icon-font style="font-size: 14px" :type="getIconType(row)" /> -->
        </div>
      </template>
      <template #title="item">
        <a-dropdown v-if="!item.isNameEdit" :trigger="['contextmenu']">
          <div>
            <span v-if="leftIsExpand" @dblclick="doubleClick(item)">
              <icon-font style="font-size: 14px" :type="getIconType(item)" />
              {{ item.name }}
              <span v-if="store.$state.type === 'yssh'">
                <span v-if="item.type === 'delete'">（送审）</span>
                <span v-if="item.type === 'add'">（审定）</span>
              </span>
            </span>
            <a-tooltip v-else placement="right">
              <template #title>
                <span> {{ item.name }}</span>
              </template>
              <icon-font style="font-size: 14px" :type="getIconType(item)" />
            </a-tooltip>
          </div>
          <template #overlay v-if="!store.isOpenIndexModal.open">
            <a-menu @click="menu => onContextMenuClick(menu, item)">
              <a-sub-menu
                title="快速新建单位工程"
                :disabled="
                  visibleMethod(
                    menuOperator.menus.find(x => x.menuLevel === 120),
                    item
                  )
                "
                v-if="
                  menuOperator.menus.filter(x => x.menuLevel === 120).length > 0
                "
              >
                <a-menu-item
                  v-for="(item, index) in engineerMajorList"
                  :key="index + 50"
                  @click.stop="quickAddUnit(item.value)"
                  >{{ item.value }}</a-menu-item
                >
              </a-sub-menu>
              <a-menu-item
                v-for="menuitem in menuOperator.menus.filter(
                  x => x.menuLevel !== 120
                )"
                :disabled="visibleMethod(menuitem, item)"
                :key="menuitem.menuLevel"
                >{{ menuitem.name }}</a-menu-item
              >
            </a-menu>
          </template>
        </a-dropdown>
        <a-input
          v-else
          :placeholder="getPlaceholder(item.levelType)"
          v-model:value="findNodeById(gData, item.id).name"
          type="text"
          @keyup="item.name = inputName(item.name)"
          @blur="saveInfo(findNodeById(gData, item.id))"
          class="my-input"
          autofocus
          @focus="
            event => {
              event.target.select();
            }
          "
          ref="clickInsideContent"
        />
      </template>
    </a-tree>
    <common-modal
      className="dialog-comm"
      width="530px"
      v-model:modelValue="dialogVisible"
      :title="dialogTitle"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :label-col="{ span: 7 }"
        :wrapper-col="{ span: 15 }"
        autocomplete="off"
        @finish="handleOk"
      >
        <template v-if="dialogType === ConstructMenuOperator.unitLevel">
          <a-form-item
            label="单位工程名称"
            name="name"
            :rules="[{ required: true, message: '请输入单位工程名称!' }]"
          >
            <a-input
              :maxlength="50"
              v-model:value.trim="formData.name"
              @input="
                () => {
                  formData.name = inputName(formData.name);
                }
              "
              placeholder="请输入单位工程名称"
            />
          </a-form-item>
          <a-form-item
            label="定额标准"
            name="deStandardId"
            v-if="store.deType === '22'"
            :rules="[{ required: true, message: '请选择定额标准!' }]"
          >
            <a-select
              v-model:value="formData.deStandardId"
              placeholder="请选择定额标准"
              :options="deTypeRationList"
              @change="deTypeChange"
              :fieldNames="{
                label: 'name',
                value: 'sequenceNbr',
              }"
            >
            </a-select>
          </a-form-item>
          <a-form-item
            label="清单专业"
            name="type"
            :rules="[{ required: true, message: '请选择清单专业!' }]"
          >
            <a-select
              v-model:value="formData.type"
              placeholder="请选择清单专业"
              :options="engineerMajorList"
              @change="typeChange"
            >
            </a-select>
          </a-form-item>
          <a-form-item label="主定额册名称" name="type" v-if="formData.type">
            <a-select
              v-model:value="formData.libraryCode"
              placeholder="请选择主定额册名称"
              :options="majorTypeDropdownList"
              :fieldNames="{
                label: 'libraryName',
                value: 'libraryCode',
              }"
            >
            </a-select>
          </a-form-item>
          <a-form-item
            label="定额专业"
            name="type"
            :rules="[{ required: true, message: '请选择定额专业!' }]"
          >
            <a-select
              v-model:value="formData.secondInstallationProjectName"
              :placeholder="`请选择${
                formData.secondInstallationProjectName
                  ? formData.secondInstallationProjectName
                  : ''
              }定额专业`"
              :options="secondDropdownList"
              :fieldNames="{ label: 'cslbName', value: 'cslbName' }"
            >
            </a-select>
          </a-form-item>
        </template>
        <a-form-item
          v-if="dialogType === ConstructMenuOperator.singleLevel"
          label="单项工程名称"
          name="name"
          :rules="[{ required: true, message: '请输入单项工程名称!' }]"
        >
          <a-input
            :maxlength="50"
            v-model:value.trim="formData.name"
            @input="
              () => {
                formData.name = inputName(formData.name);
              }
            "
            placeholder="请输入单项工程名称"
          />
        </a-form-item>
        <a-form-item
          v-if="dialogType === ConstructMenuOperator.singleChildLevel"
          label="子单项工程名称"
          name="name"
          :rules="[{ required: true, message: '请输入子单项工程名称!' }]"
        >
          <a-input
            :maxlength="50"
            v-model:value.trim="formData.name"
            @input="
              () => {
                formData.name = inputName(formData.name);
              }
            "
            placeholder="请输入子单项工程名称"
          />
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 7, span: 15 }">
          <a-button @click="dialogVisible = false">取消</a-button>
          <a-button
            style="margin-left: 10px"
            type="primary"
            html-type="submit"
            :loading="submitLoading"
            >确定</a-button
          >
        </a-form-item>
      </a-form>
    </common-modal>
    <addDelModal
      v-model:visible="addDelVisible"
      :type="addDelType"
      :treeData="gData"
      @saveTree="saveTree"
      :currentInfo="currentInfo"
    />
    <BatchModify
      :treeData="gData"
      v-model:visible="batchModifyVisible"
    ></BatchModify>
  </div>
</template>
<script>
export default {
  name: 'commonAsideTree',
};
</script>
<script setup>
import {
  watch,
  ref,
  reactive,
  watchEffect,
  onMounted,
  nextTick,
  defineAsyncComponent,
  toRaw,
  computed,
} from 'vue';
import { CaretDownOutlined } from '@ant-design/icons-vue';
import xeUtils from 'xe-utils';
import csProject from '@/api/csProject';
import feePro from '@/api/feePro';
import { ConstructMenuOperator } from '@/components/editProjectStructure/ConstructMenuOperator';
import { inputName } from '@/utils/index';
import { proModelStore } from '@/store/proModel.js';
import { projectDetailStore } from '@/store/projectDetail';
import operateList from '@/views/projectDetail/customize/operate';
import { message, Modal } from 'ant-design-vue';
import { VXETable } from 'vxe-table';
import { useRoute } from 'vue-router';
import addDelModal from './add-del-modal.vue';
import infoMode from '@/plugins/infoMode.js';
import { onClickOutside } from '@vueuse/core';
const BatchModify = defineAsyncComponent(() => import('./BatchModify.vue'));
let batchModifyVisible = ref(false);
const openBatchModify = () => {
  batchModifyVisible.value = true;
};
const route = useRoute();
const store = projectDetailStore();
const proStore = proModelStore();

const menuOperator = new ConstructMenuOperator();
// console.log('menuOperator', menuOperator);
const props = defineProps(['treeData', 'leftIsExpand']);
const emit = defineEmits(['getTreeList', 'drop']);
const gData = ref([]);

const isDraggable = computed(() => {
  const info = gData.value.find(item => item.levelType === 1);
  return info?.optionLock; // 标段保护不可拖动
});
const selectedKeys = ref([]);
const expandList = ref([]);
let dragNode = ref();
const addDelVisible = ref(false);
const addDelType = ref('copyTo');
const commonAside = ref(null);
const doubleClick = async item => {
  if (store?.currentTreeGroupInfo?.optionLock) return;
  item.isNameEdit = true;
  console.log(
    'eeeeeeeeeeeeeeeeeeeeeeee1',
    item,
    (findNodeById(gData.value, item.id).isNameEdit = true)
  );
  setTimeout(() => {
    clickInsideContent.value?.focus();
    if (commonAside.value.parentElement) {
      commonAside.value.parentElement.scrollLeft = 0;
    }
  });
};
// 根据id查找目标节点
function findNodeById(treeData, targetId) {
  // 遍历树形数据
  for (let i = 0; i < treeData.length; i++) {
    const node = treeData[i];
    // 检查当前节点是否是目标节点
    if (node.id === targetId) {
      return node; // 返回找到的节点
    }
    // 如果当前节点有子节点，则递归查找
    if (node.children && node.children.length > 0) {
      const foundNode = findNodeById(node.children, targetId);
      if (foundNode) {
        return foundNode; // 如果在子节点中找到了目标节点，则返回
      }
    }
  }
  // 如果遍历完整个树都没有找到目标节点，则返回 null
  return null;
}
const constructSequenceNbr = route.query.constructSequenceNbr;
let unifyData = ref(); //统一应用按钮是否禁用
let visible = ref(false);
let submitLoading = ref(false);
let dialogVisible = ref(false);
let dialogTitle = ref('');
let dialogType = ref('');
const dialogInfo = {
  // 弹框操作
  [ConstructMenuOperator.singleLevel]: {
    title: '添加单项工程',
  },
  [ConstructMenuOperator.unitLevel]: {
    title: '添加单位工程',
  },
  [ConstructMenuOperator.singleChildLevel]: {
    title: '添加子单项工程',
  },
};
let engineerMajorList = ref([]);
let majorTypeDropdownList = ref([]);
let secondDropdownList = ref([]);
let addItem = reactive({
  // levelId: null,
  levelType: null,
  parentLevel: null,
  parentId: null,
});

const formRef = ref(null);
let formData = reactive({
  name: null,
  type: null,
  clickId: '',
  deStandardId: null,
  clickLevelType: '',
  libraryCode: null,
  secondInstallationProjectName: null,
});
let isQuickUnit = ref(false); // 是否为快速新建单位工程
let quickUnitName = ref(null); // 快速新建单位工程选中的专业
let currentInfo = ref({});
watch(
  () => [
    isQuickUnit.value,
    formData.secondInstallationProjectName,
    formData.libraryCode,
    formData.deStandardId,
  ],
  value => {
    if (
      isQuickUnit.value &&
      formData.secondInstallationProjectName &&
      formData.libraryCode &&
      formData.deStandardId
    ) {
      formData.type = quickUnitName.value;
      formData.name = quickUnitName.value;
      addUnit();
    }
  }
);
onMounted(() => {
  console.log('进来预算左侧树了', store.type);
  // gData.value = []
  // console.log('0000',gData.value,props.treeData)
  gData.value = JSON.parse(JSON.stringify(xeUtils.toArrayTree(props.treeData)));
  // gData.value = JSON.parse(JSON.stringify(buildTree(props.treeData)));
  console.log('111q11111111111', props.treeData, store.standardGroupOpenInfo);
  if (store.standardGroupOpenInfo.selectProjectId) {
    //如果是从标准组价返回到工作台，需要直接定位到原先单位工程的分部分项页面
    let { selectProjectId } = store.standardGroupOpenInfo;
    let target = props.treeData.find(i => i.id === selectProjectId);
    let parent = props.treeData.find(i => i.id === target.parentId);
    setCheckRow(target);
    ininGetList(parent);
    store.standardGroupOpenInfo.selectProjectId = null;
  } else if (store.checkVisible) {
    ininGetList(props.treeData[0]);
  } else {
    initHandler();
  }
  queryRationList();
  setTimeout(() => {
    queryEngineerMajorList('all');
  }, 500);
});
//获取定额标准下拉列表
let deTypeRationList = ref([]);
const queryRationList = async () => {
  const postData = {
    areaId: 130000,
    type: '2',
  };
  const result = await csProject.quotaStandardDropdownList(postData);
  if (result.status === 200) {
    deTypeRationList.value = result.result || [];
    formData.deStandardId = deTypeRationList.value[0]?.sequenceNbr;
    if (store.deType === '12') {
      formData.deStandardId = deTypeRationList.value.find(
        item => item.releaseYear === '12'
      )?.sequenceNbr;
    }
  }
};
// 当前选中的定额标准
const currentDeType = computed(() => {
  if (!deTypeRationList.value.length) {
    return store.deType;
  }
  const info = deTypeRationList.value.find(
    item => item.sequenceNbr === formData.deStandardId
  );
  // console.log(formData.deStandardId, info, info.releaseYear);
  return info?.releaseYear || store.deType;
});
const deTypeChange = val => {
  formData.libraryCode = null;
  formData.type = null;
  formData.secondInstallationProjectName = null;
  engineerMajorList.value = [];
  majorTypeDropdownList.value = [];
  secondDropdownList.value = [];
  queryEngineerMajorList();
};
const treeSelect = (data, { selected, selectedNodes, node, event }) => {
  console.log(
    '🚀 ~ treeSelect ~ selectedNodes:',
    selectedNodes,
    props.treeData
  );
  console.log('🚀 ~ treeSelect ~ selected:', selected);
  console.log('🚀 ~ treeSelect ~ node:', node);
  if (selected) {
    currentChangeEvent({ row: props.treeData.find(a => a.id === node.id) });
  }
  if (!store.isOpenIndexModal.open) {
    selectedKeys.value = [node.id];
  }
};

const getPlaceholder = levelType => {
  const map = {
    1: '项目工程',
    2: '单项工程',
    3: '单位工程',
  };

  return `请输入${map[levelType]}名称`;
};

const saveInfo = (row, event) => {
  // 移开鼠标清除编辑状态
  let flag = /^[^`\^。>？!！￥~!@$^&*\=+[\]{}\\|;:<>/?]*$/.test(row.name); //校验名称是否符合规则
  if (!flag) {
    row.name = row.copyName;
    message.warning('名称输入有特殊字符，请重新输入名称');
    return;
  }
  const oldRow = props.treeData?.find(i => i.id === row.id);
  if (isRepeat(row.name, row.parentId, row.id)) {
    // $table.revertData(row, field);
    row.name = oldRow.name;
    row.copyName = oldRow.name;
    message.error('同级名称不可重复，请重新输入名称');
    return;
  }
  row.isNameEdit = false;
  // row.name = inputName(row.name);
  row.name = row.name.trim();
  let value = row.name;
  if (!row.name) {
    row.name = row.copyName;
    return;
  }
  //字符长度50限制
  if (row.name.length > 50) {
    row.name = value.slice(0, 50);
  }
  //判断值是否变化
  if (oldRow.name === row.name) return;
  oldRow.name = row.name;
  if (row.levelType === ConstructMenuOperator.singleLevel) {
    updateSingleProject(row);
  } else if (row.levelType === ConstructMenuOperator.unitLevel) {
    updateUnitProject(row);
  } else {
    updateConstructProject(row);
  }
  row.copyName = row.name;
};
const updateConstructProject = row => {
  let apiData = {
    constructId: row.id,
    constructName: row.name,
  };
  csProject.editConstructProject(apiData).then(res => {
    if (res.status === 200) {
      message.success('修改成功');
      // emit('getTreeList')
      store.SET_IS_REFRESH_PROJECT_TREE(true);
      store.SET_IS_REFRESH_BASE_INFO(!store.isRefreshBaseInfo);
      store.SET_PROJECT_NAME(row.name);
    }
  });
};
const updateSingleProject = row => {
  let apiData = {
    constructId: constructSequenceNbr,
    singleId: row.id,
    singleName: row.name,
  };
  csProject.updateSingleProject(apiData).then(res => {
    if (res.status === 200) {
      message.success('修改成功');
      // emit('getTreeList')
      store.SET_IS_REFRESH_PROJECT_TREE(true);
    }
  });
};

const updateUnitProject = row => {
  let apiData = {
    constructId: constructSequenceNbr,
    singleId: row.parentId,
    unitId: row.id,
    unitName: row.name,
  };
  csProject.updateUnitProject(apiData).then(res => {
    if (res.status === 200) {
      message.success('修改成功');
      store.SET_IS_REFRESH_BASE_INFO(!store.isRefreshBaseInfo);
      // emit('getTreeList')
      store.SET_IS_REFRESH_PROJECT_TREE(true);
    }
  });
};
const initHandler = () => {
  nextTick(() => {
    setCheckRow(props.treeData[0]); //默认选中第一条数据
  });
  //默认展开第一项
  setTimeout(() => {
    let single = props.treeData.find(
      item => item.levelType === 2 && item.children && item.children.length > 0
    );
    single ? ininGetList(single) : ininGetList(props.treeData[0]); //展开第一个有children的单项
  }, 0);
};
const openVisible = () => {
  visible.value = true;
};
watchEffect(() => {
  if (proStore.openEditModalStatus) {
    openVisible();
    // 编辑弹窗还是自动打开，则关闭
    proStore.onEditModal(false);
  }
});
watch(
  () => props.treeData,
  (newVal, oldVal) => {
    console.log('gData.value111', newVal);
    // gData.value = []
    gData.value = JSON.parse(JSON.stringify(buildTree(newVal)));
    console.log('gData.value', gData.value);
    store.projectTree = newVal;
    // selectedKeys.value = [gData.value[0].id];

    let change;
    if (newVal && oldVal && newVal.length > oldVal.length) {
      let change = newVal.find(x => !oldVal.some(y => y.id === x.id));
      console.log('change--------', change);
      if (change) {
        change && setCheckRow(change);
        selectedKeys.value = [change.id];
        let singleNum = props.treeData.filter(i => i.levelType === 2);
        if (
          change.levelType === 2 &&
          singleNum.length === 1 &&
          props.treeData.find(i => i.levelType === 3)
        ) {
          //当只有工程项目和单位，添加单项时需要展开数据
          let parant = newVal.find(x => x.id === change.parentId);
          ininGetList(parant); //展开第一个有children的单项
        }
      }
    } else if (newVal && oldVal && newVal.length < oldVal.length) {
      if (!change) return;
      change = oldVal.filter(x => !newVal.some(y => y.id === x.id))[0];
      const select = newVal.filter(item => item.id === change.parentId);
      console.log(select[0]);
      select && setCheckRow(select[0]);
    } else if (store.checkVisible) {
    } else {
      // 没有新增或删减就默认选中以前选中的数据
      console.log('selectedKeys.value', selectedKeys.value, newVal);
      if (selectedKeys.value.length > 0) {
        let target = newVal.find(a => a.id === selectedKeys.value[0]);
        setCheckRow(target || newVal[0]);
      } else {
        setCheckRow(newVal[0]);
      }
    }
  }
);
watch(
  () => props.leftIsExpand,
  (newVal, oldVal) => {
    console.log('leftIsExpand.value111', newVal);
    if (!newVal) {
      gData.value = JSON.parse(JSON.stringify(props.treeData)).map(a => {
        if (a.children?.length > 0) a.children = [];
        return a;
      });
    } else {
      console.log(props.treeData);
      gData.value = JSON.parse(
        JSON.stringify(xeUtils.toArrayTree(props.treeData))
      );
    }
  }
);
const ininGetList = data => {
  //获取需要展开的列表
  expandList.value = [props.treeData[0]?.id];
  getExpandList(data);
};
const getExpandList = data => {
  expandList.value.push(data?.id);
  if (data?.children?.length > 0) {
    data?.children?.map(i => {
      getExpandList(i);
    });
  }
};
const getIconType = row => {
  // console.log(row);
  const map = {
    1: 'icon-gongchengxiangmu',
    2: 'icon-danxianggongcheng',
    3: 'icon-danweigongcheng1',
  };
  return map[row.levelType] || 'icon-danweigongcheng1';
};
const visibleMethod = (menuItem, treeItem) => {
  let parentId = props.treeData.find(a => a.id === treeItem.id).parentId;
  let parent = props.treeData.find(a => a.id === parentId);
  let row = props.treeData.find(a => a.id === treeItem.id);
  // row.parent = parent;
  menuOperator.resetMenus(row, props.treeData);
  menuOperator.menusJson();
  return !menuItem.isValid;
};

const rightClick = () => {
  queryEngineerMajorList('all');
};

const onContextMenuClick = async (menu, treeItem) => {
  // console.log(`treeKey: ${JSON.stringify(row)}, menuKey: ${menu}`);
  let row = props.treeData.find(a => a.id === treeItem.id);
  console.log('点击右键操作', menu, row);
  setCheckRow(row);
  const menuLevel = menu.key;
  currentInfo.value = row;
  if (menuLevel === ConstructMenuOperator.delete) {
    const title = '提示';
    const content =
      row.levelType === ConstructMenuOperator.singleLevel
        ? '是否确定删除，删除后将会将单项工程下关联的所有的数据删除？'
        : '是否删除该单位工程？';
    await deleteTip(title, content);
    return;
  }
  setDialogInfo(menuLevel);
};
const setDialogInfo = async type => {
  console.log('setDialogInfo', type);
  if (type === 66) {
    store.SET_ASIDETREE_COPY_INFO(currentInfo.value);
    return;
  } else if (type === 67) {
    console.log('左侧树粘贴', store.asideTreeCopyInfo);
    return;
  } else if (type === 66.5) {
    console.log('复制到', store.asideTreeCopyInfo);
    addDelVisible.value = true;
    addDelType.value = 'copyTo';
    return;
  } else if (type === 9998) {
    console.log('批量删除', store.asideTreeCopyInfo);
    addDelVisible.value = true;
    addDelType.value = 'batchDelete';
    return;
  } else if (type === ConstructMenuOperator.batchModify) {
    console.log('批量修改');
    openBatchModify();
    return;
  } else if (type === ConstructMenuOperator.optionLock) {
    optionLock();
    return;
  }
  formData = reactive({
    name: null,
    type: null,
    libraryCode: null,
    deStandardId: null,
    secondInstallationProjectName: null,
  });
  formRef.value?.resetFields();
  dialogTitle.value = dialogInfo[type]?.title;
  dialogType.value = type;
  addItem.levelType = type;
  if (type === 2.5) {
    addItem.parentId = currentInfo.value.id;
  } else if (type === 2) {
    currentInfo.value.levelType === 1
      ? (addItem.parentId = currentInfo.value.id)
      : (addItem.parentId = currentInfo.value.parentId);
  } else {
    currentInfo.value.levelType <= 2
      ? (addItem.parentId = currentInfo.value.id)
      : (addItem.parentId = currentInfo.value.parentId);
  }
  addItem.parentLevel = currentInfo.value.levelType;
  console.log(store.deType);
  await queryRationList();
  if (![50, 51, 52, 53, 54, 55].includes(type)) {
    dialogVisible.value = true;
  }
  queryEngineerMajorList('all');
  console.log('formData.deStandardId', formData.deStandardId);
};
const setCheckRow = checkRow => {
  let newValue = { row: { ...checkRow } };
  currentChangeEvent(newValue); //将选中的当前行存储信息
  selectedKeys.value = [checkRow?.id];
  // vexTable.value?.setCurrentRow(checkRow);
  // expandVexTable.value.setCurrentRow(checkRow);
};

const optionLock = () => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    optionLock: !store.currentTreeGroupInfo?.optionLock,
  };
  console.log('标段保护参数', apiData);
  csProject.optionLock(apiData).then(res => {
    console.log('标段保护', res);
    if (res.status === 200) {
      emit('getTreeList');
    }
  });
};

const currentChangeEvent = newValue => {
  // let newValue = {};
  // newValue.row = props.treeData.find(a => a.id === id[0]);
  console.log('currentChangeEvent', newValue);
  if (store.isOpenIndexModal.open) {
    selectedKeys.value = [store.currentTreeInfo.id];
    console.log('store.isOpenIndexModal', store.currentTreeInfo);
    return;
  }
  if (store.humanUpdataData && store.humanUpdataData.isEdit) {
    // vexTable.value?.setCurrentRow(newValue.oldValue); //人材机数据有需要保存的，先确定是否要先保存
    saveHumanData(newValue);
    return;
  }
  if (newValue.row) {
    let obj = {
      constructId: '',
      name: '',
      singleId: '',
      singleName: '',
    };
    if (newValue.row.levelType === 3) {
      props.treeData.forEach(item => {
        if (item.levelType === 1) {
          obj.constructId = item.id;
          obj.ssConstructId = item.ysshConstructId || item.id;
          obj.name = item.name;
          obj.optionLock = item.optionLock;
        } else if (item.id === newValue.row.parentId) {
          obj.singleId = item.id;
          obj.ssSingleId = newValue.row.ysshSingleId || item.id;
          obj.singleName = item.name;
        }
      });
    } else if (newValue.row.levelType === 2) {
      props.treeData.forEach(item => {
        if (item.levelType === 1) {
          obj.constructId = item.id;
          obj.ssConstructId = item.ysshConstructId;
          obj.optionLock = item.optionLock;
        }
        // obj.name = item.name;
      });
      obj.singleId = newValue.row.id;
      obj.ssSingleId = newValue.row.ysshSingleId || newValue.row.id;
      obj.name = newValue.row.name;
    } else {
      obj.constructId = newValue.row.id;
      obj.ssConstructId = newValue.row.ysshConstructId || newValue.row.id;
      obj.name = newValue.row.name;
      obj.optionLock = newValue.row.optionLock;
    }
    // console.log('9999newValue', newValue.row);
    // console.log(store);
    store.SET_CURRENT_TREE_INFO(newValue.row);
    store.SET_CURRENT_TREE_GROUP_INFO(obj);
  }
};
const saveHumanData = oldVal => {
  unifyData.value = operateList.value.find(
    item => item.name === store.humanUpdataData.name
  );
  let infoText =
    store.humanUpdataData.name === 'unify'
      ? '当前取费设置中存在费率或政策文件变更，是否统一修改并应用至所有单位工程？'
      : `人材机数据已修改，是否应用整个${
          store.currentTreeInfo.levelType === 1 ? '工程项目' : '单项工程'
        }?`;
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText,
    confirm: () => {
      store.humanUpdataData?.name === 'unify'
        ? feeTotalSave(oldVal)
        : humanSave(oldVal);
      infoMode.hide();
    },

    close: () => {
      resetHumanData(oldVal);
      unifyData.value.disabled = true;
      infoMode.hide();
    },
  });
  // Modal.confirm({
  //   title: `${infoText}`,
  //   onOk() {
  //     store.humanUpdataData.name === 'unify'
  //       ? feeTotalSave(oldVal)
  //       : humanSave(oldVal);
  //   },
  //   onCancel() {
  //     resetHumanData(oldVal);
  //     unifyData.value.disabled = true;
  //   },
  // });
};
const getParamsData = data => {
  let apiData = { ...data };
  apiData.constructId =
    store.currentTreeInfo.levelType === 1
      ? store.currentTreeInfo?.id
      : store.currentTreeGroupInfo?.constructId;
  if (store.currentTreeInfo.levelType === 2) {
    apiData.singleId = store.currentTreeInfo?.id; //单项ID
  }
  if (store.currentTreeInfo.levelType === 3) {
    apiData.singleId = store.currentTreeGroupInfo?.singleId; //单项ID
    apiData.unitId = store.currentTreeInfo?.id; //单位ID
  }
  return apiData;
};
const humanSave = async oldVal => {
  if (!store.humanUpdataData) {
    return;
  }
  setGlobalLoading(true, '统一应用中，请稍后...');
  if (!store.humanUpdataData?.unitIdList) store.humanUpdataData.unitIdList = [];
  let postData = getParamsData({});
  if (store.humanUpdataData.adjustFactor?.isEdit) {
    let apiData = {
      ...postData,
      coefficient: store.humanUpdataData.adjustFactor.marcketFactor * 1,
      rcjList: JSON.parse(
        JSON.stringify(store.humanUpdataData.adjustFactor.selectRows)
      ),
      unitIdList: [...store.humanUpdataData.unitIdList],
    };
    await csProject.constructAdjustmentCoefficient(apiData).then(res => {
      console.log('统一应用系数', res);
    });
  }
  if (store.humanUpdataData.updataData) {
    setGlobalLoading(true);
    let apiData = {
      ...postData,
      constructProjectRcjList: JSON.parse(
        JSON.stringify(store.humanUpdataData.updataData)
      ),
      unitIdList: [...store.humanUpdataData.unitIdList],
    };
    let apiFunName =
      store.currentTreeInfo.levelType === 1
        ? 'changeRcjConstructProject'
        : 'changeRcjSingleProject';
    await csProject[apiFunName](apiData).then(res => {
      console.log('统一应用接口返回结果', res);
    });
  }
  if (store.humanUpdataData.sourcePriceData) {
    let apiData = {
      ...postData,
      constructProjectRcjList: JSON.parse(
        JSON.stringify(store.humanUpdataData.sourcePriceData)
      ),
      unitIdList: [...store.humanUpdataData.unitIdList],
    };
    console.log('统一应用接口参数', apiData);
    await feePro.rcjFromUnitUpdate(apiData).then(res => {
      console.log('统一应用接口返回结果', res);
    });
  }
  if (store.humanUpdataData.rcjwjcList?.length > 0) {
    let apiData = {
      ...postData,
      excludeRcjIdList: [...store.humanUpdataData.rcjwjcList],
    };
    let apiFunName =
      store.currentTreeInfo.levelType === 2
        ? 'singleRcjWjc'
        : 'constructRcjWjc';
    console.log('统一应用接口参数', apiData);
    await feePro[apiFunName](apiData).then(res => {
      console.log('统一应用接口返回结果', res);
    });
  }
  resetHumanData(oldVal);
  unifyData.value.disabled = true;
  setGlobalLoading(false);
};
const setGlobalLoading = (bol, msg = '设置中，请稍后...') => {
  //处理全局loading效果
  if (bol) {
    store.SET_GLOBAL_LOADING({
      loading: bol,
      info: msg,
    });
  } else {
    setTimeout(() => {
      store.SET_GLOBAL_LOADING({
        loading: bol,
        info: msg,
      });
    }, 200);
  }
};
const feeTotalSave = async oldVal => {
  setGlobalLoading(true, '统一应用中，请稍后...');
  if (store.humanUpdataData.updataData.policy) {
    await feePro
      .checkPolicyDocument(
        JSON.parse(JSON.stringify(store.humanUpdataData.updataData.policy))
      )
      .then(res => {});
  }
  if (store.humanUpdataData.updataData.feeTotal) {
    await feePro
      .unifiedUse(
        JSON.parse(JSON.stringify(store.humanUpdataData.updataData.feeTotal))
      )
      .then(res => {});
  }
  if (store.humanUpdataData.updataData.calEditData) {
    let apiData = {
      constructId:
        store.currentTreeInfo.levelType === 1
          ? store.currentTreeInfo?.id
          : store.currentTreeGroupInfo?.constructId,
      feeCalculateBaseList: JSON.parse(
        JSON.stringify(store.humanUpdataData.updataData.calEditData)
      ),
    };
    await feePro.updateProjectUnitCalculateBaseApply(apiData).then(res => {
      if (res.status === 200) {
        console.log('updateProjectUnitCalculateBaseApply1', res, apiData);
      }
    });
  }
  setGlobalLoading(false, '统一应用中，请稍后...');
  resetHumanData(oldVal);
  unifyData.value.disabled = true;
};
const resetHumanData = oldVal => {
  store.SET_HUMAN_UPDATA_DATA(null);
  // vexTable.value?.setCurrentRow(oldVal.newValue);
  currentChangeEvent(oldVal);
};
//获取专业列表下拉列表
const queryEngineerMajorList = status => {
  csProject
    .getEngineerMajorList({
      deStandard: status === 'all' ? store.deType : currentDeType.value,
    })
    .then(function (response) {
      console.log(
        '🚀 ~ 获取专业列表下拉列表:',
        response,
        currentDeType.value,
        store.deType
      );
      if (response.status === 200) {
        engineerMajorList.value = [];
        response.result.map(item => {
          engineerMajorList.value.push({
            key: item.sequenceNbr,
            value: item.unitProjectName,
          });
        });
      }
    });
};

const deleteTip = (title, content) => {
  new Promise((resolve, reject) => {
    VXETable.modal
      .confirm({
        content: content,
        className: 'dialog-comm confirm-dialog',
        status: 'error',
        title,
        iconStatus: 'vxe-icon-info-circle',
      })
      .then(res => {
        if (res === 'confirm') {
          if (currentInfo.value.levelType === 2) {
            deleteSingleProject();
          } else {
            deleteUnitProject();
          }
          resolve(true);
        } else {
          resolve(false);
        }
      });
  });
};
const typeChange = value => {
  formData.secondInstallationProjectName = null;
  // if (value === '安装工程') {
  secondInstallationProjectNameByDropdownList();
  // }
  constructMajorTypeDropdownList();
};
// 删除单项工程
const deleteSingleProject = () => {
  csProject
    .delSingleProject({
      constructId: constructSequenceNbr,
      singleId: currentInfo.value.id,
    })
    .then(res => {
      if (res.status === 200) {
        message.success('删除成功');
        emit('getTreeList');
      }
    });
};

// 删除单位工程
const deleteUnitProject = () => {
  csProject
    .delUnitProject({
      constructId: constructSequenceNbr,
      singleId: currentInfo.value.parentId,
      unitId: currentInfo.value.id,
    })
    .then(res => {
      if (res.status === 200) {
        message.success('删除成功');
        emit('getTreeList');
      }
    });
};
const constructMajorTypeDropdownList = () => {
  let apiData = {
    deType: isQuickUnit.value ? store.deType : currentDeType.value,
    constructMajorType: formData.type,
  };
  console.log('constructMajorTypeDropdownList', apiData);
  csProject.getMainDeLibrary(apiData).then(res => {
    if (res.status === 200) {
      majorTypeDropdownList.value = res.result;
      majorTypeDropdownList.value.forEach(item => {
        if (item.defaultDeFlag === 1) {
          formData.libraryCode = item.libraryCode;
        }
      });
    }
  });
};

const secondInstallationProjectNameByDropdownList = () => {
  csProject
    .getSecondInstallationProjectName({
      constructMajorType: formData.type,
      deStandard: currentDeType.value,
    })
    .then(res => {
      if (res.status === 200) {
        secondDropdownList.value = res.result;
        const hasDq = res.result.some(i => {
          return i.cslbName === '电气设备安装工程';
        });
        // if (hasDq) {
        //   formData.secondInstallationProjectName = '电气设备安装工程';
        // }
        formData.secondInstallationProjectName =
          hasDq && formData.type === '安装工程'
            ? '电气设备安装工程'
            : secondDropdownList.value[0]?.cslbName;
      }
    });
};
const handleOk = () => {
  if (submitLoading.value) return;
  // if (isRepeat(formData.name, addItem.parentId)) {
  //   formData.name = null;
  //   message.error('同级名称不可重复，请重新输入名称');
  //   return;
  // }
  submitLoading.value = true;
  if (dialogType.value === ConstructMenuOperator.singleLevel) {
    addSingle();
  } else if (dialogType.value === ConstructMenuOperator.singleChildLevel) {
    addChildSingle();
  } else {
    addUnit();
  }
};
const isRepeat = (name, parentId, tarId = null) => {
  //判断统计添加的单位/单项名称是否重复出现
  let flag = props.treeData.some(
    item =>
      // item.levelId === addItem.levelId &&
      // item.levelType === addItem.levelType && //leveltype不同不作判断
      item.id !== tarId &&
      item.parentId === parentId &&
      item.name.trim() === name.trim()
  );
  console.log('isRepeat', addItem, name, parentId, tarId, props.treeData, flag);

  return flag; //true--名称重复，flse---名称不重复
};
// 添加单项工程
const addSingle = () => {
  let apiData = {
    constructId: route.query.constructSequenceNbr,
    singleName: formData.name,
    clickId: currentInfo.value.id,
    clickLevelType: currentInfo.value.levelType,
  };
  if (currentInfo.value.levelType === 2) {
    apiData.oldSingleId = currentInfo.value.id;
  }
  console.log('添加单项传参', apiData);
  csProject
    .addSingleProject(apiData)
    .then(res => {
      // console.log('添加单项', res);
      if (res.status === 200) {
        init();
      }
    })
    .finally(() => {
      setTimeout(() => {
        submitLoading.value = false;
      }, 500);
    });
};
//添加子单项工程
const addChildSingle = () => {
  let apiData = {
    constructId: route.query.constructSequenceNbr,
    singleName: formData.name,
    clickId: currentInfo.value.id,
    clickLevelType: currentInfo.value.levelType,
  };
  if (currentInfo.value.levelType === 2) {
    apiData.oldSingleId = currentInfo.value.id;
  }
  csProject
    .addSubSingleProject(apiData)
    .then(res => {
      console.log('添加子单项', apiData, res);
      if (res.status === 200) {
        init();
      }
    })
    .finally(() => {
      setTimeout(() => {
        submitLoading.value = false;
      }, 500);
    });
};
const init = () => {
  dialogVisible.value = false;
  emit('getTreeList');
};

// 快速新建单位工程
const quickAddUnit = name => {
  formData.type = name;
  formData.name = name;
  isQuickUnit.value = true;
  quickUnitName.value = name;
  typeChange();
};

// 添加单位工程
const addUnit = () => {
  let apiData = {
    constructId: route.query.constructSequenceNbr,
    singleId:
      currentInfo.value.levelType === 3
        ? currentInfo.value.parentId
        : currentInfo.value.levelType === 2
        ? currentInfo.value.id
        : '',
    unitName: formData.name,
    deStandardId: formData.deStandardId,
    constructMajorType: formData.type,
    clickId: currentInfo.value.id,
    clickLevelType: currentInfo.value.levelType,
    libraryCode: formData.libraryCode,
    secondInstallationProjectName: formData.secondInstallationProjectName,
  };
  if (currentInfo.value.levelType === 2) {
    apiData.oldSingleId = currentInfo.value.id;
  }
  if (currentInfo.value.levelType === 3) {
    apiData.oldUnitId = currentInfo.value.id;
  }
  console.log('添加单位参数：', apiData);
  csProject
    .addUnitProject(apiData)
    .then(res => {
      // console.log('添加单位', res, apiData);
      if (res.status === 200) {
        init();
      }
    })
    .finally(() => {
      setTimeout(() => {
        submitLoading.value = false;
        isQuickUnit.value = false;
      }, 500);
    });
};
function calculateDepth(node) {
  if (!node.children || node.children.length === 0) {
    return 0; // Leaf node, depth is 0
  } else {
    let maxChildDepth = 0;
    for (let child of node.children) {
      const childDepth = calculateDepth(child);
      maxChildDepth = Math.max(maxChildDepth, childDepth);
    }
    return maxChildDepth + 1; // Add 1 to include current node
  }
}
function calculateLevelToRoot(nodeId, treeData) {
  // 辅助函数，用于递归地查找节点
  function findNode(currentNode, targetId, currentLevel) {
    if (currentNode.id === targetId) {
      return currentLevel;
    }
    if (currentNode.children) {
      for (let child of currentNode.children) {
        const result = findNode(child, targetId, currentLevel + 1);
        if (result !== -1) {
          return result;
        }
      }
    }
    return -1;
  }

  // 调用辅助函数来计算层级数
  const levelFromRoot = findNode(treeData, nodeId, 0);
  return levelFromRoot;
}
const onDragEnter = info => {
  // console.log('onDragEnter',info);
  // expandedKeys 需要展开时
  // expandedKeys.value = info.expandedKeys;
};
const getParentInfo = node => {
  if (node.levelType === 1) {
    return node;
  }
  return node.parent?.nodes.find(item => item.levelType === 1);
};
const dragstart = info => {
  if (info.node.biddingType === 0) {
    info.event.preventDefault();
    return false;
  }
  dragNode.value = info.node;
};
const onDragOver = info => {
  // info.event.dataTransfer.dropEffect = 'none'

  // 1. 单位工程限制
  // 1.1. 单位工程不能为单位工程子集
  // 1.2. 单位工程移动到目标单项时，此单项必须为最子节点
  // 1.3. 单位工程最根节点单项下
  // 1.4. 单位工程移动到目标单项时，目标单项不能有单项工程
  // 2. 单项工程限制
  // 2.1. 单项工程移动到目标单项时，此单项必须为最子节点
  // 2.2. 单项工程移动到目标单项时，目标单项不能有单位工程
  // 2.3. 单项工程不能被放在单位工程下
  // 3.项目节点只有一个，且不能拖动
  // 其他
  // 同一工程下只能有相同的工程
  // console.log(
  //   info
  // );
  if (
    calculateDepth(dragNode.value) +
      calculateLevelToRoot(info.node.id, gData.value[0]) >
    10
  ) {
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }
  if (dragNode.value.levelType < info.node.levelType) {
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }
  if (info.node.levelType === 2 && info.node.hasOwnProperty('children')) {
    if (info.node.children.findIndex(item => item.levelType === 3) !== -1) {
      info.event.dataTransfer.dropEffect = 'none';
      info.event.preventDefault();
      return false;
    }
  }
  if (dragNode.value.levelType === 3 && info.node.levelType === 1) {
    // 单位不可移动到最根节点下
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }
  if (
    dragNode.value.levelType === 3 &&
    info.node.levelType === 2 &&
    info.node.hasOwnProperty('children')
  ) {
    // 单位工程移动到目标单项时，此单项必须为最子节点
    if (info.node.children.findIndex(item => item.levelType === 2) !== -1) {
      info.event.dataTransfer.dropEffect = 'none';
      info.event.preventDefault();
      return false;
    }
  }
};
const onDrop = info => {
  // console.log(info);
  console.log('isLastNodeAtLevel(info.node,info.node.levelType)', info);

  if (
    info.dragNode.levelType === 3 &&
    info.node.levelType === 3 &&
    !info.dropToGap
  ) {
    // 1.1. 单位工程不能为单位工程子集
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }
  //不可拖拽到工程项目上边位置
  if (info.node.levelType === 1 && info.dropPosition === -1) {
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }
  if (
    info.dragNode.levelType === 3 &&
    info.node.levelType === 2 &&
    info.dropToGap
  ) {
    // 1.1. 单位工程不能跟单项同级
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }
  if (
    info.dragNode.levelType === 3 &&
    info.node.levelType === 2 &&
    !info.dropToGap &&
    info.node.children &&
    info.node.children?.findIndex(item => item.levelType === 2) !== -1
  ) {
    // 1.1. 单位工程不能跟单项同级
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }
  if (
    info.dragNode.levelType === 3 &&
    info.node.levelType < info.dragNode.levelType &&
    info.dropToGap
  ) {
    // 单位工程移动到目标单项时，此单项必须为最子节点
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }

  if (
    info.dragNode.levelType === 2 &&
    info.node.levelType > info.dragNode.levelType
  ) {
    // 单位工程移动到目标单项时，此单项必须为最子节点
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }
  //单位工程不可拖拽到有单项的工程项目下
  if (
    info.dragNode.levelType === 3 &&
    info.node.levelType === 1 &&
    info.node.children?.length > 0 &&
    info.node.children.find(a => a.levelType === 2)
  ) {
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }
  const dropKey = info.node.key; // 目标节点的key
  const dragKey = info.dragNode.key; // 拖拽节点的key
  const dropPos = info.node.pos.split('-'); // 目标节点的pos
  const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]); // 目标节点的dropPosition
  const loop = (data, key, callback) => {
    data.forEach((item, index) => {
      // console.log('isitem.key === key', item.key,key,item);
      if (item.id === key) {
        return callback(item, index, data);
      }
      if (item.children) {
        return loop(item.children, key, callback);
      }
    });
  };
  const data = xeUtils.clone([...gData.value]);

  // Find dragObject
  let dragObj = null;
  loop(data, dragKey, (item, index, arr) => {
    console.log('arr', arr);
    arr.splice(index, 1);
    dragObj = item;
  });
  console.log('dragObj', info.dragNode, info.node);

  if (!info.dropToGap) {
    // Drop on the content
    loop(data, dropKey, item => {
      item.children = item.children || [];
      /// where to insert 示例添加到头部，可以是随意位置
      item.children.unshift(dragObj);
    });
  } else if (
    (info.node.children || []).length > 0 && // Has children
    info.node.expanded && // Is expanded
    dropPosition === 1 // On the bottom gap
  ) {
    loop(data, dropKey, item => {
      item.children = item.children || [];
      // where to insert 示例添加到头部，可以是随意位置
      item.children.unshift(dragObj);
    });
  } else {
    let ar = [];
    let i = 0;
    loop(data, dropKey, (_item, index, arr) => {
      ar = arr;
      i = index;
    });
    if (dropPosition === -1) {
      ar.splice(i, 0, dragObj);
    } else {
      ar.splice(i + 1, 0, dragObj);
    }
  }
  if (info.dropToGap) {
    // 移动到同级
    dragObj.parentId = info.node.parentId;
    let parentTree = deepQuery(data, info.node.parentId);
    console.log(
      parentTree,
      parentTree.children.filter(a => a.name === dragObj.name)
    );
    if (parentTree.children.filter(a => a.name === dragObj.name).length > 1) {
      parentTree.children.filter(a => a.id === dragObj.id)[0].name =
        addObjectWithName(parentTree.children, dragObj.name);
    }
  } else {
    // 移动到子级
    dragObj.parentId = info.node.id;
    if (info.node.children?.filter(a => a.name === dragObj.name).length > 1) {
      info.node.children.filter(a => a.id === dragObj.id)[0].name =
        addObjectWithName(info.node.children, dragObj.name);
    }
  }
  function addObjectWithName(data, newName) {
    // 获取当前存在的name值
    const existingNames = new Set(data.map(item => item.name));

    // 如果newName已经存在，则找到下一个可用的name
    let newUniqueName = newName;
    let counter = 1;
    while (existingNames.has(newUniqueName)) {
      // 如果newName已经是a-X格式，则递增X
      if (newUniqueName.match(/^a-(\d+)$/)) {
        const currentNumber = parseInt(RegExp.$1, 10);
        newUniqueName = `a-${currentNumber + 1}`;
      } else {
        // 否则，在newName后添加计数器
        newUniqueName = `${newName}-${counter}`;
        counter++;
      }
    }
    return newUniqueName;
  }
  console.log(dragObj);
  // debugger;
  if (
    dragObj.levelType === 2 &&
    dragObj.parentId !== store.currentTreeGroupInfo?.constructId &&
    store.proCheckTab?.find(a => a.id == dragObj.id)
  ) {
    //拖拽成非一及子单项需要删除记录
    if (store.proCheckTab) {
      let list = [...store.proCheckTab];
      list = list.filter(a => a.id !== dragObj.id);
      store.SET_PRO_CHECK_TAB(list); //此处设置除单项之外的项目点击的tab栏都是项目概况
    }
  }
  gData.value = data;
  saveTree(JSON.parse(JSON.stringify(data[0])));
  // console.log(data[0].id);
  //下面注释两行setCheckRow里面设置了-重复
  // selectedKeys.value = [dragObj.id];
  // store.SET_CURRENT_TREE_INFO(dragObj);
  store.SET_IS_REFRESH_PROJECT_TREE(false);
  setCheckRow(dragObj);
  console.log('gData.value', gData.value);
};
const saveTree = (postData, nofresh = true) => {
  console.log('postData', postData);
  csProject.postEditStructureV2(postData).then(res => {
    console.log('保存树成功', res);
    if (!nofresh) {
      selectedKeys.value = [gData.value[0].id];
    }
    emit('getTreeList', nofresh);
  });
};
function deepQuery(tree, id) {
  var isGet = false;
  var retNode = null;
  function deepSearch(tree, id) {
    for (var i = 0; i < tree.length; i++) {
      if (tree[i].children && tree[i].children.length > 0) {
        deepSearch(tree[i].children, id);
      }
      if (id === tree[i].id || isGet) {
        isGet || (retNode = tree[i]);
        isGet = true;
        break;
      }
    }
  }
  deepSearch(tree, id);
  return retNode;
}
// 函数用于查找给定parentId的节点
function findParent(parentId, nodesArray) {
  if (parentId === null) return null;
  for (let node of nodesArray) {
    if (node.id === parentId) return node;
  }
  return null;
}

// 函数用于将节点组装成树形结构
function buildTree(nodesArray) {
  let tree = [];
  for (let node of nodesArray) {
    // 找到父节点
    let parent = findParent(node.parentId, nodesArray);
    if (parent) {
      // 如果父节点不存在children属性，则添加一个空数组
      if (!parent.children) parent.children = [];
      // 将当前节点添加到父节点的children数组中
      parent.children.push(node);
    } else {
      // 如果没有父节点，则当前节点就是树的根节点
      tree.push(node);
    }
  }
  return tree;
}
const clickInsideContent = ref(null);
const clickOutside = () => {
  props.treeData.forEach(
    i =>
      i.id !== selectedKeys.value &&
      (findNodeById(gData.value, i.id).isNameEdit = false)
  );
};
onClickOutside(clickInsideContent, clickOutside);
defineExpose({ setCheckRow });
</script>
<style lang="scss" scoped>
.common-aside {
  width: 100%;
  :deep(.ant-tree) {
    width: 100%;
    background: transparent;
    .ant-tree-node-content-wrapper {
      flex: none !important;
    }
    .ant-tree-switcher {
      display: flex;
      align-items: center;
    }
    .ant-tree-switcher-icon {
      display: flex;
      align-items: center;
      margin: 0 0px 0 5px;
    }
  }
}
:deep(.leftIsExpand) {
  .ant-tree-switcher {
    width: 13px;
  }
}
</style>
