import {BaseModel} from "./BaseModel";


/**
 * 人材机设备明细表(RcjDetails)DTO类
 * 与BS保持一致
 */

export class RcjDetails extends BaseModel {


    /**
     * 材料表id
     */
    public rcjId: string;

    /**
     * base rcj ID
     */
    public standardId: string

    /**
     * 配比材料编码
     */
    public pbCode: string;

    /**
     * 配比材料名称
     */
    public pbName: string;

    /**
     * 类型
     */
    public type: string;

    /**
     * 材料编码
     */
    public materialCode: string;

    /**
     * 项目类别(0：其他费；1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比)
     */
    public kind: number;

    /**
     * 材料名称
     */
    public materialName: string;

    /**
     * 规格
     */
    public specification: string;

    /**
     * 单位
     */
    public unit: string;

    /**
     * 序号
     */
    public dispNo:number;

    /**
     * 材料消耗量
     */
    public resQty: number;

    /**
     * 合价
     */
    private total: number;

    /**
     * 合计数量
     */
    private totalNumber: number;

    /**
     * 定额价
     */
    public dePrice: number;

    /**
     * 市场价
     */
    public marketPrice: number;

    /**
     * 市场价公式
     */
    public marketPriceFormula: string;

    /**
     * 价格来源
     */
    public sourcePrice: string;

    /**
     * 除税系数
     */
    public taxRemoval: number;

    /**
     * 除税系数备份
     */
    public taxRemovalBackUp: number;

    /**
     * 定额册编码
     */
    public libraryCode: string;


    /**
     * 甲供价格
     */
    public donorMaterialPrice: number;

    /**
     * 甲供名称
     */
    public donorMaterialName: string;

    /**
     * 甲供规格型号
     */
    public donorSpecification: string;

    /**
     * 是否是甲供(0:不是，1：是)
     */
    public ifDonorMaterial: number;

    /**
     * 甲供数量
     */
    public donorMaterialNumber: number;


    /**
     * kind备份
     */
    public kindBackUp: number;


    /**
     * 甲供材料ID
     */
    public materialSequenceNbr: string;

    /**
     * 是否锁定材料价格(0：否  1：是)
     */
    public ifLockStandardPrice: number;


    /**
     * 是否锁定工程量(0:不是，1：是)
     */
    public ifLockQuantity: number;

    /**
     * 暂估名称
     */
    public provisionalEstimateName: string;

    /**
     * 暂估价格
     */
    public provisionalEstimatePrice: number;

    /**
     * 暂估型号
     */
    public provisionalEstimateSpecification: string;

    /**
     * 暂估ID
     */
    public provisionalEstimateSequenceNbr: string;

    /**
     * 是否是暂估(0:不是，1：是)
     */
    public ifProvisionalEstimate: number;

    /**
     *所属定额id
     */
    public deId: string;


    /**
     * 原始含量
     */
    public initResQty: number;


    /**
     * 是否汇总(解析) 1代表解析 0代表不解决  默认是1 解析
     */
    private markSum: number;


    /**
     * 产地
     */
    public producer: string;

    /**
     * 厂家
     */
    public manufactor: string;

    /**
     * 品牌
     */
    public brand: string;

    /**
     * 送达地点
     */
    public deliveryLocation: string;


    /**
     * 质量等级
     */
    public qualityGrade: string;

    /**
     * 价差
     */
    public priceDifferenc: number;

    /**
     * 价差合计
     */
    public priceDifferencSum: number;

    /**
     * 进项合计
     */
    public jxTotal: number;

    /**
     * 编辑记录
     */
    public referenceRecord: string;

    /**
     * 1代表 费用定额人材机 (无法编辑 类型，名称，规格型号，单位，市场价，市场价锁定 )
     *
     */
    public edit: number;

    /**
     * 1代表 补充人材机
     *
     */
    public supplementDeRcjFlag: number;

    /**
     * 载价前原来人材机的市场价
     */
    public marketPriceBeforeLoading:number;

    /**
     * 是否勾选执行载价
     */
    public isExecuteLoadPrice:number;

    /**
     * 载价编辑弹窗的待载价格是否高亮
     */
    public highlight:boolean;

    /**
     * 临时删除标识  true 临时删除  false 不临时删除
     */
    public tempDeleteFlag: boolean;

    //三材类别
    public kindSc: string;

    //三材系数
    public transferFactor: string;

    // '不含税基期价',
    public priceBaseJournal: number;

    // '含税基期价',
    public priceBaseJournalTax: number;

    // '不含税市场价'
    public priceMarket: number;

    // '不含税市场价'公式
    public priceMarketFormula: string;

    // '含税市场价'
    public priceMarketTax: number;

    // '含税市场价'公式
    public priceMarketTaxFormula: string;

    //税率
    public taxRate: number;

    //代表补充人材机
    public isSupplement : number;

    //kind 7 排序设置
    public kind7Sort:string;

    //是否是承包人材料  (非1:不是，1：是)
    public isCbrRcj :number;

    //单位批注
    public unitPostil:any;

    //人材机单位批注 批注展示状态 0展示,1不展示
    public unitPostilState:any;

    //单项批注
    public singlePostil:any;

    //人材机单项批注 批注展示状态 0展示,1不展示
    public singlePostilState:any;

    //工程项目批注
    public constructPostil:any;

    //人材机工程项目批注 批注展示状态 0展示,1不展示
    public constructPostilState:any;


    constructor(sequenceNbr: string, recUserCode: string, recStatus: string, recDate: string, extend1: string, extend2: string, extend3: string, description: string, rcjId: string, standardId: string, pbCode: string, pbName: string, type: string, materialCode: string, kind: number, materialName: string, specification: string, unit: string, dispNo: number, resQty: number, total: number, totalNumber: number, dePrice: number, marketPrice: number, sourcePrice: string, taxRemoval: number, taxRemovalBackUp: number, libraryCode: string, donorMaterialPrice: number, donorMaterialName: string, donorSpecification: string, ifDonorMaterial: number, donorMaterialNumber: number, kindBackUp: number, materialSequenceNbr: string, ifLockStandardPrice: number, ifLockQuantity: number, provisionalEstimateName: string, provisionalEstimatePrice: number, provisionalEstimateSpecification: string, provisionalEstimateSequenceNbr: string, ifProvisionalEstimate: number, deId: string, initResQty: number, markSum: number, producer: string, manufactor: string, brand: string, deliveryLocation: string, qualityGrade: string, priceDifferenc: number, priceDifferencSum: number, jxTotal: number, referenceRecord: string, edit: number, supplementDeRcjFlag: number, marketPriceBeforeLoading: number, isExecuteLoadPrice: number, highlight: boolean, tempDeleteFlag: boolean, kindSc: string, transferFactor: string, priceBaseJournal: number, priceBaseJournalTax: number, priceMarket: number, priceMarketTax: number, taxRate: number, isSupplement: number,kind7Sort:string,isCbrRcj :number, unitPostil:any,unitPostilState:any,singlePostil:any,singlePostilState:any,constructPostil:any,constructPostilState:any) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.rcjId = rcjId;
        this.standardId = standardId;
        this.pbCode = pbCode;
        this.pbName = pbName;
        this.type = type;
        this.materialCode = materialCode;
        this.kind = kind;
        this.materialName = materialName;
        this.specification = specification;
        this.unit = unit;
        this.dispNo = dispNo;
        this.resQty = resQty;
        this.total = total;
        this.totalNumber = totalNumber;
        this.dePrice = dePrice;
        this.marketPrice = marketPrice;
        this.sourcePrice = sourcePrice;
        this.taxRemoval = taxRemoval;
        this.taxRemovalBackUp = taxRemovalBackUp;
        this.libraryCode = libraryCode;
        this.donorMaterialPrice = donorMaterialPrice;
        this.donorMaterialName = donorMaterialName;
        this.donorSpecification = donorSpecification;
        this.ifDonorMaterial = ifDonorMaterial;
        this.donorMaterialNumber = donorMaterialNumber;
        this.kindBackUp = kindBackUp;
        this.materialSequenceNbr = materialSequenceNbr;
        this.ifLockStandardPrice = ifLockStandardPrice;
        this.ifLockQuantity = ifLockQuantity;
        this.provisionalEstimateName = provisionalEstimateName;
        this.provisionalEstimatePrice = provisionalEstimatePrice;
        this.provisionalEstimateSpecification = provisionalEstimateSpecification;
        this.provisionalEstimateSequenceNbr = provisionalEstimateSequenceNbr;
        this.ifProvisionalEstimate = ifProvisionalEstimate;
        this.deId = deId;
        this.initResQty = initResQty;
        this.markSum = markSum;
        this.producer = producer;
        this.manufactor = manufactor;
        this.brand = brand;
        this.deliveryLocation = deliveryLocation;
        this.qualityGrade = qualityGrade;
        this.priceDifferenc = priceDifferenc;
        this.priceDifferencSum = priceDifferencSum;
        this.jxTotal = jxTotal;
        this.referenceRecord = referenceRecord;
        this.edit = edit;
        this.supplementDeRcjFlag = supplementDeRcjFlag;
        this.marketPriceBeforeLoading = marketPriceBeforeLoading;
        this.isExecuteLoadPrice = isExecuteLoadPrice;
        this.highlight = highlight;
        this.tempDeleteFlag = tempDeleteFlag;
        this.kindSc = kindSc;
        this.transferFactor = transferFactor;
        this.priceBaseJournal = priceBaseJournal;
        this.priceBaseJournalTax = priceBaseJournalTax;
        this.priceMarket = priceMarket;
        this.priceMarketTax = priceMarketTax;
        this.taxRate = taxRate;
        this.isSupplement = isSupplement;
        this.kind7Sort = kind7Sort;
        this.isCbrRcj = isCbrRcj;
        this.unitPostil = unitPostil;
        this.unitPostilState = unitPostilState;
        this.singlePostil = singlePostil;
        this.singlePostilState = singlePostilState;
        this.constructPostil = constructPostil;
        this.constructPostilState = constructPostilState;
    }
}

