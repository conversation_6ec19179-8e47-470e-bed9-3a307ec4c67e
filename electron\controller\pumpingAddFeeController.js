const { Controller } = require('../../core');
const { ResponseData } = require('../utils/ResponseData');

/**
 * 泵送增加费
 */
class PumpingAddFeeController extends Controller {


  /**
   * 构造函数
   * @param ctx
   */
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 获取泵送增加费弹窗数据
   */
  async getPumpingAddFeeViewData(args) {
    return ResponseData.success(await this.service.pumpingAddFeeService.getPumpingAddFeeViewData(args));
  }

  /**
   * 计算泵送增加费
   */
  async calculationPumpingAddFee(args) {
    return ResponseData.success(await this.service.pumpingAddFeeService.calculationPumpingAddFee(args));
  }

}

PumpingAddFeeController.toString = () => '[class PumpingAddFeeController]';
module.exports = PumpingAddFeeController;