<!--
 * @@Descripttion: 
 * @Author: wangru
 * @Date: 2024-04-07 10:31:58
 * @LastEditors: wangru
 * @LastEditTime: 2025-03-27 15:25:26
-->
<template>
  <div class="table-content">
    <div class="content">
      <vxe-table
        align="center"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true, isCurrent: true }"
        :edit-config="{
          trigger: 'dblclick',
          mode: 'cell',
        }"
        @edit-closed="editClosedEvent"
        @cell-click="
          cellData => {
            useCellClickEvent(cellData, null, ['ifDonorMaterial']);
          }
        "
        :cell-class-name="selectedClassName"
        class="table-edit-common table-content"
        :data="tableData"
        height="auto"
        keep-source
        :cell-style="cellStyle"
        @current-change="currentChange"
        ref="lyfxTable"
      >
        <vxe-column
          v-for="columns of showColumns"
          v-bind="columns"
        >
          <template #default="{ column, row, $columnIndex }">
            <template v-if="column.field === 'location'">
              <span> {{ row.singleName }}/{{ row.unitName }}</span>
            </template>
            <template v-else-if="
                [
                  'priceMarketTotal',
                  'marketPrice',
                  'total',
                  'priceMarketTotal',
                  'priceMarketTaxTotal',
                ].includes(column.field)
              ">
              {{ isChangeAva(row) ? '-' : row[column.field] }}
            </template>

            <template v-else-if="
                ['priceMarketTax', 'priceMarket'].includes(column.field)
              ">{{ getValueByDeType('12', row, column.field) }}</template>
            <template v-else-if="column.field === 'ifDonorMaterial'">
              {{ getDonorMaterialText(row.ifDonorMaterial) }}
            </template>
            <template v-else-if="column.field === 'donorMaterialNumber'">
              <span v-if="row.checkIsShow">{{ row.donorMaterialNumber }}</span>
            </template>

            <template v-else>{{ row[column.field] }}</template>
          </template>

          <template
            v-if="columns.slot"
            #edit="{ column, row, $columnIndex }"
          >
            <template v-if="column.field === 'materialName'">
              <vxe-input
                v-if="Number(row.edit) !== 1 && !isOtherMaterial"
                :clearable="false"
                v-model.trim="row.materialName"
                type="text"
                @blur="clear()"
              ></vxe-input>
              <span v-else>{{ row.materialName }}</span>
            </template>
            <template v-else-if="column.field === 'specification'">
              <vxe-input
                :clearable="false"
                v-model.trim="row.specification"
                type="text"
                @blur="clear()"
                v-if="Number(row.edit) !== 1 && !isOtherMaterial"
              ></vxe-input>
              <span v-else>{{ row.specification }}</span>
            </template>
            <template v-else-if="column.field === 'marketPrice'">
              <vxe-input
                v-if="isEditPrice(row,'marketPrice')"
                :clearable="false"
                v-model.trim="row.marketPrice"
                type="text"
                @blur="
                    row.marketPrice = pureNumber(row.marketPrice, 2);
                    clear();
                  "
              ></vxe-input>
              <span v-else>{{
                  isChangeAva(row) ? '-' : row.marketPrice
                }}</span>
            </template>
            <template v-else-if="column.field === 'priceMarket'">
              <template v-if="row.deStandardReleaseYear === '12'">
                <span v-if="getValueByDeType('12', row, column.field) === '/'">/</span>
                <vxe-input
                  v-else-if="isEditPrice(row,'priceMarket')
                    "
                  :clearable="false"
                  v-model.trim="row.marketPrice"
                  type="text"
                  @blur="
                      row.marketPrice = pureNumber(row.marketPrice, 2);
                      clear();
                    "
                ></vxe-input>
                <span v-else>{{
                    isChangeAva(row) ? '-' : row.marketPrice
                  }}</span>
              </template>
              <vxe-input
                v-else-if="isEditPrice(row,'priceMarket')
                  "
                :clearable="false"
                v-model.trim="row.priceMarket"
                type="text"
                @blur="
                    row.priceMarket = pureNumber(row.priceMarket, 2);
                    clear();
                  "
              ></vxe-input>
              <span v-else>{{
                  isChangeAva(row) ? '-' : row.priceMarket
                }}</span>
            </template>
            <template v-else-if="column.field === 'priceMarketTax'">
              <template v-if="row.deStandardReleaseYear === '12'">
                <span v-if="getValueByDeType('12', row, column.field) === '/'">/</span>
                <vxe-input
                  v-else-if="isEditPrice(row,'priceMarketTax')
                    "
                  :clearable="false"
                  v-model.trim="row.marketPrice"
                  type="text"
                  @blur="
                      row.marketPrice = pureNumber(row.marketPrice, 2);
                      clear();
                    "
                ></vxe-input>
                <span v-else>{{
                    isChangeAva(row) ? '-' : row.marketPrice
                  }}</span>
              </template>
              <vxe-input
                v-else-if="isEditPrice(row,'priceMarketTax')
                  "
                :clearable="false"
                v-model.trim="row.priceMarketTax"
                type="text"
                @blur="
                    row.priceMarketTax = pureNumber(row.priceMarketTax, 2);
                    clear();
                  "
              ></vxe-input>
              <span v-else>{{
                  isChangeAva(row) ? '-' : row.priceMarketTax
                }}</span>
            </template>
            <template v-else>
              <vxe-input
                :clearable="false"
                v-model.trim="row[column.field]"
                type="text"
                @blur="clear()"
              ></vxe-input>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed, reactive, toRaw } from 'vue';
import feePro from '@/api/feePro';
import jieSuanApi from '@/api/jieSuanApi';
import { projectDetailStore } from '@/store/projectDetail';
import { useCellClick } from '@/hooks/useCellClick';
import { pureNumber } from '@/utils/index';
import { message } from 'ant-design-vue';
import { columnWidth } from '@/hooks/useSystemConfig';
import { useFormatTableColumns } from '@/hooks/useFormatTableColumns.js';
import ObjectUtils from '@/components/qdQuickPricing/utils/ObjectUtils';
import NumberUtil from '@/components/qdQuickPricing/utils/NumberUtil';
import getTableColumns, {
  isDeType,
  getDonorMaterialText,
} from './sourceColumn';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const emits = defineEmits(['getUpList']);
const props = defineProps(['showInfo', 'unitIdList']);

const store = projectDetailStore();
let tableData = ref([]);
let lyfxTable = ref();
watch(
  () => props.showInfo,
  () => {
    setTableInfo();
  }
);
let showColumns = ref([]);
const setTableInfo = () => {
  showColumns.value = getTableColumns().map(item => {
    item.width = columnWidth(item.width);
    return item;
  });
  getTableData();
};
onMounted(() => {
  setTableInfo();
});
const isEditPrice = (row, column) => {
  //判断市场价-含税/不含税市场价  税率是否可以编辑
  console.log(isPartEdit.value, isOtherMaterial.value);
  let isFlag =
    row.ifLockStandardPrice !== 1 &&
    isPartEdit.value &&
    !(row.markSum === 1 && [1, 2].includes(Number(row.levelMark))) &&
    Number(row.edit) !== 1 &&
    !isChangeAva(row) &&
    !isOtherMaterial.value;
  let isEdit =
    ['marketPrice', 'priceMarket', 'priceMarketTax'].includes(column) && isFlag;
  return isEdit;
};
// 基期价、市场价为“-
const isChangeAva = row => {
  return (
    store.deStandardReleaseYear === '22' &&
    ![null, undefined].includes(row.isChangeAva) &&
    Number(row.isChangeAva) === 0
  );
};
let oldTablesData = reactive([]);
const getTableData = async () => {
  const formdata = {
    constructId: store.currentTreeGroupInfo?.constructId,
    rcjSequenceNbr: props.showInfo?.sequenceNbr,
    unitIdList: [...props.unitIdList],
  };
  // console.log('---------来源分析', formdata);
  if (store.currentTreeInfo.levelType === 2) {
    formdata.singleId = store.currentTreeInfo?.id; //单项ID
  }
  let apiName =
    store.currentTreeInfo.levelType === 1 ? 'rcjFromUnit' : 'singleRcjFromUnit';
  await feePro[apiName](formdata).then(res => {
    if (res.status === 200) {
      console.log('---------来源分析弹框结果', formdata, apiName, res.result);
      if (res.result?.length > 0) {
        let finallyData = setCacheData(res.result);
        tableData.value = [...finallyData];
        //判断是否有缓存数据设置颜色
        oldTablesData = JSON.parse(JSON.stringify(res.result));
      } else {
        tableData.value = [];
        oldTablesData = [];
      }
    }
  });
};
const setCacheData = data => {
  let obj = { ...store.humanUpdataData };
  let list = [];
  if (obj?.sourcePriceData) {
    data.map(a => {
      let target = obj.sourcePriceData.find(
        b => b.sequenceNbr === a.sequenceNbr
      );
      if (target) {
        let newRow = { ...a, ...target.constructProjectRcj };
        newRow.isBgCulumn = Object.keys(target.constructProjectRcj);
        if (
          newRow.isBgCulumn.includes('marketPrice') &&
          +store.deType === 22 &&
          +store.taxMade === 1
        ) {
          newRow.setBgCulumn = setRowBgCulumn(
            newRow.isBgCulumn,
            'marketPrice',
            'priceMarket'
          );
        } else {
          newRow.setBgCulumn = [...newRow.isBgCulumn];
        }
        newRow.isChange = true;
        list.push(newRow);
      } else {
        list.push(a);
      }
    });
  } else {
    list = [...data];
  }
  return list;
};
const clear = () => {
  //清除编辑状态
  const $table = lyfxTable.value;
  $table.clearEdit();
};
/**
 * 是否在含税/不含税市场价格字段操作市场价字段，只在工程级别得12定额数据下存在
 */
const isPriceMarketOperateMarketPrice = (row, field) => {
  const taxMade = 'priceMarket' === field ? 1 : 0;
  return (
    isDeType('12', row.deStandardReleaseYear) &&
    ['priceMarketTax', 'priceMarket'].includes(field) &&
    Number(store.taxMade) === taxMade
  );
};
/**
 * 是否市场价字段
 * 工程项目级别，12定额数据市场价显示和操作是在含税市场价或者不含税市场价字段上操作得
 * @param row
 * @param field
 */
const isMarketPriceField = (row, field) => {
  return 'marketPrice' === field || isPriceMarketOperateMarketPrice(row, field);
};
const editClosedEvent = e => {
  // 市场价修改联动计算
  const { $table, row, column } = e;
  let field = column.field;
  // 市场价以前是数字类型，编辑框后返回的是字符串，需要处理
  if (isMarketPriceField(row, field)) {
    field = 'marketPrice';
    row[field] = +row[field];
  }
  let value = row[field];
  if (
    ['marketPrice', 'priceMarket', 'priceMarketTax', 'taxRate'].includes(
      field
    ) &&
    (value < 0 || value == '')
  ) {
    $table.revertData(row, field);
    return;
  }
  switch (field) {
    case 'materialName':
    case 'specification':
    case 'producer':
    case 'manufactor':
    case 'brand':
    case 'deliveryLocation':
    case 'qualityGrade':
      //输入长度不超过2000字符
      if (value && value.length > 2000) {
        message.warn('输入内容长度应在2000字符以内');
        row[field] = value.slice(0, 2000);
      }
      break;
  }
  if (field === 'marketPrice' && value > 0) {
    if (row.marketPrice.length > 20) row.marketPrice = value.slice(0, 20);
    row.total = (row.totalNumber * value).toFixed(2);
    row.priceDifferenc = (
      Number(row.marketPrice) - Number(row.dePrice)
    ).toFixed(2);
    row.priceDifferencSum = (row.totalNumber * row.priceDifferenc).toFixed(2);
  }
  if (['priceMarket', 'priceMarketTax'].includes(field)) {
    if (field === 'priceMarket' && !ObjectUtils.is_Undefined(value)) {
      //不含税市场价
      row.priceMarketTax = NumberUtil.numberScale2(
        NumberUtil.multiply(
          0.01,
          NumberUtil.multiply(value, NumberUtil.add(100, row.taxRate))
        )
      );
    }
    //含税市场价
    if (field === 'priceMarketTax' && !ObjectUtils.is_Undefined(value)) {
      //不含税市场价
      row.priceMarket = NumberUtil.numberScale2(
        NumberUtil.multiply(
          100,
          NumberUtil.divide(value, NumberUtil.add(100, row.taxRate))
        )
      );
    }
  }
  if (
    ['priceMarket', 'priceMarketTax', 'taxRate', 'marketPrice'].includes(field)
  ) {
    row.sourcePrice = '自行询价';
  }
  row.isChange = true;
  console.log(row);
  setBgColorList(row, column);
  let updateList = getUpdateList();
  console.log('updateList', updateList);
  setProUpdate(updateList);
};
const getUpdateList = () => {
  let list = [];
  let updateList = tableData.value.filter(a => a.isBgCulumn?.length > 0);
  updateList.map(item => {
    let obj = {
      unitId: item.unitId,
      singleId: item.singleId,
      constructProjectRcj: {},
      sequenceNbr: item.sequenceNbr,
    };
    let constructProjectRcj = {};
    for (let a of item.isBgCulumn) {
      constructProjectRcj[a] = item[a];
    }
    obj.constructProjectRcj = constructProjectRcj;
    list.push(obj);
  });
  return list;
};
const setProUpdate = updataData => {
  if (updataData.length > 0) {
    let obj = { ...store.humanUpdataData };
    if (obj?.sourcePriceData) {
      // debugger;
      updataData.map(a => {
        let target = obj.sourcePriceData.find(
          b => b.sequenceNbr === a.sequenceNbr
        );
        if (target) {
          let idx = obj.sourcePriceData.findIndex(
            b => b.sequenceNbr === a.sequenceNbr
          );
          target = { ...target, ...a };
          obj.sourcePriceData[idx] = { ...target };
        } else {
          obj.sourcePriceData.push(a);
        }
      });
    } else {
      obj.sourcePriceData = updataData;
    }
    store.SET_HUMAN_UPDATA_DATA({
      ...obj,
      name: 'unify-humanMachineSummary',
      isEdit: true,
    });
  } else if (
    updataData.length === 0 &&
    !store.humanUpdataData?.updataData &&
    !store.humanUpdataData?.adjustFactor
  ) {
    if (
      !store.humanUpdataData?.sourcePriceData ||
      (store.humanUpdataData?.sourcePriceData &&
        store.humanUpdataData.sourcePriceData.every(
          b => JSON.stringify(b.constructProjectRcj) === '{}'
        ))
    ) {
      store.SET_HUMAN_UPDATA_DATA(null);
    }
  }
};
const setBgColorList = (row, column) => {
  let field = column.field;
  const oldField = column.field;
  if (isMarketPriceField(row, field)) {
    field = 'marketPrice';
    row[field] = +row[field];
  }
  let value = row[field];
  let tar = oldTablesData.find(a => a.sequenceNbr === row.sequenceNbr);
  if (tar) {
    let flag = ['marketPrice', 'priceMarket', 'priceMarketTax'].includes(field);
    console.log(
      !(tar[field] && value),
      tar[field],
      value,
      (flag && tar[field] / 1 != value / 1) || (!flag && tar[field] != value)
    );
    let isAllNull =
      [null, undefined, ''].includes(tar[field]) &&
      [null, undefined, ''].includes(value);
    if (
      !isAllNull &&
      ((flag && tar[field] / 1 != value / 1) || (!flag && tar[field] != value))
    ) {
      row.isBgCulumn =
        row?.isBgCulumn?.length > 0 ? [field, ...row.isBgCulumn] : [field];
    } else {
      if (row?.isBgCulumn?.length > 0) {
        // console.log(store.humanUpdataData);
        // debugger;
        let index = row.isBgCulumn?.indexOf(field);
        if (index !== -1) row.isBgCulumn.splice(index, 1);
        if (store.humanUpdataData?.sourcePriceData) {
          //将存储的数据也删除
          let target = store.humanUpdataData.sourcePriceData.findIndex(
            a =>
              a.sequenceNbr === row.sequenceNbr &&
              a.constructProjectRcj.hasOwnProperty(field)
          );
          if (target != -1)
            delete store.humanUpdataData.sourcePriceData[target][
              'constructProjectRcj'
            ][field];
          let emptyObjIdx = store.humanUpdataData.sourcePriceData.findIndex(
            b => JSON.stringify(b.constructProjectRcj) === '{}'
          );
          if (emptyObjIdx !== -1)
            store.humanUpdataData.sourcePriceData.splice(emptyObjIdx, 1);
        }
      }
    }
  }
  if (row?.isBgCulumn?.length > 0 && field && oldField && field !== oldField) {
    row.setBgCulumn = setRowBgCulumn(row.isBgCulumn, field, oldField);
  } else {
    row.setBgCulumn = row?.isBgCulumn ? [...row.isBgCulumn] : [];
  }
};
const setRowBgCulumn = (isBgCulumn, field, oldField) => {
  let list = [...isBgCulumn];
  let index = list.indexOf(field);
  list.splice(index, 1);
  return [...list, oldField];
};
let currentInfo = ref(null);
const isPartEdit = computed(() => {
  console.log(' isCurrent.value', currentInfo.value);
  return !(
    [
      'QTCLFBFB',
      '34000001-2',
      'J00004',
      'J00031',
      'J00031',
      'C11384',
      'C00007',
      'C000200',
    ].includes(currentInfo.value?.materialCode) &&
    currentInfo.value?.unit === '%'
  );
});
const currentChange = ({ row }) => {
  currentInfo.value = { ...toRaw(row) };
};
const isOtherMaterial = computed(() => {
  const { materialCode } = currentInfo.value || {};
  return [
    'QTCLFBFB',
    '34000001-2',
    'J00004',
    'J00031',
    'J00031',
    'C11384',
    'C00007',
    'C000200',
  ].includes(materialCode);
});
const cellStyle = ({ row, column }) => {
  if (row.setBgCulumn?.length > 0) {
    if (row.setBgCulumn.includes(column.field)) {
      return {
        backgroundColor: '#F5FFDB !important',
      };
    }
  }
};
const getValueByDeType = (deType, row, field = '') => {
  if (isDeType(deType, row.deStandardReleaseYear)) {
    if (['priceMarketTax', 'priceMarket'].includes(field)) {
      const taxMade = 'priceMarket' === field ? 1 : 0;
      if (Number(store.taxMade) === taxMade) {
        return isChangeAva(row) ? '-' : row.marketPrice;
      }
    }
    return '/';
  }
  return isChangeAva(row) ? '-' : row[field];
};
</script>
<style lang="scss" scoped>
.table-content {
  .content {
    width: 100%;
    height: calc(100% - 45px);
  }
}
</style>
