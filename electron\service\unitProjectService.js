const {Service} = require("../../core");
const {UnitProject} = require("../model/UnitProject");

const {getRepository  } =require('typeorm');
const {Snowflake} = require("../utils/Snowflake");
const {OrganizationInstructions} = require("../model/OrganizationInstructions");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const zjfx_jyjs_12 = require("../jsonData/12_zjfx_jyjs.json");
const zjfx_ybjs_12 = require("../jsonData/12_zjfx_ybjs.json");
const zjfx_jyjs_22 = require("../jsonData/22_zjfx_jyjs.json");
const zjfx_ybjs_22 = require("../jsonData/22_zjfx_ybjs.json");
const ProjectLevelConstant = require("../enum/ProjectLevelConstant");
const ConstructBiddingTypeConstant = require("../enum/ConstructBiddingTypeConstant");
const TaxCalculationMethodEnum = require("../enum/TaxCalculationMethodEnum");
const ConstantUtil = require("../enum/ConstantUtil");
const {ObjectUtil} = require("../../common/ObjectUtil");
const {MainMaterialSetting} = require("../model/MainMaterialSetting");
const {RcjClassificationTable} = require("../model/RcjClassificationTable ");
const {CostAnalysisVO} = require("../model/CostAnalysisVO");
const {CostAnalysisSingleVO} = require("../model/CostAnalysisSingleVO");
const {NumberUtil} = require("../utils/NumberUtil");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {CostAnalysisUnitVO} = require("../model/CostAnalysisUnitVO");
const {TAX_MODE_1} = require("../enum/ProjectLevelConstant");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ResponseData} = require("../utils/ResponseData");
const {BaseGsjRate} = require("../model/BaseGsjRate");
const {BaseCSLB, BaseCSLB2022} = require("../model/BaseCSLB");
const { dialog } = require('electron');
class UnitProjectService extends Service{

    constructor(ctx) {
        super(ctx);
    }

    getMainDeLibrary(constructMajorType,is22) {
        let tableSuffix = "base_fee_file_project \n";
        if(is22)tableSuffix="base_fee_file_project_2022 \n";
        let sql = "select distinct f.library_code as libraryCode,\n" +
            "       library_name as libraryName, \n" +
            "       default_de_flag as defaultDeFlag  from\n" +
            tableSuffix+
            "f left join base_speciality_de_fee_relation s\n" +
            "on f.library_code = s.library_code\n" +
            "where f.unit_project_name = ?";
        let sqlres = this.app.betterSqlite3DataSource.prepare(sql).all(constructMajorType);
        let hasDefault = false;
        for (let i = 0 ; i < sqlres.length ; ++i) {
            if (sqlres[i].defaultDeFlag && sqlres[i].defaultDeFlag === 1) {
                hasDefault = true;
            }
        }
        if (!hasDefault) {
            sqlres[0].defaultDeFlag = 1;
        }
        return sqlres;
    }

    async getSecondInstallationProjectName(constructMajorType, deStandard) {
        let is2022 = deStandard === ConstantUtil.DE_STANDARD_22;
        let result = await this.app.appDataSource.getRepository(is2022?BaseCSLB2022:BaseCSLB).find({
            where: {
                unitProjectName: constructMajorType
            }
        });
        if (!ObjectUtils.isEmpty(result)){
            result.sort((a, b) => a.sortNo - b.sortNo);
        }


        return result;
    }

    //根据id查询单位工程数据
    async findOneById(id) {
        let unitProjectRepository = getRepository(UnitProject);
        let res = await unitProjectRepository.findOneById(id);
        return res;
    }
    async getConstructMajorTypeByUnitId(args){
        let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
        return unit.constructMajorType;
    }

    /**
     * 获取单位工程设置主要材料
     * @param args
     * @returns {Promise<*>}
     */
    async getUnitMainMaterialSetting(args){
        let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
        return unit.mainMaterialSetting;
    }

    /**
     * 修改 单位工程设置主要材料
     * @param args
     * @returns {Promise<void>}
     */
    async updateUnitMainMaterialSetting(args){

        //应用范围
        let appliedRange = args.appliedRange;
        if (ObjectUtils.isEmpty(appliedRange)){
            return ;
        }


        let unitArray = new Array();
        if (appliedRange ==1){
            //当前单位工程
            let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
            unitArray.push(unit);

        }else if (appliedRange==2){
            //同专业单位工程
            let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
            let constructMajorType = unit.constructMajorType;

            let unitList = PricingFileFindUtils.getUnitList(args.constructId);
            for (let unitListKey of unitList) {
                if (unitListKey.constructMajorType ==constructMajorType){
                    unitArray.push(unitListKey);
                }
            }
        }else if (appliedRange==3){
            //整个工程项目
            let unitList = PricingFileFindUtils.getUnitList(args.constructId);
            for (let unitListKey of unitList) {
                unitArray.push(unitListKey);
            }
        }

        if (ObjectUtils.isEmpty(unitArray)){
            return ;
        }

        for (let unitArrayElement of unitArray) {
            let mainMaterialSetting = new MainMaterialSetting();

            //设置方式  0 自动设置   1手动设置
            let type = args.type;
            if (type ==0){
                //自动设置
                mainMaterialSetting.type =type;
                mainMaterialSetting.pattern =args.pattern;
                mainMaterialSetting.proportion =args.proportion;

            }else if (type ==1) {
                //手动设置
                mainMaterialSetting.type =type;
                mainMaterialSetting.materialCodeList =args.materialCodeList;
            }

            unitArrayElement.mainMaterialSetting = mainMaterialSetting;
        }




        /*let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
        let mainMaterialSetting = unit.mainMaterialSetting;
        //设置方式  0 自动设置   1手动设置
        let type = args.type;
        if (type ==0){
            //自动设置
            mainMaterialSetting.type =type;
            mainMaterialSetting.pattern =args.pattern;
            mainMaterialSetting.proportion =args.proportion;

        }else if (type ==1) {
            //手动设置
            mainMaterialSetting.type =type;
            mainMaterialSetting.materialCodeList =args.materialCodeList;
        }*/

    }

    /**
     * 新增单位工程人材机汇总 右键分类
     * @param args
     * @returns {Promise<void>}
     */
    async addRcjClassificationTable(args){
        let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
        if (ObjectUtils.isEmpty(unit)){
            return ;
        }

       let newRcjClassificationTable = new RcjClassificationTable();

        newRcjClassificationTable.name = args.name;
        newRcjClassificationTable.rcjType = args.rcjType;
        newRcjClassificationTable.rcjDetailType = args.rcjDetailType;
        newRcjClassificationTable.donorMaterial = args.donorMaterial;
        newRcjClassificationTable.includeYuan = args.includeYuan;
        newRcjClassificationTable.includeZanGu = args.includeZanGu;
        newRcjClassificationTable.outputReport = args.outputReport;

        if (ObjectUtils.isEmpty(unit.rcjClassificationTableList)){
            newRcjClassificationTable.kind = 30;
            let array = new Array();
            array.push(newRcjClassificationTable);
            unit.rcjClassificationTableList = array;
        }else {
            let rcjClassificationTableList = unit.rcjClassificationTableList;

            let maxKind = rcjClassificationTableList.reduce((max, obj) => Math.max(max, obj.kind), 0);
            newRcjClassificationTable.kind = maxKind+1;
            rcjClassificationTableList.push(newRcjClassificationTable);
            unit.rcjClassificationTableList = rcjClassificationTableList;
        }

    }



    /**
     * 修改单位工程人材机汇总 右键分类
     * @param args
     * @returns {Promise<void>}
     */
    async updateRcjClassificationTable(args){
        let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
        if (ObjectUtils.isEmpty(unit) && ObjectUtils.isEmpty(unit.rcjClassificationTableList)){
            return ;
        }
        let rcjClassificationTableList = unit.rcjClassificationTableList;

        let rcjClassificationTable = rcjClassificationTableList.find(i=>i.kind == args.kind);

        if (ObjectUtils.isEmpty(rcjClassificationTable)){
            return ;
        }

        rcjClassificationTable.name = args.name;
        rcjClassificationTable.rcjType = args.rcjType;
        rcjClassificationTable.rcjDetailType = args.rcjDetailType;
        rcjClassificationTable.donorMaterial = args.donorMaterial;
        rcjClassificationTable.includeYuan = args.includeYuan;
        rcjClassificationTable.includeZanGu = args.includeZanGu;
        rcjClassificationTable.outputReport = args.outputReport;

    }


    /**
     * 删除单位工程人材机汇总 右键分类
     * @param args
     * @returns {Promise<void>}
     */
    async deleteRcjClassificationTable(args){
        let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
        if (ObjectUtils.isEmpty(unit)){
            return ;
        }

        let rcjClassificationTableList = unit.rcjClassificationTableList;

        unit.rcjClassificationTableList = rcjClassificationTableList.filter(i=>i.kind!=args.kind);

    }


    //新增单位工程
    async saveUnitProject(args) {
        // //rg.levelType, arg.constructId,arg.singleId, arg.unitId
        //
        // //项目工程ID
        // let constructId = args.constructId;
        // let singleId = args.singleId;
        // let unitId = Snowflake.nextId();
        // let upName = args.unitProject.upName;
        // let constructMajorType = args.unitProject.constructMajorType;
        // let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
        // let {constructProjectService} = this.service;
        // //单位工程项目类型
        // let unitProject =await constructProjectService.initUnit(projectObj,singleId,unitId,upName,constructMajorType);
        // //创建单位级别的工程基本信息和工程特征
        // constructProjectService.initProjectOrUnitData(unitProject, 3)
        // //初始化编制说明
        // constructProjectService.initProjectOrUnitBZSM(3,unitProject);
        // //初始分部分项
        // constructProjectService.initUnitFb(unitProject);

       // await PricingFileWriteUtils.writeUnitProJect(unitProject,constructId,singleId);
    }

    /**
     * 删除单位工程
     * @param id
     * @return {Promise<DeleteResult>}
     */
    async deleteUnitProject(arg) {
        let constructId = arg.constructId;
        let projectObj = await PricingFileFindUtils.getProjectObjById(constructId);

        if (2 == projectObj.biddingType){
            let unitProjects = projectObj.unitProjects.filter((item) => item.sequenceNbr !== arg.unitId);
            projectObj.unitProjects = unitProjects;
        }else {
            let singleProject = PricingFileFindUtils.getOneFromSingleProjects(projectObj.singleProjects, arg.singleId);
            let unitProjects = singleProject.unitProjects.filter((item) => item.sequenceNbr !== arg.unitId);
            singleProject.unitProjects = unitProjects;
        }

        //todo 对于删除后得项目数据进行汇总计算


    }

    // 修改单位工程名称
    updateUnitProject(arg) {
        let constructId = arg.constructId;
        let singleId = arg.singleId;
        let unitId = arg.unitId;
        let unitName = arg.unitName;
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        if (ObjectUtils.is_Undefined(unit) || ObjectUtils.isNull(unit)){
            return ResponseData.fail("修改失败");
        }
        unit.upName = unitName;

        let unitJBXXProjectOverview = unit.unitJBXX;
        let upName = unitJBXXProjectOverview.find((item ) => item.addFlag === 0 &&item.name ==='单位工程名称');
        upName.remark = unitName;
        return ResponseData.success();
    }

    async getCostAnalysisDataContainUnits(singleObj,dispNo) {
        //存放单项工程数据
        let singleCostAnalysisVO= new CostAnalysisSingleVO();
        singleCostAnalysisVO.dispNo = dispNo;
        singleCostAnalysisVO.projectName = singleObj.projectName;
        singleCostAnalysisVO.sequenceNbr = singleObj.sequenceNbr;
        let unitProjects = singleObj.unitProjects;
        let gczj = 0;
        let fbfxhj = 0;
        let fbfxrgf = 0;
        let fbfxclf = 0;
        let fbfxjxf = 0;
        let fbfxzcf = 0;
        let fbfxlr = 0;
        let fbfxglf = 0;
        let fbfxzgj = 0;
        let csxhj = 0;
        let djcsxhj = 0;
        let djcsxrgf = 0;
        let djcsxclf = 0;
        let djcsxjxf = 0;
        let djcsxzcf = 0;
        let djcsxglf = 0;
        let djcsxlr  = 0;
        let zjcsxhj  = 0;
        let zjcsxrgf = 0;
        let zjcsxclf = 0;
        let zjcsxjxf = 0;
        let zjcsxzcf = 0;
        let zjcsxglf = 0;
        let zjcsxlr  = 0;
        let qtxmhj = 0;
        let qtxmzlje    = 0;
        let qtxmzygczgj = 0;
        let qtxmzcbfwf  = 0;
        let qtxmjrg     = 0;
        let gfee = 0;
        let safeFee = 0;
        let jxse = 0;
        let xxse = 0;
        let zzsynse = 0;
        let fjse = 0;
        let sj = 0;
        let sbfsj = 0;
        let unitcost = 0;
        //存放所有单位对应的造价分析
        let unitCostAnalysis =await this.generateSingleCostAnalysisData(unitProjects,singleCostAnalysisVO.dispNo);
        if(!ObjectUtils.isEmpty(unitProjects)){
            for (let j =0 ;j<unitProjects.length ;j++) {
                let unitProject = unitProjects[j];
                //单项合计计算
                gczj = NumberUtil.add(gczj,unitProject.gczj);
                fbfxhj =NumberUtil.add(fbfxhj, unitProject.fbfxhj);
                fbfxrgf = NumberUtil.add(fbfxrgf,unitProject.fbfxrgf);
                fbfxclf = NumberUtil.add(fbfxclf,unitProject.fbfxclf);
                fbfxjxf = NumberUtil.add(fbfxjxf,unitProject.fbfxjxf);
                fbfxzcf = NumberUtil.add(fbfxzcf,unitProject.fbfxzcf);
                fbfxlr  = NumberUtil.add(fbfxlr ,unitProject.fbfxlr );
                fbfxglf = NumberUtil.add(fbfxglf,unitProject.fbfxglf);
                fbfxzgj = NumberUtil.add(fbfxzgj,unitProject.fbfxzgj);
                csxhj = NumberUtil.add(csxhj,unitProject.csxhj);
                djcsxhj  = NumberUtil.add(djcsxhj ,unitProject.djcsxhj );
                djcsxrgf = NumberUtil.add(djcsxrgf,unitProject.djcsxrgf);
                djcsxclf = NumberUtil.add(djcsxclf,unitProject.djcsxclf);
                djcsxjxf = NumberUtil.add(djcsxjxf,unitProject.djcsxjxf);
                djcsxzcf = NumberUtil.add(djcsxzcf,unitProject.djcsxzcf);
                djcsxglf = NumberUtil.add(djcsxglf,unitProject.djcsxglf);
                djcsxlr  = NumberUtil.add(djcsxlr ,unitProject.djcsxlr );
                zjcsxhj  = NumberUtil.add(zjcsxhj ,unitProject.zjcsxhj );
                zjcsxrgf = NumberUtil.add(zjcsxrgf,unitProject.zjcsxrgf);
                zjcsxclf = NumberUtil.add(zjcsxclf,unitProject.zjcsxclf);
                zjcsxjxf = NumberUtil.add(zjcsxjxf,unitProject.zjcsxjxf);
                zjcsxzcf = NumberUtil.add(zjcsxzcf,unitProject.zjcsxzcf);
                zjcsxglf = NumberUtil.add(zjcsxglf,unitProject.zjcsxglf);
                zjcsxlr  = NumberUtil.add(zjcsxlr ,unitProject.zjcsxlr );
                qtxmhj = NumberUtil.add(qtxmhj,unitProject.qtxmhj);
                qtxmzlje    = NumberUtil.add(qtxmzlje   ,unitProject.qtxmzlje   );
                qtxmzygczgj = NumberUtil.add(qtxmzygczgj,unitProject.qtxmzygczgj);
                qtxmzcbfwf  = NumberUtil.add(qtxmzcbfwf ,unitProject.qtxmzcbfwf );
                qtxmjrg     = NumberUtil.add(qtxmjrg    ,unitProject.qtxmjrg    );
                gfee = NumberUtil.add(gfee,unitProject.gfee);
                safeFee = NumberUtil.add(safeFee,unitProject.safeFee);
                jxse = NumberUtil.add(jxse,unitProject.jxse);
                xxse = NumberUtil.add(xxse,unitProject.xxse);
                zzsynse = NumberUtil.add(zzsynse,unitProject.zzsynse);
                fjse = NumberUtil.add(fjse,unitProject.fjse);
                sj = NumberUtil.add(sj,unitProject.sj);
                sbfsj = NumberUtil.add(sbfsj,unitProject.sbfsj);
                // average = NumberUtil.add(average,unitProject.average);
                // unitcost = NumberUtil.add(unitcost,unitProject.unitcost);
            }
        }
        singleCostAnalysisVO.gczj = gczj;
        singleCostAnalysisVO.fbfxhj = fbfxhj;
        singleCostAnalysisVO.fbfxrgf = fbfxrgf;
        singleCostAnalysisVO.fbfxclf = fbfxclf;
        singleCostAnalysisVO.fbfxjxf = fbfxjxf;
        singleCostAnalysisVO.fbfxzcf = fbfxzcf;
        singleCostAnalysisVO.fbfxlr  = fbfxlr ;
        singleCostAnalysisVO.fbfxglf = fbfxglf;
        singleCostAnalysisVO.fbfxzgj = fbfxzgj;
        singleCostAnalysisVO.csxhj = csxhj;
        singleCostAnalysisVO.djcsxhj  = djcsxhj ;
        singleCostAnalysisVO.djcsxrgf = djcsxrgf;
        singleCostAnalysisVO.djcsxclf = djcsxclf;
        singleCostAnalysisVO.djcsxjxf = djcsxjxf;
        singleCostAnalysisVO.djcsxzcf = djcsxzcf;
        singleCostAnalysisVO.djcsxglf = djcsxglf;
        singleCostAnalysisVO.djcsxlr  = djcsxlr ;
        singleCostAnalysisVO.zjcsxhj  = zjcsxhj ;
        singleCostAnalysisVO.zjcsxrgf = zjcsxrgf;
        singleCostAnalysisVO.zjcsxclf = zjcsxclf;
        singleCostAnalysisVO.zjcsxjxf = zjcsxjxf;
        singleCostAnalysisVO.zjcsxzcf = zjcsxzcf;
        singleCostAnalysisVO.zjcsxglf = zjcsxglf;
        singleCostAnalysisVO.zjcsxlr  = zjcsxlr ;
        singleCostAnalysisVO.qtxmhj = qtxmhj;
        singleCostAnalysisVO.qtxmzlje   = qtxmzlje   ;
        singleCostAnalysisVO.qtxmzygczgj= qtxmzygczgj;
        singleCostAnalysisVO.qtxmzcbfwf = qtxmzcbfwf ;
        singleCostAnalysisVO.qtxmjrg    = qtxmjrg    ;
        singleCostAnalysisVO.gfee = gfee;
        singleCostAnalysisVO.safeFee = safeFee;
        singleCostAnalysisVO.jxse = jxse;
        singleCostAnalysisVO.xxse = xxse;
        singleCostAnalysisVO.zzsynse = zzsynse;
        singleCostAnalysisVO.fjse = fjse;
        singleCostAnalysisVO.sj = sj;
        singleCostAnalysisVO.sbfsj = sbfsj;
        singleCostAnalysisVO.average = singleObj.average;
        singleCostAnalysisVO.unitcost = singleObj.unitcost;
        singleCostAnalysisVO.childrenList= unitCostAnalysis;
        singleCostAnalysisVO.levelType = ProjectLevelConstant.single;

        return singleCostAnalysisVO;
    }

    /**
     * 工程项目造价分析
     */
    async generateConstructCostAnalysisData(projectObj){
        let singleProjects = projectObj.singleProjects;
        let costAnalysisVOList = new Array();
        // 获取工程项目下所有的单项造价分析数据
        if(singleProjects){
            for (let i =0 ;i<singleProjects.length ;i++) {
                //获取单项工程对应的造价分析
                let singleCostAnalysis =await this.getCurrentSingleCostAnalysisData(singleProjects[i],i+1+"");
                //工程项目级别，整合12、22定额标准展示全量的数据列，其中22定额的单位工程无规费，其数据行对应的规费列展示为“/”
                // if(projectObj.deStandardReleaseYear == ConstantUtil.DE_STANDARD_22){
                //     singleCostAnalysis.gfee="/";
                // }
                costAnalysisVOList.push(singleCostAnalysis);
            }
        }
        return costAnalysisVOList;
    }

    /**
     * 得到当前子单项的造价分析数据
     * @param singleObj
     * @param dispNo  当前单项的序号
     * @returns {Promise<CostAnalysisSingleVO|*[]>}
     */
    async getCurrentSingleCostAnalysisData(singleObj,dispNo){

        //遍历到最底层有单位的子单项  返回该子单项
        if (singleObj.unitProjects != null && singleObj.unitProjects.length > 0) {
            return await this.getCostAnalysisDataContainUnits(singleObj,dispNo);
        }
        let singleProjects = singleObj.subSingleProjects;
        //如果单项既没有单位也没有子单项  说明为空
        if (singleProjects == null || singleProjects.length == 0) {
            return await this.getCostAnalysisDataContainUnits(singleObj,dispNo);
        }
        //对于中间的子单项  进行累计
        let dataArray = [];
        let subDispNo = 0;
        for (let i = 0; i < singleProjects.length; i++) {
            subDispNo = dispNo+"."+(i+1);
            //拿到子单项以后 进行该一层级的累计  最后返回该一层级的单项数据
            let subSingleData = await this.getCurrentSingleCostAnalysisData(singleProjects[i],subDispNo);
            dataArray.push(subSingleData);
        }
        return await this.getSubSingleCostAnalysisDataTotal(dataArray,dispNo,singleObj);
    }


    /**
     * 获取单项下的工程项目
     * @param unitProjects
     * @returns {Promise<CostAnalysisSingleVO[]>}
     */
    async generateSingleCostAnalysisData(unitProjects,singDispNo){
        //存放所有单位对应的造价分析
        let unitCostAnalysis = new Array();
        if(!ObjectUtils.isEmpty(unitProjects)){
            for (let j =0 ;j<unitProjects.length ;j++) {
                let costAnalysisSingleVO = new CostAnalysisSingleVO();
                let unitProject = unitProjects[j];
                costAnalysisSingleVO.gczj = unitProject.gczj;
                costAnalysisSingleVO.fbfxhj = unitProject.fbfxhj;
                costAnalysisSingleVO.fbfxrgf = unitProject.fbfxrgf;
                costAnalysisSingleVO.fbfxclf = unitProject.fbfxclf;
                costAnalysisSingleVO.fbfxjxf = unitProject.fbfxjxf;
                costAnalysisSingleVO.fbfxzcf = unitProject.fbfxzcf;
                costAnalysisSingleVO.fbfxlr  = unitProject.fbfxlr ;
                costAnalysisSingleVO.fbfxglf = unitProject.fbfxglf;
                costAnalysisSingleVO.fbfxzgj = unitProject.fbfxzgj;
                costAnalysisSingleVO.csxhj = unitProject.csxhj;
                costAnalysisSingleVO.djcsxhj  = unitProject.djcsxhj ;
                costAnalysisSingleVO.djcsxrgf = unitProject.djcsxrgf;
                costAnalysisSingleVO.djcsxclf = unitProject.djcsxclf;
                costAnalysisSingleVO.djcsxjxf = unitProject.djcsxjxf;
                costAnalysisSingleVO.djcsxzcf = unitProject.djcsxzcf;
                costAnalysisSingleVO.djcsxglf = unitProject.djcsxglf;
                costAnalysisSingleVO.djcsxlr  = unitProject.djcsxlr ;
                costAnalysisSingleVO.zjcsxhj  = unitProject.zjcsxhj ;
                costAnalysisSingleVO.zjcsxrgf = unitProject.zjcsxrgf;
                costAnalysisSingleVO.zjcsxclf = unitProject.zjcsxclf;
                costAnalysisSingleVO.zjcsxjxf = unitProject.zjcsxjxf;
                costAnalysisSingleVO.zjcsxzcf = unitProject.zjcsxzcf;
                costAnalysisSingleVO.zjcsxglf = unitProject.zjcsxglf;
                costAnalysisSingleVO.zjcsxlr  = unitProject.zjcsxlr ;
                costAnalysisSingleVO.qtxmhj = unitProject.qtxmhj;
                costAnalysisSingleVO.qtxmzlje    = unitProject.qtxmzlje   ;
                costAnalysisSingleVO.qtxmzygczgj = unitProject.qtxmzygczgj;
                costAnalysisSingleVO.qtxmzcbfwf  = unitProject.qtxmzcbfwf ;
                costAnalysisSingleVO.qtxmjrg     = unitProject.qtxmjrg    ;
                costAnalysisSingleVO.gfee = unitProject.gfee;
                costAnalysisSingleVO.safeFee = unitProject.safeFee;
                costAnalysisSingleVO.jxse = unitProject.jxse;
                costAnalysisSingleVO.xxse = unitProject.xxse;
                costAnalysisSingleVO.zzsynse = unitProject.zzsynse;
                costAnalysisSingleVO.fjse = unitProject.fjse;
                costAnalysisSingleVO.sj = unitProject.sj;
                costAnalysisSingleVO.sbfsj = unitProject.sbfsj;
                costAnalysisSingleVO.average = unitProject.average;
                costAnalysisSingleVO.unitcost = unitProject.unitcost;
                costAnalysisSingleVO.levelType = ProjectLevelConstant.unit;
                costAnalysisSingleVO.projectName = unitProject.upName;
                costAnalysisSingleVO.sequenceNbr = unitProject.sequenceNbr;
                if(singDispNo!== null){
                    costAnalysisSingleVO.dispNo = singDispNo+'.'+(j+1)
                }else {
                    costAnalysisSingleVO.dispNo = (j+1)
                }
                unitCostAnalysis.push(costAnalysisSingleVO);
            }
        }
        return unitCostAnalysis;
    }

    /**
     * 对同一层级的子单项造价分析数据进行汇总
     * @param subSingleProjects
     * @param dispNo
     * @returns {Promise<any[]>} 返回该单项及子单项的造价分析数据
     */
    async getSubSingleCostAnalysisDataTotal(subSingleProjects,dispNo,singleObj){
        //存放单项工程数据
        let singleCostAnalysisVO= new CostAnalysisSingleVO();
        singleCostAnalysisVO.dispNo = dispNo;
        singleCostAnalysisVO.projectName = singleObj.projectName;
        singleCostAnalysisVO.sequenceNbr = singleObj.sequenceNbr;
        let gczj = 0;
        let fbfxhj = 0;
        let fbfxrgf = 0;
        let fbfxclf = 0;
        let fbfxjxf = 0;
        let fbfxzcf = 0;
        let fbfxlr = 0;
        let fbfxglf = 0;
        let fbfxzgj = 0;
        let csxhj = 0;
        let djcsxhj = 0;
        let djcsxrgf = 0;
        let djcsxclf = 0;
        let djcsxjxf = 0;
        let djcsxzcf = 0;
        let djcsxglf = 0;
        let djcsxlr  = 0;
        let zjcsxhj  = 0;
        let zjcsxrgf = 0;
        let zjcsxclf = 0;
        let zjcsxjxf = 0;
        let zjcsxzcf = 0;
        let zjcsxglf = 0;
        let zjcsxlr  = 0;
        let qtxmhj = 0;
        let qtxmzlje    = 0;
        let qtxmzygczgj = 0;
        let qtxmzcbfwf  = 0;
        let qtxmjrg     = 0;
        let gfee = 0;
        let safeFee = 0;
        let jxse = 0;
        let xxse = 0;
        let zzsynse = 0;
        let fjse = 0;
        let sj = 0;
        let sbfsj = 0;
        let unitcost = 0;
        //存放所有单位对应的造价分析
        if(!ObjectUtils.isEmpty(subSingleProjects)){
            for (let j =0 ;j<subSingleProjects.length ;j++) {
                let subSingleProject = subSingleProjects[j];
                //单项合计计算
                gczj = NumberUtil.add(gczj,subSingleProject.gczj);
                fbfxhj =NumberUtil.add(fbfxhj, subSingleProject.fbfxhj);
                fbfxrgf = NumberUtil.add(fbfxrgf,subSingleProject.fbfxrgf);
                fbfxclf = NumberUtil.add(fbfxclf,subSingleProject.fbfxclf);
                fbfxjxf = NumberUtil.add(fbfxjxf,subSingleProject.fbfxjxf);
                fbfxzcf = NumberUtil.add(fbfxzcf,subSingleProject.fbfxzcf);
                fbfxlr  = NumberUtil.add(fbfxlr ,subSingleProject.fbfxlr );
                fbfxglf = NumberUtil.add(fbfxglf,subSingleProject.fbfxglf);
                fbfxzgj = NumberUtil.add(fbfxzgj,subSingleProject.fbfxzgj);
                csxhj = NumberUtil.add(csxhj,subSingleProject.csxhj);
                djcsxhj  = NumberUtil.add(djcsxhj ,subSingleProject.djcsxhj );
                djcsxrgf = NumberUtil.add(djcsxrgf,subSingleProject.djcsxrgf);
                djcsxclf = NumberUtil.add(djcsxclf,subSingleProject.djcsxclf);
                djcsxjxf = NumberUtil.add(djcsxjxf,subSingleProject.djcsxjxf);
                djcsxzcf = NumberUtil.add(djcsxzcf,subSingleProject.djcsxzcf);
                djcsxglf = NumberUtil.add(djcsxglf,subSingleProject.djcsxglf);
                djcsxlr  = NumberUtil.add(djcsxlr ,subSingleProject.djcsxlr );
                zjcsxhj  = NumberUtil.add(zjcsxhj ,subSingleProject.zjcsxhj );
                zjcsxrgf = NumberUtil.add(zjcsxrgf,subSingleProject.zjcsxrgf);
                zjcsxclf = NumberUtil.add(zjcsxclf,subSingleProject.zjcsxclf);
                zjcsxjxf = NumberUtil.add(zjcsxjxf,subSingleProject.zjcsxjxf);
                zjcsxzcf = NumberUtil.add(zjcsxzcf,subSingleProject.zjcsxzcf);
                zjcsxglf = NumberUtil.add(zjcsxglf,subSingleProject.zjcsxglf);
                zjcsxlr  = NumberUtil.add(zjcsxlr ,subSingleProject.zjcsxlr );
                qtxmhj = NumberUtil.add(qtxmhj,subSingleProject.qtxmhj);
                qtxmzlje    = NumberUtil.add(qtxmzlje,subSingleProject.qtxmzlje);
                qtxmzygczgj = NumberUtil.add(qtxmzygczgj,subSingleProject.qtxmzygczgj);
                qtxmzcbfwf  = NumberUtil.add(qtxmzcbfwf,subSingleProject.qtxmzcbfwf );
                qtxmjrg     = NumberUtil.add(qtxmjrg,subSingleProject.qtxmjrg    );
                gfee = NumberUtil.add(gfee,subSingleProject.gfee);
                safeFee = NumberUtil.add(safeFee,subSingleProject.safeFee);
                jxse = NumberUtil.add(jxse,subSingleProject.jxse);
                xxse = NumberUtil.add(xxse,subSingleProject.xxse);
                zzsynse = NumberUtil.add(zzsynse,subSingleProject.zzsynse);
                fjse = NumberUtil.add(fjse,subSingleProject.fjse);
                sj = NumberUtil.add(sj,subSingleProject.sj);
                sbfsj = NumberUtil.add(sbfsj,subSingleProject.sbfsj);
                // average = NumberUtil.add(average,unitProject.average);
                // unitcost = NumberUtil.add(unitcost,unitProject.unitcost);
            }
        }
        singleCostAnalysisVO.gczj = gczj;
        singleCostAnalysisVO.fbfxhj = fbfxhj;
        singleCostAnalysisVO.fbfxrgf = fbfxrgf;
        singleCostAnalysisVO.fbfxclf = fbfxclf;
        singleCostAnalysisVO.fbfxjxf = fbfxjxf;
        singleCostAnalysisVO.fbfxzcf = fbfxzcf;
        singleCostAnalysisVO.fbfxlr  = fbfxlr ;
        singleCostAnalysisVO.fbfxglf = fbfxglf;
        singleCostAnalysisVO.fbfxzgj = fbfxzgj;
        singleCostAnalysisVO.csxhj = csxhj;
        singleCostAnalysisVO.djcsxhj  = djcsxhj ;
        singleCostAnalysisVO.djcsxrgf = djcsxrgf;
        singleCostAnalysisVO.djcsxclf = djcsxclf;
        singleCostAnalysisVO.djcsxjxf = djcsxjxf;
        singleCostAnalysisVO.djcsxzcf = djcsxzcf;
        singleCostAnalysisVO.djcsxglf = djcsxglf;
        singleCostAnalysisVO.djcsxlr  = djcsxlr ;
        singleCostAnalysisVO.zjcsxhj  = zjcsxhj ;
        singleCostAnalysisVO.zjcsxrgf = zjcsxrgf;
        singleCostAnalysisVO.zjcsxclf = zjcsxclf;
        singleCostAnalysisVO.zjcsxjxf = zjcsxjxf;
        singleCostAnalysisVO.zjcsxzcf = zjcsxzcf;
        singleCostAnalysisVO.zjcsxglf = zjcsxglf;
        singleCostAnalysisVO.zjcsxlr  = zjcsxlr;
        singleCostAnalysisVO.qtxmhj = qtxmhj;
        singleCostAnalysisVO.qtxmzlje = qtxmzlje;
        singleCostAnalysisVO.qtxmzygczgj= qtxmzygczgj;
        singleCostAnalysisVO.qtxmzcbfwf = qtxmzcbfwf;
        singleCostAnalysisVO.qtxmjrg = qtxmjrg;
        singleCostAnalysisVO.gfee = gfee;
        singleCostAnalysisVO.safeFee = safeFee;
        singleCostAnalysisVO.jxse = jxse;
        singleCostAnalysisVO.xxse = xxse;
        singleCostAnalysisVO.zzsynse = zzsynse;
        singleCostAnalysisVO.fjse = fjse;
        singleCostAnalysisVO.sj = sj;
        singleCostAnalysisVO.sbfsj = sbfsj;
        singleCostAnalysisVO.average = singleObj.average;
        singleCostAnalysisVO.unitcost = singleObj.unitcost;
        singleCostAnalysisVO.childrenList= subSingleProjects;
        singleCostAnalysisVO.levelType = ProjectLevelConstant.single;

        return singleCostAnalysisVO;

    }

    /**
     * 获取单位工程造价分析
     * @param taxMode
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<CostAnalysisUnitVO[]>}
     */
    async generateUnitCostAnalysisData(constructId,singleId,unitId){
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        let taxMode =unit.projectTaxCalculation.taxCalculationMethod;
        //改为获取单位的定额标准
        let constructDeStandard = PricingFileFindUtils.getUnit(constructId,singleId,unitId).deStandardReleaseYear;
        let array = new Array();
        //一般计税
        if(taxMode == TaxCalculationMethodEnum.GENERAL.code){
            if (constructDeStandard == ConstantUtil.DE_STANDARD_12) {
                for (let i in zjfx_ybjs_12) {
                    let obj = new CostAnalysisUnitVO();
                    ConvertUtil.setDstBySrc(zjfx_ybjs_12[i], obj);
                    array.push(obj);
                }
            }else {
                for (let i in zjfx_ybjs_22) {
                    let obj = new CostAnalysisUnitVO();
                    ConvertUtil.setDstBySrc(zjfx_ybjs_22[i], obj);
                    array.push(obj);
                }
            }

        }else {
            if (constructDeStandard == ConstantUtil.DE_STANDARD_12) {
                for (let i in zjfx_jyjs_12) {
                    let obj = new CostAnalysisUnitVO();
                    ConvertUtil.setDstBySrc(zjfx_jyjs_12[i], obj)
                    array.push(obj);
                }
            }else {
                for (let i in zjfx_jyjs_22) {
                    let obj = new CostAnalysisUnitVO();
                    ConvertUtil.setDstBySrc(zjfx_jyjs_22[i], obj)
                    array.push(obj);
                }
            }
        }


        let contexts = new Array();
        contexts.push(NumberUtil.numToCny(unit.gczj));   // gczj
        contexts.push(unit.gczj);
        if(!ObjectUtils.isEmpty(unit.gczjsbsj)){
            contexts.push(NumberUtil.numToCny(unit.gczjsbsj));  // gczjsbsj
        }else {
            contexts.push(null);
        }
        contexts.push(unit.gczjsbsj);
        if(!ObjectUtils.isEmpty(unit.gczjsbsjjg)) {
            contexts.push(NumberUtil.numToCny(unit.gczjsbsjjg));   // gczjsbsjjg
        }else {
            contexts.push(null);
        }
        contexts.push(unit.gczjsbsjjg);
        contexts.push(unit.fbfxhj); // fbfxhj
        contexts.push(unit.fbfxrgf);   // fbfxrgf
        contexts.push(unit.fbfxclf);   // fbfxclf
        contexts.push(unit.fbfxjxf);   // fbfxjxf
        contexts.push(unit.fbfxzcf);              // fbfxzcf
        contexts.push(unit.fbfxglf);   // fbfxglf
        contexts.push(unit.fbfxlr);     // fbfxlr
        contexts.push(NumberUtil.numberScale2(unit.fbfxzgj));              // fbfxzgj
        contexts.push(NumberUtil.numToCny(unit.csxhj));  // csxhj
        contexts.push(unit.csxhj);  // csxhj
        contexts.push(unit.djcsxhj); // djcsxhj
        contexts.push(unit.djcsxrgf);      // djcsxrgf
        contexts.push(unit.djcsxclf);      // djcsxclf
        contexts.push(unit.djcsxjxf);      // djcsxjxf
        contexts.push(unit.djcsxzcf);              // djcsxzcf
        contexts.push(unit.djcsxglf);      // djcsxglf
        contexts.push(unit.djcsxlr);        // djcsxlr
        contexts.push(unit.zjcsxhj); // zjcsxhj
        contexts.push(unit.zjcsxrgf);      // zjcsxrgf
        contexts.push(unit.zjcsxclf);      // zjcsxclf
        contexts.push(unit.zjcsxjxf);      // zjcsxjxf
        contexts.push(unit.zjcsxzcf);             // zjcsxzcf
        contexts.push(unit.zjcsxglf);      // zjcsxglf
        contexts.push(unit.zjcsxlr);       // zjcsxlr
        contexts.push(unit.qtxmhj);      // qtxmhj
        contexts.push(unit.qtxmzlje);            // qtxmzlje
        contexts.push(unit.qtxmzygczgj);       // qtxmzygczgj
        contexts.push(unit.qtxmzcbfwf);         // qtxmzcbfwf
        contexts.push(unit.qtxmjrg);              // qtxmjrg
        if (ConstantUtil.DE_STANDARD_12 == constructDeStandard) {
            contexts.push(unit.gfee);   // gfee  规费
        }
        contexts.push(unit.safeFee);  // safeFee

        //一般计税
        if(taxMode == TaxCalculationMethodEnum.GENERAL.code && ConstantUtil.DE_STANDARD_12 == constructDeStandard){
            contexts.push(NumberUtil.numberScale2(unit.jxse));  // jxse
            contexts.push(NumberUtil.numberScale2(unit.xxse));  // xxse
            contexts.push(NumberUtil.numberScale2(unit.zzsynse));  // zzsynse
            contexts.push(NumberUtil.numberScale2(unit.fjse));  // fjse
        }
        contexts.push(unit.sj);   // sj
        contexts.push(unit.sbfsj);  // sbfsj
        contexts.push(unit.sbfsjjg);    // sbfsjjg
        contexts.push(unit.average);  // average
        contexts.push(unit.unitcost);      // unitcost


        for (let i = 0; i < array.length; i++) {
            array[i].context = contexts[i]
        }

        let result = new Array();
        let parentItem = new CostAnalysisUnitVO();
        for (const item of array) {
            if( item.dispNo.toString().indexOf(".")=== -1){
                //说明是父级
                parentItem = item;
                parentItem.childrenList= new Array();
                result.push(parentItem)
            }else {
                //子集
                parentItem.childrenList.push(item)
            }
        }
        return result;
    }

    /**
     * 获取造价分析
     * @param args
     * @returns {Promise<CostAnalysisVO>}
     */
    async getCostAnalysisData(args){

        let projectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
        //let taxMode = projectObj.projectTaxCalculation.taxCalculationMethod;
        //返回对象
        let costAnalysisVO = new CostAnalysisVO();

        if(ConstructBiddingTypeConstant.unitProject===projectObj.biddingType){
            //单位工程项目
            let costAnalysisUnitVOList = await this.generateUnitCostAnalysisData(args.constructId,args.singleId, args.unitId)
            costAnalysisVO.costAnalysisUnitVOList = costAnalysisUnitVOList;
        }else {
            //招标和投标项目
            if(ProjectLevelConstant.construct === args.levelType ){
                // 工程项目层级
                let costAnalysisVOList = await this.generateConstructCostAnalysisData(projectObj);
                costAnalysisVO.costAnalysisConstructVOList = costAnalysisVOList;
            }else if(ProjectLevelConstant.single === args.levelType){
                // 单项层级
                // let singleProject = projectObj.singleProjects.find((item ) => item.sequenceNbr === args.singleId);

                let singleProject = await PricingFileFindUtils.getOneFromSingleProjects(projectObj.singleProjects,args.singleId);
                costAnalysisVO.costAnalysisSingleVOList = await this.getCurrentSingleCostAnalysisData(singleProject,"1");
            }else if(ProjectLevelConstant.unit === args.levelType){
                // 单位层级
                let costAnalysisUnitVOList = await this.generateUnitCostAnalysisData(args.constructId,args.singleId, args.unitId)
                costAnalysisVO.costAnalysisUnitVOList = costAnalysisUnitVOList;
            }
        }
        return costAnalysisVO;
    }

    async updateCostAnalysis(args){
        if(ProjectLevelConstant.single === args.levelType){
            let projectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
            let singleProject = PricingFileFindUtils.getOneFromSingleProjects(projectObj.singleProjects, args.singleId);
            //修改单项的建筑面积
            singleProject.average =args.average;
            if (args.flag && singleProject.subSingleProjects != null && singleProject.subSingleProjects.length > 0) {
                for (let i = 0; i < singleProject.subSingleProjects.length; i++) {
                    args.singleId = singleProject.subSingleProjects[i].sequenceNbr;
                    args.levelType = ProjectLevelConstant.single;
                    await this.updateCostAnalysis(args);
                }
            }

            if (args.flag && singleProject.unitProjects!=null && singleProject.unitProjects.length>0) {
                let units = singleProject.unitProjects;
                    for (let i = 0 ; i < units.length ; ++i) {
                        let upt = units[i];
                        let param = {
                            "levelType": 3,
                            "average": args.average,
                            "constructId": args.constructId,
                            "singleId": args.singleId,
                            "unitId": upt.sequenceNbr,

                        }

                        await this.updateCostAnalysis(param);
                        await this.service.unitCostCodePriceService.countCostCodePrice({
                            constructId: args.constructId,
                            singleId: args.singleId,
                            unitId: upt.sequenceNbr,
                        });
                    }
            }

        }else if(ProjectLevelConstant.unit === args.levelType){
            //修改单位的建筑面积
            let projectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
            if (ConstructBiddingTypeConstant.unitProject ==  projectObj.biddingType){
                //单位工程
                let unitProject = projectObj.unitProject;
                unitProject.average = args.average;
                //单方造价
                unitProject.unitcost = NumberUtil.costPriceAmountFormat(NumberUtil.divide(unitProject.gczj, unitProject.average))
                if (unitProject.unitGCTZ) {
                    let k = unitProject.unitGCTZ.filter(f=>f.name === "建筑面积(m、㎡)");
                    if (k){
                        let f = k[0];
                        f.context = args.average;
                    }
                }
                await this.service.unitCostCodePriceService.countCostCodePrice({
                    constructId: args.constructId,
                    singleId: args.singleId,
                    unitId: args.unitId,
                });
            }else {
                //单项
                let singleProject = PricingFileFindUtils.getSingleProject(args.constructId,args.singleId);
                if (ObjectUtils.isEmpty(singleProject)){
                    if (!ObjectUtils.isEmpty( projectObj.unitProjectArray)){
                        let unitProject =  projectObj.unitProjectArray.find((item ) => item.sequenceNbr === args.unitId);
                        unitProject.average = args.average;
                        //同时变更单方造价
                        unitProject.unitcost = NumberUtil.costPriceAmountFormat(NumberUtil.divide(unitProject.gczj, unitProject.average))
                        if (unitProject.unitGCTZ) {
                            let k = unitProject.unitGCTZ.filter(f=>f.name === "建筑面积(m、㎡)");
                            if (k){
                                let f = k[0];
                                f.context = args.average;
                            }
                        }
                        await this.service.unitCostCodePriceService.countCostCodePrice({
                            constructId: args.constructId,
                            singleId: args.singleId,
                            unitId: unitProject.sequenceNbr,
                        });
                    }
                }else {
                    let unitProject = singleProject.unitProjects.find((item ) => item.sequenceNbr === args.unitId);
                    unitProject.average = args.average;
                    //单方造价
                    unitProject.unitcost = NumberUtil.costPriceAmountFormat(NumberUtil.divide(unitProject.gczj, unitProject.average))
                    if (unitProject.unitGCTZ) {
                        let k = unitProject.unitGCTZ.filter(f=>f.name === "建筑面积(m、㎡)");
                        if (k){
                            let f = k[0];
                            f.context = args.average;
                        }
                    }
                    await this.service.unitCostCodePriceService.countCostCodePrice({
                        constructId: args.constructId,
                        singleId: args.singleId,
                        unitId: args.unitId,
                    });
                }
            }
        }
    }

    lockAll(args) {
        let {constructId, singleId, unitId} = args;
        //获取所有数据
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let allFbFx= unit["itemBillProjects"].getAllNodes();
        let allCs =unit["measureProjectTables"].getAllNodes();
        allFbFx = allFbFx || [];
        allCs = allCs || [];

        let all = [...allFbFx, ...allCs];

        if (!all || all.length == 0) {
            return ResponseData.success(true);
        } else {
            for (let i = 0 ; i < all.length ; ++i) {
                if (all[i].kind === "03") {
                    all[i].isLocked = 1;
                    this.service.baseBranchProjectOptionService.handleDeAddQdStatus(all,all[i],1);
                }
            }
        }
        return ResponseData.success(true);
    }
    unLockAll(args) {
        let {constructId, singleId, unitId} = args;
        //获取所有数据
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let allFbFx= unit["itemBillProjects"].getAllNodes();
        let allCs =unit["measureProjectTables"].getAllNodes();
        allFbFx = allFbFx || [];
        allCs = allCs || [];

        let all = [...allFbFx, ...allCs];

        if (!all || all.length == 0) {
            return ResponseData.success(true);
        } else {
            for (let i = 0 ; i < all.length ; ++i) {
                if (all[i].kind === "03") {
                    delete all[i].isLocked;
                    this.service.baseBranchProjectOptionService.handleDeAddQdStatus(all,all[i],2);
                }
            }
        }
        return ResponseData.success(true);
    }

    /**
     * 修改单位筛选条件
     * @param screenCondition  筛选条件
     * @returns {ResponseData}
     */
    updateUnitScreenCondition(args) {
        let {constructId, singleId, unitId,screenCondition} = args;
        //获取单位工程数据
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        unit.screenCondition=screenCondition;
        return true;
    }


    /**
     * 修改单位颜色筛选条件
     * @param screenCondition  筛选条件
     * @returns {ResponseData}
     */
    updateUnitColor(args) {
        let {constructId, singleId, unitId,checkColorList} = args;
        //获取单位工程数据
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        unit.checkColorList=checkColorList;
        return true;
    }


    /**
     * 修改单位锁定清单筛选条件
     * @param screenCondition  筛选条件
     * @returns {ResponseData}
     */
    updateUnitQdLockPriceFlag(args) {
        let {constructId, singleId, unitId,lockPriceFlag} = args;
        //获取单位工程数据
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        unit.lockPriceFlag=lockPriceFlag;
        return true;
    }

    /**
     * 获取单位分部整理数据
     * @param args
     * @returns {boolean}
     */
    queryFbArrangeList(args) {
        let {constructId, singleId, unitId} = args;
        //获取单位工程数据
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        return unit.fbArrangeList;
    }

    getUnitQdSortFlag(args) {
        let {constructId, singleId, unitId} = args;
        //获取单位工程数据
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        return  unit.qdSortSaveFlag;
    }



    /**
     * 获取三材汇总表
     * @returns {Promise<void>}
     */
    async getThreeMaterialsSummary(args) {
        let {constructId,singleId,unitId,levelType} = args;
        let total1 = 0; //钢材数量合计
        let total2 = 0; //其中钢筋
        let total3 = 0; //木材
        let total4 = 0; //水泥
        let total5 = 0; //商品砼
        let total6 = 0; //商品砂浆
        let unitArray = [];
        if (ProjectLevelConstant.construct == levelType) {
            let unitList = PricingFileFindUtils.getUnitList(constructId);
            unitArray.push(...unitList);

        }else if (ProjectLevelConstant.single == levelType) {
            let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);
            let array = new Array();
            array = PricingFileFindUtils.getSubSingleUnits(singleProject,array);
            unitArray.push(...array);
        }else if (ProjectLevelConstant.unit == levelType) {
            let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
            unitArray.push(unit);
        }
        for (let i = 0; i < unitArray.length; i++) {
            let unitElement = unitArray[i];
            let map = await this.getUnitThreeMaterialsSummary(unitElement);
            if (!ObjectUtils.isEmpty(map)){
                total1+=map.get("钢材");
                total2+=map.get("其中钢筋");
                total3+=map.get("木材");
                total4+=map.get("水泥");
                total5+=map.get("商品砼");
                total6+=map.get("商品砂浆");
            }
        }

        class InnerClass {
            constructor(dispNo,name,unit,amount) {
                this.dispNo = dispNo;
                this.name = name;
                this.unit = unit;
                this.amount = amount;
            }
        }

        return [
            new InnerClass(1,"钢材","吨",total1),
            new InnerClass(2,"其中钢筋","吨",total2),
            new InnerClass(3,"木材","立方米",total3),
            new InnerClass(4,"水泥","吨",total4),
            new InnerClass(5,"商品砼","立方米",total5),
            new InnerClass(6,"商品砂浆","吨",total6),
        ];
    }

    async getUnitThreeMaterialsSummary(unit) {
        let constructProjectRcjs = unit.constructProjectRcjs;
        let rcjDetailList = unit.rcjDetailList;
        if (ObjectUtils.isEmpty(constructProjectRcjs) && ObjectUtils.isEmpty(rcjDetailList)){
            return null;
        }
        rcjDetailList = !ObjectUtils.isEmpty(rcjDetailList)?rcjDetailList:[];
        let concat = constructProjectRcjs.concat(rcjDetailList);


        //针对钢材
        let rcjs = concat.filter(item => item.kindSc=="钢筋"||item.kindSc=="钢材");
        let total1 = 0;
        for (let i = 0; i < rcjs.length; i++) {
            let heji = NumberUtil.multiplyParams(rcjs[i].totalNumber,rcjs[i].transferFactor);
            total1 = NumberUtil.add(total1,heji);
        }
        //针对其中钢筋
        let rcjs2 = concat.filter(item => item.kindSc=="钢筋");
        let total2 = 0;
        for (let i = 0; i < rcjs2.length; i++) {
            let heji = NumberUtil.multiplyParams(rcjs2[i].totalNumber,rcjs2[i].transferFactor);
            total2 = NumberUtil.add(total2,heji);
        }
        //针对木材
        let rcjs3 = concat.filter(item => item.kindSc=="木材");
        let total3 = 0;
        for (let i = 0; i < rcjs3.length; i++) {
            let heji = NumberUtil.multiplyParams(rcjs3[i].totalNumber,rcjs3[i].transferFactor);
            total3 = NumberUtil.add(total3,heji);
        }
        //针对水泥
        let rcjs4 = concat.filter(item => item.kindSc=="水泥");
        let total4 = 0;
        for (let i = 0; i < rcjs4.length; i++) {
            let heji = NumberUtil.multiplyParams(rcjs4[i].totalNumber,rcjs4[i].transferFactor);
            total4 = NumberUtil.add(total4,heji);
        }
        //针对商品砼
        let rcjs5 = concat.filter(item => item.kindSc=="商砼");
        let total5 = 0;
        for (let i = 0; i < rcjs5.length; i++) {
            let heji = NumberUtil.multiplyParams(rcjs5[i].totalNumber,rcjs5[i].transferFactor);
            total5 = NumberUtil.add(total5,heji);
        }
        //针对商品砂浆
        let rcjs6 = concat.filter(item => item.kindSc=="商品砂浆");
        let total6 = 0;
        for (let i = 0; i < rcjs6.length; i++) {
            let heji = NumberUtil.multiplyParams(rcjs6[i].totalNumber,rcjs6[i].transferFactor);
            total6 = NumberUtil.add(total6,heji);
        }

        let map = new Map();
        map.set("钢材",total1);
        map.set("其中钢筋",total2);
        map.set("木材",total3);
        map.set("水泥",total4);
        map.set("商品砼",total5);
        map.set("商品砂浆",total6);
        return map;
    }

    /**
     * 修改单位颜色筛选条件
     * @param screenCondition  筛选条件
     * @returns {ResponseData}
     */
    updateUnitCheckColor(args) {
        let {constructId, singleId, unitId,checkColorList,type} = args;
        //获取单位工程数据
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        if(ObjectUtils.isEmpty(unit.checkColorList)){
            unit.checkColorList={};
        }
        unit.checkColorList[type]=checkColorList;
        return true;
    }



    /**
     * 局部汇总
     * @param args
     * @returns {Promise<void>}
     */
    async unitPartialSummary(args){
        let { constructId,singleId, unitId} = args;
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);

        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtils.isEmpty(unit)){
            return null;
        }

        let defaultStoragePath = await this.service.commonService.getSetStoragePath(unit.upName);

        if (!defaultStoragePath.toUpperCase().endsWith(ConstantUtil.YUSUAN_FILE_DOT_SUFFIX)) {
            defaultStoragePath += ConstantUtil.YUSUAN_FILE_DOT_SUFFIX;
        }

        let newUnit ={};
        newUnit.constructName = unit.upName;
        newUnit.constructCode = null;
        newUnit.mainfest = "河北13国标清单规范";
        newUnit.ration = PricingFileFindUtils.is22Unit(unit)?"河北22定额标准":"河北12定额标准";
        newUnit.importUrl = null;
        newUnit.importName = null;
        newUnit.fileExtension = null;
        newUnit.xmlFactory = null;
        newUnit.constructMajorType = unit.constructMajorType;
        newUnit.libraryCode = unit.mainDeLibrary;
        newUnit.secondInstallationProjectName = unit.secondInstallationProjectName;
        newUnit.taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;
        newUnit.taxReformDocumentsId = unit.projectTaxCalculation.taxReformDocumentsId;
        newUnit.ssProvince = projectObjById.ssProvince;
        newUnit.ssProvinceName = projectObjById.ssProvinceName;
        newUnit.biddingType = 2;
        newUnit.qdStandardId = projectObjById.qdStandardId;
        newUnit.deStandardId = unit.deStandardId;


        let sequenceNbr = await this.service.constructProjectService.createProject(newUnit, defaultStoragePath);

        await this.service.ossService.deleteFile(defaultStoragePath);

        let newProjectObjById = PricingFileFindUtils.getProjectObjById(sequenceNbr);
        let cloneDeepUnit = ObjectUtils.cloneDeep(unit);
        cloneDeepUnit.spId = null;
        cloneDeepUnit.constructId = sequenceNbr;
        newProjectObjById.unitProject = cloneDeepUnit;
        let spreadArgs = {};
        spreadArgs.constructId = sequenceNbr;
        spreadArgs.singleId = null;
        spreadArgs.unitId = unit.sequenceNbr;
        spreadArgs.hierachy = "all";
        spreadArgs.pageType = "fbfx";
        this.service.itemBillProjectOptionService.spread(spreadArgs);
        spreadArgs.pageType = "csxm";
        this.service.itemBillProjectOptionService.spread(spreadArgs);

        return {
            "constructId":sequenceNbr,
            "singleId":null,
            "unitId":unit.sequenceNbr
        };

    }


    /**
     * 局部汇总预览功能
     * @param args
     * @returns {Promise<void>}
     */
    async previewUnitPartialSummary(args){
        let { constructId,singleId, unitId,fbfxIdList,csxmIdList,qtxmIdList} = args;
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        if (ObjectUtils.isEmpty(unit)){
            return null;
        }

        //分部分项处理
        if (!ObjectUtils.isEmpty(fbfxIdList)){
            let itemBillProjects = unit.itemBillProjects;
            let nodesByKind = itemBillProjects.getNodesByKind("03");

            for (const [key, value] of nodesByKind) {
                if (!fbfxIdList.includes(key) ){
                    await this.service.itemBillProjectOptionService.removeLine(constructId, singleId, unitId, value, true);
                }
            }

            await this.service.management.sycnTrigger("unitDeChange");
            await this.service.management.trigger("itemChange");
        }

        //措施项目处理
        if (!ObjectUtils.isEmpty(csxmIdList)){
            let measureProjectTables = unit.measureProjectTables;
            let nodesByKind1 = measureProjectTables.getNodesByKind("03");
            for (const [key, value] of nodesByKind1) {
                if (!csxmIdList.includes(key) ){
                    await this.service.stepItemCostService.removeLine(constructId, singleId, unitId, value, true);
                }
            }

            //状态数据处理
            this.service.baseBranchProjectOptionService.setItemDtaStatus(PricingFileFindUtils.getCSXM(constructId, singleId, unitId).getAllNodes());
            await this.service.management.sycnTrigger("unitDeChange");
            await this.service.unitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
            });
        }

        //其他项目删除
        if (!ObjectUtils.isEmpty(qtxmIdList)){
            let qtxm ={};
            qtxm.constructId  = constructId;
            qtxm.singleId  = singleId;
            qtxm.unitId  = unitId;
            qtxm.levelType = 3;
            qtxm.operateType  = 2;

            let otherProjects = await this.service.otherProjectService.getOtherProjectList(qtxm);
            //let otherProjects = cloneDeepUnit.otherProjects;
            if(!ObjectUtils.isEmpty(otherProjects)){
                for (let otherProject of otherProjects) {
                    if (!ObjectUtils.isEmpty(otherProject.sequenceNbr) && !qtxmIdList.includes(otherProject.sequenceNbr)){
                        qtxm.targetSequenceNbr = otherProject.sequenceNbr;
                        await this.service.otherProjectService.otherProjectLineData(qtxm);
                    }
                }
            }

            await this.service.management.sycnTrigger("unitDeChange");
            await this.service.management.trigger("itemChange");
        }


        //费用汇总
        let unitCostSummary = PricingFileFindUtils.getUnitCostSummary(constructId, singleId, unitId);

        //人材机汇总
        let rcjList = this.service.rcjProcess.unitConstructProjectRcjQuery(constructId, singleId, unitId, 0,null);


        return {
            "unitCostSummary":unitCostSummary,
            "rcjList":rcjList
        };
    }

    /**
     *局部汇总 生成功能
     * @param args
     * @returns {Promise<void>}
     */
    async createUnitPartialSummary(args){

        let { constructId,singleId, unitId,fromConstructId,fromSingleId, fromUnitId,fbfxIdList,csxmIdList,qtxmIdList} = args;

        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

        let defaultStoragePath = projectObjById.path;
        const dialogOptions = {
            title: '保存文件',
            defaultPath: defaultStoragePath.toString(),
            filters: [{ name: '云算房文件', extensions: [ConstantUtil.YUSUAN_FILE_SUFFIX_D] }]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        if (result && !result.canceled) {
            projectObjById.path = result;

            let unit1 = PricingFileFindUtils.getUnit(fromConstructId,fromSingleId, fromUnitId);
            if (ObjectUtils.isEmpty(unit1)){
                return ;
            }

            let cloneDeepUnit = ObjectUtils.cloneDeep(unit1);
            cloneDeepUnit.spId = null;
            cloneDeepUnit.constructId = constructId;
            projectObjById.unitProject = cloneDeepUnit;

            let spreadArgs = {};
            spreadArgs.constructId = constructId;
            spreadArgs.singleId = null;
            spreadArgs.unitId = cloneDeepUnit.sequenceNbr;
            spreadArgs.hierachy = "all";
            spreadArgs.pageType = "fbfx";
            await this.service.itemBillProjectOptionService.spread(spreadArgs);
            spreadArgs.pageType = "csxm";
            await this.service.itemBillProjectOptionService.spread(spreadArgs);

            //分部分项处理
            if (!ObjectUtils.isEmpty(fbfxIdList)){
                let itemBillProjects = cloneDeepUnit.itemBillProjects;
                let nodesByKind = itemBillProjects.getNodesByKind("03");

                for (const [key, value] of nodesByKind) {
                    if (!fbfxIdList.includes(key) ){
                        await this.service.itemBillProjectOptionService.removeLine(constructId, singleId, unitId, value, true);
                    }
                }

                await this.service.management.sycnTrigger("unitDeChange");
                await this.service.management.trigger("itemChange");
            }

            //措施项目处理
            if (!ObjectUtils.isEmpty(csxmIdList)){
                let measureProjectTables = cloneDeepUnit.measureProjectTables;
                let nodesByKind1 = measureProjectTables.getNodesByKind("03");
                for (const [key, value] of nodesByKind1) {
                    if (!csxmIdList.includes(key) ){
                        await this.service.stepItemCostService.removeLine(constructId, singleId, unitId, value, true);
                    }
                }

                //状态数据处理
                this.service.baseBranchProjectOptionService.setItemDtaStatus(PricingFileFindUtils.getCSXM(constructId, singleId, unitId).getAllNodes());
                await this.service.management.sycnTrigger("unitDeChange");
                await this.service.unitCostCodePriceService.countCostCodePrice({
                    constructId: constructId,
                    singleId: singleId,
                    unitId: unitId,
                });
            }

            //其他项目删除
            if (!ObjectUtils.isEmpty(qtxmIdList)){
                let qtxm ={};
                qtxm.constructId  = constructId;
                qtxm.singleId  = singleId;
                qtxm.unitId  = unitId;
                qtxm.levelType = 3;
                qtxm.operateType  = 2;

                let otherProjects = await this.service.otherProjectService.getOtherProjectList(qtxm);
                //let otherProjects = cloneDeepUnit.otherProjects;
                if(!ObjectUtils.isEmpty(otherProjects)){
                    for (let otherProject of otherProjects) {
                        if (!ObjectUtils.isEmpty(otherProject.sequenceNbr) && !qtxmIdList.includes(otherProject.sequenceNbr)){
                            qtxm.targetSequenceNbr = otherProject.sequenceNbr;
                            await this.service.otherProjectService.otherProjectLineData(qtxm);
                        }
                    }
                }

                await this.service.management.sycnTrigger("unitDeChange");
                await this.service.management.trigger("itemChange");
            }

            await this.deleteFb(cloneDeepUnit.itemBillProjects,constructId,singleId, unitId,"fbfx");
            await this.deleteFb(cloneDeepUnit.measureProjectTables,constructId,singleId, unitId,"csxm");

            await this.service.ysfHandlerService.creatYsfFile(projectObjById);

            return true;
        }
        return false;
    }

    /**
     * 局部汇总 取消操作
     * @param args
     * @returns {Promise<void>}
     */
    async cancelUnitPartialSummary(args){
        let { constructId } =  args;
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtils.isEmpty(projectObjById)){
            return null;
        }

        delete global.constructProject[constructId];
    }

    /**
     * 清除 没有清单的分部
     * @param itemBillProjects
     * @returns {Promise<void>}
     */
    async deleteFb(itemBillProjects,constructId, singleId, unitId,type){
        //清单
        let qdList = itemBillProjects.getNodesByKind("03");
        //子分部
        let zfbList = itemBillProjects.getNodesByKind("02");
        //分部
        let fbList = itemBillProjects.getNodesByKind("01");

        if (!ObjectUtils.isEmpty(qdList)){
            let set = new Set();
            for (const [key, value] of qdList) {
                await this.getParentId(value,set);
            }

            if (!ObjectUtils.isEmpty(zfbList)) {
                for (const [key, value] of zfbList) {
                    if (!set.has(value.sequenceNbr)) {
                        if (type == "fbfx") {
                            await this.service.itemBillProjectOptionService.removeLine(constructId, singleId, unitId, value, true);
                        } else {
                            await this.service.stepItemCostService.removeLine(constructId, singleId, unitId, value, true);
                        }
                    }
                }
            }

            if (!ObjectUtils.isEmpty(fbList)) {
                for (const [key, value] of fbList) {
                    if (!set.has(value.sequenceNbr)) {
                        if (type == "fbfx") {
                            await this.service.itemBillProjectOptionService.removeLine(constructId, singleId, unitId, value, true);
                        } else {
                            await this.service.stepItemCostService.removeLine(constructId, singleId, unitId, value, true);
                        }
                    }
                }
            }

        }

    }

    /**
     * 递归获取父级ID set
     * @param qd
     * @param set
     * @returns {Promise<void>}
     */
    async getParentId(qd,set){
        if (!ObjectUtils.isEmpty(qd.parent)){
            set.add(qd.parent.sequenceNbr);
            this.getParentId(qd.parent,set);
        }
    }


    /**
     * 项目级别的默认费用
     */


    constructDefaultXM =[
        {amount:0,show:true,name:'项目总造价'},
        {amount:0,show:true,name:'设备费'},
        {amount:0,show:true,name:'分部分项合计'},
        {amount:0,show:true,name:'单价措施项目合计'},
        {amount:0,show:true,name:'其他项目合计'},
        {amount:0,show:true,name:'其他总价措施项目合计'},
        {amount:0,show:true,name:'税金'},
        {amount:0,show:true,name:'安全生产、文明施工费（单位工程汇总）'},
        {amount:0,show:true,name:'安全生产、文明施工费（含税）'},
        {amount:0,show:true,name:'规费合计'},
        {amount:0,show:false,name:'措施项目合计'},
        {amount:0,show:false,name:'人工费合计'},
        {amount:0,show:false,name:'材料费合计'},
        {amount:0,show:false,name:'机械费合计'},
        {amount:0,show:false,name:'主材费合计'},
        {amount:0,show:false,name:'管理费合计'},
        {amount:0,show:false,name:'利润合计'}
    ]

    /**
     * 单相级别费用
     * @type {{}}
     */
    singleDefaultXM = [
        {amount:0,show:true,name:'工程造价'},
        {amount:0,show:true,name:'设备费'},
        {amount:0,show:true,name:'分部分项合计'},
        {amount:0,show:true,name:'单价措施项目合计'},
        {amount:0,show:true,name:'其他项目合计'},
        {amount:0,show:true,name:'其他总价措施项目合计'},
        {amount:0,show:true,name:'税金'},
        {amount:0,show:true,name:'安全生产、文明施工费'},
        {amount:0,show:false,name:'规费合计'},
        {amount:0,show:false,name:'措施项目合计'},
        {amount:0,show:false,name:'人工费合计'},
        {amount:0,show:false,name:'材料费合计'},
        {amount:0,show:false,name:'机械费合计'},
        {amount:0,show:false,name:'主材费合计'},
        {amount:0,show:false,name:'管理费合计'},
        {amount:0,show:false,name:'利润合计'}
    ]

    unit12DefaultXM =[
        {name:'工程造价',amount:0,show:true},
        {name:'设备费',amount:0,show:true},
        {name:'分部分项合计',amount:0,show:true},
        {name:'单价措施项目合计',amount:0,show:true},
        {name:'其他项目合计',amount:0,show:true},
        {name:'规费',amount:0,show:true},
        {name:'进项税额',amount:0,show:true},
        {name:'销项税额',amount:0,show:true},
        {name:'增值税应纳税额',amount:0,show:true},
        {name:'其他总价措施项目合计',amount:0,show:true},
        {name:'附加税费',amount:0,show:true},
        {name:'税金',amount:0,show:true},
        {name:'安全生产、文明施工费',amount:0,show:true},
        {name:'措施项目合计',amount:0,show:false},
        {name:'人工费合计',amount:0,show:false},
        {name:'材料费合计',amount:0,show:false},
        {name:'机械费合计',amount:0,show:false},
        {name:'主材费合计',amount:0,show:false},
        {name:'管理费合计',amount:0,show:false},
        {name:'利润合计',amount:0,show:false}
    ]

    unit22DefaultXM =[
        {name:'工程造价',amount:0,show:true},
        {name:'设备费',amount:0,show:true},
        {name:'分部分项合计',amount:0,show:true},
        {name:'单价措施项目合计',amount:0,show:true},
        {name:'其他项目合计',amount:0,show:true},
        {name:'其他总价措施项目合计',amount:0,show:true},
        {name:'税金',amount:0,show:true},
        {name:'安全生产、文明施工费',amount:0,show:true},
        {name:'措施项目合计',amount:0,show:false},
        {name:'人工费合计',amount:0,show:false},
        {name:'材料费合计',amount:0,show:false},
        {name:'机械费合计',amount:0,show:false},
        {name:'主材费合计',amount:0,show:false},
        {name:'管理费合计',amount:0,show:false},
        {name:'利润合计',amount:0,show:false}
    ]
    /**
     * 费用查看
     * @param args
     * @returns {Promise<void>}
     */
    async costView(args){
        let{constructId,levelType} = args

        //不管选那个层级 一定会有工程项目级别的返回值
        let construct =await this.getCostAnalysisData({levelType:1,constructId:constructId});
        let res={};
        if(levelType == 3){
            let unit =PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId)
            let unitRes =await this.generateSingleCostAnalysisData([unit]);

            let unitXM = []
            if(unit.costView){
                unitXM = unit.costView
            }else {
                if(PricingFileFindUtils.is22Unit(unit)){
                    unitXM = this.unit22DefaultXM
                }else {
                    unitXM = this.unit12DefaultXM
                }

            }
            await this.setValue(unitXM,unitRes[0])
            res. unitXM = unitXM;
        }

        if(levelType == 2){
            let single =await this.getCostAnalysisData(args)
            if(single.costAnalysisSingleVOList){
                let singleProject  = await PricingFileFindUtils.getSingleProject(args.constructId,args.singleId)
                let singleXM=[]
                if(singleProject.costView){
                    singleXM = singleProject.costView
                }else {
                    singleXM = this.singleDefaultXM
                }
                await this.setValue(singleXM,single.costAnalysisSingleVOList)
                res.singleXM = singleXM
            }
        }
        if(construct.costAnalysisConstructVOList){

            //增加固定安文费

            let projectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
            let costAnalysisSingleVO = new CostAnalysisSingleVO();
            costAnalysisSingleVO.projectName = "安全生产、文明施工费（含税）";
            costAnalysisSingleVO.safeFee = NumberUtil.add(projectObj.securityFee,0)
            costAnalysisSingleVO.safeFeeTax =NumberUtil.add(projectObj.securityFee,0)//含税 下方计算 安全生产、文明施工费（含税）用
            construct.costAnalysisConstructVOList.push(costAnalysisSingleVO);


            const xm = construct.costAnalysisConstructVOList.reduce((acc, obj) => {
                for (const key in obj) {
                    if (typeof obj[key] === 'number') {
                        acc[key] = NumberUtil.add ((acc[key] || 0) ,obj[key]);
                    }
                }
                return acc;
            }, {});

            let constructXM =[]
            if(projectObj.costView){
                //如果设置过 显示和上下移动保存到内存中
                constructXM = projectObj.costView
            }else {
                constructXM = this.constructDefaultXM
            }

            await this.setValue(constructXM,xm)

            res.constructXM  = constructXM
        }

        return res
    }

    /**
     * 设置值
     * @param data
     * @param amount
     * @returns {Promise<void>}
     */
    async setValue(data,amount){

        for (let i = 0; i < data.length; i++) {
            switch (data[i].name) {
                case '工程造价':
                    data[i].amount = amount.gczj
                    break;
                case '项目总造价':
                    data[i].amount = amount.gczj
                    break;
                case '设备费':
                    data[i].amount = amount.sbfsj
                    break;
                case '分部分项合计':
                    data[i].amount = amount.fbfxhj
                    break;
                case '单价措施项目合计':
                    data[i].amount = amount.djcsxhj
                    break;
                case '其他项目合计':
                    data[i].amount = amount.qtxmhj
                    break;
                case '规费':
                    data[i].amount = amount.gfee
                    break;
                case '规费合计':
                    data[i].amount = amount.gfee
                    break;
                case '进项税额':
                    data[i].amount = amount.jxse
                    break;
                case '销项税额':
                    data[i].amount = amount.xxse
                    break;
                case '增值税应纳税额':
                    data[i].amount = amount.zzsynse
                    break;
                case '其他总价措施项目合计':
                    data[i].amount = amount.zjcsxhj
                    break;
                case '附加税费':
                    data[i].amount = amount.fjse
                    break;
                case '税金':
                    data[i].amount = amount.sj
                    break;
                case '安全生产、文明施工费':
                    data[i].amount = amount.safeFee
                    break;
                case '安全生产、文明施工费（单位工程汇总）':
                    data[i].amount = amount.safeFee
                    break;
                case '安全生产、文明施工费（含税）':
                    data[i].amount = NumberUtil.add( amount.safeFeeTax,0)
                    break;
                case '措施项目合计':
                    data[i].amount = amount.csxhj
                    break;
                case '人工费合计':
                    data[i].amount = NumberUtil.numberScale2(NumberUtil.addParams(amount.fbfxrgf,amount.djcsxrgf,amount.zjcsxrgf))
                    break;
                case '材料费合计':
                    data[i].amount = NumberUtil.numberScale2(NumberUtil.addParams(amount.fbfxclf,amount.djcsxclf,amount.zjcsxclf))
                    break;
                case '机械费合计':
                    data[i].amount = NumberUtil.numberScale2(NumberUtil.addParams(amount.fbfxjxf,amount.djcsxjxf,amount.zjcsxjxf))
                    break;
                case '主材费合计':
                    data[i].amount = NumberUtil.numberScale2(NumberUtil.addParams(amount.fbfxzcf,amount.djcsxzcf,amount.zjcsxzcf))
                    break;
                case '管理费合计':
                    data[i].amount = NumberUtil.numberScale2(NumberUtil.addParams(amount.fbfxglf,amount.djcsxglf,amount.zjcsxglf))
                    break;
                case '利润合计':
                    data[i].amount = NumberUtil.numberScale2(NumberUtil.addParams(amount.fbfxlr,amount.djcsxlr,amount.zjcsxlr))
                    break;
            }
        }
    }



    async choseCost(args){
        let {xm,constructId,singleId,unitId,levelType} = args
        if(levelType ==1){
            let construct =PricingFileFindUtils.getProjectObjById(constructId)
            construct.costView = xm
        }
        if(levelType ==2){
            let single =PricingFileFindUtils.getSingleProject(constructId,singleId)
            single.costView = xm
        }
        if(levelType ==3){
            let unit =PricingFileFindUtils.getUnit(constructId,singleId,unitId)
            unit.costView = xm
        }
    }





    moveUpDown(args){
        let {xm,constructId,singleId,unitId,levelType,move,index} = args
        let costView =[]
        if(levelType ==1){
            let construct =PricingFileFindUtils.getProjectObjById(constructId)

            if(construct.costView){
                costView = construct.costView
            }else {
                costView = this.constructDefaultXM
            }
        }
        if(levelType ==2){
            let single =PricingFileFindUtils.getSingleProject(constructId,singleId)
            if(single.costView){
                costView = single.costView
            }else {
                costView = this.singleDefaultXM
            }
        }
        if(levelType ==3){
            let unit =PricingFileFindUtils.getUnit(constructId,singleId,unitId)
            if(unit.costView){
                costView = unit.costView
            }else {
                if(PricingFileFindUtils.is22Unit(unit)){
                    costView = this.unit22DefaultXM
                }else {
                    costView = this.unit12DefaultXM
                }

            }
        }

        let indexNum = Number(index)
        if(move=='up'){
            costView = this.moveUp(costView,indexNum)
        }else {
            costView = this.moveDown(costView,indexNum)
        }

    }

    moveUp(array, index) {
        if (index > 0) {
            array.splice(index - 1, 0, array.splice(index, 1)[0]);
        }
        return array;
    }

    moveDown(array, index) {
        if (index >= 0 && index < array.length - 1) {
            array.splice(index + 1, 0, array.splice(index, 1)[0]);
        }
        return array;
    }

    /**
     * 批量修改主材 查询
     * @returns {Promise<void>}
     */
    async zcRcjBatchSelect(args){
        //pageType = fbfx 分部分项 ,pageType = csxm 措施项目
        let {constructId,singleId,unitId,pageType} = args
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        if (ObjectUtils.isEmpty(unit) || ObjectUtils.isEmpty(unit.constructProjectRcjs)){
            return ;
        }

        let deRcjMap = new Map();
        //获取人材机对应定额id
        for (let rcj of unit.constructProjectRcjs) {
            if (rcj.kind == 5 || rcj.kind == 4){
                let deepCopyRcj = ConvertUtil.deepCopy(rcj);
                if (deRcjMap.has(rcj.deId)){
                    let deRcjList = deRcjMap.get(rcj.deId);
                    deRcjList.push(deepCopyRcj);
                }else {
                    let deRcjList = [deepCopyRcj];
                    deRcjMap.set(rcj.deId,deRcjList);
                }
            }
        }

        if (ObjectUtils.isEmpty(deRcjMap)){
            return ;
        }
        let allNodes = null;
        if (pageType == "fbfx"){
            //分部分项
            let itemBillProjects = unit.itemBillProjects.getAllNodes();
            allNodes = ConvertUtil.deepCopy(itemBillProjects);
        }else if (pageType == "csxm"){
            //措施项目
            let measureProjectTables = unit.measureProjectTables.getAllNodes();
            allNodes = ConvertUtil.deepCopy(measureProjectTables);
        }

        if (ObjectUtils.isEmpty(allNodes)){
            return ;
        }

        //获取 有主材设备的清单id list
        let qdMap = new Map();
        for (let allNode of allNodes) {
            if (allNode.kind == "04" && deRcjMap.has(allNode.sequenceNbr)){
                if (qdMap.has(allNode.parentId)){
                    let deList = qdMap.get(allNode.parentId);
                    deList.push(allNode);
                }else {
                    let deList = [allNode];
                    qdMap.set(allNode.parentId,deList);
                }
            }
        }

        let arr = [];
        for (let allNode of allNodes) {
            if (allNode.kind !="03" && allNode.kind != "04" ){
                arr.push(allNode);
            }else if (allNode.kind == "03" && qdMap.has(allNode.sequenceNbr)){
                let qdFeatures = await this.service.listFeatureService.getQdFeatureListByQdId(allNode.sequenceNbr, constructId, singleId, unitId);
                allNode.qdFeatures = qdFeatures;
                arr.push(allNode);
                let deList = qdMap.get(allNode.sequenceNbr);
                for (let de of deList) {
                    de.qdFeatures = qdFeatures;
                    arr.push(de);
                    let rcjList = deRcjMap.get(de.sequenceNbr);
                    for (let rcj of rcjList) {
                        rcj.qdFeatures = qdFeatures;
                        rcj.name = rcj.materialName;
                        rcj.parentId = rcj.deId;
                        arr.push(rcj);
                    }
                }
            }/*else if (allNode.kind == "04" && allNode.rcjFlag == 1){
                let qdFeatures = await this.service.listFeatureService.getQdFeatureListByQdId(allNode.parentId, constructId, singleId, unitId);
                allNode.qdFeatures = qdFeatures;
                arr.push(allNode);
                let deepCopy = ConvertUtil.deepCopy(allNode);
                deepCopy.parentId = deepCopy.sequenceNbr;
                arr.push(deepCopy);
            }*/
        }

        /*for (let arrElement of arr) {
            delete arrElement.next;
            delete arrElement.prev;
            if (!ObjectUtils.isEmpty(arrElement.parent)){
                if (!ObjectUtils.isEmpty(arrElement.parent.children)){
                    delete arrElement.parent.children;
                }
            }
        }*/

        return arr;
    }

    /**
     *
     * @param args
     * @returns {Promise<void>}
     */
    async zcRcjBatchUpdate(args){
        //pageType = fbfx 分部分项 ,pageType = csxm 措施项目
        let {constructId,singleId,unitId,type,libraryCode,constructProjectRcjList,pageType} = args
        let deepCopy = ConvertUtil.deepCopy(args);
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        if (ObjectUtils.isEmpty(unit) || ObjectUtils.isEmpty(constructProjectRcjList)){
            return ;
        }

        for (let constructProjectRcjListElement of constructProjectRcjList) {
            if (constructProjectRcjListElement.rcjOrDe == "rcj") {
                deepCopy.sequenceNbr = constructProjectRcjListElement.sequenceNbr;
                deepCopy.type = 1;
                deepCopy.libraryCode = constructProjectRcjListElement.libraryCode;
                delete constructProjectRcjListElement.sequenceNbr;
                delete constructProjectRcjListElement.type;
                delete constructProjectRcjListElement.libraryCode;
                delete constructProjectRcjListElement.rcjOrDe;
                deepCopy.constructProjectRcj = constructProjectRcjListElement;
                await this.service.constructProjectRcjService.updateConstructAndDetailRcj(deepCopy);

                if (constructProjectRcjListElement.rcjLabelDe == 1){
                    //代表定额级别人材机
                    if (!ObjectUtils.isEmpty(constructProjectRcjListElement.materialName)){
                        deepCopy.unitWorkId = unitId;
                        deepCopy.pointLineId = constructProjectRcjListElement.deId;
                        deepCopy.column = "name";
                        deepCopy.value = constructProjectRcjListElement.materialName;

                        if (pageType =="fbfx"){
                            await this.service.itemBillProjectOptionService.updateByList(constructId, singleId, unitId, constructProjectRcjListElement.deId,
                                {column:"name", value:constructProjectRcjListElement.materialName});
                        }else {
                            await this.service.stepItemCostService.upDateOnList(constructId, singleId, unitId, constructProjectRcjListElement.deId,
                                {column:"name", value:constructProjectRcjListElement.materialName});
                        }
                    }

                    if (!ObjectUtils.isEmpty(constructProjectRcjListElement.specification)){
                        if (pageType =="fbfx"){
                            await this.service.itemBillProjectOptionService.updateByList(constructId, singleId, unitId, constructProjectRcjListElement.deId,
                                {column:"specification", value:constructProjectRcjListElement.specification});
                        }else {
                            await this.service.stepItemCostService.upDateOnList(constructId, singleId, unitId, constructProjectRcjListElement.deId,
                                {column:"specification", value:constructProjectRcjListElement.specification});
                        }
                    }
                }
            }else if (constructProjectRcjListElement.rcjOrDe == "de") {
                deepCopy.unitWorkId = unitId;
                deepCopy.pointLineId = constructProjectRcjListElement.sequenceNbr;
                deepCopy.column = constructProjectRcjListElement.column;
                deepCopy.value = constructProjectRcjListElement.value;
                if (pageType =="fbfx"){
                    await this.service.itemBillProjectOptionService.updateByList(constructId, singleId, unitId, constructProjectRcjListElement.sequenceNbr,
                        {column:constructProjectRcjListElement.column, value:constructProjectRcjListElement.value});
                }else {
                    await this.service.stepItemCostService.upDateOnList(constructId, singleId, unitId, constructProjectRcjListElement.sequenceNbr,
                        {column:constructProjectRcjListElement.column, value:constructProjectRcjListElement.value});
                }
            }
        }

        await this.service.autoCostMathService.autoCostMath({
            constructId: args.constructId,
            singleId: args.singleId,
            unitId: args.unitId
        });

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");

    }


    getMainFeeFile(args){
        let {constructId, singleId, unitId} = args
       return   PricingFileFindUtils.getMainFeeFile(constructId, singleId, unitId);
    }

}
UnitProjectService.toString = () => '[class UnitProjectService]';
module.exports = UnitProjectService;
