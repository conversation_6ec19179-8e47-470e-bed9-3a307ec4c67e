/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2024-02-02 10:35:04
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-10-30 14:56:32
 */
import infoMode from '@/plugins/infoMode';
import gSdetailApi from '@/gaiSuanProject/api/projectDetail.js';
import csProject from '@gaiSuan/api/csProject';
import detailApi from '@/api/projectDetail.js';
import { projectDetailStore } from '@/store/projectDetail';
import { useVirtualList } from '@/hooks/useVirtualList';
import { message } from 'ant-design-vue';
import { ref, nextTick, toRaw } from 'vue';
import {
  quantityExpressionHandler,
  removeSpecialCharsFromPrice,
  everyNumericHandler,
} from '@/utils/index';
import xeUtils from 'xe-utils';
import da from '../views/data';
import deMapFun from '@/gaiSuanProject/views/projectDetail/customize/deMap';

// vexTable.value: 表格ref
// codeField：编码字段名
// nameField: 名称字段名
// operateList: 操作列表
// resetCellData 重置当前单元格方法

export const useSubItem = ({
  operateList,
  vexTable,
  codeField = 'deCode',
  nameField = 'deName',
  frameSelectRef = null,
  pageType,
  resetCellData = () => {},
  checkUnit = () => {},
  emits = () => {},
  api = {
    updateData: detailApi.updateFbData,
    gsUpdateData: gSdetailApi.updateFbData,
    getList: detailApi.queryBranchDataByFbIdV1,
    getGsList: gSdetailApi.getDeTree4Unit,
  },
}) => {
  const isFBFX = codeField === 'deCode'; // 是否分部分项
  const projectStore = projectDetailStore();
  let otherApi = {
    ys: detailApi,
    gs: gSdetailApi,
  }[projectStore.$state.type];
  console.log('otherApi', projectStore.$state.type, otherApi);
  let currentInfo = ref();
  let feeFileList = ref([]);
  let tableData = ref([]);
  let originalTableData = ref([]); // 表格原始数据
  let selectData = ref(null);
  let loading = ref(false);
  let page = ref(1);
  let limit = ref(300000);
  let lockFlag = ref(0); // 整体锁定状态 0 不锁定 1 锁定
  let addDataSequenceNbr = ref('');
  let isIndexAddInfo = ref(false); // 是否从索引页面添加数据
  let initDataListObject = {}; // 初始化数据列表
  //多单位------
  let addCurrentInfo = ref(); // 多单位选择时选中的清单数据
  let showUnitTooltip = ref(false); // 是否多单位选择
  let selectUnit = ref(); // 多单位时选择单位
  //end----------
  // 提示信息框------
  let infoVisible = ref(false); // 提示信息框是否显示
  let infoText = ref('工程量明细已被调用，是否清空工程量明细？'); // 提示信息框的展示文本
  let iconType = ref(''); // 提示信息框的图标
  let isSureModal = ref(false); // 提示信息框是否为确认提示框
  //end------
  let isUpdateQuantities = ref(false); // 是否更新工程量明细数据
  let isUpdateFile = ref(false);
  const indexVisible = ref(false);
  // 编辑弹框相关
  let editKey = ref(''); // 单独编辑弹框记录得当前编辑字段
  let isShowModel = ref(false);

  let qdVisible = ref(false);
  let deVisible = ref(false);
  let rcjVisible = ref(false);
  let priceVisible = ref(false);
  let deCode = ref('');
  let isSortQdCode = ref(false); // 编码重复是否继续
  let isClearEdit = ref(false); // 是否手动清除编辑状态
  let ishasRCJList = ref(false); //人材机定额且有人材机明细数据---不可编辑单价

  let mainMaterialTableData = ref([]); // 定额数据下挂的主材数据,如若为空,则不展示设置主材价格弹框
  let materialVisible = ref(false); // 是否设置主材市场价弹框
  let materialType = ref('')
  let rowType = ref('');
  let materialRow = ref({})
  let DJGCrefreshFeeFile = ref(false); //单价构成需要刷新取费文件列表
  let batchDeleteVisible = ref(false); // 批量删除弹框是否展示
  let batchDataType = ref(1); // 批量删除类型 1 批量删除所有临时删除项  2 批量删除所有工程量为0项
  let openLevelCheckList = ref();
  // const {
  //   initVirtual,
  //   getScroll,
  //   renderedList,
  //   init,
  //   EnterType,
  //   onDragHeight,
  //   scrollToPosition,
  // } = useVirtualList();

  /**
   * 首字母大写 originalBdCode\originalFxCode
   */
  const originalCode = `original${codeField
    .charAt(0)
    .toUpperCase()}${codeField.slice(1)}`;
  const originalName =
    nameField === 'name'
      ? 'originalFxName'
      : `original${nameField.charAt(0).toUpperCase()}${nameField.slice(1)}`;
  const isUpdateByRow = (row, field, newValue, oldValue) => {
    return row[field] !== oldValue;
  };
  /**
   * 编辑完成之后
   * @param {*} param0
   * @returns
   */
  const editClosedEvent = ({ row, column }, newValue, oldValue) => {
    if (isClearEdit.value) return;
    if (row.kind === '05') {
      editRcj({ row, column }, newValue, oldValue);
      return;
    }
    let field = column.field;
    const codeValue = row[codeField]; // deCode,fxCode
    console.log('row[field]', row[field], newValue, oldValue);
    // 判断单元格值是否被修改
    if (field === 'originalQuantity') {
      if (newValue == oldValue) {
        return;
      } else {
        if (['originalQuantity'].includes(field)) {
          console.log('originalQuantity', row[field]);
          // if (!/^\d+(\.\d+)?$/.test(newValue)) {
          //   row[field] = oldValue;
          //   // vexTable.value.revertData(currentInfo.value, 'originalQuantity');
          //   return message.error('非法数据');
          // }
          function replaceChineseBrackets(str) {
            return str
              .replace(/[（（]/g, '(')
              .replace(/[））]/g, ')')
              .replace(/×/g, '*')
              .replace(/÷/g, '/');
          }
          row.originalQuantity = replaceChineseBrackets(newValue);
          if (row.originalQuantityExpression === 'GCLMXHJ') {
            infoMode.show({
              iconType: 'icon-qiangtixing',
              infoText:
                '已从工程量明细中引用，修改工程量将清空明细区数据，是否继续？',
              confirm: () => {
                updateGclData(row, field, oldValue);
                infoMode.hide();
              },
              close: () => {
                row.originalQuantity = oldValue;
                infoMode.hide();
                // vexTable.value.revertData(currentInfo.value, 'originalQuantity');
              },
            });
          } else {
            updateGclData(row, field, oldValue);
          }
        }
      }
    } else if (row[field] && newValue == oldValue && field != 'type') return;
    console.log(
      'aaa',
      newValue == oldValue,
      row[field] && newValue == oldValue && field != 'type'
    );
    if (
      field === codeField &&
      !newValue &&
      !['00', '01', '02', '-1'].includes(row.kind)
    ) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-qiangtixing',
        infoText: '编码不可为空',
        confirm: () => {
          infoMode.hide();
          currentInfo.value[codeField] = currentInfo.value[originalCode];
        },
      });
      return;
    }

    nextTick(() => {
      console.log('222field---', field, row[field]);
      // 如果索引弹窗出现，则不出现补充弹窗
      if ([codeField].includes(column.field) && indexVisible.value) return;
      if (deMapFun.isDe(row.kind) && newValue) {
        isMainQuotaLibraryCode(field, newValue);
      }

      if (
        ['resQty', 'totalNumber', 'price', 'costMajorName', 'unit'].includes(
          field
        )
      ) {
        if (
          (field === 'price' &&
            row.CSum === 0 &&
            row.JSum === 0 &&
            row.RSum === 0 &&
            newValue != 0 &&
            ['03', '04'].includes(row.kind)) ||
          ['08'].includes(row.kind)
        ) {
          row.originaPrice = oldValue;
          row[field] = newValue;
          priceVisible.value = true;
          return;
        } else if (
          field === 'price' &&
          row.CSum === 0 &&
          row.JSum === 0 &&
          row.RSum === 0 &&
          newValue == 0 &&
          !['06'].includes(row.kind)
        ) {
          return;
        }
        // if (field === 'price' && newValue == 0) {
        //   row.price = Math.round(row.price * 100) / 100;
        //   if (Number(newValue) === 0) {
        //     infoVisible.value = true;
        //     isSureModal.value = true;
        //     infoText.value = '定额单价不能为0';
        //     iconType.value = 'icon-qiangtixing';
        //     row.price = oldValue;
        //     return;
        //   }
        // }
        if (
          !/^\d+(\.\d+)?$/.test(newValue) &&
          ['resQty', 'totalNumber'].includes(field)
        ) {
          row[field] = oldValue;
          return message.error('非法数据');
        }
        if (!/^(-?\d+(\.\d+)?)$/.test(newValue) && ['price'].includes(field)) {
          row[field] = oldValue;
          return message.error('非法数据');
        }
        if (field == 'unit' && !row[field]) {
          return;
        }
        console.log('单价！！！！！！！');
        row[field] = newValue ?? oldValue;
        if (newValue !== '') priceAndTolalEditEvent(field, row, oldValue); //单价编辑
      }
      let rcjIds = ['RSum', 'CSum', 'JSum', 'ZSum', 'SSum'];
      if (rcjIds.includes(field)) {
        row[field] = newValue ?? oldValue;
        updateRCJPrice(row, field, oldValue);
      }
      if (
        ![
          codeField,
          'resQty',
          'originalQuantity',
          'totalNumber',
          'price',
          'costMajorName',
          'unit',
          ...rcjIds,
        ].includes(field)
      ) {
        if (['type'].includes(field)) {
          row.deResourceKind = newValue;
        } else {
          row[field] = newValue;
        }
        updateFbData(row, field);
      }
    });
  };
  const editRcj = ({ row, column }, newValue, oldValue) => {
    if (newValue == oldValue) {
      return;
    }
    let field = column.field;
    if (!row.deCode) {
      currentInfo.value.deCode = currentInfo.value.originalDeCode;
      return;
    }
    let rcjRow = xeUtils.clone(row, true);
    if (field === 'deCode') {
      field = 'materialCode';
      rcjRow.materialCode = newValue;
    }
    if (field === 'deName') {
      field = 'materialName';
      rcjRow.materialName = newValue;
    }
    if (field === 'originalQuantity') {
      field = 'totalNumber';
      rcjRow.totalNumber = newValue;
    }
    if (field === 'price') {
      field = 'marketPrice';
      rcjRow.marketPrice = newValue;
    }
    if (field === 'type') {
      rcjRow.deResourceKind = newValue;
    }
    if (field === 'resQty') {
      rcjRow.resQty = newValue;
    }
    if (field === 'unit') {
      rcjRow.unit = newValue;
    }
    if (field === 'specification') {
      rcjRow.specification = newValue;
    }
    updateConstructRcj(rcjRow, field);
    isRcjCodeMainQuotaLibrary(field, row.materialCode);
  };
  /**
   * 修改定额内的单价
   * @param {Object} row - 当前行
   * @param {string} field -  column field
   */
  const updateRCJPrice = (row, field, oldValue) => {
    let idMap = {
      RSum: 1,
      CSum: 2,
      JSum: 3,
      ZSum: 5,
      SSum: 4,
    };
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      deRowId: row.sequenceNbr,
      price: row[field],
      type: idMap[field],
    };
    console.log('updateRCJPrice', apiData);
    otherApi.updateRCJPrice(apiData).then(res => {
      if (res.status === 200) {
        message.success('修改成功');
        queryBranchDataById('noPosition');
      } else {
        row[field] = oldValue;
        message.error(res.message);
      }
    });
  };
  /**
   * 判断输入的定额编码是否为主定额库编码
   * @param {*} field
   * @param {*} deCode
   * @returns
   */
  const isMainQuotaLibraryCode = (field, deCode) => {
    console.log('判断是否为主定额册下的标准定额参数', field, codeField);
    if (field !== codeField) return;
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      deRowId: currentInfo.value.sequenceNbr,
      deCode,
    };
    console.log('判断是否为主定额册下的标准定额参数', apiData);
    otherApi.queryDeAndAppendDe(apiData).then(res => {
      console.log('判断是否为主定额册下的标准定额', res);
      if (res.status === 200) {
        if (res.result) {
          // updateDeReplaceData(code);
          addDeInfo.value = res.result;
          queryBranchDataById('queryRule');
        } else {
          isStandardDe(deCode);
        }
      }
    });
  };

  /**
   * 判断输入的定额编码是否是标准定额
   * @param {*} code
   */
  const isStandardDe = code => {
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    otherApi.isStandardDe(apiData).then(res => {
      console.log('判断输入的定额编码是否是标准定额', res);
      if (res.status === 200) {
        if (res.result) {
          updateDeReplaceData(code);
        } else {
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '标准定额库下未找到该定额，是否补充定额？',
            confirm: () => {
              deVisible.value = true;
              deCode.value = code;
              infoMode.hide();
            },
            close: () => {
              infoMode.hide();
              currentInfo.value[codeField] = currentInfo.value[originalCode];
            },
          });
        }
        console.log('判断输入的定额编码是否为主定额库编码', res);
      }
    });
  };

  /**
   * 分部分项 措施项目 替换定额数据
   * @param {*} code
   */
  const updateDeReplaceData = code => {
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      deRowId: currentInfo.value.sequenceNbr,
      code,
    };
    console.log('通过标准编码插入定额', apiData);
    otherApi.appendUserDe(apiData).then(res => {
      console.log('通过标准编码插入定额结果', res);
      if (res.status === 200 && res.result) {
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        deVisible.value = false;
        message.success('定额替换成功');
        queryBranchDataById();
      }
    });
  };
  // 修改人材机定额
  const updateConstructRcj = (row, field) => {
    if (field === 'materialCode') return;
    let value;
    if (field === 'type') {
      value = row.deResourceKind;
    }

    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      deId: row.deRowId,
      rcjDetailId: row.sequenceNbr,
      constructRcj: {
        [field === 'type' ? 'kind' : field]:
          field === 'type' ? value : row[field],
      },
    };
    console.log('修改人材机明细数据参数', apiData);
    otherApi.updateConstructRcj(apiData).then(res => {
      if (res.status === 200) {
        message.success('修改成功');
        queryBranchDataById('noPosition');
      }
    });
  };
  /**
   * 判断输入的材料编码是否与主定额库编码相同
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isRcjCodeMainQuotaLibrary = (field, code) => {
    if (field !== codeField) return;
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    otherApi.isRcjCodeMainQuotaLibrary(apiData).then(res => {
      if (res.status === 200) {
        if (res.result) {
          // 输入的编码为主定额库编码
          updateBjqRcjReplaceData(code);
        } else {
          isStandardRcj(code);
        }
      }
    });
  };

  /**
   * 分部分项 措施项目 替换编辑区的人材机数据
   * @param {*} code
   */
  const updateBjqRcjReplaceData = code => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
      code: code,
      region: 0,
    };
    otherApi.updateBjqRcjReplaceData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        message.success('人材机替换成功');
        queryBranchDataById();
      }
    });
  };

  /**
   * 判断输入的材料编码是否标准人材机数据
   * @param {*} code
   */
  const isStandardRcj = code => {
    let apiData = {
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      code: code,
    };
    otherApi.isStandardRcj(apiData).then(res => {
      console.log('=============');
      if (res.status === 200) {
        if (res.result) {
          updateBjqRcjReplaceData(code);
        } else {
          infoMode.show({
            iconType: 'icon-querenshanchu',
            infoText: '标准定额下不存在该材料编码,是否补充人材机？',
            confirm: () => {
              rcjVisible.value = true;
              deCode.value = code;
              infoMode.hide();
            },
            close: () => {
              currentInfo.value.deCode = currentInfo.value.originalBdCode;
              infoMode.hide();
            },
          });
        }
      }
    });
  };

  /**
   * 判断是否是标准清单
   * @param {*} field
   * @param {*} code
   * @returns
   */
  const isStandQd = (field, code) => {
    if (field !== codeField) return;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      code: code,
    };
    otherApi.isStandQd(apiData).then(res => {
      console.log('判断是否是标准清单', res);
      if (res.status === 200) {
        if (res.result) {
          const unit = res.result.unit;
          const unitArr = Array.isArray(unit) ? unit : unit?.split('/');
          res.result.unit = unitArr;
          addCurrentInfo.value = res.result;
          addCurrentInfo.value.bdCodeLevel04 = code;
          if (code.length === 9) {
            if (unitArr && unitArr.length > 1) {
              showUnitTooltip.value = true;
            } else {
              updateQdByCode(code, unitArr[0]);
            }
          } else {
            isQdCodeExist(code, res.result);
          }
        } else {
          isQdCodeExist(code, res.result);
        }
      }
    });
  };

  /**
   * 判断清单编码是否存在
   * @param {*} code
   * @param {*} obj
   */
  const isQdCodeExist = (code, obj) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      code: code,
    };
    otherApi.isQdCodeExist(apiData).then(res => {
      console.log('判断清单编码是否存在', res, obj);
      // if (res.status === 200) {
      if (res) {
        // 若存在,则弹框提示是否继续
        infoMode.show({
          iconType: 'icon-querenshanchu',
          infoText: obj ? '' : '是否补充清单？',
          descText: obj
            ? '当前单位工程有相同清单编码，是否自动排序清单编码？'
            : '当前单位工程有相同清单编码，是否继续?',
          confirm: () => {
            if (!obj) {
              deCode.value = code;
              qdVisible.value = true;
              isSortQdCode.value = false;
            } else {
              isSortQdCode.value = true;
              if (obj.unit && obj.unit.length > 1) {
                showUnitTooltip.value = true;
              } else {
                updateQdByCode(code, obj.unit ? obj.unit[0] : null);
              }
            }
            infoMode.hide();
          },
          close: () => {
            infoMode.hide();
            if (obj) {
              isSortQdCode.value = false;
              if (obj.unit && obj.unit.length > 1) {
                showUnitTooltip.value = true;
              } else {
                updateQdByCode(code, obj.unit ? obj.unit[0] : null);
              }
            } else {
              currentInfo.value[codeField] = currentInfo.value[originalCode];
            }
          },
        });
      } else {
        // 根据是否为标准数据判断替换或补充
        if (!obj) {
          deCode.value = code;
          qdVisible.value = true;
        } else {
          if (obj.unit && obj.unit.length > 1) {
            showUnitTooltip.value = true;
          } else {
            updateQdByCode(code, obj.unit ? obj.unit[0] : null);
          }
        }
      }
      // }
    });
  };

  /**
   * 通过标准编码插入清单
   * @param {*} code
   * @param {*} unit
   */
  const updateQdByCode = (code, unit) => {
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
      code: code,
      unit: unit,
      isSortQdCode: isSortQdCode.value,
    };
    console.log('==============标准清单编码插入api参数', apiData);
    otherApi.updateQdByCode(apiData).then(res => {
      console.log('标准清单编码插入', res);
      if (res.status === 200 && res.result) {
        selectUnit.value = '';
        message.success('清单插入成功');
        queryBranchDataById('noPosition');
      }
    });
  };

  const costMajorNameEditEvent = (field, row) => {
    if (field !== 'costMajorName') return;
    console.log(
      'bcd',
      feeFileList.value.filter(x => x.qfName === row.costMajorName)[0].qfCode
    );
    row.costFileCode = feeFileList.value.filter(
      x => x.qfName === row.costMajorName
    )[0].qfCode;
  };
  const priceAndTolalEditEvent = (field, row, oldValue) => {
    console.log('priceAndTolalEditEvent', field, row);

    updateField(field, row, oldValue);
  };
  const updateField = (field, row, oldValue) => {
    console.log('updateField', currentInfo.value);
    let apiRoute = {
      price: 'updatePrice',
      totalNumber: 'updateTotal',
      resQty: 'updateResQty',
      costMajorName: 'updateChargingDiscipline',
      unit: 'updateUnit',
    };
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.id,
      deRowId: JSON.parse(JSON.stringify(row)).sequenceNbr,
      [field]: JSON.parse(JSON.stringify(row))[field],
    };
    if (field === 'costMajorName') {
      apiData.costFileCode = feeFileList.value.filter(
        x => x.qfName === apiData[field]
      )[0].qfCode;
    }
    console.log(
      'otherApi[apiRoute[field]]',
      otherApi[apiRoute[field]],
      apiData
    );
    otherApi[apiRoute[field]](apiData).then(res => {
      if (res.status === 200) {
        message.success('修改成功');
        queryBranchDataById('noPosition');
      } else {
        row[field] = oldValue;
        message.error(res.message);
      }
    });
  };
  // 更新工程量数据
  const updateGclData = (row, field, oldValue) => {
    console.log('更新工程量数据');
    isUpdateFile.value = true;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      deId: row.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      quantity: row.originalQuantity,
    };
    console.log('apiDataupdateQuantity', apiData);
    otherApi.updateQuantity(apiData).then(res => {
      if (res.status === 200 && res.result) {
        message.success('修改成功');
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        queryBranchDataById('noPosition');
      } else {
        row.originalQuantity = oldValue;
        message.error(res.message);
      }
    });
  };
  /**
   * 更新数据
   * @param {*} row
   * @param {*} field
   */
  const updateFbData = (row, field) => {
    if (projectStore.type === 'ys') {
      updateYs(row, field);
    }
    if (projectStore.type === 'gs') {
      updateGs(row, field);
    }
  };
  const updateYs = (row, field) => {
    isUpdateFile.value = true;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitWorkId: projectStore.currentTreeInfo?.id,
      pointLineId: row.sequenceNbr,
      upDateInfo: {
        [nameField]: row[nameField],
        projectAttr: row.projectAttr,
        unit: row.unit,
        costFile: {
          code: row.costFileCode,
          name: row.costMajorName,
        },
        itemCategory: row.itemCategory,
        measureType: row.measureType,
        description: row.description,
        quantityExpression: row.quantityExpression,
        zjfPrice: row.zjfPrice,
        index: row.index,
      },
    };
    api.updateData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        if (res.result.enableUpdatePrice) {
          if (field !== 'seq') {
            message.success('修改成功');
            if (editKey.value) {
              isShowModel.value = false;
            }
            if (infoVisible.value) {
              isUpdateQuantities.value = true;
              infoVisible.value = false;
            }
          }
          if (
            (row.kind === '01' || row.kind === '02' || row.kind === '0') &&
            field === nameField
          ) {
            emits('updateMenuList');
          }
          if (
            field === 'costMajorName' ||
            field === 'zjfPrice' ||
            field === 'quantityExpression' ||
            field === 'quantity'
          ) {
            isUpdateFile.value = true;
          }
          addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
          queryBranchDataById();
        } else {
          infoMode.show({
            isSureModal: true,
            iconType: 'icon-qiangtixing',
            infoText: '调整后存在人工/材料/机械单价为0，请重新输入',
            confirm: () => {
              infoMode.hide();
              currentInfo.value.zjfPrice = currentInfo.value.originalZjfPrice;
            },
          });
        }
      }
    });
  };
  const updateGs = (row, field) => {
    costMajorNameEditEvent(field, row);
    isUpdateFile.value = true;
    let apiData = {
      deRow: JSON.parse(JSON.stringify(row)),
    };
    console.log('概算更新数据参数', apiData);
    api.gsUpdateData(apiData).then(res => {
      console.log('gsUpdateData修改后', res);
      if (res.status === 200) {
        if (field !== 'seq') {
          message.success('修改成功');
          if (editKey.value) {
            isShowModel.value = false;
          }
          if (infoVisible.value) {
            isUpdateQuantities.value = true;
            infoVisible.value = false;
          }
        }
        if (
          (row.kind === '01' || row.kind === '02' || row.kind === '0') &&
          field === nameField
        ) {
          emits('updateMenuList');
        }
        if (
          field === 'costMajorName' ||
          field === 'zjfPrice' ||
          field === 'quantityExpression' ||
          field === 'quantity'
        ) {
          isUpdateFile.value = true;
        }
        if (currentInfo.value) {
          addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        }
        if (field == 'annotations' || field == 'isShowAnnotations') {
          queryBranchDataById('annotations');
        } else {
          queryBranchDataById();
        }
      }
    });
  };
  const initObject = row => {
    let obj = {};
    Object.keys(initDataListObject).forEach(key => {
      obj[key] = JSON.parse(JSON.stringify(row))[key];
    });
    return obj;
  };
  const addLevelToTree = (data, parentLevel = 0) => {
    return data.map(node => ({
      ...node,
      customLevel: parentLevel + 1,
      children:
        (node.children || []).length > 0
          ? addLevelToTree(node.children, parentLevel + 1)
          : [],
    }));
  };
  const getYsList = (EnterTypes, posId) => {
    if (isFBFX && !projectStore.asideMenuCurrentInfo?.sequenceNbr) return;
    if (!isFBFX && (!projectStore.currentTreeInfo?.id || loading.value)) return;
    checkUnit();
    loading.value = true;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
      unitId: projectStore.currentTreeInfo?.id,
      sequenceNbr: projectStore.asideMenuCurrentInfo?.sequenceNbr,
      pageSize: limit.value,
      pageNum: page.value,
      isAllFlag: !!posId,
    };

    api.getList(apiData).then(res => {
      // debugger
      if (res.status === 200) {
        if (!res.result) {
          loading.value = false;
          tableData.value = [];
          // 快速组价存的当前列表数据
          return;
        }

        let testTreeData = xeUtils.toArrayTree(res.result.data, {
          key: 'sequenceNbr',
          parentKey: 'parentId',
        });
        res.result.data = xeUtils.toTreeArray(addLevelToTree(testTreeData));
        changeListHandler(res.result.data);
        tableData.value = res.result.data;
        originalTableData.value = JSON.parse(JSON.stringify(tableData.value));

        // virtualListHandler(EnterTypes, posId);
        nextTick(() => {
          if (projectStore.combinedSearchList?.length > 0) {
            filterData(projectStore.combinedSearchList);
          }
        });

        // frameSelectRef?.value.clearSelect();
        // lockFlagHandler();
        // addDataHandler(posId);
        loading.value = false;
        emits('getCurrentInfo', currentInfo.value);
        setTimeout(() => {
          addDataSequenceNbr.value = '';
          isIndexAddInfo.value = false;
          addCurrentInfo.value = null;
        }, 500);
      }
    });
  };
  const getGsList = (EnterTypes, posId) => {
    // if (isFBFX && !projectStore.asideMenuCurrentInfo?.sequenceNbr) return;
    // if (!isFBFX && (!projectStore.currentTreeInfo?.id || loading.value)) return;
    // checkUnit();
    function countTreeNodes(treeData) {
      let count = 0;

      // 遍历数组中的每个元素
      for (const node of treeData) {
        count++; // 当前节点计数加1

        // 如果节点有子节点，递归计算子节点的数量
        if (node.children && Array.isArray(node.children)) {
          count += countTreeNodes(node.children);
        }
      }

      return count;
    }
    console.log(
      'projectStore.asideMenuCurrentInfo?',
      projectStore.asideMenuCurrentInfo
    );
    loading.value = true;
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.id,
      deRowId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
      posId,
    };
    if (EnterTypes !== 'annotations') {
      apiData['isShowAnnotations'] = false;
    }
    // let res = {
    //   result: da,
    // };
    // tableData.value = res.result
    // console.log('apiData', tableData.value,countTreeNodes(tableData.value));
    // loading.value = false;
    console.info('获取概算项目参数！', apiData);
    api.getGsList(apiData).then(async res => {
      console.info(res);
      if (res.result[0]) {
        if (res.result[0].type == '0') {
          res.result[0].kind = '00';
          res.result[0].deName = res.result[0].deName || '单位工程';
        }
        let conversionRuleList = await conversionRuleListAll();
        const idMap = {};
        // 遍历数据，找到所有type为03的条目
        let testTreeData = xeUtils.toArrayTree(res.result, {
          key: 'sequenceNbr',
          parentKey: 'parentId',
        });
        res.result = xeUtils.toTreeArray(
          addLevelToTree(
            testTreeData,
            projectStore.asideMenuCurrentInfo?.customLevel - 1
          )
        );
        res.result.forEach((item, index) => {
          item.annotationsVisible = item.isShowAnnotations;
          // 标准换算标识
          conversionRuleList.map(a => {
            if (a.sequenceNbr === item.sequenceNbr) {
              item.redArray = a.redArray;
              item.blackArray = a.blackArray;
            }
          });
          idMap[item.deRowId] = item;
          item.key = item.sequenceNbr;
          delete item.children;
          // item.dispNo = index
          if (item.type === '05') {
            item.parentKind = res.result.find(
              a => a.sequenceNbr === item.parentId
            ).type;
          }
          // 判断是否可以上下移动
          let datas = res.result.filter(a => a.parentId == item.parentId);
          let rowIndex = datas.findIndex(
            a => a.sequenceNbr == item.sequenceNbr
          );
          if (rowIndex === 0) {
            item.isFirst = true;
          }
          if (rowIndex === datas.length - 1) {
            item.isLast = true;
          }
          // // 判断是否可以升降级
          // if (item.type === '01') {
          //   item.isUp = true
          //   if(res.result[index+1]?.type !== '02'||index === res.result.length-1) {
          //     item.isDown = true
          //   }
          // }
          // if (item.type === '02') {
          //   if(res.result[index+1]?.type !== '02'||index === res.result.length-1) {
          //     item.isDown = true
          //   }
          //   if(res.result[index-1]?.type !== '02'&&res.result[index-1]?.type !== '01') {
          //     item.isUp = true
          //   }
          // }
          // 查找父级条目
          const parentId = item.parentId;
          const parentItem = parentId ? idMap[parentId] : null;
          // 检查父级type是否为0、01或02
          if (parentItem && ['0', '01', '02'].includes(parentItem.type)) {
            // 设置一个属性标识这是最父级type为03的数据
            item.isTopLevelType03 = true;
            if (item.type != '07') item.resQty = '';
          }
        });
        changeListHandler(res.result);
        tableData.value = res.result.map(a => {
          return { kind: a.type, optionMenu: [3, 4, 5], ...a };
        });
        originalTableData.value = JSON.parse(JSON.stringify(tableData.value));
        console.log('gstableData', tableData.value);
        // virtualListHandler(EnterTypes, posId);
        nextTick(() => {
          if (!currentInfo.value && !posId) {
            currentInfo.value = tableData.value[0];
          } else {
            console.log(posId, 'listObj');
            currentInfo.value =
              tableData.value.find(
                a => a.sequenceNbr === currentInfo.value.sequenceNbr
              ) || tableData.value[0];
            if (
              !tableData.value.find(
                a => a.sequenceNbr === currentInfo.value.sequenceNbr
              )
            ) {
              message.error('未查询到当前数据!');
            }
          }
          if (currentInfo.value && !posId && EnterTypes === 'position') {
            setTimeout(() => {
              vexTable.value.scrollTo(
                { rowKey: currentInfo.value.key },
                'auto'
              );
              let key = vexTable.value.getSelectedRange()[0].columns[0].key;
              vexTable.value.clearAllSelectedRange();
              vexTable.value.appendCellToSelectedRange({
                rowStartIndex: tableData.value.findIndex(
                  a => a.sequenceNbr === currentInfo.value.sequenceNbr
                ),
                rowEndIndex: tableData.value.findIndex(
                  a => a.sequenceNbr === currentInfo.value.sequenceNbr
                ),
                columnStartKey: key,
                columnEndKey: key,
              });
            }, 100);
          }
          if (projectStore.combinedSearchList?.length > 0) {
            filterData(projectStore.combinedSearchList);
          }
          if (posId) {
            let listObj = tableData.value.find(i => i.sequenceNbr == posId);
            console.log('listObj', posId, listObj);
            if (!listObj) {
              message.error('未查询到当前数据!');
            }
            vexTable?.value.scrollTo({ rowKey: currentInfo.value.key }, 'auto');
          }
          if (EnterTypes === 'queryRule') {
            queryRcjDataByDeId();

          }
        });
        // addDataHandler(posId);
        // tableRef.value.scrollTo(posId,'auto');
        loading.value = false;
        emits('getCurrentInfo', currentInfo.value);
        queryFeeFileData(true);
        getSubItemProjectBtn();
        setTimeout(() => {
          addDataSequenceNbr.value = '';
          isIndexAddInfo.value = false;
          addCurrentInfo.value = null;
        }, 500);
        // let elements = document.querySelectorAll(".tableCellClass");
        // console.info(1111111111111111)
        // console.info(elements)
        // setTimeout(() => {
        //   nextTick(() => {
        //     let num=9999
        //     elements.forEach(function(element) {
        //       element.style.zIndex = num;
        //       num--
        //     });
        //   });
        // },1000)
      }
    });
  };
  // 分部分项，展开判断
  const getSubItemProjectBtn = () => {
    csProject
      .openLevelCheckList({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        unitId: projectStore.currentTreeInfo?.id,
      })
      .then(res => {
        openLevelCheckList.value = res.result;
        console.log('getSubItemProjectBtn', openLevelCheckList.value);
        operateList.value.forEach(item => {
          if (item.name == 'openData') {
            item.options.forEach(item => {
              item.isValid = res.result[item.kind]?.hasCheck;
            });
          }
        });
      });
  };
  const conversionRuleListAll = async materialCode => {
    return new Promise((resolve, reject) => {
      gSdetailApi
        .conversionRuleListAll({
          constructId: projectStore.currentTreeGroupInfo?.constructId,
          unitId: projectStore.currentTreeInfo?.id,
        })
        .then(res => {
          resolve(res.result);
        })
        .catch(error => {
          reject(error);
        });
    });
  };

  /**
   * 处理新的表格数据
   */
  const handleNewTable = async result => {
    let res = {
      result,
    };
    if (res.result[0]) {
      if (res.result[0].type == '0') {
        res.result[0].kind = '00';
        res.result[0].deName = res.result[0].deName || '单位工程';
      }
      let conversionRuleList = await conversionRuleListAll();
      const idMap = {};
      // 遍历数据，找到所有type为03的条目
      let testTreeData = xeUtils.toArrayTree(res.result, {
        key: 'sequenceNbr',
        parentKey: 'parentId',
      });
      res.result = xeUtils.toTreeArray(addLevelToTree(testTreeData));
      res.result.forEach((item, index) => {
        item.annotationsVisible = item.isShowAnnotations;
        // 标准换算标识
        conversionRuleList.map(a => {
          if (a.sequenceNbr === item.sequenceNbr) {
            item.redArray = a.redArray;
            item.blackArray = a.blackArray;
          }
        });
        idMap[item.deRowId] = item;
        item.key = item.sequenceNbr;
        item.displaySign = 0
        delete item.children;
        // item.dispNo = index
        if (deMapFun.isDe(item.type)) {
          // 查找父级条目
          const parentId = item.parentId;
          const parentItem = parentId ? idMap[parentId] : null;
          // 检查父级type是否为0、01或02
          if (parentItem && ['0', '01', '02'].includes(parentItem.type)) {
            // 设置一个属性标识这是最父级type为03的数据
            item.isTopLevelType03 = true;
            if (item.type != '07') item.resQty = '';
          }
        }
      });
      changeListHandler(res.result);
      tableData.value = res.result.map(a => {
        return { kind: a.type, optionMenu: [3, 4, 5], ...a };
      });
      console.log('gstableData', tableData.value);

      queryFeeFileData(true);
      setTimeout(() => {
        addDataSequenceNbr.value = '';
        isIndexAddInfo.value = false;
        addCurrentInfo.value = null;
      }, 500);
    }
  };

  /**
   * 获取列表信息
   * @param {*} EnterType //other 从其他页面需要初始化数据 ，Refresh, 修改了刷新数据
   */
  const queryBranchDataById = (EnterTypes = 'Refresh', posId = '') => {
    if (projectStore.type === 'ys') {
      getYsList(EnterTypes, posId);
    }
    if (projectStore.type === 'gs') {
      getGsList(EnterTypes, posId);
    }
  };

  /**
   * 虚拟滚动处理
   * @param {*} type
   * @param {*} posId
   */
  const virtualListHandler = (type, posId) => {
    EnterType.value = type;
    // const initList = init(tableData.value);
    setTimeout(() => {
      // initList();
      if (posId) {
        // scrollToPosition(posId, tableData.value);
        currentInfo.value = tableData.value.find(
          item => item.sequenceNbr === posId
        );
        // vexTable.value.setCurrentRow(currentInfo.value);
        projectStore.isAutoPosition = false;
      }
    }, 10);
  };

  /**
   * 插入数据逻辑处理
   * @returns
   */
  // const addDataHandler = posId => {
  //   if (addDataSequenceNbr.value) {
  //     tableData.value.forEach(item => {
  //       if (!isIndexAddInfo.value) {
  //         if (item.sequenceNbr === addDataSequenceNbr.value) {
  //           currentInfo.value = item;
  //           vexTable.value.setCurrentRow(item);
  //           vexTable.value.scrollToRow(item);
  //         }
  //       } else if (item.sequenceNbr === currentInfo.value.sequenceNbr) {
  //         currentInfo.value = item;
  //       }
  //     });
  //     nextTick(() => {
  //       frameSelectRef?.clearSelect();
  //       vexTable.value.setCurrentRow(currentInfo.value);
  //       resetCellData();
  //       console.log('nextTick', currentInfo.value);
  //     });
  //   } else if (!isIndexAddInfo.value) {
  //     if (posId) return;
  //     let hasCurrentInfo = true; // 有点击选中的数据
  //     if (!currentInfo.value?.sequenceNbr) {
  //       // 没有点击选中的，则直接替换成数据第一个
  //       hasCurrentInfo = false;
  //     } else {
  //       // 有选中数据，但是在列表中没有找到
  //       if (
  //         tableData.value.every(
  //           item => item.sequenceNbr !== currentInfo.value.sequenceNbr
  //         )
  //       ) {
  //         hasCurrentInfo = false;
  //       } else {
  //         currentInfo.value = tableData.value.find(
  //           item => item.sequenceNbr === currentInfo.value.sequenceNbr
  //         );
  //       }
  //     }

  //     let handleCurrentInfo = currentInfo.value; // 重新赋值，为了人才机等子页面能监听到变化掉借口
  //     if (!hasCurrentInfo) {
  //       // 没有选中的数据，默认第一个选中，并清除所有数据
  //       handleCurrentInfo = tableData.value[0];
  //     }
  //     currentInfo.value = '';
  //     nextTick(() => {
  //       currentInfo.value = handleCurrentInfo;
  //       vexTable.value.setCurrentRow(currentInfo.value);
  //     });
  //   }
  // };

  /**
   * 锁定处理
   */
  const lockFlagHandler = () => {
    lockFlag.value = tableData.value
      .filter(data => data.kind === '03')
      .some(item => item.isLocked);
    operateList.value.find(item => item.name === 'lock-subItem').label =
      lockFlag.value ? '整体解锁' : '整体锁定';
  };

  /**
   * 组价方案匹配条件筛选
   * @param {*} val
   */
  const filterData = val => {
    let tempList = [];
    tableData.value = [];
    if (val.length === 0 || !val) {
      tableData.value = originalTableData.value;
    } else {
      originalTableData.value.forEach(item => {
        if (val.includes(item.matchStatus)) {
          tempList.push(item.sequenceNbr);
        }
      });
      for (let i = 0; i < originalTableData.value.length; i++) {
        if (
          tempList.includes(originalTableData.value[i].sequenceNbr) ||
          tempList.includes(originalTableData.value[i].parentId)
        ) {
          tableData.value.push(originalTableData.value[i]);
        }
      }
      tableData.value.forEach((item, index) => {
        item.index = (page.value - 1) * limit.value + (index + 1);
      });
    }
    // const initList = init(tableData.value);
    nextTick(() => {
      initList();
    });
  };

  /**
   * 对列表原数据做处理，对之前分开处理做合并一块处理，后续需要对数据做循环处理，统一在这里做处理
   * @param {} data
   */
  const changeListHandler = data => {
    for (let i = 0; i < data.length; ++i) {
      let item = data[i];
      if (item.defaultLine) {
        item.measureType = '';
      }
      if (item.appendType && item.appendType.length > 0) {
        if (item.appendType.includes('换')) {
          item.changeFlag = '换';
        }
        if (item.appendType.includes('借')) {
          item.borrowFlag = '借';
        }
      }
      item.index = (page.value - 1) * limit.value + (i + 1);
      item[originalCode] = item[codeField];
      item[originalName] = item[nameField];
      item.originalQuantityExpression = item.quantityExpression;
      // item.originalQuantity = item.quantity;
      // item.originalZjfPrice = item.zjfPrice;
    }
  };

  /**
   * 获取所有的取费文件列表
   */
  const queryFeeFileData = (isRefresh = false) => {
    otherApi.queryFeeFileData().then(res => {
      if (res.status === 200 && res.result) {
        feeFileList.value = res.result
          .map(a => {
            return {
              ...a,
              qfCode: a.libraryCode,
              qfName: a.projectType,
            };
          })
          .filter(a => a.qfName);
        console.log('queryFeeFileData', feeFileList.value);
        DJGCrefreshFeeFile.value = false;
      }
    });
  };

  let addDeInfo = ref(null); // 通过索引页面插入定额数据,用于设置标准换算
  /**
   * 获取人材机明细数据
   * @param {*} bol
   * @param {*} deItem
   * @returns
   */
  const queryRcjDataByDeId = (bol = true, deItem = null) => {
    let apiData = {
      deRowId: bol ? addDeInfo.value?.sequenceNbr : deItem.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      type: bol ? addDeInfo.value?.type : deItem.type,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
    };
    if (!apiData.deRowId) return;
    otherApi.getAllRcjDetail(apiData).then(res => {
      console.log('定额明细数据-查询标准换算', res, apiData);
      if (res.status === 200) {
        if (res.result?.length > 0) {
          ishasRCJList.value = true;
        }

        if (bol) {
          mainMaterialTableData.value = res.result.filter(
            x => x.kind === 5 || x.kind === 4
          );
          console.log('cda',res.result,mainMaterialTableData.value)
          if (mainMaterialTableData.value.length > 0) {
            materialVisible.value = true;
          } else {
            queryRule();
          }
        }
      }
    });
  };
  /**
   * 选中单条分部分项数据
   * @param {*} param0
   */
  const currentChangeEvent = ({ row }) => {
    // const $table = vexTable.value;
    // 判断单元格值是否被修改
    // if ($table.isEditByRow(currentInfo.value)) return;
    currentInfo.value = row;
    ishasRCJList.value = false;
    if (row.kind === '04' && row.rcjFlag === 1) {
      queryRcjDataByDeId(false, row);
    }
    projectStore.SET_SUB_CURRENT_INFO(row);
    // emits('getCurrentInfo', currentInfo.value);
  };

  let editContent = ref('');
  // 编辑内容保存事件
  const saveContent = () => {
    const valueType = typeof editContent.value;
    if (valueType !== 'string' || editContent.value.trim() === '') {
      if (editKey.value === 'quantityExpression') {
        infoMode.show({
          isSureModal: true,
          iconType: 'icon-qiangtixing',
          infoText: '请输入工程量表达式',
          confirm: () => {
            infoMode.hide();
          },
        });
      }
      return;
    }
    currentInfo.value[editKey.value] = editContent.value;

    if (editKey.value !== 'quantityExpression') {
      updateFbData(currentInfo.value, editKey.value);
    }
  };

  let showModelTitle = ref('清单名称编辑');
  /**
   * 打开编辑弹框方法
   * @param {*} field
   */
  const openEditDialog = field => {
    editKey.value = field;
    switch (field) {
      case nameField:
        showModelTitle.value =
          currentInfo.value.kind === '03' ? '清单名称编辑' : '定额名称编辑';
        break;
      case 'projectAttr':
        showModelTitle.value = '项目特征编辑';
        break;
      case 'quantityExpression':
        showModelTitle.value = '工程量表达式编辑';
        break;
    }
    isShowModel.value = true;
    editContent.value = currentInfo.value[field];
  };

  let standardVisible = ref(false);
  /**
   * 获取定额是否存在标准换算信息
   * @returns
   */
  const queryRule = () => {
    if (!addDeInfo.value?.standardId) {
      return;
    }
    let apiData = {
      standardDeId: addDeInfo.value?.standardId,
      fbFxDeId: addDeInfo.value?.sequenceNbr,
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId: projectStore.currentTreeGroupInfo?.singleId,
    };
    console.log('标准换算列表参数', apiData);
    otherApi.queryRule(apiData).then(res => {
      if (res.status === 200 && res.result.conversionList) {
        if (res.result.conversionList && res.result.conversionList.length > 0) {
          console.log('标准换算列表数据', res);
          standardVisible.value = true;
        }
      }
    });
  };
  // 临时删除
  const updateDelTempStatusColl = row => {
    let ids = [];
    let deRowId = '';
    if (row.kind === '05') {
      ids.push(row.sequenceNbr);
      deRowId = row.parentId;
    } else if (selectData.value?.length > 1) {
      selectData.value.forEach(item => {
        ids.push(item.sequenceNbr);
      });
    } else {
      ids.push(currentInfo.value.sequenceNbr);
    }

    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      idList: ids,
      deRowId,
    };
    console.log('临时删除参数', apiData);
    gSdetailApi.tempRemoveDeRow(apiData).then(res => {
      console.log('res临时删除', res);
      if (res.status === 200) {
        message.success(res.message);
        queryBranchDataById();
      }
    });
  };
  // 取消临时删除
  const updateCancelDelTempStatusColl = row => {
    let ids = [];
    let deRowId = '';
    if (row.kind === '05') {
      ids.push(row.sequenceNbr);
      deRowId = row.parentId;
    } else if (selectData.value?.length > 1) {
      selectData.value.forEach(item => {
        ids.push(item.sequenceNbr);
      });
    } else {
      ids.push(currentInfo.value.sequenceNbr);
    }

    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      idList: ids,
      deRowId,
    };
    console.log('取消临时删除参数', apiData);
    gSdetailApi.cancelTempRemoveDeRow(apiData).then(res => {
      console.log('res取消临时删除', res);
      if (res.status === 200) {
        message.success(res.message);
        queryBranchDataById();
      }
    });
  };
  // 取消临时删除
  const batchDeleteFun = () => {
    let ids = [];
    if (selectData.value?.length > 1) {
      selectData.value.forEach(item => {
        ids.push(item.sequenceNbr);
      });
    } else {
      ids.push(currentInfo.value.sequenceNbr);
    }

    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      idList: ids,
    };
    console.log('取消临时删除参数', apiData);
    gSdetailApi.cancelTempRemoveDeRow(apiData).then(res => {
      console.log('res取消临时删除', res);
      if (res.status === 200) {
        message.success(res.message);
        queryBranchDataById();
      }
    });
  };

  let areaStatus = ref(false);
  let areaVisibleType = ref('');
  let AnnotationsCurrent = ref(null);
  let AnnotationsRefList = ref({});
  // 鼠标批注右键操作
  const handleNoteClick = (item, row) => {
    // console.log('🚀 ~ handleNoteClick ~ row:', row);
    delete row.children;
    switch (item.code) {
      case 'edit-note':
      case 'add-note':
        editAnnotations(row);
        break;
      case 'del-note':
        updateFbData(
          { ...row, annotations: '', isShowAnnotations: false },
          'annotations'
        );
        break;
      case 'show-note':
        updateFbData({ ...row, isShowAnnotations: true }, 'isShowAnnotations');
        break;
      case 'hide-note':
        updateFbData({ ...row, isShowAnnotations: false }, 'isShowAnnotations');
        break;
      case 'del-all-note':
        areaStatus.value = true;
        areaVisibleType.value = 'note-all';
        break;
      default:
        break;
    }
  };

  const onFocusNode = row => {
    if (AnnotationsCurrent.value != row.sequenceNbr) {
      console.log('🚀 ~手动选中');
      editAnnotations(row);
    }
  };

  const editAnnotations = row => {
    row.noteEditVisible = true;
    row.annotationsVisible = true;
    tableData.value = [...tableData.value];
    nextTick(() => {
      AnnotationsCurrent.value = row.sequenceNbr;
      AnnotationsRefList.value[row.sequenceNbr]?.focusNode();
    });
  };
  // 关闭编辑的
  const closeAnnotations = (v, row) => {
    if (!row?.isShowAnnotations) {
      row.noteEditVisible = false;
      row.noteViewVisible = false;
      row.annotationsVisible = false;
    }
    if (v == row?.annotations) {
      return;
    }
    updateFbData({ ...row, annotations: v }, 'annotations');
  };

  const getAnnotationsRef = (el, row) => {
    if (el) {
      AnnotationsRefList.value[row.sequenceNbr] = el;
    } else {
      AnnotationsRefList.value[row.sequenceNbr] = null;
    }
  };
  const closeAreaModal = v => {
    areaStatus.value = false;
    if (v) {
      let apiData = {
        constructId: projectStore.currentTreeGroupInfo?.constructId,
        singleId: projectStore.currentTreeGroupInfo?.singleId,
        unitId: projectStore.currentTreeInfo?.id,
        applyConstruct: true,
      };
      gSdetailApi
        .deleteAllDeAnnotations(apiData)
        .then(res => {
          queryBranchDataById('other');
        })
        .finally(() => {
          areaVisibleType.value = '';
        });
    }
  };
  return {
    queryBranchDataById,
    getSubItemProjectBtn,
    openLevelCheckList,
    queryFeeFileData,
    editClosedEvent,
    currentChangeEvent,
    updateFbData,
    loading,
    mainMaterialTableData,

    saveContent,
    openEditDialog,
    showModelTitle,

    currentInfo,
    isShowModel,
    editContent,
    editKey,
    infoVisible,
    infoText,
    iconType,
    isSureModal,

    ishasRCJList,
    isClearEdit,
    isSortQdCode,
    deCode,
    rcjVisible,
    deVisible,
    qdVisible,
    DJGCrefreshFeeFile,
    isUpdateFile,
    indexVisible,
    isUpdateQuantities,
    selectUnit,
    showUnitTooltip,
    addCurrentInfo,
    isIndexAddInfo,
    addDataSequenceNbr,
    lockFlag,
    feeFileList,
    tableData,
    originalTableData,
    materialVisible,
    materialType,
    materialRow,
    rowType,
    updateDelTempStatusColl,
    updateCancelDelTempStatusColl,
    batchDeleteFun,
    selectData,
    batchDeleteVisible,
    batchDataType,
    handleNoteClick,
    areaStatus,
    areaVisibleType,
    onFocusNode,
    closeAnnotations,
    getAnnotationsRef,
    AnnotationsRefList,
    closeAreaModal,
    priceVisible,
    handleNewTable,
    standardVisible,
    addDeInfo,
  };
};
