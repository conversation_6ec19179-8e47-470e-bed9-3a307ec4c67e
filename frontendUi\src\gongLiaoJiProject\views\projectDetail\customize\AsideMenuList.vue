<!--
 * @Descripttion: 内容侧边菜单列表
 * @Author: renmingming
 * @Date: 2023-05-17 10:44:13
 * @LastEditors: sunchen
 * @LastEditTime: 2025-03-10 10:20:27
-->
<template>
  <div class="aside-menu-list" style="position: relative">
    <div
      :class="['aside-list', isContract ? 'aside-contract' : 'aside-scroll']"
      :style="asideStyle"
      @click="titleContractHandle"
    >
      <div
        :class="[
          store.componentId === 'summaryExpense' ? 'cursor-pointer' : '',
          'title',
          store.componentId === 'summaryExpense' && categoryIndex == -1
            ? 'use-title'
            : '',
        ]"
        @click="summaryMenuClick"
      >
        <icon-font
          class="icon"
          v-show="!isContract"
          type="icon-xiangmugaikuang"
        />{{ props.title }}
      </div>
      <ul class="menu-list" v-if="!isTreeData" v-show="!isContract">
        <!--{{store.componentId}} -->
        <a-directory-tree
          v-if="
            store.componentId === 'humanMachineSummary' && menuList.length > 0
          "
          v-model:selectedKeys="selectedKeyshuman"
          :expandedKeys="['0']"
          :tree-data="rcjMenuList"
          expandAction="false"
          defaultExpandAll
          autoExpandParent
          @select="selectChildren"
          :field-names="{
            title: 'name',
            key: 'code',
          }"
          class="table-scrollbar"
        >
          <template #icon="{ key }">
            <template v-if="key !== '0-0'"></template>
          </template>
          <template #title="data">
            <a-dropdown :trigger="['contextmenu']">
              <span style="display: inline-block; width: 100%">{{
                data.name
              }}</span>
              <template #overlay v-if="store.currentTreeInfo.type == 3">
                <a-menu
                  @click="
                    ({ key: menuKey }) => onContextMenuClick(data, menuKey)
                  "
                >
                  <a-menu-item key="1">新增</a-menu-item>
                  <a-menu-item :disabled="data.original === 1" key="2"
                    >修改</a-menu-item
                  >
                  <a-menu-item :disabled="data.original === 1" key="3"
                    >删除</a-menu-item
                  >
                </a-menu>
              </template>
            </a-dropdown>
          </template>
        </a-directory-tree>
        <li
          v-else
          v-for="(item, index) in props.menuList"
          :key="item.name"
          @click.stop="handleSelect(item, index)"
          :class="{ on: categoryIndex === index }"
        >
          <a-tooltip placement="right" :title="item.name">
            <span class="name-content">
              <!-- 设置取费文件标示 -->
              <icon-font
                class="icon-fee"
                v-if="
                  item.defaultFeeFlag === 1 && store.currentTreeInfo.type === 3
                "
                type="icon-zhufeiyongwenjianbiaoshi"
              />
              {{ item.name }}</span
            >
          </a-tooltip>
        </li>
      </ul>
      <div class="selectTree" v-if="isTreeData" v-show="!isContract">
        <a-directory-tree
          v-model:expandedKeys="expandedkeys"
          v-model:selectedKeys="selectedKeys"
          :tree-data="menuList"
          :show-line="true"
          expandAction="false"
          @select="selectChildren"
          :field-names="{
            title: 'bdName',
            children: 'childTreeModel',
            key: 'sequenceNbr',
          }"
          class="table-scrollbar"
        >
          <template #icon="{ key }">
            <template v-if="key !== '0-0'"></template>
          </template>
        </a-directory-tree>
      </div>
    </div>
    <div class="btnExpand">
      <div
        class="btn"
        :style="{ right: isContract ? '-7px' : '11px' }"
        @click.stop="contractHandle"
      >
        <img
          :src="isContract ? getUrl('expandnew.png') : getUrl('retractnew.png')"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, getCurrentInstance, nextTick } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import { getUrl } from '@/utils/index';
import xeUtils from 'xe-utils';
import operateList, { updateOperateByName } from './operate';
import infoMode from '@/plugins/infoMode.js';
import csProject from '@gongLiaoJi/api/csProject';
import { projectStore } from '@gongLiaoJi/store/project';
const GLJStore = projectStore();
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const expandedkeys = ref(['0-0']);
const selectedKeys = ref(['1664092215682068481']);
const store = projectDetailStore();
const rcjMenuList = ref([]);
const props = defineProps({
  title: {
    type: String,
    default: '工程概况',
    // default: "其他项目",
  },
  isTreeData: {
    type: Boolean,
    default: false,
  },
  menuList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  updateStatus: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['currentMenu', 'update:updateStatus']);
let isContract = ref(false); // 是否收缩
let categoryIndex = ref(0);
let selectedKeyshuman = ref([]);
const asideStyle = computed(() => {
  return {
    width: !isContract.value ? '188px' : '36px',
  };
});

watch(
  () => store.currentTreeInfo,
  () => {
    categoryIndex.value = 0;
    if (store.componentId === 'summaryExpense') {
      categoryIndex.value = -1;
    }
  }
);
watch(
  () => store.categoryIndex,
  (newValue, oldValue) => {
    categoryIndex.value = store.categoryIndex;
    console.log('💡 ~ categoryIndex.value:', categoryIndex.value);
    if (store.componentId === 'subItemProject') {
      selectedKeys.value = [props.menuList[newValue]?.sequenceNbr];
    }
    if (
      newValue != oldValue &&
      !(
        (store.componentId == 'humanMachineSummary' &&
          props.menuList.length > 0) ||
        store.componentId == 'summaryExpense'
      )
    ) {
      console.log('💡 ~ store.componentId:', store.componentId);
      handleSelect(props.menuList[newValue ?? 0], newValue ?? 0);
    }
  },
  { deep: true, immediate: true }
);
watch(
  () => store.tabSelectName,
  val => {
    if (props.isTreeData) {
      selectedKeys.value[0] = props.menuList[0]?.sequenceNbr;
      expandedkeys.value[0] = props.menuList[0]?.sequenceNbr;
    } else {
      categoryIndex.value = 0;
      if (store.componentId === 'humanMachineSummary') {
        selectedKeyshuman.value = [props.menuList[0]?.code];
      }
    }
    if (store.componentId === 'summaryExpense') {
      categoryIndex.value = -1;
    }
  }
);
watch(
  () => [store.currentTreeInfo, store.tabSelectName],
  ([a, b], [c, d]) => {
    // console.info(***********,store.currentTreeInfo.sequenceNbr,store.tabSelectName)
    // let gljCheckTab=projectStore.gljCheckTab
    // let upSelRow=gljCheckTab[projectStore.currentTreeInfo.sequenceNbr].tabList.find(a=>a.tabName=='预算书')
    // store.SET_ASIDE_MENU_CURRENT_INFO(props.menuList[0]);
  }
);
// 递归函数，通过 id 获取节点
function getNodeById(tree, id) {
  for (const node of tree) {
    if (node.sequenceNbr === id) {
      return node;
    }
    if (node.childTreeModel && node.childTreeModel.length > 0) {
      const result = getNodeById(node.childTreeModel, id);
      if (result) {
        return result;
      }
    }
  }
  return null;
}
// 递归获取父节点
function getParentIds(tree, targetId) {
  let parentIds = [];
  function traverse(node) {
    if (node.childTreeModel && node.childTreeModel.length > 0) {
      for (let child of node.childTreeModel) {
        traverse(child);
      }
    }
    if (node.sequenceNbr === targetId) {
      // We found the target node, now backtrack to collect parent IDs
      let currentNode = node;
      while (currentNode.parentId !== null) {
        const parentNode = findNodeById(tree, currentNode.parentId);
        if (parentNode) {
          parentIds.unshift(parentNode.sequenceNbr); // Add to the beginning of the array
          currentNode = parentNode;
        } else {
          break; // Should not happen in a properly structured tree
        }
      }
    }
  }

  function findNodeById(nodes, id) {
    for (let node of nodes) {
      if (node.sequenceNbr === id) {
        return node;
      }
      const found = findNodeById(node.childTreeModel, id);
      if (found) {
        return found;
      }
    }
    return null;
  }

  traverse(tree[0]); // Start traversing from the root node
  return parentIds;
}
watch(
  () => props.menuList,
  () => {
    let gljCheckTab = store.gljCheckTab;
    let upSelRow = gljCheckTab[store.currentTreeInfo.sequenceNbr]?.tabList.find(
      a => a.tabName == store.tabSelectName
    );
    GLJStore.menuList = props.menuList;
    console.log(
      '----upSelRow--------',
      store.tabSelectName,
      upSelRow,
      props.menuList
    );

    let summaryExpenseDataTab =
      props.menuList && props.menuList?.length > 0
        ? props.menuList[0]?.sequenceNbr
        : 'all';

    if (upSelRow && upSelRow.leftTwoTreeId && upSelRow.leftTwoTreeId !== '') {
      let tableList = JSON.parse(JSON.stringify(props.menuList));
      if (props.menuList[0]?.childTreeModel) {
        let obj = getNodeById(tableList, upSelRow.leftTwoTreeId);
        let idArr = getParentIds(tableList, upSelRow.leftTwoTreeId);
        idArr.push(upSelRow.leftTwoTreeId);
        selectedKeys.value[0] = upSelRow.leftTwoTreeId;
        if (store.componentId === 'humanMachineSummary') {
          rcjMenuList.value = props.menuList;
          if (store.currentTreeInfo.type == 3) {
            rcjMenuList.value.push({
              code: '99',
              name: '三材汇总表',
              original: 1,
              parentCode: null,
            });
          }
          selectedKeyshuman.value = [upSelRow.leftTwoTreeId];
        } else {
          expandedkeys.value = idArr;
          store.SET_ASIDE_MENU_CURRENT_INFO(obj);
        }
        selectChildren(upSelRow.leftTwoTreeId, { node: { dataRef: obj } });
      } else {
        let obj = props.menuList.find(
          a => a.sequenceNbr === upSelRow.leftTwoTreeId
        );
        let objIndex = props.menuList.findIndex(
          a => a.sequenceNbr === upSelRow.leftTwoTreeId
        );
        if (obj) {
          categoryIndex.value = objIndex;
          handleSelect(obj, objIndex);
        } else {
          if (store.componentId === 'summaryExpense') {
            setTimeout(() => {
              bus.emit('summaryExpenseData', summaryExpenseDataTab);
            }, 500);
            categoryIndex.value = summaryExpenseDataTab != 'all' ? 0 : -1;
          } else {
            handleSelect(props.menuList[0], 0);
          }
        }
        if (store.componentId === 'summaryExpense') {
          return;
        }

        store.SET_ASIDE_MENU_CURRENT_INFO(obj);
      }
      return;
    } else {
      if (store.componentId === 'summaryExpense') {
        categoryIndex.value = summaryExpenseDataTab != 'all' ? 0 : -1;
        setTimeout(() => {
          bus.emit('summaryExpenseData', summaryExpenseDataTab);
        }, 500);
      }
    }
    console.log(
      'props.menuList',
      props.menuList,
      selectedKeys.value[0],
      xeUtils
        .toTreeArray(props.menuList, {
          key: 'sequenceNbr',
          children: 'childTreeModel',
        })
        .map(a => {
          return a.sequenceNbr;
        })
    );
    if (props.isTreeData) {
      if (!props.updateStatus) {
        selectedKeys.value[0] = props.menuList[0]?.sequenceNbr;
        expandedkeys.value[0] = props.menuList[0]?.sequenceNbr;
        store.SET_ASIDE_MENU_CURRENT_INFO(props.menuList[0]);
        emits('update:updateStatus', false);
      } else {
        emits('update:updateStatus', false);
      }
      console.log('selectedKey', selectedKeys, props.updateStatus);
    } else {
      if (store.componentId === 'humanMachineSummary') {
        rcjMenuList.value = props.menuList;
        if (store.currentTreeInfo.type == 3) {
          rcjMenuList.value.push({
            code: '99',
            name: '三材汇总表',
            original: 1,
            parentCode: null,
          });
        }
        if (store.selectedKeyshuman == 0) {
          selectedKeyshuman.value = [props.menuList[0]?.code];
          store.SET_ASIDE_MENU_CURRENT_INFO(props.menuList[0]);
        } else {
          selectedKeyshuman.value = [
            props.menuList[props.menuList.length - 1]?.code,
          ];
          store.SET_ASIDE_MENU_CURRENT_INFO(
            props.menuList[props.menuList.length - 1]
          );
        }
      } else {
        if (store.componentId === 'summaryExpense') {
          return;
        }
        store.SET_ASIDE_MENU_CURRENT_INFO(props.menuList[0]);
      }
    }

    if (
      selectedKeys.value[0] &&
      !xeUtils
        .toTreeArray(props.menuList, {
          key: 'sequenceNbr',
          children: 'childTreeModel',
        })
        .map(a => {
          return a.sequenceNbr;
        })
        .includes(selectedKeys.value[0])
    ) {
      if (store.componentId === 'summaryExpense') {
        return;
      }
      selectedKeys.value[0] = props.menuList[0]?.sequenceNbr;
      store.SET_ASIDE_MENU_CURRENT_INFO(props.menuList[0]);
    }
  }
);
const onContextMenuClick = (menudata, menuKey) => {
  bus.emit('humanMenu', { code: menudata.code, menuKey, menudata });
};
const currentSelectedMenu = name => {
  if (!props.isTreeData) {
    setTimeout(() => {
      currentSelectedMenu(name);
    }, 1000);
    return;
  }
  if (props.menuList) {
    selectedKeys.value[0] = props.menuList[0].childTreeModel.filter(
      x => x.bdName === name
    )[0]?.sequenceNbr;
    expandedkeys.value[0] = props.menuList[0].childTreeModel.filter(
      x => x.bdName === name
    )[0]?.sequenceNbr;
    emits('currentMenu', { key: selectedKeys.value[0] });
  }
  console.log('侧边菜单栏进来不', selectedKeys.value);
};

const contractHandle = () => {
  isContract.value = !isContract.value;
};
const titleContractHandle = () => {
  if (isContract.value) {
    isContract.value = false;
  }

  // 费用汇总，一个单位中，预算书有多个工程专业
  if (store.tabSelectName === '费用汇总' && props.menuList.length !== 0) {
    bus.emit('summaryExpenseData', 'all');
    categoryIndex.value = -1;
  }
};

const handleSelect = (item, index) => {
  console.log('-----handleSelect----', item);

  categoryIndex.value = index;
  if (store.tabSelectName === '取费表') {
    store.SET_Fee_With_Drawal_Info(item);
  }
  if (store.tabSelectName === '费用汇总') {
    setTimeout(() => {
      bus.emit('summaryExpenseData', item.key);
    }, 500);
  }
  console.log('💡 ~ handleSelect ~ store:', store);
  store.asideMenuCurrentInfo = item;
  emits('currentMenu', item);
};

const summaryMenuClick = () => {
  if (store.componentId === 'summaryExpense') {
    store.SET_ASIDE_MENU_CURRENT_INFO(null);
    categoryIndex.value = -1;
  }
};

const selectChildren = (selectedKeys, e) => {
  console.log('💡 ~ selectChildren ~ selectedKeys:', selectedKeys);
  if (store.humanUpdataData && store.humanUpdataData.isEdit) {
    saveHumanData(e.node.dataRef);
    return;
  }
  store.SET_ASIDE_MENU_CURRENT_INFO(e.node.dataRef);
  if (selectedKeys[0] && selectedKeys[0].indexOf('qtxm') > -1) {
    emits('currentMenu', { key: selectedKeys[0] });
  }
};

let unifyData = ref(); //统一应用按钮是否禁用---工程项目人材机汇总和取费表更改的豪华切换侧边栏需要保存
const saveHumanData = oldVal => {
  unifyData.value = operateList.value.find(
    item => item.name === store.humanUpdataData.name
  );
  let infoText =
    store.humanUpdataData.name === 'unify'
      ? '当前取费设置中存在费率或政策文件变更，是否统一修改并应用至所有单位工程？'
      : `人材机数据已修改，是否应用整个${
          store.currentTreeInfo.type == 1 ? '工程项目' : '单项工程'
        }?`;
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText,
    confirm: () => {
      store.humanUpdataData?.name === 'unify'
        ? feeTotalSave(oldVal)
        : humanSave(oldVal);
      infoMode.hide();
    },
    close: () => {
      resetHumanData(oldVal);
      unifyData.value.disabled = true;
      infoMode.hide();
    },
  });
};

const resetHumanData = oldVal => {
  store.SET_HUMAN_UPDATA_DATA(null);
  store.SET_ASIDE_MENU_CURRENT_INFO(oldVal);
  store.SET_CURRENT_STAGE_INFO(null);
  if (store.tabSelectName === '取费表') {
    store.SET_Fee_With_Drawal_Info(oldVal);
  }
  emits('currentMenu', oldVal);
};
const humanSave = oldVal => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    levelType: +store.currentTreeInfo.type,
    constructProjectRcjList: JSON.parse(
      JSON.stringify(store.humanUpdataData.updataData)
    ),
  };
  csProject.changeRcjConstructProject(apiData).then(res => {
    console.log('asideTree统一应用接口返回结果', res);
    if (res.status === 200) {
      resetHumanData(oldVal);
      unifyData.value.disabled = true;
    }
  });
};

const feeTotalSave = oldVal => {
  if (store.humanUpdataData.updataData.policy) {
    feePro
      .checkPolicyDocument(
        JSON.parse(JSON.stringify(store.humanUpdataData.updataData.policy))
      )
      .then(res => {});
  }
  if (store.humanUpdataData.updataData.feeTotal) {
    feePro
      .unifiedUse(
        JSON.parse(JSON.stringify(store.humanUpdataData.updataData.feeTotal))
      )
      .then(res => {});
  }
  if (store.humanUpdataData.updataData.calEditData) {
    let apiData = {
      constructId:
        store.currentTreeInfo.levelType === 1
          ? store.currentTreeInfo?.id
          : store.currentTreeGroupInfo?.constructId,
      feeCalculateBaseList: JSON.parse(
        JSON.stringify(store.humanUpdataData.updataData.calEditData)
      ),
    };
    feePro.updateProjectUnitCalculateBaseApply(apiData).then(res => {
      if (res.status === 200) {
        console.log('updateProjectUnitCalculateBaseApply2', res, apiData);
      }
    });
  }
  resetHumanData(oldVal);
  unifyData.value.disabled = true;
};

defineExpose({
  currentSelectedMenu,
});
</script>
<style lang="scss" scoped>
.aside-menu-list {
  &:hover .btnExpand .btn {
    display: block;
  }
}
.aside-list {
  position: relative;
  height: 100%;
  width: 188px;
  border-right: 1px solid #dcdfe6;
  border-left: 1px solid #dcdfe6;
  transition: all 0.4s;
  background: #f8fbff;
  overflow: hidden;
  .cursor-pointer {
    cursor: pointer;
  }
}

.use-title {
  background-color: #deeaff;
}
.aside-contract {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  .title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-85%, -50%);
    width: 14px;
    padding: 0;
    height: auto;
    border-bottom: none;
    white-space: normal;
  }
  .btn span {
    transform: rotate(180deg);
  }
}
.aside-scroll {
  overflow-y: hidden;
  &:hover {
    overflow-y: auto;
  }
  &::-webkit-scrollbar {
    width: 5px;
    height: 6px;
  }
}

::-webkit-scrollbar-thumb {
  //滚动条的设置
  background-color: rgba(24, 144, 255, 0.2);
  background-clip: padding-box;
  min-height: 28px;
  border-radius: 5px;
}
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(24, 144, 255, 0.8);
}
.btnExpand {
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  font-size: 12px;
  height: 80px;
  z-index: 100;
  text-align: center;
  transition: all 0.1s linear;
  cursor: pointer;
  user-select: none;
  .btn {
    display: none;
  }
  span {
    display: inline-block;
    transform: translateX(-1px);
    transition: all 0.4s;
  }
}
.btnExpand:hover .btn {
  display: block;
}
.title {
  display: flex;
  align-items: center;
  height: 35px;
  padding: 0 15px;
  transition: all 0.4s;
  border-bottom: 2px solid #dcdfe6;
  color: #131414;
  white-space: nowrap;
  .icon {
    width: 16px;
    height: 16px;
    margin-right: 7px;
    background-color: #dfdfdf;
  }
}
.menu-list {
  list-style: none;
  padding: 1px 0px 9px;
  li {
    text-align: left;
    // margin: 0 0 6px 6px;
    margin: 0 0 1px 6px;

    cursor: pointer;
    .name-content {
      display: block;
      padding: 0 20px;

      line-height: 1.6;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行；
    }
    &:hover {
      background-color: #dae7f4;
    }
  }
}
.icon-fee {
  margin-left: -18px;
}
.selectTree {
  :deep(.ant-tree-show-line .ant-tree-switcher) {
    background: transparent;
  }
  :deep(.ant-tree) {
    background-color: #f8fbff;
    max-height: calc(100vh - 180px);
    overflow-y: hidden;
    color: #131414;
    &:hover {
      overflow-y: auto;
    }
    .ant-tree-treenode-selected {
      color: #131414 !important;
      background-color: #deeaff !important;
    }
    .ant-tree-node-selected {
      color: #131414 !important;
      background-color: #deeaff !important;
    }
    .ant-tree-switcher {
      color: #131414 !important;
    }
    .ant-tree-treenode {
      padding: 0px !important;
    }
    .ant-tree-treenode-selected::before {
      background-color: #deeaff !important;
    }
  }
}

.on {
  background-color: #deeaff;
}
</style>
