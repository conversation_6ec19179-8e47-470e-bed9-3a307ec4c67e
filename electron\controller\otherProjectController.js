const {ResponseData} = require("../utils/ResponseData");
const {Controller} = require("../../core");
const {ObjectUtils} = require("../utils/ObjectUtils");
class OtherProjectController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 初始化默认其他项目数据
     */
    getDefaultOtherProject(){
        const res = this.service.otherProjectService.getDefaultOtherProject();
        return ResponseData.success(res);
    }

    getOtherProject(args){
        const res = this.service.otherProjectService.getOtherProject(args);
        return ResponseData.success(res);
    }

    /**
     * 获取其他项目列表
     */
    async getOtherProjectList(args){
        let res = await this.service.otherProjectService.getOtherProjectList(args);
        return ResponseData.success(res);
    }


    /**
     * 编辑其他项目
     */
    async updateOtherProject(args){
        const res = await this.service.otherProjectService.updateOtherProject(args);

        if (!ObjectUtils.isEmpty(res)) {
            return ResponseData.fail(res);
        }

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");

        return ResponseData.success(res);
    }

    /**
     * 获取暂列金额列表
     */
    getOtherProjectZljeList(args){
        var res = this.service.otherProjectService.getOtherProjectZljeList(args);
        return ResponseData.success(res);
    }


    /**
     * 获取材料暂估价列表
     */
    getOtherProjectClzgjList(args){
        var res = this.service.otherProjectService.getOtherProjectClzgjList(args);
        return ResponseData.success(res);
    }


    /**
     * 获取设备暂估价列表
     */
    getOtherProjectSbzgjList(args){
        var res = this.service.otherProjectService.getOtherProjectSbzgjList(args);
        return ResponseData.success(res);
    }


    /**
     * 获取 专业工程暂估价 列表
     */
    getOtherProjectZygcZgjList(args){
        var res = this.service.otherProjectService.getOtherProjectZygcZgjList(args);
        return ResponseData.success(res);
    }

    /**
     * 获取 总承包服务费 列表
     */
    getOtherProjectZcbfwfList(args){
        var res = this.service.otherProjectService.getOtherProjectZcbfwfList(args);
        return ResponseData.success(res);
    }

    /**
     * 获取 记日工 列表
     */
    getOtherProjectJrgList(args){
        var res = this.service.otherProjectService.getOtherProjectJrgList(args);
        return ResponseData.success(res);
    }

    /**
     * 获取 签证与索赔计价表
     */
    getOtherProjectQzSp(args){
        var res = this.service.otherProjectService.getOtherProjectQzSp(args);
        return ResponseData.success(res);
    }

    /**
     * 获取 主要材料设备 列表
     */
    getOtherProjectZyclSbList(args){
        var res = this.service.otherProjectService.getOtherProjectZyclSbList(args);
        return ResponseData.success(res);
    }

    /**
     * 获取 甲供材料设备 列表
     */
    getOtherProjectJgclSbList(args){
        var res = this.service.otherProjectService.getOtherProjectJgclSbList(args);
        return ResponseData.success(res);
    }


    /**
     * 根据定额版本获取其他项目模板列表
     */
    getOtherProjectTemplate(args){
        // libraryCodeVersion：定额版本
        const res = this.service.otherProjectService.getOtherProjectTemplate(args);
        return ResponseData.success(res);
    }

    /**
     * 根据模板名称获取数据
     */
    getTemplateData(args){
        // constructId：项目id
        // singleId：单项id
        // unitId：单位id
        // template：模版编号
        const res = this.service.otherProjectService.getTemplateData(args);
        return ResponseData.success(res);
    }

    /**
     * 根据选择模版修改其他项目
     */
    settingsTemplateData(args){
        // constructId：项目id
        // singleId：单项id
        // unitId：单位id
        // template：模版编号
        const res = this.service.otherProjectService.settingsTemplateData(args);
        if (ObjectUtils.isEmpty(res)) {
            return ResponseData.success(res);
        }
        return ResponseData.fail(res);
    }


    /**
     * 查询费用代码基础数据分类
     * @param args
     * @returns {ResponseData}
     */
    costCodeTypeList(args){
        const res = this.service.otherProjectService.costCodeTypeList(args);
        return ResponseData.success(res);
    }

    /**
     * 查询单位工程的费用代码和对应的金额
     * @param args
     * @returns {ResponseData}
     */
    costCodePrice(args){
        const res = this.service.otherProjectService.costCodePrice(args);
        return ResponseData.success(res);
    }

    /**
     * 获取其他项目费用类型列表
     * @returns {ResponseData}
     */
    getOtherProjectTypeListColl(){
        let otherProjectTypeList = this.service.otherProjectService.getOtherProjectTypeList();
        return ResponseData.success(otherProjectTypeList);
    }



    /**
     * 其他项目数据行操作   新增、删除
     */
    async otherProjectLineDataColl(arg) {
        await this.service.otherProjectService.otherProjectLineData(arg);
        return ResponseData.success(true);
    }



}

OtherProjectController.toString = () => '[class OtherProjectController]';
module.exports = OtherProjectController;
