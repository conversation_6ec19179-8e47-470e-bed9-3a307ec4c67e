const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");
const {allArr, CostCodeTypeEnum, rcjArr, zmdmArr} = require("../unit_price_composition/compute/rules/chargecode");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
class DanjiagouchengController extends Controller{
    constructor(ctx) {
        super(ctx);
    }

    async applyEditor(args) {
        let {service}= this;
        try {
            await service.danjiagouchengService.applyEditor(args);
        } catch (e) {
            return ResponseData.fail(e.message);
        }
        return ResponseData.success("应用成功");
    }
    /*
    * 编辑区编辑完成后查询接口
    * */
    queryforDeId(args){
        return ResponseData.success(this.service.danjiagouchengService.queryforDeId(args));
    }
    cancelEditor(args){
        let {service}= this;
        service.danjiagouchengService.cancelEditor(args);
    }

    resetRate(args) {
        let {service} = this;
        service.danjiagouchengService.resetRate(args);
        return ResponseData.success("重置成功");
    }
        /*
        * 编辑区编辑接口
        * */
    editor(args){
        let {service}= this;
        try {
            service.danjiagouchengService.editor(args);
        }catch (e) {
            return ResponseData.fail(e.message);
        }
        return ResponseData.success("编辑成功");
    }

    loadUPCtemplate(args){
        try {
            let {service} = this;
            let result = service.danjiagouchengService.loadUPCtemplate(args);
            return ResponseData.success(result);
        } catch (e) {
            return ResponseData.fail(e.message);
        }

    }

    async upcFolder(args) {
        let {service} = this;
        let filelist = await service.danjiagouchengService.upcFolder(args);
        return ResponseData.success(filelist);
    }

    async upcPreview(args) {
        let {service} = this;
        let list = await service.danjiagouchengService.upcPreview(args);
        return ResponseData.success(list);
    }

    async saveUPCtemplate(args) {
        let {service} = this;
        let flag = await service.danjiagouchengService.saveUPCtemplate(args);
        return ResponseData.success(flag ? "保存成功" : "操作取消");
    }

    upcTemplates(args){
        let {service}= this;
      let data =  service.danjiagouchengService.upcTemplates(args);
        return ResponseData.success(data);
    }

    upcTypes(args) {
        let {service} = this;
        let data = service.danjiagouchengService.upcTypes(args);
        return ResponseData.success(data);
    }
    upcTemplatesByCode(args){
        let {service}= this;
        let data =  service.danjiagouchengService.upcTemplatesByCode(args);
        return ResponseData.success(data);
    }
    /**
     * 查询费用代码基础数据分类
     * @param args
     * @returns {ResponseData}
     */
    costCodeTypeList(args){
        return ResponseData.success(CostCodeTypeEnum);
    }
    /**
     * 根据类型获取列表
     * @param args
     * @returns {ResponseData}
     */
    costCodeListBytype(args){
        let {constructId, singleId, unitId, sequenceNbr, type} = args
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId)

        // Create a function to safely clone objects
        const safeClone = (obj) => {
            if (!obj || typeof obj !== 'object') return obj;
            if (Array.isArray(obj)) return obj.map(safeClone);
            const cloned = {};
            for (const key in obj) {
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    const value = obj[key];
                    // Skip functions and non-serializable values
                    if (typeof value !== 'function' && value !== undefined) {
                        cloned[key] = safeClone(value);
                    }
                }
            }
            cloned.name =cloned.description||cloned.name;
            return cloned;
        };

        if (type == 1) {
            const zmdmArr = safeClone(unit.upcTemp[sequenceNbr]["zmdmArr"]);
            return ResponseData.success(zmdmArr);
        }
        if (type == 2) {
            const rcjArr = safeClone(unit.upcTemp[sequenceNbr]["rcjArr"]);
            return ResponseData.success(rcjArr);
        }

        let all = [];
        const zmdmArr = safeClone(unit.upcTemp[sequenceNbr]["zmdmArr"]);
        const rcjArr = safeClone(unit.upcTemp[sequenceNbr]["rcjArr"]);

        for (const allKey in zmdmArr) {
            all.push(zmdmArr[allKey]);
        }
        for (const allKey in rcjArr) {
            all.push(rcjArr[allKey]);
        }
        return ResponseData.success(all);
    }
}

DanjiagouchengController.toString = () => '[class DanjiagouchengController]';
module.exports = DanjiagouchengController;
