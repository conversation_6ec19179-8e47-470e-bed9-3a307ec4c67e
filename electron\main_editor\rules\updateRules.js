const { PricingFileFindUtils } = require('../../utils/PricingFileFindUtils');
const { NumberUtil } = require('../../utils/NumberUtil');
const EE = require('../../../core/ee');
const ConstructionMeasureTypeConstant = require('../../enum/ConstructionMeasureTypeConstant');
const { ObjectUtils } = require('../../utils/ObjectUtils');
const ConstantUtil = require('../../enum/ConstantUtil');
const { UPCContext } = require('../../unit_price_composition/core/UPCContext');
const BranchProjectLevelConstant = require('../../enum/BranchProjectLevelConstant');
const {UPCCupmuteDe} = require("../../unit_price_composition/compute/UPCCupmute");
const {RcjCalculateHandler} = require("../../rcj_handle/calculate/RcjCalculateHandler");
const {RcjApplicationContext} = require("../../rcj_handle/RcjContext");
const FindRcjStrategy = require("../../rcj_handle/find/findRcjStrategy");
const UpdateRcjStrategy = require("../../rcj_handle/update/updateRcjStrategy");
const CalculationTool = require("../../unit_price_composition/compute/CalculationTool");
const RcjTypeEnum = require("../../enum/RcjTypeEnum");
const {UnitRcjCacheUtil} = require("../../rcj_handle/cache/UnitRcjCacheUtil");
const {getDeUnitFormatEnum,getUnitFormatEnum} = require("./format");
/**
 * 重置工程量明细数据
 * @param pointLine
 * @param upDateInfo
 * @param oldquantityExpression
 */
function quantityExpressionNbrClear(pointLine, upDateInfo,oldquantityExpression ) {
	oldquantityExpression = oldquantityExpression.toString();
	if (oldquantityExpression.includes(ConstantUtil.GCLMXHJ) && !upDateInfo.value.includes(ConstantUtil.GCLMXHJ)) {
		let quantities = pointLine.quantities;
		if(ObjectUtils.isNotEmpty(quantities)){
			quantities.forEach(f=>{
				f.mathIllustrate=null;
				f.mathFormula=null;
				f.mathResult=null;
				f.variables=null;
			})
		}
	}
}

async function updateMaterialCode(constructId, singleId, unitId,rcj){
	let {service} = EE.app;
	let rcjApplicationContext = new RcjApplicationContext({
		constructId, singleId, unitId,
		projectObj: PricingFileFindUtils.getProjectObjById(constructId)
	});
	let initCode=rcj.materialCode;
	await rcjApplicationContext.materialCodeHandler(rcj);
	let unit= PricingFileFindUtils.getUnit(constructId, singleId, unitId);
	if(initCode!=rcj.materialCode){
		service.constructProjectRcjService.setRcjChildrenCode(rcj,unit);
		UnitRcjCacheUtil.add(unit,rcj,rcj.deId);
	}else {
		//更新缓存数据
		UnitRcjCacheUtil.update(unit, rcj,ObjectUtils.cloneDeep(rcj));
	}






}

/**
 * 检查并更新 系统取费文件
 * @param qfCode
 * @param constructId
 * @param singleId
 * @param unitId
 * @param isMain
 * @returns {Promise<*>}
 */
async function checkAndSaveFeeFile({qfCode, constructId, singleId, unitId, isMain}) {
	let {service} = EE.app;
	let feeFile = PricingFileFindUtils.getUnit(
		constructId,
		singleId,
		unitId
	).feeFiles.filter((f) => {
		//如果是随主工程取费文件，那么找主取费文件的code
		return isMain
			? f.defaultFeeFlag && f.defaultFeeFlag === 1
			: f.feeFileCode === qfCode;
	})[0];
	if (!feeFile) {
		feeFile = await service.baseFeeFileService.updateFBFXFeeFile(
			constructId,
			singleId,
			unitId,
			qfCode
		);
	}
	return feeFile;
}

let deRules = {
	name:async ({ pointLine, ctx, upDateInfo }) => {
		let { constructId, singleId, unitId } = ctx;
		let { value } = upDateInfo;
		if (pointLine.rcjFlag && pointLine.rcjFlag === 1) {
			let rcj = PricingFileFindUtils.getRcjList(
				constructId,
				singleId,
				unitId
			).filter((f) => f.deId === pointLine.sequenceNbr);
			if (rcj) {
				rcj = rcj[0];
				rcj.materialName = value;
				await updateMaterialCode(constructId, singleId, unitId,rcj);
				pointLine.bdCode = rcj.materialCode;
				pointLine.fxCode = rcj.materialCode;
			}

		}
	},
	projectAttr: ({ pointLine, ctx, upDateInfo }) => {
		let { value } = upDateInfo;
		pointLine.oriAttr = value;
	},
	unit:async ({pointLine, ctx, upDateInfo, oldValue, allData}) => {
		let { value } = upDateInfo;
		let { constructId, singleId, unitId } = ctx;
		let orgUnitNum = Number.parseInt(oldValue);
		if (Number.isNaN(orgUnitNum)) {
			orgUnitNum = 1;
		}
		let nowUnitNum = Number.parseInt(value);
		if (Number.isNaN(nowUnitNum)) {
			nowUnitNum = 1;
		}
		if (pointLine.rcjFlag && pointLine.rcjFlag === 1) {
			let rcj = PricingFileFindUtils.getRcjList(
				constructId,
				singleId,
				unitId
			).filter((f) => f.deId === pointLine.sequenceNbr);
			if (rcj) {
				rcj = rcj[0];
				rcj.unit = value;
				await updateMaterialCode(constructId, singleId, unitId,rcj);
				pointLine.bdCode = rcj.materialCode;
				pointLine.fxCode = rcj.materialCode;
			}
		}
		let unitNum = getDeUnitFormatEnum(value).value;
		if (orgUnitNum !== nowUnitNum) {
			nowUnitNum = nowUnitNum ? nowUnitNum : 1;
			pointLine.quantity =NumberUtil.numberScale(NumberUtil.divide(
				pointLine.quantityExpressionNbr,
				nowUnitNum
			),unitNum);
			let { service } = EE.app;
			service.rcjProcess.reSetquantity(
				constructId,
				singleId,
				unitId,
				pointLine
			);
		}
		//处理kind3
		EE.app.service.conversionDeService.deQuantityChaneToKind3b(constructId, singleId, unitId, allData, pointLine);
	},
	itemCategory: ({ pointLine, ctx, upDateInfo }) => {
		let { value } = upDateInfo;
		pointLine.constructionMeasureType =
			value === '单价措施项目'
				? ConstructionMeasureTypeConstant.DJCS
				: ConstructionMeasureTypeConstant.ZJCS;
	},
	//修改规格型号 定额类型的人材机
	specification: async ({oldValue, pointLine, ctx, upDateInfo}) => {
		let {value} = upDateInfo;
		//修改对应的人材机类型
		if (pointLine.rcjFlag) {
			let {unit, constructId, singleId, unitId,pageType} = ctx;
			let rcjList = unit.constructProjectRcjs.filter(k => pointLine.sequenceNbr == k.deId);
			rcjList.forEach(k => {
				k.specification = value;
			});

			let updateRcjStrategy = new UpdateRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId),pageType});
			for (let item of rcjList) {
				let arg = {};
				arg.specification = item.specification;
				await updateRcjStrategy.execute({pointLineId:item.sequenceNbr,upDateInfo:arg,type:1});
			}





			//重新计算人材机
			let rcjCalculateHandler = new RcjCalculateHandler({
				constructId,
				singleId,
				unitId,
				projectObj: PricingFileFindUtils.getProjectObjById(constructId)
			});
			await rcjCalculateHandler.calculate(pointLine);
		}
	},
	zjfPrice:async ({ oldValue,pointLine, ctx, upDateInfo }) => {
		let { value } = upDateInfo;
		let rate = value / oldValue;
		// if (rate != 1) {
		// 	if (!pointLine.appendType) {
		// 		pointLine.appendType = [];
		// 	} else {
		// 		pointLine.appendType = pointLine.appendType.filter(
		// 			(f) => f !== '换'
		// 		);
		// 	}
		// 	pointLine.appendType.push('换');
		// }
		//TODO 人材机数据更新
		let {unit,constructId, singleId, unitId,pageType} = ctx;
		let rcjList = unit.constructProjectRcjs.filter(k =>pointLine.sequenceNbr == k.deId
			&& !(ConstantUtil.SPECIAL_RCJ.includes(k.materialCode) && k.unit == "%"));
		//修改人材机消耗量
		// rcjList.forEach(k =>{
		// 	k.resQty = NumberUtil.numberScale(NumberUtil.multiply(k.resQty,rate),6);
		//
		// });
		//定额级别人材机
		if (pointLine.rcjFlag == 1){
			let arg = {};
			if (ctx.isSimple){
				arg.priceMarketTax = pointLine.zjfPrice;
			}else {
				arg.priceMarket = pointLine.zjfPrice;
			}
			let updateRcjStrategy = new UpdateRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId),pageType});
			for (let item of rcjList) {
				await updateRcjStrategy.execute({pointLineId:item.sequenceNbr,upDateInfo:arg,type:1});
				let de = unit.itemBillProjects.getNodeById(item.deId);
				//重新计算人材机
				let rcjCalculateHandler = new RcjCalculateHandler({constructId, singleId, unitId, projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
				await rcjCalculateHandler.calculate(de);
			}
			//相同人材机
			let rcjs = unit.constructProjectRcjs.filter(k =>k.materialCode == pointLine.bdCode ||k.materialCode == pointLine.fxCode);
			//重新计算人材机
			let rcjCalculateHandler = new RcjCalculateHandler({constructId, singleId, unitId, projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
			let allData =pageType == "fbfx"?PricingFileFindUtils.getFbFx(constructId, singleId, unitId):PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
			let calculationTool = new CalculationTool({constructId, singleId, unitId, allData:allData });
			for (let item of rcjs) {
				let itemBillde = unit.itemBillProjects.getNodeById(item.deId);
				if (ObjectUtils.isNotEmpty(itemBillde)){
					await rcjCalculateHandler.calculate(itemBillde);
				}
				let measurede = unit.measureProjectTables.getNodeById(item.deId);
				if (ObjectUtils.isNotEmpty(measurede)){
					await rcjCalculateHandler.calculate(measurede);
				}
				calculationTool.calculationChian({sequenceNbr: item.deId});
			}



		}
		//重新计算人材机
		let rcjCalculateHandler = new RcjCalculateHandler({constructId, singleId, unitId, projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
		await rcjCalculateHandler.calculate(pointLine);
	},
	// 工程量
	quantityExpression: async ({pointLine, ctx, upDateInfo, allData, oldValue}) => {
		let { value } = upDateInfo;
		let { constructId, singleId, unitId } = ctx;
		let { service } = EE.app;
		let unitNum = getDeUnitFormatEnum(pointLine.unit).value;
		pointLine.quantity = NumberUtil.numberScale(pointLine.quantity,unitNum);
		pointLine.quantityExpression = value.toUpperCase();
		service.baseBranchProjectOptionService.caculateQuantityExpressionAndQuantity(
			constructId,
			singleId,
			unitId,
			pointLine
		);
		//处理kind3
		service.conversionDeService.deQuantityChaneToKind3b(constructId, singleId, unitId, allData, pointLine);
		quantityExpressionNbrClear(pointLine, upDateInfo, oldValue);
		//处理子定额工程量表达式
		let filter = allData.filter(zde=>zde.parentDeId==pointLine.sequenceNbr);
		if(ObjectUtils.isNotEmpty(filter)){
			let rcjCalculateHandler = new RcjCalculateHandler({constructId, singleId, unitId, projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
			for (let deLine of filter) {
			    if(deLine.quantityExpression.includes("GCL")){
					service.baseBranchProjectOptionService.caculateQuantityExpressionAndQuantity(
						constructId,
						singleId,
						unitId,
						deLine
					);
					await rcjCalculateHandler.calculate(deLine);
					//联动计算计算定额
					let upcccore = new UPCCupmuteDe(deLine, constructId, singleId, unitId, allData);
					upcccore.prepare();
					upcccore.cupmute();
				}
			}

		}
		if (pointLine.rcjFlag == 1) {
			let rcj = ctx.unit.constructProjectRcjs.filter(k => k.deId == pointLine.sequenceNbr)[0];
			let qd = allData.find(zde=>zde.sequenceNbr==pointLine.parentId);
			rcj.resQty = pointLine.quantity;
			rcj.confficientInitQty=NumberUtil.numberScale6(NumberUtil.divide( rcj.resQty,qd.adjustmentCoefficient));
		}
	},
	qfCode: async ({pointLine, ctx, upDateInfo}) => {
		let {constructId, singleId, unitId, is2022} = ctx;
		let {value} = upDateInfo;
		let templates = UPCContext.getUpcTemplateSelectList(is2022, constructId, unitId);
		for (let i = 0; i < templates.length; i++) {
			if (value == templates[i].qfCode) {
				pointLine.qfName = templates[i].qfName;
				break;
			}
		}
		let qfCode = value;
		//如果是增量模板获取到模板对应的取费文件
		if (UPCContext.qfCodeMap.has(constructId + ',' + unitId + qfCode)) {
			qfCode = UPCContext.qfCodeMap.get(
				constructId + ',' + unitId + qfCode
			);
		}
		//当前的取费文件 和修改后的是否一样
		//获取对应的取费文件
		let feeFileData = UPCContext.getFeeFileData(
			is2022 ? '22' : '12'
		).filter((item) => item.qfCode == qfCode)[0];
		pointLine.costMajorName = feeFileData.qfName;
		pointLine.costFileCode = feeFileData.qfCode;
		//切换对应的取费文件
		await checkAndSaveFeeFile({qfCode, constructId, singleId, unitId, isMain: false});

	},
	costFileCode: async ({ pointLine, ctx, upDateInfo }) => {
		let { constructId, singleId, unitId, is2022 } = ctx;
		let { column, value } = upDateInfo;
		//都是随主
		let isMain = ('SUIZHUGONGCHENG' == value)||('SYSTEM_SZGC'==value);
		let feeFileData = UPCContext.getFeeFileData(
			is2022 ? '22' : '12'
		).filter((item) => item.qfCode == value)[0];
		pointLine.costMajorName = feeFileData.qfName;
		let qfCode = value;
		//切换对应的取费文件

		if (!isMain) {
			pointLine.qfCode = value; //这里指的是取费文件对应的单价模板code
			pointLine.qfName = feeFileData.qfName;
		} else {
			let feeFile = await checkAndSaveFeeFile({qfCode, constructId, singleId, unitId, isMain});
			pointLine.costFileCode = feeFile.feeFileCode; //这个是当前选择的取费文件的qfcode
			pointLine.qfCode = feeFile.feeFileCode; //这里指的是取费文件对应的单价模板code
			pointLine.qfName = feeFile.feeFileName;
		}
	},
	//如果将批注设置为空 将显示状态设置为不显示
	annotations: ({ pointLine, upDateInfo, allData }) => {
		let { value } = upDateInfo;
		if (ObjectUtils.isEmpty(value)) {
			pointLine.isShowAnnotations = false;
		}
	},
	type: async({ pointLine, upDateInfo, ctx,allData }) => {
		let { value } = upDateInfo;
		let {unit, constructId, singleId, unitId,pageType} = ctx;
		if (pointLine.rcjFlag == 1){
			let rcjType = null;
			for (let enumKey in RcjTypeEnum) {
				if (RcjTypeEnum[enumKey].desc === value) {
					rcjType = RcjTypeEnum[enumKey].code;
				}
			}
			let rcjList = unit.constructProjectRcjs.filter(k => pointLine.sequenceNbr == k.deId);
			let updateRcjStrategy = new UpdateRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId),pageType});
			for (let item of rcjList) {
				item.type = value;
				item.kind = rcjType;
				let arg = {};
				arg.kind = item.kind;
				await updateRcjStrategy.execute({pointLineId:item.sequenceNbr,upDateInfo:arg,type:1});
			}
		}
	},
};
let qdRules = {
	projectAttr: ({ pointLine, ctx, upDateInfo }) => {
		let { value } = upDateInfo;
		pointLine.oriAttr = value;
	},
	// 工程量
	quantityExpression: async ({pointLine, ctx, upDateInfo, allData, oldValue}) => {
		let { value } = upDateInfo;
		let { constructId, singleId, unitId } = ctx;
		let { service } = EE.app;
	
		pointLine.quantityExpression = value.toUpperCase();
		service.baseBranchProjectOptionService.caculateQuantityExpressionAndQuantity(
			constructId,
			singleId,
			unitId,
			pointLine
		);
		//处理清单  QDL 联动
		let qdLine = allData.getNodeById(pointLine.sequenceNbr);
		let deLines = qdLine.children;
		let rcjCalculateHandler = new RcjCalculateHandler({constructId, singleId, unitId, projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
		if (!ObjectUtils.isEmpty(deLines)) {
			for (let deLine of deLines) {
				if(deLine.quantityExpression&&deLine.quantityExpression.toString().indexOf('QDL')==-1){
					continue
				}
				service.baseBranchProjectOptionService.caculateQuantityExpressionAndQuantity(
					constructId,
					singleId,
					unitId,
					deLine
				);
				service.conversionDeService.deQuantityChaneToKind3b(constructId, singleId, unitId, allData, deLine);
				await rcjCalculateHandler.calculate(deLine);
				//联动计算计算定额
				let upcccore = new UPCCupmuteDe(deLine, constructId, singleId, unitId, allData);
				upcccore.prepare();
				upcccore.cupmute();

				//处理子定额工程量表达式
				let filter = allData.filter(zde=>zde.parentDeId==deLine.sequenceNbr);
				if(ObjectUtils.isNotEmpty(filter)){
					for (let zdeLine of filter) {
						if(zdeLine.quantityExpression.includes("GCL")){
							service.baseBranchProjectOptionService.caculateQuantityExpressionAndQuantity(
								constructId,
								singleId,
								unitId,
								zdeLine
							);
							await rcjCalculateHandler.calculate(zdeLine);
							//联动计算计算定额
							upcccore = new UPCCupmuteDe(zdeLine, constructId, singleId, unitId, allData);
							upcccore.prepare();
							upcccore.cupmute();
						}
					}

				}

			}
		}
		quantityExpressionNbrClear(pointLine, upDateInfo, oldValue);
	},
	//如果改的是是否是主要清单  需要进行关联数据操作
	ifMainQd: ({ pointLine, upDateInfo, allData ,ctx}) => {
		let { service } = EE.app;
		if(pointLine.kind==BranchProjectLevelConstant.top){
			// 获取当前节点下的所有子节点
			let findAllSubsets = allData.findAllSubsets(pointLine);
			if (ObjectUtils.isNotEmpty(findAllSubsets)) {
				for (const node of findAllSubsets) {
					if (node.kind != BranchProjectLevelConstant.de) {
						node.ifMainQd = upDateInfo.value;
						if(!upDateInfo.value){
							node.mainQdParentAllCheckFlag=upDateInfo.value;
						}
					}
				}
			}
		}
		service.baseBranchProjectOptionService.statusMainAndLock(pointLine,allData,1);


		// //获取所有的父节点
		// let findNodeTreeData = allData.findNodeTreeData([pointLine],allData.find(top=>top.kind==BranchProjectLevelConstant.top),allData);

		// if(!upDateInfo.value){
		// 	if(ObjectUtils.isNotEmpty(findNodeTreeData)){
		// 		findNodeTreeData.forEach(fb=>{
		// 				fb.ifMainQd=upDateInfo.value;
		// 			if(fb!=BranchProjectLevelConstant.qd){
		// 				fb.ifMainQd=false;
		// 			}
		// 			if(fb.kind==BranchProjectLevelConstant.fb){
		// 				fb.parent.ifMainQd=upDateInfo.value;
		// 				fb.parent.mainQdParentAllCheckFlag=false;
		// 			}
		// 		});
		// 	}
		// }else {
		// 	service.baseBranchProjectOptionService.statusMainAndLock(pointLine,allData,1);
		// }
	},
	//如果将批注设置为空 将显示状态设置为不显示
	annotations: ({ pointLine, upDateInfo, allData }) => {
		let { value } = upDateInfo;
		if (ObjectUtils.isEmpty(value)) {
			pointLine.isShowAnnotations = false;
		}
	},
	unit: async ({pointLine, ctx, upDateInfo, allData, oldValue}) =>{
		let { value } = upDateInfo;
		pointLine.unit = value

		let orgUnitNum = Number.parseInt(oldValue);
		if (Number.isNaN(orgUnitNum)) {
			orgUnitNum = 1;
		}
		let nowUnitNum = Number.parseInt(value);
		if (Number.isNaN(nowUnitNum)) {
			nowUnitNum = 1;
		}
		if (orgUnitNum !== nowUnitNum) {
			nowUnitNum = nowUnitNum ? nowUnitNum : 1;
			pointLine.quantity = NumberUtil.divide(
				pointLine.quantityExpressionNbr,
				nowUnitNum
			);
			upDateInfo.value =  pointLine.quantityExpression
			await qdRules.quantityExpression({pointLine, ctx, upDateInfo, allData, oldValue})
		}

	},
	//措施项目调整系数
	adjustmentCoefficient: async ({pointLine, ctx, upDateInfo}) => {
		let {value} = upDateInfo;
		//获取清单下的所有材料
		let {service} = EE.app;
		let {constructId, singleId, unitId} = ctx;
		if (pointLine.kind == BranchProjectLevelConstant.qd) {
			let findRcjStrategy = new FindRcjStrategy({
				constructId,
				singleId,
				unitId,
				projectObj: PricingFileFindUtils.getProjectObjById(ctx.constructId)
			});
			let csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
			//获取清单下的所有定额数据
			let deList = csxm.filter(de => de.parentId == pointLine.sequenceNbr);
			for (const de of deList) {
				let rcjList = ctx.unit.constructProjectRcjs.filter(rcj => rcj.deId == de.sequenceNbr);
				if (ObjectUtils.isEmpty(rcjList)) {
					return;
				}
				let args = {constructId, singleId, unitId, rcjList, value,de};
				//定额级别人材机回填工程量值
				if (de.rcjFlag == 1) {
					await service.constructProjectRcjService.updateDeRcjResQty(args);
				}else {
					await service.constructProjectRcjService.updateResQtyConstructAndDetailRcj(args);
				}
			}

		}
	},
	//清单综合单价锁定
	lockPriceFlag: ({ pointLine, ctx, upDateInfo }) => {
		let { value } = upDateInfo;
		pointLine.lockPriceFlag = value;
		if(value){
			pointLine.lockPriceBack=pointLine.price;
		}else {
			delete  pointLine.lockPriceBack;
		}


		let { service } = EE.app;
		let {constructId, singleId, unitId} = ctx;
		let qd = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes().find(qd=>qd.sequenceNbr==pointLine.sequenceNbr);
		let allData;
		if(ObjectUtils.isEmpty(qd)){
			allData=PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
		}else{
			allData= PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
		}


		if(pointLine.kind==BranchProjectLevelConstant.top){
			// 获取当前节点下的所有子节点
			let findAllSubsets = allData.findAllSubsets(pointLine);
			if (ObjectUtils.isNotEmpty(findAllSubsets)) {
				for (const node of findAllSubsets) {
					if (node.kind != BranchProjectLevelConstant.de) {
						node.lockPriceFlag = upDateInfo.value;
						if(value){
							node.lockPriceBack=node.price;
						}else {
							delete  node.lockPriceBack;
							//计算清单的单价构成
							if(node.kind==BranchProjectLevelConstant.qd){
								//计算单价构成
								let calculationTool = new CalculationTool({constructId, singleId, unitId, allData});
								calculationTool.calculationChian(node);
							}
						}
						if(!upDateInfo.value){
							node.parentLockPriceFlag=upDateInfo.value;
						}
					}
				}
			}
		}

		service.baseBranchProjectOptionService.statusMainAndLock(pointLine,allData,2);
	},
};


let fbRules = {
	projectAttr: ({ pointLine, ctx, upDateInfo }) => {
		let { value } = upDateInfo;
		pointLine.oriAttr = value;
	},
	//如果将批注设置为空 将显示状态设置为不显示
	annotations: ({ pointLine, upDateInfo, allData }) => {
		let { value } = upDateInfo;
		if (ObjectUtils.isEmpty(value)) {
			pointLine.isShowAnnotations = false;
		}
	},
	ifMainQd: ({ pointLine, ctx, upDateInfo }) => {
		let { value } = upDateInfo;
		let {service} = EE.app;
		let { constructId, singleId, unitId } = ctx;
		pointLine.ifMainQd = value;
		if(!value){
			pointLine.mainQdParentAllCheckFlag=value;
		}
		//将该分部下所有的子分部以及清单状态同步
		let allData;
		let fb = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes().find(fb=>fb.sequenceNbr==pointLine.sequenceNbr);
		if(ObjectUtils.isEmpty(fb)){
			allData=PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
		}else{
			allData= PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
		}
		let flattenTreeOpen = allData.flattenTreeOpen(pointLine, null).filter(data=>data.kind!=BranchProjectLevelConstant.de);
		let allNodes = allData.getAllNodes();
		flattenTreeOpen.forEach(i=>{
			allNodes.forEach(data=>{
				if(data.sequenceNbr==i.sequenceNbr){
					data.ifMainQd=value;
					if(!value){
						data.mainQdParentAllCheckFlag=value;
					}
				}
			})
		});

		service.baseBranchProjectOptionService.statusMainAndLock(pointLine,allData,1);

	},

	//清单综合单价锁定
	lockPriceFlag: ({ pointLine, ctx, upDateInfo }) => {
		let { value } = upDateInfo;
		let { constructId, singleId, unitId } = ctx;
		let {service} = EE.app;
		pointLine.lockPriceFlag = value;
		if(!value){
			pointLine.parentLockPriceFlag=value;
		}
		//将该分部下所有的子分部以及清单状态同步
		let allData;
		let fb = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes().find(fb=>fb.sequenceNbr==pointLine.sequenceNbr);
		if(ObjectUtils.isEmpty(fb)){
			allData=PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
		}else{
			allData= PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
		}
		let flattenTreeOpen = allData.flattenTreeOpen(pointLine, null).filter(data=>data.kind!=BranchProjectLevelConstant.de);
		let allNodes = allData.getAllNodes();
		flattenTreeOpen.forEach(i=>{
			allNodes.forEach(data=>{
				if(data.sequenceNbr==i.sequenceNbr){
					data.lockPriceFlag=value;
					if(data.kind==BranchProjectLevelConstant.qd){
						data.lockPriceBack=i.price;
						if(!value){
							data.parentLockPriceFlag=value;
						}
					}
				}
			})
		});

		service.baseBranchProjectOptionService.statusMainAndLock(pointLine,allData,2);

	},
};


module.exports = {fbRules,deRules, qdRules, checkAndSaveFeeFile};
