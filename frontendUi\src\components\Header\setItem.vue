<!--
 * @Descripttion:
 * @Author:
 * @Date: 2024-12-30 16:35:50
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-03-20 14:56:14
-->
<template>
  <template v-for="option of props.options">
    <div class="list" v-if="option.type === 'checkbox'">
      <a-checkbox v-model:checked="option.value" :disabled="option.disabled">{{
        option.name
      }}</a-checkbox>
    </div>
    <div class="list" v-if="option.type === 'radio'">
      <span v-if="option.name" class="name">{{ option.name }}：</span>
      <a-radio-group v-model:value="option.value" :disabled="option.disabled">
        <a-radio :value="item.value" v-for="item of option.options">{{
          item.label
        }}</a-radio>
      </a-radio-group>
    </div>
    <div class="list" v-if="option.type === 'input'">
      <span v-if="option.name" class="name">{{ option.name }}：</span>
      <a-input
        style="width: auto"
        v-model:value="option.value"
        :disabled="option.disabled"
      ></a-input>
    </div>
    <div class="list" v-if="option.type === 'file'">
      <span v-if="option.name" class="name">{{ option.name }}：</span>
      <a-button disabled>更改路径</a-button>
      <span style="margin-left: 8px">{{ option.value }}</span>
    </div>
    <div class="list" v-if="option.type === 'table'">
      <a-table
        :pagination="false"
        size="small"
        bordered
        :dataSource="option.dataSource"
        :columns="option.columns"
      />
    </div>
  </template>
</template>

<script setup>
import { ref } from 'vue';
const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
});
</script>
<style lang="scss" scoped>
.list {
  font-size: 14px;
  padding: 2px 0;
  color: #2a2a2a;
  .name {
    white-space: nowrap;
  }
  :deep(.ant-radio-group) {
    padding: 2px 0;
    // 重写radio样式为checkbox样式
    // .ant-radio-checked .ant-radio-inner {
    //   background-color: #1890ff;
    // }
    // .ant-radio-inner {
    //   border-radius: 2px;
    //   &::after {
    //     position: absolute;
    //     top: 50%;
    //     left: 21.5%;
    //     width: 5.71428571px;
    //     height: 9.14285714px;
    //     margin-top: 0;
    //     margin-left: 0;
    //     background-color: transparent;
    //     border-radius: 0;
    //     display: table;
    //     border: 2px solid #fff;
    //     border-top: 0;
    //     border-left: 0;
    //     transform: rotate(45deg) scale(1) translate(-50%, -50%);
    //     opacity: 1;
    //     transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    //     content: ' ';
    //   }
    // }
  }
  :deep(.ant-radio-disabled + span) {
    color: rgba(0, 0, 0, 0.85);
  }
  :deep(.ant-checkbox-disabled + span) {
    color: rgba(0, 0, 0, 0.85);
  }
}
</style>
