/*
 * @Descripttion: 
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-14 16:00:51
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-11-15 09:30:37
 */
import { ipc } from '@/utils/ipcRenderer';
import { ipcApiRoute } from './main';

export default {
  createModal: params => ipc.invoke(ipcApiRoute.createModal, params),//创建窗体
  setModalState: params => ipc.invoke(ipcApiRoute.setModalState, params),//设置窗体状态
}