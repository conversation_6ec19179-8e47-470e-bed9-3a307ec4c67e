/*
 * @Descripttion:
 * @Author: renmingming
 * @Date: 2023-05-18 17:43:43
 * @LastEditors: wangru
 * @LastEditTime: 2025-05-22 14:12:57
 */
import decimalUtils from './decimalUtils.js';
/**
 * 完整解析的静态资源 URL
 * @param {*} url
 * @returns
 */
export const getUrl = url => {
  return new URL(`../assets/img/${url}`, import.meta.url).href;
};

/**
 * 过滤特殊字符
 * @param {*} url
 * @returns
 */
export const removeSpecialChars = str => {
  const regex = /[`\^。.>？!！￥~!@$%^&*()\-=+[\]{}\\|;:'",.<>/?]/g;
  //左侧树名字要拼接_1  所以正则过滤_
  return str.replace(regex, '');
};
/**
 * 输入项目名称过滤某些特殊字符
 * @param {*} url
 * @returns
 */
export const inputName = str => {
  const regex = /[`\^。>？!！￥~!@$^&*\=+[\]{}\\|;:<>/?]/g;
  return str.replace(regex, '');
};
/**
 * 输入数组小数位数处理
 * @param {*} value,length   value保留length位小数
 */
export const getNumberlength = (value, length) => {
  if (value.length > length) {
    value = parseFloat(value).toFixed(length);
  }
  return value;
};

/**
 * 输入纯数字+小数点判断
 * @param {*} value  输入内容   isEmpty 是否可以为空   length小数位数  totalLength---输入内容总长度
 */
export const pureNumber = (value, length = -1, isEmpty = false) => {
  if ((!isEmpty && !value?.length) || Number(value) < 0) return value;
  let newVal;
  let num = value.split('.').length;
  if (num > 2) {
    //输入两个以上小数点
    newVal = '0.00';
    return newVal;
  }
  newVal = value * 1 + '';
  newVal = newVal.replace(/[^\d.]/g, '');
  let after = newVal.split('.')[1];
  if (after > 0 && length > 0 && after.length > length) {
    newVal = parseFloat(newVal).toFixed(length);
  }
  if (newVal.lastIndexOf('.') === newVal.length - 1) {
    newVal = newVal.slice(0, newVal.length - 1); //输入内容最后一项是小数点则去掉
  }
  // if (totalLength > 0) {
  //   newVal = newVal.slice(0, totalLength);
  // }
  return newVal;
};

/**
 * 输入纯数字+小数点判断 并且不能输入负数
 * @param {*} value  输入内容   isEmpty 是否可以为空   length小数位数  totalLength---输入内容总长度
 */
export const pureNumber0 = (value, length = -1, isEmpty = false) => {
  if (!isEmpty && !value?.length) {
    return value;
  }

  if (Number(value) < 0) {
    return 0;
  }
  let newVal;
  let num = value.split('.').length;
  if (num > 2) {
    //输入两个以上小数点
    newVal = '0.00';
    return newVal;
  }
  newVal = value * 1 + '';
  newVal = newVal.replace(/[^\d.]/g, '');
  let after = newVal.split('.')[1];
  if (after > 0 && length > 0 && after.length > length) {
    newVal = parseFloat(newVal).toFixed(length);
  }
  if (newVal.lastIndexOf('.') === newVal.length - 1) {
    newVal = newVal.slice(0, newVal.length - 1); //输入内容最后一项是小数点则去掉
  }
  // if (totalLength > 0) {
  //   newVal = newVal.slice(0, totalLength);
  // }
  return newVal;
};

/**
 * 输入纯数字+小数点判断 带短横线 可输入负数
 * @param {*} value  输入内容   isEmpty 是否可以为空   length小数位数  totalLength---输入内容总长度
 */
export const pureNumberSHL = (value, length = -1, isEmpty = false) => {
  if (!isEmpty && !value.length) return value;
  let newVal;
  let num = value.split('.').length;
  if (num > 2) {
    //输入两个以上小数点
    newVal = '0.00';
    return newVal;
  }
  newVal = value.replace(/[^-\d.]/g, '');
  let after = newVal.split('.')[1];
  if (after > 0 && length > 0 && after.length > length) {
    newVal = parseFloat(newVal).toFixed(length);
  }
  if (newVal.lastIndexOf('.') === newVal.length - 1) {
    newVal = newVal.slice(0, newVal.length - 1); //输入内容最后一项是小数点则去掉
  }
  // if (totalLength > 0) {
  //   newVal = newVal.slice(0, totalLength);
  // }
  return newVal;
};
/**
 * 表格序号等限制  字母数字小数点组成  有长度限制
 * @param {*} value  输入内容   isEmpty 是否可以为空
 */
export const sortAndlength = (value, length, isEmpty = true) => {
  if (!value.length) return value;
  let newVal = value.replace(/[^\w.]/g, '');
  newVal = newVal.slice(0, length);
  return newVal;
};
/**
 * 工程量表达式输入是否合法
 * @param row
 * @returns {boolean}
 */
export const quantityExpressionHandler = row => {
  const errMsg = '计算式输入非法，请重新输入标准四则运算表达式或数值';
  let {
    quantityExpression: value,
    quantityVariableName,
    quantityVariableValue,
    originalQuantityExpression = '',
  } = row;
  originalQuantityExpression = originalQuantityExpression
    ? originalQuantityExpression + ''
    : originalQuantityExpression; //转字符串
  if (!value.length) return [true, '工程量表达式不能为空'];
  const reg = /[^0-9a-zA-Z_|\-|\*|\+|\/|\.|(|)|（|）]/g;
  if (reg.test(value)) return [true, errMsg];
  let arr = value.match(/[A-Za-z0-9_]+(\.\d+)?/g);
  if (arr === null) return [true, errMsg];
  let result = [];
  for (let f of arr) {
    if (isNaN(Number(f))) {
      if (
        (quantityVariableName !== f &&
          f.toUpperCase() !== 'QDL' &&
          !row.parentDeId &&
          !originalQuantityExpression.includes('BSHNTL_')) ||
        (!originalQuantityExpression.includes('BSHNTL_') &&
          f.toUpperCase().includes('BSHNTL_')) ||
        (originalQuantityExpression.includes('BSHNTL_') &&
          f.toUpperCase() !== originalQuantityExpression) ||
        (row.kind === '04' &&
          row.parentDeId &&
          !['GCL', 'QDL'].includes(f.toUpperCase()))
      )
        return [true, errMsg];
      const reg = new RegExp(`${f}`, 'g');
      console.log('替换：', f, quantityVariableValue);
      value = value.replace(reg, quantityVariableValue);
    }
  }
  try {
    const runResult = new Function(`return ${value}`);
    if (!isFinite(runResult())) return [true, errMsg];
  } catch (error) {
    return [false, ''];
  }
  return [false, ''];
};

/**
 * 纯数字表达式
 * @param {*} value
 * @returns
 */
export const isNumericExpression = (value, varObjMap = null) => {
  const errMsg = '计算式输入非法，请重新输入标准四则运算表达式或数值';
  if (!value.length) return [true, '工程量表达式不能为空'];
  const reg = /[^0-9a-zA-Z|\-|\*|\+|\/|\.|(|)|（|）]/g;
  if (reg.test(value)) return [true, errMsg];
  let arr = value.match(/[A-Za-z0-9]+(\.\d+)?/g);
  if (arr === null) return [true, errMsg];
  if (varObjMap) {
    let result = [];
    for (let f of arr) {
      const mapVal = varObjMap.get(f.toUpperCase());
      console.log(isNaN(Number(f)), f, 'isNaN(Number(f))');
      if (isNaN(Number(f)) && f.toUpperCase() !== 'GCGM') {
        if (!mapVal) return [true, errMsg];
        const reg = new RegExp(`${f}`, 'g');
        value = value.replace(reg, mapVal.mathResult);
      }
    }
  }
  try {
    const runResult = new Function(`return ${value}`);
    if (!isFinite(runResult())) return [true, errMsg];
  } catch (error) {
    return [false, ''];
  }
  return [false, ''];
};

/**
 * 处理表达式的每个数字
 * @param {*} value
 */
export const everyNumericHandler = (value, decimalPlaces = 6) => {
  if (!value.length) return value;
  let arr = value.split(/-|\+|\*|\/|\(|\)/);
  let result = [];
  arr.forEach(f => {
    if (f.length && !isNaN(Number(f))) {
      const reg = new RegExp(`^${f}$`, 'g');
      console.log('替换：', Number(f));
      // value = value.replace(f, Math.round(dealNumber(f) * 1000000) / 1000000);
      value = value.replace(
        f,
        decimalUtils.toFixed(dealNumber(f), decimalPlaces)
      );
    }
  });
  return value;
};

export const dealNumber = s => {
  let reg = /^0+\./;
  let reg2 = /^0+[1-9]/;
  let ss = s.replace(reg, '0.');
  if (reg2.test(ss)) {
    return ss.replace(/^0+/, '');
  }
  console.log('ss11111', ss);
  return ss;
};

/**
 * 输入值是否为正整数校验
 * @param str
 * @returns {number|null|*}
 */
export const getPositiveInteger = str => {
  return str.replace(/[^1-9]/g, '');
};

/**
 * 价格输入是否合法.
 * @param {string} value
 * @returns {string}
 */
export const removeSpecialCharsFromPrice = (value, fractionDigits = 2) => {
  if (value === null) return value;
  const reg = /[^\d\.]/g;
  if (reg.test(value)) {
    return '';
  }
  return Number(Number(value).toFixed(fractionDigits)) || '';
};
//补充人材机单价校验
export const rcjPrice = (value, fractionDigits = 2) => {
  if (value === null) return value;
  const reg = /[^\d\.]/g;
  if (reg.test(value)) {
    return '0';
  }
  return Number(Number(value).toFixed(fractionDigits)) || '0';
};

export function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * @description: 下载文件
 * @param {*} data 文件
 * @param {*} fileName 文件名
 * @param {*} fileType 文件类型
 * @return {*}
 */
export function downloadFileByUrl(data, fileName = new Date().toString()) {
  // 非IE下载
  const elink = document.createElement('a');
  elink.download = fileName;
  elink.style.display = 'none';
  elink.href = data;
  elink.click();
}

/**
 * @description: 下载ysf文件
 * @param {*} data 文件流
 * @param {*} fileName 文件名
 * @param {*} fileType 文件类型
 * @return {*}
 */
export function downloadYsfFile(data, name = new Date().toString()) {
  const resBlob = new Blob([data]);
  const reader = new FileReader();
  reader.readAsText(resBlob, 'utf-8');
  reader.onload = () => {
    try {
      const result = JSON.parse(reader.result);
      message.error(result.message);
      return '';
    } catch (error) {
      const blob = new Blob([data], {
        type: 'application/octet-stream',
      });
      const fileName = `${name}.ysf`;
      if ('download' in document.createElement('a')) {
        // 非IE下载
        const elink = document.createElement('a');
        elink.download = fileName;
        elink.style.display = 'none';
        elink.href = window.URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        window.URL.revokeObjectURL(elink.href); // 释放URL 对象
        document.body.removeChild(elink);
      } else {
        // IE10+下载
        if (navigator.msSaveBlob) {
          navigator.msSaveBlob(blob, fileName);
        }
      }
    }
  };
}

/**
 * 数据添加level
 * @param {*} nodes
 * @param {*} rootId
 * @returns
 */
export function addHierarchy(nodes, rootId) {
  const tree = [];
  const childrenMap = {};

  // 创建childrenMap，方便查找子节点
  nodes.forEach(node => {
    const parentId = node.parentId || rootId;
    if (!childrenMap[parentId]) childrenMap[parentId] = [];
    childrenMap[parentId].push(node);
  });

  // 递归函数为每个节点添加level属性
  function addLevel(parentId, level) {
    if (childrenMap[parentId]) {
      childrenMap[parentId].forEach(node => {
        node.level = level;
        addLevel(node.sequenceNbr, level + 1);
      });
    }
  }

  // 从rootId开始添加level
  addLevel(rootId, 1);

  // 将根节点及其所有子节点添加到tree数组
  function addToTree(parentId) {
    if (childrenMap[parentId]) {
      childrenMap[parentId].forEach(node => {
        tree.push(node);
        addToTree(node.sequenceNbr);
      });
    }
  }

  // 从rootId开始添加节点
  addToTree(rootId);

  return tree;
}

/**
 * @description: 审定增删改标识
 * @param {*} change value
 * @return {*}
 */
export function shChangeLabel(change) {
  // console.log(change)

  if (typeof change === 'object') {
    let classNameList = ['', 'row-add', 'row-del', 'row-modify'];
    return classNameList[Number(change?.row?.ysshSysj?.change)];
  }
  let labelList = [
    {
      class: '',
      label: '',
    },
    {
      class: 'sh-add',
      label: '增',
    },
    {
      class: 'sh-del',
      label: '删',
    },
    {
      class: 'sh-modify',
      label: '改',
    },
  ];
  return (
    labelList[Number(change)] || {
      class: '',
      label: '',
    }
  );
}

/**
 * 过滤特殊字符  ()不过滤
 * @param {*} url
 * @returns
 */
export const YSremoveSpecialChars = str => {
  const regex = /[^-.a-zA-Z0-9\u4e00-\u9fa5()]/;
  return str.replace(regex, '');
};

// 循环添加序号
export function addLevelNumbers(node, prefix = 1) {
  node.sortIndex = prefix;
  if (node.children) {
    // 遍历当前节点的所有子节点
    for (let i = 0; i < node.children.length; i++) {
      // 为子节点生成新的编号，基于当前节点的编号
      const childPrefix = `${prefix}${prefix ? '.' : ''}${i + 1}`;
      addLevelNumbers(node.children[i], childPrefix);
    }
  }
}
/**
 * 是否数字类型
 * @param {*} value
 * @returns
 */
export function isNumber(value) {
  return typeof value === 'number' && !isNaN(value);
}

export function setCssProperty(key, value) {
  document.documentElement.style.setProperty(key, value);
}

function checkBrackets(str) {
  const stack = [];
  const brackets = { '(': ')' }; // 定义括号的匹配关系

  for (const char of str) {
    if (char === '(') {
      stack.push(char); // 遇到左括号入栈
    } else if (char === ')') {
      if (stack.length === 0 || brackets[stack.pop()] !== char) {
        return false; // 栈空或括号不匹配时返回失败
      }
    }
  }

  return stack.length === 0; // 栈空表示所有括号匹配
}

/**
 * 校验输入是否为数字或者四则运算
 * @param {string} inputValue - 输入值
 * @returns {boolean} - 是否合法
 */
export function validateExpression(inputValue) {
  // 基础正则验证：仅允许数字、英文括号、四则运算符和小数点
  const baseCheck = /^[\d+\-*/().]+$/.test(inputValue);
  if (!baseCheck) return false;

  // 扩展校验
  return (
    checkBrackets(inputValue) && // 括号对称性（确保checkBrackets处理英文括号）
    !/(\.\d*\.)/.test(inputValue) && // 禁止多个小数点，允许如0.5或.5（若允许）
    !/[+\-*/]{2,}/.test(inputValue) && // 连续运算符检查
    !/^[+*/]/.test(inputValue) && // 禁止以+、*、/开头
    !/[+\-*/]$/.test(inputValue) && // 禁止以运算符结尾
    !/(^\..*\.)|(^\.$)/.test(inputValue)
  ); // 禁止多个点或以单个点结尾
}
