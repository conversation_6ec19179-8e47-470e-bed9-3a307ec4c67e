const {Controller} = require("../../core");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {ResponseData} = require("../utils/ResponseData");
const OptionMenuHandler = require("../main_editor/optionMenuHandler");
class BranchProjectController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 分部分项和措施项目的分部、清单、定额上下移接口
     * @param args
     * @param type
     * @param operateAction
     * @returns {Promise<*>}
     */
    async qdDeUpAndDown (args) {
        //分部、清单id、定额id、上移下移动作
        let {upId,constructId,singleId,selectId,operateAction,type} = args;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, upId);

        await this.service.baseBranchProjectOptionService.qdDeUpAndDown(operateAction,selectId,unit,type);

        return ResponseData.success(true);

    }


    /**
     * 分部分项分部数据升降级
     * @param args
     * @param operateAction   up 升级  down降级
     * @returns {Promise<ResponseData >}
     */
    async fbDataUpAndDownController (args) {
        //分部数据升级降级
        let {upId,constructId,singleId,selectId,operateAction,type} = args;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, upId);
        await this.service.baseBranchProjectOptionService.fbDataUpAndDown(operateAction,selectId,unit,type,constructId, singleId, upId);
        // 填充操作菜单
        let itemBillProject = unit.itemBillProjects.find(top=>top.kind=="0");
        let nodesByKind = unit.itemBillProjects.getNodeById(itemBillProject.sequenceNbr);
        OptionMenuHandler.setOptionMenu(nodesByKind);
        // this.service.baseBranchProjectOptionService._fillOptionMenu(itemBillProjects,0,itemBillProjects.length-1);
        //重新查询下挂清单定额数据的dispNo
        return ResponseData.success(true);
    }


    /**
     * 分部分项层级拖拽调整
     * @param constructId
     * @param singleId
     * @param unitId
     * @param fbLeveObj  分部层级结构
     * @returns {Promise<*>}
     */
    async fbDragMoveAdjustControllerOld (args) {
        //清单id、定额id、上移下移动作
        let {constructId,singleId,unitId,fbTreeModel} = args;
        await this.service.baseBranchProjectOptionService.fbDragMoveAdjustOld(constructId,singleId,unitId,fbTreeModel);
        // 填充操作菜单
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let itemBillProject = unit.itemBillProjects.find(top=>top.kind=="0");
        let nodesByKind = unit.itemBillProjects.getNodeById(itemBillProject.sequenceNbr);
        OptionMenuHandler.setOptionMenu(nodesByKind);
        //重新查询下挂清单定额数据的dispNo
        return ResponseData.success(true);
    }

    /**
     * 分部分项层级拖拽调整
     * @param constructId
     * @param singleId
     * @param unitId
     * @param sourceFbId  移动分部id
     * @param targetFbId  目标分部id
     * @returns {Promise<*>}
     */
    async fbDragMoveAdjustController (args) {
        //清单id、定额id、上移下移动作
        let {constructId,singleId,unitId,sourceFbId,targetFbId,positionFlag} = args;
        await this.service.baseBranchProjectOptionService.fbDragMoveAdjust(constructId,singleId,unitId,sourceFbId,targetFbId,positionFlag);
        // // 填充操作菜单
        // let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        // let itemBillProject = unit.itemBillProjects.find(top=>top.kind=="0");
        // let nodesByKind = unit.itemBillProjects.getNodeById(itemBillProject.sequenceNbr);
        // OptionMenuHandler.setOptionMenu(nodesByKind);
        //重新查询下挂清单定额数据的dispNo
        return ResponseData.success(true);
    }


    /**
     * 分部分项、措施项目   取消所有主要清单
     * @param type  1整体工程项目  2当前单位工程
     * @returns {Promise<ResponseData>}
     */
    async batchRmoveMainQdController(args) {
        let {constructId,singleId,unitId,type} = args;
        await this.service.baseBranchProjectOptionService.batchRmoveMainQd(constructId,singleId,unitId,type);
        return ResponseData.success(true);
    }

    /**
     * 分部分项、措施项目   删除所有批注
     *  @param type  1整体工程项目  2当前单位工程
     * @returns {Promise<ResponseData>}
     */
    async delBatchAnnotationsController(args) {
        let {constructId,singleId,unitId,type} = args;
        await this.service.baseBranchProjectOptionService.delBatchAnnotations(constructId,singleId,unitId,type);
        return ResponseData.success(true);
    }


    /**
     * 分部整理
     *  @param typeList  整理条件
     * @returns {Promise<ResponseData>}
     */
    async branchArrangeColl(args) {
        let {constructId,singleId,unitId,typeList} = args;
        let result = await this.service.baseBranchProjectOptionService.branchArrange(constructId,singleId,unitId,typeList);
        return ResponseData.success(result);
    }


    /**
     * 清单排序
     *  @param typeList
     * @returns {Promise<ResponseData>}
     */
    async qdSortColl(args) {
        let {constructId,singleId,unitId,type} = args;
        let result = await this.service.baseBranchProjectOptionService.qdSort(constructId,singleId,unitId,type);
        return ResponseData.success(result);
    }













}

BranchProjectController.toString = () => '[class BranchProjectController]';
module.exports = BranchProjectController;
