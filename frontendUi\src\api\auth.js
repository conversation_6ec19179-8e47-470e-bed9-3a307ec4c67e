/*
 * @Descripttion:
 * @Author: liuxia
 * @Date: 2023-05-18 15:13:04
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-04-30 16:43:42
 */
import request from '../utils/request';
import requestInfo from '../utils/requestInfo';
import Qs from 'qs';

/**
 * 登录
 * @param token
 */
export function signIn(token) {
  return request({
    // baseURL: '/pricingApi',
    url: `/pricingApi/auth-api/pricing-bs-auth/oauth/token`,
    method: 'post',
    headers: {
      Authorization: 'Basic cml2ZXQ6eGpsaWRvbmcxMjM=',
      Agency_code: 'HZJT',
      'Content-Type': 'application/x-www-form-urlencoded',
      Product_code: 'HZJT_YZJ_WEB',
    },
    data: Qs.stringify({
      token,
      grant_type: 'xilidata',
      scope: 'all',
    }),
  });
}

export function logout() {
  return request({
    url: `/api/passport-resources/1/passport/oauth/logout`,
    method: 'post',
    headers: {
      Authorization: localStorage.getItem('token'),
    },
  });
}

export function projectList(data) {
  return request({
    url: '/pricing-bs-resource/1/controlBoard/areaDropdownList',
    method: 'post',
    data: data,
  });
}
//获取验证码图片
export function getCaptcha(uuid) {
  return request({
    url: `/api/auth-resource/1/captcha?uuid=${uuid}`,
    method: 'get',
    responseType: 'blob',
  });
}
//登录获取tocken
export function login(data) {
  // console.log('----------------', data);
  return request({
    url: '/api/auth-resource/oauth/token',
    method: 'post',
    headers: {
      Authorization: 'Basic UFJJQ0VJTkdfQ0xJRU5UOnhqbGlkb25nMTIz',
      Product_code: 'PRICEING_CLIENT',
    },
    data: Qs.stringify(data),
  });
}

export const accountPermission = () =>
  request({
    url: `/api/product-resource/1/productOrderDetail/accountPermission`,
    method: 'get',
  });
export const checkUserIsAvailable = () =>
  request({
    url: `/api/product-resource/1/productTrialAccount/checkUserIsAvailable`,
    method: 'get',
  });

export function getEnterprise() {
  return request({
    url: `/api/agency-resource/1/agencies/userAllAgency`,
    method: 'get',
    // headers: {
    //   Authorization: token,
    //   'Cache-Control': 'no-cache',
    //   PRODUCT_CODE: 'AGENCY_ADMIN',
    //   Pragma: 'no-cache',
    // },
  });
}
export function checkNormalAgency() {
  return request({
    url: `/api/agency-resource/1/agenciesNormal/checkNormalAgency`,
    method: 'get',
  });
}
export function userInfo() {
  return request({
    url: '/api/passport-resources/1/passport/userDetail',
    method: 'get',
  });
}

export function checkMobile(mobile, type) {
  //验证手机号是否注册过
  return request({
    url: `/api/passport-resources/1/passport/${type}/${mobile}/checkMobile`,
    method: 'get',
    headers: {
      Authorization: ' ',
    },
  });
}
export function getSmsCode(mobile, type) {
  //获取短信验证码
  return request({
    url: `/api/auth-resource/1/${type}/${mobile}/smsCode`,
    method: 'get',
  });
}

export function registerInfo(data) {
  //登录-注册
  return request({
    url: '/api/auth-resource/1/register',
    method: 'post',
    data,
    headers: {
      agency: '',
      'Content-Type': 'application/json;charset=UTF-8',
      Authorization: ' ',
    },
  });
}

export function changePassWord(data) {
  //登录-忘记密码/重置密码
  return request({
    url: '/api/auth-resource/1/reset',
    method: 'post',
    data,
  });
}

/**
 * @description: 切换身份
 * @return {*}
 */
export function changeAgency(data) {
  return request({
    url: `/api/agency-resource/1/agencies/changeAgency`,
    method: 'post',
    data,
  });
}
//判断当前登录用户是否是vip身份
export function getVip(code) {
  return request({
    url: '/api/order-resource/1/order/verify/vip',
    method: 'get',
    headers: {
      Agency_code: code,
    },
  });
}
//获取信息价列表
export const getPeriodical = code =>
  requestInfo({
    url: `1/portal/periodical/${code}`,
    method: 'get',
  });

//获取信息价列表
export const getmateriallist = data =>
  requestInfo({
    url: `1/collect/material/list`,
    method: 'post',
    data,
  });

//判断当前登录用户是否是vip身份
export function getUpdateJson() {
  return request({
    url: 'https://pricing-dev.oss-cn-hangzhou.aliyuncs.com/test/updater.json',
    method: 'get',
  });
}
