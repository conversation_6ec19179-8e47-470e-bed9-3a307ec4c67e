<!--
 * @Descripttion: 
-->
<template>
  <commonModal
    className="dialog-comm"
    :width="910"
    v-model:modelValue="show"
    title="造价系数调整"
    destroy-on-close
    @close="close"
    @cancel="close"
  >
    <a-spin :spinning="spinning" tip="Loading...">
      <div class="main">
        <div class="setting-content">
          <div class="tree-box vxe-grid-table">
            <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
            </vxe-grid>
          </div>
          <div class="other-box">
            <div class="tab-menu">
              <div
                :class="{ item: true, active: tabIndex === index }"
                v-for="(item, index) of tabMenuList"
                :key="item.key"
                @click="tabChange(index)"
              >
                {{ item.title }}
              </div>
            </div>
            <div class="tab-content" v-if="tabMenuList[tabIndex]">
              <div class="specific-box">
                <div class="sub-title">调整系数</div>
                <div class="sub-content">
                  <template v-for="item in tabMenuList[tabIndex].params">
                    <div class="list">
                      <span class="name">{{ item.name }}</span>
                      <a-input-number
                        v-model:value="item.value"
                        :precision="3"
                        :min="0.001"
                        @blur="leaveInput(item)"
                      />
                    </div>
                  </template>
                </div>
              </div>
              <div class="global-box">
                <div class="sub-title">
                  全局选项
                  <a-tooltip placement="right" v-if="disableCheckList.length">
                    <template #title>
                      <div v-for="item in disableCheckList">
                        {{ item.name }}
                      </div>
                    </template>
                    <QuestionCircleOutlined style="color: #979797" />
                  </a-tooltip>
                </div>
                <div class="sub-content">
                  <a-checkbox-group
                    v-model:value="tabMenuList[tabIndex].globalOptions"
                    style="width: 100%"
                    @change="filterMaterial"
                  >
                    <div v-for="option in checkboxList">
                      <a-checkbox
                        v-show="!option.disabled"
                        :value="option.value"
                        :disabled="option.disabled"
                        >{{ option.name }}</a-checkbox
                      >
                    </div>
                  </a-checkbox-group>
                  <a-button @click="lockClick">锁定材料</a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="preview-content">
          <div class="sub-title">工程造价预览</div>
          <div class="vxe-grid-table">
            <vxe-grid ref="previewGridRef" v-bind="previewGridOptions">
            </vxe-grid>
          </div>
        </div>
      </div>
      <div class="tip">
        <icon-font type="icon-tishi-cheng"></icon-font
        ><span>统一调价功能将会改变当前工程的造价，建议备份当前工程！</span>
      </div>
      <div class="footer-btn-list">
        <div class="left">
          <a-button type="primary" ghost @click="backups">备份工程</a-button>
          <a-button type="primary" ghost @click="openProject"
            >打开备份工程</a-button
          >
        </div>
        <a-button
          type="primary"
          ghost
          :disabled="spinning"
          @click="constructPreview"
          >工程造价预览</a-button
        >
        <a-button type="primary" @click="adjustClick" :disabled="spinning"
          >调整</a-button
        >
        <a-button type="primary" ghost :disabled="spinning" @click="close"
          >关闭</a-button
        >
      </div>
    </a-spin>
  </commonModal>
  <lockingMaterials
    v-model:visible="lockVisible"
    :tableList="materialList[tabIndex]"
    @callback="lockCallback"
  ></lockingMaterials>
</template>

<script setup>
import { ref, computed, watch, reactive, toRaw, nextTick } from 'vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
import { constructLevelTreeStructureList } from '@/api/csProject';
import api from '@/api/projectDetail';
import feePro from '@/api/feePro';
import infoMode from '@/plugins/infoMode.js';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import lockingMaterials from './lockingMaterials.vue';
const projectStore = projectDetailStore();
const props = defineProps({
  visible: Boolean,
});
const emit = defineEmits(['update:visible', 'refreshTableList']);
let spinning = ref(false);
const show = computed({
  get: () => {
    return props.visible;
  },
  set: val => {
    emit('update:visible', val);
  },
});
const leaveInput = item => {
  //输入数值为空时，设置为1
  if (!item.value) item.value = 1;
};
const backups = () => {
  const { constructId } = projectStore.currentTreeGroupInfo;
  api.backups({ constructId }).then(res => {
    if (res.result) {
      message.success('备份成功！');
    }
  });
};
const closeWindow = () => {
  const { constructId } = projectStore.currentTreeGroupInfo;
  api.closeWindow({ constructId });
};
const close = () => {
  closeWindow();
  show.value = false;
  initTabMenu();
};
const open = async () => {
  initTabMenu();
  await getTreeData();
  getMaterialAllData();
  getPreviewData();
};
const openProject = () => {
  feePro.openLocalFile();
};
watch(
  () => props.visible,
  val => {
    if (val) {
      open();
    }
  }
);
let gridRef = ref();
// let isTreeCheckboxChange = ref(false);
const gridEvents = reactive({
  checkboxChange: ({ checked }) => {
    getMaterialAllData();
    // isTreeCheckboxChange.value = true;
  },
});
const gridOptions = reactive({
  border: 'full',
  height: 310,
  headerAlign: 'center',
  treeConfig: {
    expandAll: true,
    transform: true,
    showLine: false,
    iconOpen: 'vxe-icon-minus',
    iconClose: 'vxe-icon-add',
  },
  columnConfig: {
    resizable: true,
  },
  checkboxConfig: {
    showHeader: false,
    checkField: 'checked',
  },
  columns: [
    { field: 'name', title: '名称', treeNode: true, align: 'left' },
    { type: 'checkbox', title: '调整', width: 50, align: 'center' },
  ],
  data: [],
});
const getTreeData = async () => {
  const { constructId } = projectStore.currentTreeGroupInfo;
  const res = await constructLevelTreeStructureList(constructId);
  gridOptions.data = res.result.map(item => {
    item.checked = true;
    return item;
  });
  nextTick(() => {
    gridRef.value?.setAllTreeExpand(true);
  });
};
const getPreviewData = () => {
  const { constructId } = projectStore.currentTreeGroupInfo;
  api.priceWindow({ constructId }).then(res => {
    console.log('🚀 ~ api.priceWindow ~ res:', res);
    previewGridOptions.data = res.result;
    nextTick(() => {
      previewGridRef.value?.setAllTreeExpand(true);
      spinning.value = false;
    });
  });
};
let previewGridRef = ref();
let previewGridOptions = reactive({
  border: 'full',
  height: 240,
  headerAlign: 'center',
  treeConfig: {
    expandAll: true,
    transform: true,
    showLine: false,
    iconOpen: 'vxe-icon-minus',
    iconClose: 'vxe-icon-add',
  },
  columnConfig: {
    resizable: true,
  },
  columns: [
    { field: 'disNo', title: '', width: 40, align: 'center' },
    { field: 'name', title: '名称', treeNode: true, align: 'left' },
    { field: 'beforePrice', title: '调整前造价', align: 'center' },
    {
      field: 'afterPrice',
      title: '调整后造价',
      align: 'center',
      className: ({ row }) => {
        return row.difference > 0 ? 'red' : '';
      },
    },
    { field: 'difference', title: '调整', align: 'center' },
  ],
  data: [],
});

const constructPreview = () => {
  const allParams = getParams();
  console.log('🚀 ~ constructPreview ~ allParams:', allParams, materialList);
  spinning.value = true;
  api.constructPreview(allParams).then(res => {
    console.log('🚀 ~ constructPreview ~ res:', res, allParams);
    getPreviewData();
  });
};

const adjustClick = () => {
  infoMode.show({
    iconType: 'icon-querenshanchu',
    infoText: '是否备份当前项目，以便后续恢复？',
    descText: '',
    cancelText: '直接调价',
    confirmText: '备份',
    isFunction: false,
    confirm: () => {
      adjustHandle(true);
      infoMode.hide();
    },
    close: () => {
      adjustHandle(false);
      infoMode.hide();
    },
  });
};

const adjustHandle = (backups = true) => {
  spinning.value = true;
  const allParams = { ...getParams(), backups };
  console.log('🚀 ~ adjustHandle ~ allParams:', allParams);
  api.adjust(allParams).then(res => {
    spinning.value = false;
    if (res.result) {
      message.success('调整成功！');
      // if (isClose)
      close();
      console.log('🚀 ~ api.adjust ~ res:', res);
      emit('refreshTableList');
    }
  });
};

let tabIndex = ref('priceParams');
const tabChange = index => {
  tabIndex.value = index;
  filterMaterial();
};
const paramsList = [
  {
    field: 'renGong',
    name: '人工',
    value: '1',
    type: 1,
  },
  {
    field: 'caiLiao',
    name: '材料',
    value: '1',
    type: 2,
  },
  {
    field: 'jiXie',
    name: '机械',
    value: '1',
    type: 3,
  },
  {
    field: 'zhuCai',
    name: '主材',
    value: '1',
    type: 5,
  },
  {
    field: 'sheBie',
    name: '设备',
    value: '1',
    type: 4,
  },
];
const priceCheckboxList = ref([
  {
    field: 'jiaGongNotAdjust',
    name: '甲供材料不参与调整',
    value: 1,
  },
  {
    field: 'zanGuNotAdjust',
    name: '暂估材料不参与调整',
    disabled: true,
    value: 2,
  },
  {
    field: 'jiaDingNotAdjust',
    name: '甲定材料不参与调整',
    value: 3,
  },
  {
    field: 'renGongNotAdjust',
    name: '人工不参与调整',
    value: 4,
  },
  {
    field: 'sheBieNotAdjust',
    name: '设备不参与调整',
    disabled: true,
    value: 5,
  },
]);
const resQtyCheckboxList = ref([
  {
    field: 'jiaGongNotAdjust',
    name: '甲供材料不参与调整',
    value: 1,
  },
  {
    field: 'zanGuNotAdjust',
    name: '暂估材料不参与调整',
    value: 2,
  },
  {
    field: 'jiaDingNotAdjust',
    name: '甲定材料不参与调整',
    value: 3,
  },
  {
    field: 'renGongNotAdjust',
    name: '人工不参与调整',
    value: 4,
  },
  {
    field: 'sheBieNotAdjust',
    name: '设备不参与调整',
    disabled: true,
    value: 5,
  },
  {
    field: 'unitIntegerNotAdjust',
    name: '单位为整数的材料不参与调整',
    type: 'global',
    value: 6,
  },
]);
const checkboxList = computed(() => {
  if (tabIndex.value === 'priceParams') {
    return priceCheckboxList.value;
  }
  return resQtyCheckboxList.value;
});
const disableCheckList = computed(() => {
  return checkboxList.value.filter(item => item.disabled);
});

const menuList = {
  priceParams: {
    title: '人材机单价',
    key: 'priceParams',
    params: paramsList,
    globalOptions: [1, 2, 3, 5],
  },
  quantitiesParams: {
    title: '人材机消耗量',
    key: 'quantitiesParams',
    params: paramsList,
    globalOptions: [1, 5, 6],
  },
};
const tabMenuList = ref({});
const initTabMenu = () => {
  tabIndex.value = 'priceParams';
  tabMenuList.value = JSON.parse(JSON.stringify(menuList));
};
const getParamsIds = () => {
  const { constructId } = projectStore.currentTreeGroupInfo;
  let obj = {
    constructId,
    unitIds: gridOptions.data
      .filter(item => {
        return item.checked && item.levelType === 3;
      })
      .map(item => item.id),
  };
  return obj;
};
const getParams = () => {
  let obj = getParamsIds();
  for (const key in tabMenuList.value) {
    if (!obj[key]) obj[key] = {};
    obj[key].globalOptions = toRaw(tabMenuList.value[key].globalOptions);
    obj[key].rcjList = JSON.parse(
      JSON.stringify(
        materialList[key].filter(item => !item.excludedAdjust) || []
      )
    );
    tabMenuList.value[key].params.forEach(item => {
      if (!obj[key].adjustList) obj[key].adjustList = [];
      if (Number(item.value) !== 1) {
        obj[key].adjustList.push({
          kind: item.type,
          coefficient: item.value,
        });
      }
    });
  }
  return obj;
};
let lockVisible = ref(false);
let materialList = reactive({
  priceParams: [],
  quantitiesParams: [],
});
let originalMaterialList = null; // 数据备份
const lockCallback = ({ type, data }) => {
  if (type === 'cancel') {
  }
  if (type === 'ok') {
    materialList[tabIndex.value] = JSON.parse(JSON.stringify(data));
  }
};
const getMaterialAllData = async () => {
  // isTreeCheckboxChange.value = false;
  const params = getParamsIds();
  console.log('🚀 ~ getMaterialAllData ~ params:', params);
  params.type = tabIndex.value === 'priceParams' ? 1 : 2;
  const priceRes = await api.lockMaterial({ ...params, type: 1 });
  const quantityRes = await api.lockMaterial({ ...params, type: 2 });
  console.log('🚀 ~ getMaterialAllData ~ priceRes:', priceRes);
  console.log('🚀 ~ getMaterialAllData ~ quantityRes:', quantityRes);
  materialList.priceParams = priceRes.result;
  materialList.quantitiesParams = quantityRes.result;
  originalMaterialList = JSON.parse(JSON.stringify(materialList));
  filterMaterial();
};
const lockClick = () => {
  lockVisible.value = true;
  // if (isTreeCheckboxChange.value) {
  //   getMaterialAllData();
  // }
};

const unit_set = new Set([
  '丛',
  '个',
  '付',
  '件',
  '元',
  '副',
  '包',
  '卷',
  '发',
  '只',
  '台',
  '圈',
  '块',
  '套',
  '对',
  '座',
  '张',
  '户',
  '扇',
  '把',
  '支',
  '条',
  '架',
  '株',
  '根',
  '樘',
  '次',
  '片',
  '瓶',
  '盆',
  '盒',
  '盘',
  '筒',
  '管',
  '箱',
  '粒',
  '系统',
  '组',
  '节',
  '袋',
  '轴',
  '部',
  '颗',
]);

const conditionMap = new Map([
  [1, e => e.ifDonorMaterial === 1],
  [2, e => e.ifProvisionalEstimate === 1],
  [3, e => e.ifDonorMaterial === 2],
  [4, e => e.kind === 1],
  [5, e => e.kind === 4],
  [6, e => unit_set.has(e.unit)],
]);
// 基期价、市场价为“-
const isChangeAva = row => {
  return (
    projectStore.deType === '22' &&
    ![null, undefined].includes(row.isChangeAva) &&
    Number(row.isChangeAva) === 0
  );
};
const defaultChecked = row => {
  const key = tabIndex.value;
  // 锁定市场价、锁定消耗量
  return (
    isChangeAva(row) ||
    (key === 'priceParams' && row.ifLockStandardPrice === 1) ||
    (key === 'quantitiesParams' && row.isLock)
  );
};
const filterMaterial = () => {
  const globalOptions = tabMenuList.value[tabIndex.value]?.globalOptions || [];
  function processRcjElement(element, options) {
    return Array.from(options).some(
      opt => conditionMap.has(opt) && conditionMap.get(opt)(element)
    );
  }
  for (const element of materialList[tabIndex.value]) {
    element.excludedAdjust = !!element.excludedAdjust;
    element.disabled = !!element.disabled;
    if (processRcjElement(element, globalOptions) || defaultChecked(element)) {
      element.excludedAdjust = true;
      element.disabled = true;
      element.isGlobalSelect = true;
    } else if (element.isGlobalSelect) {
      element.excludedAdjust = false;
      element.disabled = false;
      element.isGlobalSelect = false;
    }
  }
  console.log(materialList[tabIndex.value]);
};
</script>
<style lang="scss" scoped>
.main {
  height: 65vh;
  overflow-y: auto;
}
.footer-btn-list {
  display: flex;
  .left {
    flex: 1;
    text-align: left;
  }
  :deep(.ant-btn) {
    margin: 0 5px !important;
  }
}
.tip {
  padding: 6px 0;

  span {
    display: inline-block;
    padding-left: 5px;
    font-size: 12px;
    line-height: 1.5;
    color: #2a2a2a;
  }
}
.preview-content {
  .sub-title {
    padding: 10px 0;
    font-size: 14px;
    color: #2a2a2a;
  }
  :deep(.red) {
    color: #de3f3f;
  }
}
.vxe-grid-table {
  :deep(.vxe-tree--btn-wrapper) {
    border: 1px solid #bfbfc0;
  }
  :deep(.vxe-tree--node-btn) {
    font-size: 9px;
    line-height: 12px;
    color: #287cfa;
  }
}
.setting-content {
  display: flex;
  .tree-box {
    width: 210px;
    margin-right: 4px;
  }
  .other-box {
    flex: 1;
    height: 310px;
    border: 1px solid #d9d9d9;
    .tab-menu {
      display: flex;
      height: 30px;
      background: #f3f3f3;
      border-bottom: 1px solid #d9d9d9;
      .item {
        text-align: center;
        width: 100px;
        line-height: 30px;
        font-size: 14px;
        color: #2a2a2a;
        background: #dbdbdb;
        cursor: pointer;
        &.active {
          background-color: #287cfa;
          color: #fff;
        }
      }
    }
  }
}
.tab-content {
  display: flex;
  height: calc(100% - 30px);
  .sub-title {
    padding: 10px 0 0;
    line-height: 20px;
    font-size: 14px;
    color: #2a2a2a;
  }
  .specific-box {
    width: 66.66%;
    padding: 0 0 0 15px;
    height: 100%;
    border-right: 1px solid #d9d9d9;
    .sub-content {
      display: flex;
      flex-wrap: wrap;
      font-size: 14px;
      color: #2a2a2a;
      .list {
        display: flex;
        align-items: center;
        width: 50%;
        padding: 10px 25px 10px 0;
        span {
          display: inline-block;
          width: 44px;
        }
        :deep(.ant-input-number) {
          flex: 1;
        }
      }
    }
  }
  .global-box {
    width: 33.33%;
    padding: 0 15px;
    .sub-title {
      padding-bottom: 10px;
    }
    .sub-content {
      position: relative;
      height: calc(100% - 40px);
    }
    :deep(.ant-btn) {
      position: absolute;
      bottom: 8px;
      left: 0;
      height: 28px;
      padding: 0 15px;
      width: 100%;
    }
  }
}
</style>
