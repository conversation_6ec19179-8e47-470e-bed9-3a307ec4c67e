'use strict';

const { Service, Log } = require('../../core');
const { app } = require('electron');
const { ConstructProject } = require('../model/ConstructProject');
const { SingleProject } = require('../model/SingleProject');
const { UnitProject } = require('../model/UnitProject');
const { PricingFileWriteUtils } = require('../utils/PricingFileWriteUtils');
const { format, addDays, differenceInDays } = require('date-fns');
const fs = require('fs');
const { ProjectOverview } = require('../model/ProjectOverview');
const gcjbxx = require('../jsonData/gcjbxx.json');
const dwjbxx = require('../jsonData/dwjbxx.json');
const gctz = require('../jsonData/gctz.json');
const { el } = require('date-fns/locale');
const { ConvertUtil } = require('../utils/ConvertUtils');
const { OrganizationInstructions } = require('../model/OrganizationInstructions');
const { PricingFileFindUtils } = require('../utils/PricingFileFindUtils');
const { FileLevelTreeNode } = require('../model/FileLevelTreeNode');
const { ItemBillProject } = require('../model/ItemBillProject');
const { ResponseData } = require('../utils/ResponseData');
const { ObjectUtils } = require('../utils/ObjectUtils');
const moment = require('moment/moment');
const { DateUtils } = require('../utils/DateUtils');
const {
  app: electronApp,
  dialog, shell, BrowserView, Notification,
  powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const path = require('path');
const { Snowflake } = require('../utils/Snowflake');
const { BasePolicyDocument } = require('../model/BasePolicyDocument');
const { In } = require('typeorm');
const PolicyDocumentTypeEnum = require('../enum/PolicyDocumentTypeEnum');
const BranchProjectDisplayConstant = require('../enum/BranchProjectDisplayConstant');
const BranchProjectLevelConstant = require('../enum/BranchProjectLevelConstant');
const { HttpUtils } = require('../utils/HttpUtils');
const BsRemoteUrl = require('../enum/BsRemoteUrl');
const os = require('os');
const ConstantUtil = require('../enum/ConstantUtil');
const { ConstructOperationUtil } = require('../utils/ConstructOperationUtil');
const CalculateBaseUnitInitEnum = require('../enum/CalculateBaseUnitInitEnum');
const CalculateBaseType = require('../enum/CalculateBaseType');

const { Tree, TreeNode } = require('../main_editor/tree');
const InsertStrategy = require('../main_editor/insert/insertStrategy');
const { arrayToTree } = require('../main_editor/tree');
const { treeToArray } = require('../main_editor/tree');
const { MainMaterialSetting } = require('../model/MainMaterialSetting');
const { IpcWinUtils } = require('../utils/IpcWinUtils');
const { ConSoleCommonHandler } = require('../console_handle/ConSoleCommonHandler');
const { ProjectFileUtils } = require('../../common/ProjectFileUtils');
const ConsoleStrategy = require('../console_handle/ConsoleStrategy');
const { NumberUtil } = require('../utils/NumberUtil');
const {UnitRcjCacheUtil} = require("../rcj_handle/cache/UnitRcjCacheUtil");
const {ReadXmlUtil} = require("../utils/ReadXmlUtil");
const { MainSetting } = require('../model/MainSetting');

/**
 * 示例服务
 * @class
 */
class ConstructProjectService extends Service {

  static majorTypeObject = {
    [ConstantUtil.DE_STANDARD_12]: {
      "01": "建筑工程",
      "": "装饰工程",
      "02": "仿古建筑工程",
      "03": "安装工程",
      "04": "市政工程",
      "05": "园林绿化工程"
    },
    [ConstantUtil.DE_STANDARD_22]: {
      "01": "建筑工程",
      "": "装饰装修工程",
      "02": "仿古建筑工程",
      "03": "安装工程",
      "04": "市政工程",
      "05": "园林绿化工程"
    }
  }

  constructor(ctx) {
    super(ctx);
  }


  async importProject(args) {
    try {
      let res = await this.service.analyzingXMLService.analysis(args);
      return res;
    } catch (error) {
      console.error('发生错误：', error.stack);
      return ResponseData.fail('解析xml出错');
    }
  }


  async analProjectXmlInfo(importUrl) {
    try {
      let data =ReadXmlUtil.readXmlMainInfo(importUrl);
      return data;
    } catch (error) {
      console.error('发生错误：', error.stack);
      return ResponseData.fail('解析xml出错');
    }
  }

  /**
   * 新建预算项目
   */
  async newBudgetProject(arg) {
    let { constructName,biddingType } = arg;
    //let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(constructName);
    let defaultStoragePath = await this.service.commonService.getSetStoragePath(constructName);
    let {extensions, dialogOptions} = await this.defaultDialog(biddingType, defaultStoragePath);
    let result = dialog.showSaveDialogSync(null, dialogOptions);
    if (result && !result.canceled) {
      let filePath = result;
      console.log(filePath);
      //查询是否覆盖路径
      this.isOverlayFile(filePath);
      // 在这里处理保存文件的操作
      if (!filePath.toUpperCase().endsWith(extensions[0])) {
        filePath += '.'+extensions[0];
      }
      let sequenceNbr = await this.createProject(arg, filePath);
      await this.service.systemService.openWindowForProject(filePath);
      return sequenceNbr;
    }

  }


  /**
   * 根据项目类型默认弹窗
   * @param biddingType
   * @param defaultStoragePath
   * @returns {{extensions: (string[]|string[]|string[]), dialogOptions: {defaultPath: string, filters: [{extensions: (string[]|string[]|string[]), name: string}], title: string}}}
   */
  defaultDialog(biddingType, defaultStoragePath,title) {
    let extensions = ''
    if (biddingType == 0) {
      extensions = [ConstantUtil.YUSUAN_FILE_SUFFIX_Z]
    } else if (biddingType == 1) {
      extensions = [ConstantUtil.YUSUAN_FILE_SUFFIX]
    } else {
      extensions = [ConstantUtil.YUSUAN_FILE_SUFFIX_D]
    }
    defaultStoragePath = defaultStoragePath.replace(/\.YSF$/, '.' + extensions[0])
    const dialogOptions = {
      title: title??'保存文件',
      defaultPath: defaultStoragePath.toString(),
      filters: [{name: '云算房文件', extensions: extensions}]
    };
    return {extensions, dialogOptions};
  }

  /**
   * 打开线上项目
   * @param arg
   * @return {Promise<void>}
   */
  async openOnlineProject(arg) {
    let sequenceNbr = arg.sequenceNbr;
    let url = BsRemoteUrl.openOnlineProject + sequenceNbr;
    let headers = {
      'AGENCY_CODE': 'HZJT',
      'PRODUCT_CODE': 'HZJT_YZJ_WEB',
      'Authorization': arg.authorization
    };
    let data = await HttpUtils.GET(url, headers, arg);
    if (data.status !== 200) {
      return data;
    }
    //获取线上项目
    let onlineProject = data.result;
    //let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(onlineProject.constructName);
    let defaultStoragePath = await this.service.commonService.getSetStoragePath(onlineProject.constructName);
    let result = dialog.showSaveDialogSync(null, {
      title: '保存文件',
      defaultPath: defaultStoragePath.toString(), // 默认保存路径
      filters: [
        { name: '云算房文件', extensions: [ConstantUtil.YUSUAN_FILE_SUFFIX] } // 可选的文件类型
      ]
    });
    if (result && !result.canceled) {
      let filePath = result;
      console.log(filePath);
      //查询是否覆盖路径
      this.isOverlayFile(filePath);
      // 在这里处理保存文件的操作
      if (!filePath.toUpperCase().endsWith(ConstantUtil.YUSUAN_FILE_DOT_SUFFIX)) {
        filePath = filePath.concat(ConstantUtil.YUSUAN_FILE_DOT_SUFFIX);
      }

      await this.service.ysfHandlerService.creatYsfFile(onlineProject);

      //打开项目
      await this.service.systemService.openWindowForProject(filePath);
      return nextId;
    }
  }
  /**
   * @description  设置modal窗口
   * @param {{windowId:number,modals:Object}} arg
   * @return {Promise<void>}
   */
  createModal({windowId,modal}) {
    modal.map(async (modalItem) => {
      return await this.service.systemService.createModalWindow({windowId,modal:modalItem});
    })
  }
  /**
   * @description  显示modal窗口
   * @param {{type:string,showType:string}} arg - type:modal类型 showType:show,hide,max
   * @return {Promise<void>}
   */
  async setModalState({windowId,type,showType}) {
    await this.service.systemService.setModalState(windowId,type,showType);
  }

  updatePropertyValue(obj, propertyKey, newValue) {
    for (let key in obj) {
      if (typeof obj[key] === 'object') {
        if (Array.isArray(obj[key])) {
          // 如果属性的值是数组，则循环遍历数组并递归调用更新函数
          obj[key].forEach((item) => {
            this.updatePropertyValue(item, propertyKey, newValue);
          });
        } else {
          // 如果属性的值是对象，则递归调用更新函数
          this.updatePropertyValue(obj[key], propertyKey, newValue);
        }
      } else if (key === propertyKey && !ObjectUtils.isEmpty(obj[key])) {
        // 如果属性的键等于目标属性键，并且属性具有值，则更新属性的值
        obj[key] = newValue;
      }
    }
  }


  /**
   * 判断文件是否覆盖
   * @param arg
   * @return {Promise<ResponseData>}
   */
  async isOverlayFile(path) {

    // let result =  await this.service.constructProjectFileService.getOneProDataByPath(path);
    //
    // //不是空 则覆盖文件
    // if (!ObjectUtils.isEmpty(result)) {
    //
    //
    //
    //
    //     this.app.appDataSource.getRepository(ConstructProjectFile).remove(result);
    // }


  }

  async createProject(arg, pathName) {
    //将数据存储在内存中
    let obj = new ConstructProject();
    ConvertUtil.setDstBySrc(arg, obj);
    //设置文件版本号
    obj.version = app.getVersion();
    //设置定额标准发布年份
    let deStandard = await this.service.baseListDeStandardService.quotaStandardById(obj.deStandardId);
    obj.deStandardReleaseYear = deStandard.releaseYear;
    if (obj.deStandardReleaseYear == ConstantUtil.DE_STANDARD_12) {
      obj.rgfInMeasureAndRPriceInMechanicalAction = true;
    }

    let projectSequenceNbr = Snowflake.nextId();
    obj.sequenceNbr = projectSequenceNbr;
    PricingFileWriteUtils.writeToMemory(obj);
    obj.path = pathName;
    obj.ssCity = 130100;
    obj.ssCityName = "石家庄市";
    obj.mainRcjShowFlag=true;
    obj.standardConversionShowFlag=true;
    //初始化子定额展开设置
    obj.deGlTcFlag=true;
    //初始化工程项目的计税方式数据
    await this.service.projectTaxCalculationService.initConstructSelectTaxCalculationMethod(obj, arg);

    //初始化政策文件数据
    await this.initConstructPolicyDocument(obj);

    //招投标项目类型
    if (0 == obj.biddingType || 1 == obj.biddingType) {
      if (!ObjectUtils.isEmpty(arg.importUrl) && !ObjectUtils.isEmpty(arg.xmlFactory)) {
        try {
          return await this.service.analyzingXMLService.analysis(obj);
        } catch (error) {
          console.error('发生错误：', error.stack);
          return ResponseData.fail('解析xml出错');
        }
        //此处调用导入xml方法
      } else {
        //初始化工程项目的基本信息
        this.initProjectOrUnitData(obj, 1);
        //初始化编制说明
        this.initProjectOrUnitBZSM(1, obj);
        //初始化单项工程招投标项目
        this.initSingle(obj);

      }


    } else {
      //单位工程项目类型
      //初始化单位
      let libraryCode = arg.libraryCode;
      let secondInstallationProjectName = arg.secondInstallationProjectName;
      await this.initUnit(obj, null, Snowflake.nextId(), obj.constructName, arg.constructMajorType, null, false, libraryCode, secondInstallationProjectName, obj.deStandardId);

    }

    //let constructProject = await PricingFileWriteUtils.creatYsfFile(obj);
    // let data = ObjectUtils.toJsonString(obj);
    // let jsonObject = JSON.parse(data);

    let globalConfig = this.service.globalConfigurationService.getGlobalConfig();
    obj.globalConfigXX = globalConfig.project;

    PricingFileWriteUtils.writeToMemory(obj);
    let constructProject = await this.service.ysfHandlerService.creatYsfFile(obj);
    return constructProject.sequenceNbr;
  }


  /**
   * 最近打开文件记录
   * @param arg
   * @return {Promise<any[]>}
   */
  async recentlyOpenedProjectList(arg) {
    let userHistoryFileData = PricingFileFindUtils.userHistoryFileListData();
    if (!ObjectUtils.isEmpty(userHistoryFileData)) {
      const sortedItems = userHistoryFileData.sort((a, b) => new Date(b.openTime) - new Date(a.openTime));
      return sortedItems.slice(0, 20);
    }
    return userHistoryFileData;
  }

  /**
   * 打开文件
   * @param arg
   * @return {Promise<void>}
   */
  async openProject(arg) {
    //检查项目文件是否存在
    if (!this.checkFileExistence(arg.path)) {
      return this.handlingPath(arg);
    }
    //打开项目
    //await this.service.systemService.openWindowForProject(arg.path);
    let consoleStrategy = new ConsoleStrategy({ path: arg.path });
    let res = await consoleStrategy.openLocalObj();

    //TODO 上传OSS
    //上传oss
    this.service.ossService.ossPut();
  }

  /**
   * 处理错误路径
   * @param arg
   * @returns {Promise<ResponseData>}
   */
  async handlingPath(arg) {
    //获取用户文件列表数据
    let userHistoryData = PricingFileFindUtils.userHistoryData();
    let idKey = PricingFileFindUtils.userInfoData();
    let userHistoryDatum = userHistoryData[idKey];
    const index = userHistoryDatum.findIndex(item => item.path === arg.path);
    if (index !== -1) {
      userHistoryDatum.splice(index, 1);
    }
    //数据写入历史文件中
    PricingFileWriteUtils.writeUserHistoryFile(PricingFileFindUtils.userHistoryDataPath(), userHistoryData);
    return ResponseData.fail('文件已被删除或移动');
  }

  async openLocalFile() {

    let storagePath = ProjectFileUtils.getSetStoragePath();
    let path = await ProjectFileUtils.openFileWin(storagePath);
    let consoleStrategy = new ConsoleStrategy({ path: path });
    await consoleStrategy.openLocalObj();


    //let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(null);
    // let defaultStoragePath = await this.service.commonService.getSetStoragePath(null);
    //
    // const options = {
    //     properties: ['openFile'],
    //     //defaultPath: defaultStoragePath.toString(), // 默认保存路径
    //     defaultPath: defaultStoragePath, // 默认保存路径
    //     filters: [
    //         {name: '云算房', extensions: [ConstantUtil.YUSUAN_FILE_SUFFIX]} // 可选的文件类型
    //     ]
    // };
    // let result = dialog.showOpenDialogSync(null, options);
    // if (ObjectUtils.isEmpty(result)) {
    //     console.log("未选中任何文件");
    //     return;
    // }
    // //获取选中的路径
    // let filePath = result[0];
    // if (!this.checkFileExistence(filePath)) {
    //     console.log("路径有误");
    //     return;
    // }
    // await this.service.systemService.openWindowForProject(filePath);
    return ResponseData.success();
  }
  /**
   * 判断后缀
   * @param obj
   * @returns {Promise<ResponseData>}
   */
  async judgeSuffix(args){
    //检查项目文件是否存在
    if (!this.checkFileExistence(args.path)) {
      return this.handlingPath(args);
    }
    let obj =await PricingFileFindUtils.getProjectObjByPath(args.path);
    // 定义文件后缀与 biddingType 的映射关系
    const suffixToBiddingType = {
      YSFZ: 0,
      YSF: 1,
      YSFD: 2,
      YJS: 3,
      YSH: 5,
      YGS: 4,
      YSFG: 7,
    };

    // 获取文件后缀
    let pathSuffix = obj.path.match(/[^.]+$/)[0];

    // 获取默认存储路径
    let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(null);

    // 检查文件后缀是否在映射表中
    if (suffixToBiddingType.hasOwnProperty(pathSuffix)) {
      // 检查 biddingType 是否匹配
      if (obj.biddingType !== suffixToBiddingType[pathSuffix]) {

        // 获取文件名和扩展名
        const filename = path.basename(args.path);
        const { name, ext } = path.parse(filename);

        // 构建目标文件路径
        let targetPath = path.join(defaultStoragePath, filename);

        // 检查文件是否已存在，如果存在则添加后缀
        let counter = 1;
        while (fs.existsSync(targetPath)) {
          const newName = `${name}(${counter})${ext}`;
          targetPath = path.join(defaultStoragePath, newName);
          counter++;
        }

        // 复制文件
        fs.copyFileSync(args.path, targetPath);


        return ResponseData.success(`文件版本更新，历史文件已备份至${defaultStoragePath}处`);
      }
    } else {
      // 如果文件后缀不在映射表中，可以处理异常情况
      return ResponseData.fail('文件后缀不支持');
    }
    return ResponseData.success()
  }

  // 检查文件是否存在
  checkFileExistence(filePath) {
    try {
      fs.accessSync(filePath, fs.constants.F_OK);
      return true;
    } catch (err) {
      return false;
    }
  }

  //检查文件是否是只读
  isFileReadOnly(filePath) {
    return new Promise((resolve, reject) => {
      fs.access(filePath, fs.constants.W_OK, (err) => {
        if (err) {
          if (err.code === 'EACCES' || err.code === 'EPERM') {
            resolve(true); // 文件是只读的
          } else {
            reject(err); // 其他错误
          }
        } else {
          resolve(false); // 文件不是只读的
        }
      });
    });
  }


  /**
   * 生成项目层级树结构-铺平
   * @param arg
   * @return {Promise<Error>}
   */
  async generateLevelTreeNodeStructure(arg) {

    //获取项目结构树
    let projectObj = await PricingFileFindUtils.getProjectObjById(arg.sequenceNbr);
    return await this.generateLevelTreeNode(projectObj);

    // //获取项目结构树
    // let projectObj = await PricingFileFindUtils.getProjectObjById(arg.sequenceNbr);
    // // 查询工程项目，生成树节点
    // let constructTreeNode = new FileLevelTreeNode();
    // constructTreeNode.id = projectObj.sequenceNbr;
    // constructTreeNode.name = projectObj.constructName;
    // constructTreeNode.levelType = 1;
    //
    // if (2 == projectObj.biddingType) {
    //     //获取单位
    //     let unitProjects = projectObj.unitProjects;
    //     let unitTreeNodeList = new Array();
    //     for (let i in unitProjects) {
    //         let unitTreeNode = new FileLevelTreeNode();
    //         let unitProject = unitProjects[i];
    //         unitTreeNode.id = unitProject.sequenceNbr;
    //         unitTreeNode.name = unitProject.upName;
    //         unitTreeNode.levelType = 3;
    //         unitTreeNodeList.push(unitTreeNode);
    //     }
    //     constructTreeNode.children = unitTreeNodeList;
    // } else {
    //     //获取单项工程集合
    //     let singleProjects = projectObj.singleProjects;
    //     let singleTreeNodeList = new Array();
    //     for (let i in singleProjects) {
    //         //每一个单项数据
    //         let constructTreeNode = new FileLevelTreeNode();
    //         let singleProject = singleProjects[i];
    //         constructTreeNode.id = singleProject.sequenceNbr;
    //         constructTreeNode.name = singleProject.projectName;
    //         constructTreeNode.levelType = 2;
    //         singleTreeNodeList.push(constructTreeNode);
    //         //单位
    //         let unitProjects = singleProject.unitProjects;
    //         let unitTreeNodeList = new Array();
    //         for (let i in unitProjects) {
    //             let unitTreeNode = new FileLevelTreeNode();
    //             let unitProject = unitProjects[i];
    //             unitTreeNode.id = unitProject.sequenceNbr;
    //             unitTreeNode.name = unitProject.upName;
    //             unitTreeNode.levelType = 3;
    //             unitTreeNodeList.push(unitTreeNode);
    //         }
    //         constructTreeNode.children = unitTreeNodeList;
    //     }
    //     constructTreeNode.children = singleTreeNodeList;
    // }
    // return constructTreeNode;

  }


  async generateLevelTreeNode(projectObj) {
    let result = new Array();
    // 查询工程项目，生成树节点
    let constructTreeNode = new FileLevelTreeNode();
    constructTreeNode.id = projectObj.sequenceNbr;
    constructTreeNode.name = projectObj.constructName;
    constructTreeNode.levelType = 1;
    constructTreeNode.biddingType = projectObj.biddingType;
    constructTreeNode.mainRcjShowFlag = projectObj.mainRcjShowFlag;
    constructTreeNode.standardConversionShowFlag = projectObj.standardConversionShowFlag;
    constructTreeNode.optionLock = projectObj.optionLock;
    result.push(constructTreeNode);
    if (2 == projectObj.biddingType) {
      //获取单位
      let unitProject = projectObj.unitProject;
      if (!ObjectUtils.isEmpty(projectObj.unitProject)) {
        let unitTreeNode = new FileLevelTreeNode();
        unitTreeNode.id = unitProject.sequenceNbr;
        unitTreeNode.name = unitProject.upName;
        unitTreeNode.levelType = 3;
        unitTreeNode.constructMajorType = unitProject.constructMajorType;
        unitTreeNode.parentId = projectObj.sequenceNbr;
        unitTreeNode.constructMajorType = unitProject.constructMajorType;
        unitTreeNode.libraryCode = unitProject.mainDeLibrary;
        unitTreeNode.screenCondition = unitProject.screenCondition;
        unitTreeNode.lockPriceFlag = unitProject.lockPriceFlag;
        unitTreeNode.secondInstallationProjectName = unitProject.secondInstallationProjectName;
        unitTreeNode.deStandardId = unitProject.deStandardId;
        unitTreeNode.deStandardReleaseYear = unitProject.deStandardReleaseYear;
        result.push(unitTreeNode);
      }
    } else {
      //获取单项工程集合
      let singleProjects = projectObj.singleProjects;
      if (ObjectUtils.isEmpty(singleProjects)) {
        let unitProjectArray = projectObj.unitProjectArray;
        for (let i in unitProjectArray) {
          let unitTreeNode = new FileLevelTreeNode();
          let unitProject = unitProjectArray[i];
          unitTreeNode.id = unitProject.sequenceNbr;
          unitTreeNode.name = unitProject.upName;
          unitTreeNode.levelType = 3;
          unitTreeNode.parentId = projectObj.sequenceNbr;
          unitTreeNode.constructMajorType = unitProject.constructMajorType;
          unitTreeNode.libraryCode = unitProject.mainDeLibrary;
          unitTreeNode.secondInstallationProjectName = unitProject.secondInstallationProjectName;
          unitTreeNode.deStandardId = unitProject.deStandardId;
          unitTreeNode.deStandardReleaseYear = unitProject.deStandardReleaseYear;
          result.push(unitTreeNode);
        }
        return result;
      }
      for (let i in singleProjects) {
        let singleTreeArray = this._generateSingleLevelTreeNode(singleProjects[i], projectObj.sequenceNbr);
        result.push(...singleTreeArray);
      }
    }
    return result;
  }

  _generateSingleLevelTreeNode(singleProject, parentId) {
    let singleTreeArray = [];
    //每一个单项数据
    let singleNode = new FileLevelTreeNode();
    singleNode.id = singleProject.sequenceNbr;
    singleNode.name = singleProject.projectName;
    singleNode.levelType = 2;
    singleNode.parentId = parentId;
    singleTreeArray.push(singleNode);

    // 子单项处理
    let subSingleProjects = singleProject.subSingleProjects;
    if (!ObjectUtils.isEmpty(subSingleProjects)) {
      for (let i = 0; i < subSingleProjects.length; i++) {
        let subTree = this._generateSingleLevelTreeNode(subSingleProjects[i], singleNode.id);
        singleTreeArray.push(...subTree);
      }
    } else {
      //单位
      let unitProjects = singleProject.unitProjects;
      for (let i in unitProjects) {
        let unitTreeNode = new FileLevelTreeNode();
        let unitProject = unitProjects[i];
        unitTreeNode.id = unitProject.sequenceNbr;
        unitTreeNode.name = unitProject.upName;
        unitTreeNode.levelType = 3;
        unitTreeNode.constructMajorType = unitProject.constructMajorType;
        unitTreeNode.parentId = singleProject.sequenceNbr;
        unitTreeNode.constructMajorType = unitProject.constructMajorType;
        unitTreeNode.deStandardId = unitProject.deStandardId;
        unitTreeNode.deStandardReleaseYear = unitProject.deStandardReleaseYear;
        unitTreeNode.libraryCode = unitProject.mainDeLibrary;
        unitTreeNode.screenCondition = unitProject.screenCondition;
        unitTreeNode.lockPriceFlag = unitProject.lockPriceFlag;
        unitTreeNode.projectTaxCalculation = unitProject.projectTaxCalculation;
        unitTreeNode.secondInstallationProjectName = unitProject.secondInstallationProjectName;
        unitTreeNode.checkColorList = unitProject.checkColorList;
        unitTreeNode.colorList = unitProject.colorList;
        singleTreeArray.push(unitTreeNode);
      }
    }

    return singleTreeArray;
  }

  /***
   * 初始化一个分部分项工程
   * @param unitId
   * @return {Promise<void>}
   */
  initUnitFb(unitObj) {
    // 生成ID
    let tree = new Tree();
    //unitObj.itemBillProjects = tree;
    Object.defineProperty(unitObj, 'itemBillProjects', {
      value: tree,
      writable: true, // 禁止重新赋值
      configurable: false, // 可选，进一步防止属性被删除或修改描述符
      enumerable: true // 可选，决定该属性是否可枚举（如在`for...in`循环中出现）
    });
  }

  initUnitFbTop(unitObj, constructId, singleId, unitId) {
    // 生成ID
    let initTopData = new ItemBillProject();
    initTopData.sequenceNbr = Snowflake.nextId();
    initTopData.bdName = '单位工程';
    initTopData.name = '单位工程';
    initTopData.kind = '0';
    initTopData.parentId = '0';
    initTopData.unitId = unitObj.sequenceNbr;
    initTopData.sortNo = 1;
    initTopData.displaySign = 1;
    initTopData.displayStatu = BranchProjectDisplayConstant.displayMax;
    let insertStrategy = new InsertStrategy({ constructId, singleId, unitId, pageType: 'fbfx' });
    insertStrategy.execute({ pointLine: null, newLine: initTopData, option: 'save' });
  }

  /***
   * 初始化单项工程
   * @param unitId
   * @return {Promise<void>}
   */
  initSingle(obj) {
    let singleProject = new SingleProject();
    singleProject.projectName = '单项工程';
    singleProject.sequenceNbr = Snowflake.nextId();
    singleProject.constructId = obj.sequenceNbr;
    let array = new Array();
    array.push(singleProject);
    obj.singleProjects = array;
  }


  /**
   * 新增单位重复名称判断
   * @returns {Promise<string>}
   */
  async repeatNameJudge(projectObj,singleId,unitId,upName){
    if(projectObj.biddingType == 2){
      return upName;
    }else {
      if(projectObj.sequenceNbr == singleId || ObjectUtils.isEmpty(singleId)){
        //说明是工程项目下直接插入单位
        if(ObjectUtils.isNotEmpty(projectObj.unitProjectArray)){
          if(projectObj.unitProjectArray.map(i=>i.upName).includes(upName)){
            //当前层级有相同名字
            return  this.findUniqueName(upName, projectObj.unitProjectArray.map(i=>i.upName))

          }
        }

      }else {
        let  unitProjects=await PricingFileFindUtils.getOneFromSingleProjects(projectObj.singleProjects, singleId).unitProjects
        if(ObjectUtils.isNotEmpty(unitProjects)){
          if(unitProjects.map(i=>i.upName).includes(upName)){
            //当前层级有相同名字
            return  this.findUniqueName(upName, unitProjects.map(i=>i.upName))

          }
        }

      }

    }
    return upName
  }


  /**
   * 生成新的名字
   * @param upName
   * @param arr
   * @returns {string}
   */
  findUniqueName(upName, arr) {
    let index = 1; // 用于生成后缀的数字
    let baseName = upName; // 基础名字，没有后缀
    let newName = upName; // 当前检查的名字，初始为baseName

    // 循环直到找到一个不在数组中的名字
    while (arr.includes(newName)) {
      newName = `${baseName}_${index}`; // 如果当前名字已存在，添加后缀并增加index
      index++; // 增加后缀数字
    }

    return newName; // 返回找到的唯一名字
  }


  /***
   * 初始化单位工程
   * @param unitId
   * @return {Promise<void>}
   */
  async initUnit(obj, singleId, unitId, upName, constructMajorType, oldUnitId, flag, libraryCode, secondInstallationProjectName, deStandardId) {
    //单位工程
    let unitProject = new UnitProject();
    unitProject.sequenceNbr = unitId;
    unitProject.upName = await this.repeatNameJudge(obj,singleId,unitId,upName);
    unitProject.constructId = obj.sequenceNbr;
    unitProject.constructMajorType = constructMajorType;
    unitProject.spId = singleId;
    unitProject.mainDeLibrary = libraryCode;
    unitProject.secondInstallationProjectName = secondInstallationProjectName;
    unitProject.constructProjectRcjs = [];
    unitProject.rcjDetailList = [];

    //设置定额标准发布年份
    let deStandard = await this.service.baseListDeStandardService.quotaStandardById(deStandardId);
    unitProject.deStandardReleaseYear = deStandard.releaseYear;
    unitProject.deStandardId = deStandardId;
    if (unitProject.deStandardReleaseYear == ConstantUtil.DE_STANDARD_12) {
      unitProject.rgfInMeasureAndRPriceInMechanicalAction = true;
    }

    //人工费ID
    unitProject.rgfId = obj.rgfId;

    if (obj.biddingType != 2) {
      if (ObjectUtils.isEmpty(obj.singleProjects)) {
        if (ObjectUtils.isEmpty(obj.unitProjectArray)) {
          let array = new Array();
          array.push(unitProject);
          obj.unitProjectArray = array;
        } else {
          if (ObjectUtils.isEmpty(oldUnitId)) {
            if (flag) {
              obj.unitProjectArray.push(unitProject);
            } else {
              obj.unitProjectArray.unshift(unitProject);
            }
          } else {
            const index = obj.unitProjectArray.findIndex(element => element.sequenceNbr == oldUnitId);
            obj.unitProjectArray.splice(index + 1, 0, unitProject);
          }
        }
      } else {
        let singleProject = PricingFileFindUtils.getSingleProject(obj.sequenceNbr, singleId);
        if (ObjectUtils.isEmpty(singleProject.unitProjects)) {
          let array = new Array();
          array.push(unitProject);
          singleProject.unitProjects = array;
        } else {
          if (ObjectUtils.isEmpty(oldUnitId)) {
            if (flag) {
              singleProject.unitProjects.push(unitProject);
            } else {
              singleProject.unitProjects.unshift(unitProject);
            }
          } else {
            const index = singleProject.unitProjects.findIndex(element => element.sequenceNbr == oldUnitId);
            singleProject.unitProjects.splice(index + 1, 0, unitProject);
          }
        }
      }
    } else {
      obj.unitProject = unitProject;
    }


    //创建单位级别的工程基本信息和工程特征
    await this.initProjectOrUnitData(unitProject, 3);
    //初始化编制说明
    await this.initProjectOrUnitBZSM(3, unitProject);
    //初始分部分项
    await this.initUnitFb(unitProject);

    //初始化取费文件
    let { baseFeeFileService, projectTaxCalculationService } = this.service;


    //初始化单位的计税方式
    await projectTaxCalculationService.initUnitTaxCalculationMethod(obj, unitProject);

    unitProject = await baseFeeFileService.initFeeFile(unitProject);


    //初始化费用代码
    await this.initUnitCostCodePrice(unitProject);
    //初始化费用汇总
    await this.initUnitCostSummary(unitProject);
    //初始化其他项目
    //this.initOtherProject(unitProject);
    //初始化计日工
    //this.initOtherProjectDayWorks(unitProject);
    //初始化总承包服务费
    //this.initOtherProjectServiceCost(unitProject);

    //初始化其他项目所有部分(模拟数据 使用真实数据之后删除)
    await this.initUnitOtherProjectList(unitProject);
    this.initUnitFbTop(unitProject, obj.sequenceNbr, singleId, unitId);
    // 初始化措施项目
    await this.service.stepItemCostService.initItemCost(obj.sequenceNbr, singleId, unitId);

    //初始化取费计算基数
    await this.initFeeCsfyCalculateBase(unitProject);

    //初始化 主要材料 设置
    await this.initMainMaterialSetting(unitProject);

    return unitProject;
  }

  /**
   * 初始化取费计算基数、自动计算措施费用计算基数
   */
  async initFeeCsfyCalculateBase(unitProject) {

    let is22Unit = PricingFileFindUtils.is22Unit(unitProject);
    let feeCalculateBaseList = CalculateBaseUnitInitEnum.fee;
    if (is22Unit) {
      feeCalculateBaseList = feeCalculateBaseList.filter(k => k.type != 'gf');
    }
    unitProject.feeCalculateBaseList = feeCalculateBaseList;
    if (ObjectUtils.isEmpty(unitProject.zjcsCostMathCache)) {
      let zjcsCostMathCache = {};
      zjcsCostMathCache.csfyCalculateBaseCode = CalculateBaseType.calculateBaseInitValue;
      zjcsCostMathCache.csfyCalculateBaseArea = CalculateBaseType.calculateBaseAreaOther;
      unitProject.zjcsCostMathCache = zjcsCostMathCache;
    }
  }

  /**
   * 主要材料 设置
   */
  async initMainMaterialSetting(unit) {

    let mainMaterialSetting = new MainMaterialSetting();
    //设置方式  0 自动设置
    mainMaterialSetting.type = 0;

    //自动设置 方式
    mainMaterialSetting.pattern = 1;

    //数量
    mainMaterialSetting.proportion = 50;

    unit.mainMaterialSetting = mainMaterialSetting;

  }

  /**
   * 初始化项目的编制说明
   * @param obj 工程或者单位对象
   * @param levelType 1 工程项目 2 单项工程 3 单位工程
   * @param sequenceNbr 所传对象对应的
   */
  initProjectOrUnitBZSM(levelType, obj) {
    let organizationInstructions = new OrganizationInstructions();
    organizationInstructions.sequenceNbr = Snowflake.nextId();
    if (levelType == 1) {
      organizationInstructions.constructId = obj.sequenceNbr;
    }
    if (levelType == 3) {
      organizationInstructions.constructId = obj.constructId;
      organizationInstructions.unitId = obj.sequenceNbr;
    }
    obj.organizationInstructions = organizationInstructions;
  }

  /**
   * 初始化项目或者单位的工程基本信息和工程特征
   * @param obj 工程或者单位对象
   * @param LeveType 1 工程项目 2 单项工程 3 单位工程
   */
  initProjectOrUnitData(obj, leveType, map) {
    let array = new Array();

    //工程项目
    if (leveType == 1) {
      for (let i in gcjbxx) {
        gcjbxx[i].type = 0;
        gcjbxx[i].addFlag = 0;
        gcjbxx[i].lockFlag = 0;
        gcjbxx[i].requiredFlag = 0;
        let listProjectOverviewXX = new ProjectOverview();
        ConvertUtil.setDstBySrc(gcjbxx[i], listProjectOverviewXX);
        if ('工程名称' === gcjbxx[i].name) {
          listProjectOverviewXX.remark = obj.constructName;
        }
        //设置基本信息必填项
        if ('招标人(发包人)' === gcjbxx[i].name || '招标人(发包人)法人或其授权人' === gcjbxx[i].name) {
          listProjectOverviewXX.requiredFlag = 1;
        }
        listProjectOverviewXX.sequenceNbr = Snowflake.nextId();
        array.push(listProjectOverviewXX);
      }
      obj.constructProjectJBXX = array;
      return;
    }
    //单位
    if (leveType == 3) {
      //工程基本信息
      let arrayXx = new Array();
      for (let i in dwjbxx) {
        dwjbxx[i].addFlag = 0;
        dwjbxx[i].type = 0;
        dwjbxx[i].lockFlag = 0;
        let listProjectOverviewXX = new ProjectOverview();
        ConvertUtil.setDstBySrc(dwjbxx[i], listProjectOverviewXX);
        listProjectOverviewXX.sequenceNbr = Snowflake.nextId();
        if ('单位工程名称' === dwjbxx[i].name) {
          listProjectOverviewXX.remark = obj.upName;
        }
        if ('工程专业' === dwjbxx[i].name) {
          listProjectOverviewXX.remark = obj.constructMajorType;
        }
        arrayXx.push(listProjectOverviewXX);
      }
      obj.unitJBXX = arrayXx;
      //工程特征
      let array = new Array();
      for (let i in gctz) {
        gctz[i].type = 1;
        gctz[i].addFlag = 0;
        gctz[i].lockFlag = 0;
        let listProjectOverviewTz = new ProjectOverview();
        ConvertUtil.setDstBySrc(gctz[i], listProjectOverviewTz);
        if (!ObjectUtils.isEmpty(map)) {
          listProjectOverviewTz.context = map.get(listProjectOverviewTz.name);
        }
        listProjectOverviewTz.sequenceNbr = Snowflake.nextId();
        array.push(listProjectOverviewTz);
      }
      obj.unitGCTZ = array;
      return;
    }
  }

  /**
   * 初始化费用代码
   * @param unitObj
   */
  initUnitCostCodePrice(unitObj) {
    let unitCostCodePrices = this.service.unitCostCodePriceService.getDefaultUnitCostCodePrice(unitObj);
    unitObj.unitCostCodePrices = unitCostCodePrices;
    //this.service.unitCostCodePriceService.countCostCodePrice(unitObj.constructId,unitObj.singleId,unitObj.sequenceNbr);
  }

  /**
   * 初始化费用汇总
   * @param unitObj
   */
  initUnitCostSummary(unitObj) {
    this.service.unitCostSummaryService.getDefaultUnitCostSummary(unitObj);
  }

  /**
   * 初始化其他项目
   */
  initOtherProject(unitObj) {
    let { otherProjectService } = this.service;
    let otherProjects = otherProjectService.getDefaultOtherProject();
    unitObj.otherProjects = otherProjects;
  }

  /**
   * 初始化计日工
   * @param unitObj
   */
  initOtherProjectDayWorks(unitObj) {
    let { otherProjectDayWorkService } = this.service;
    let otherProjectDayWorks = otherProjectDayWorkService.getDefaultOtherProjectDayWork();
    unitObj.otherProjectDayWorks = otherProjectDayWorks;
  }

  /**
   * 初始化总承包服务费
   */
  initOtherProjectServiceCost(unitObj) {
    let { otherProjectServiceCostService } = this.service;
    let otherProjectServiceCosts = otherProjectServiceCostService.getDefaultOtherProjectServiceCost();
    unitObj.otherProjectServiceCosts = otherProjectServiceCosts;
  }

  initUnitOtherProjectList(unitObj) {
    //初始化其他项目
    let {otherProjectService} = this.service;
    let otherProjectList = otherProjectService.getInitOtherProjectList(unitObj);
    unitObj.otherProjects = otherProjectList;

    //其他项目 暂列金额初始化
    let otherProjectZljeList = otherProjectService.getInitOtherProjectZljeList(unitObj);
    unitObj.otherProjectProvisionals = otherProjectZljeList;

    //其他项目 材料暂估价列表
    /*let otherProjectClzgjList = otherProjectService.getInitOtherProjectClzgjList(unitObj);
    unitObj.otherProjectClZgjs = otherProjectClzgjList;*/

    //其他项目 设备暂估价列表
    /*let otherProjectSbzgjList = otherProjectService.getInitOtherProjectSbzgjList(unitObj);
    unitObj.otherProjectSbZgjs = otherProjectSbzgjList;*/

    //其他项目 专业工程暂估价列表
    let otherProjectZygcZgjList = otherProjectService.getInitOtherProjectZygcZgjList(unitObj);
    unitObj.otherProjectZygcZgjs = otherProjectZygcZgjList;

    //其他项目 总承包服务费列表
    let otherProjectZcbfwfList = otherProjectService.getInitOtherProjectZcbfwfList(unitObj);
    unitObj.otherProjectServiceCosts = otherProjectZcbfwfList;

    //其他项目 记日工列表
    let otherProjectJrgList = otherProjectService.getInitOtherProjectJrgList(unitObj);
    unitObj.otherProjectDayWorks = otherProjectJrgList;

    //其他项目 签证与索赔计价表
    let otherProjectQzSpJjbList = otherProjectService.getInitOtherProjectQzSpJjb(unitObj);
    unitObj.otherProjectQzAndSuoPeis = otherProjectQzSpJjbList;

    //其他项目 主要材料设备列表
    let otherProjectZyclSbList = otherProjectService.getInitOtherProjectZyclSbList(unitObj);
    unitObj.OtherProjectZyclSbs = otherProjectZyclSbList;

    //其他项目 甲供材料设备列表
    let otherProjectJgclSbList = otherProjectService.getInitOtherProjectJgclSbList(unitObj);
    unitObj.OtherProjectJgclSbs = otherProjectJgclSbList;

  }


  initUnitCsxmCoefficientAndRcjQty(unitObj) {
        //获取所有的措施项目数据
    let measureProjects = unitObj.measureProjectTables.filter(qd=>qd.kind==BranchProjectLevelConstant.qd);
    for(const qd of measureProjects){
      qd.adjustmentCoefficient = 1;
    }
    //人材机数据计算
    let constructProjectRcjs = unitObj.constructProjectRcjs;
    for(const rcj of constructProjectRcjs){
      rcj.confficientInitQty=rcj.resQty;
    }
  }


  async initUnitRcjCache(unitObj) {
    //人材机数据计算
    let constructProjectRcjs = unitObj.constructProjectRcjs;
    for(const rcj of constructProjectRcjs){
      rcj.confficientInitQty=rcj.resQty;
      UnitRcjCacheUtil.add(unitObj, rcj, rcj.deId);
    }
  }



  // 修改项目工程名称
  updateConstructProject(arg) {
    let constructId = arg.constructId;
    let constructName = arg.constructName;
    let projectObj = PricingFileFindUtils.getProjectObjById(constructId);

    if (ObjectUtils.is_Undefined(projectObj) || ObjectUtils.isNull(projectObj)) {
      return ResponseData.fail('修改失败');
    }
    let find = projectObj.constructProjectJBXX.find(k => k.name === '工程名称');
    projectObj.constructName = constructName;
    find.remark = constructName;
    return ResponseData.success();
  }


  dataHandler(arg, array) {
    let children = arg.children;
    if (ObjectUtils.is_Undefined(children) || ObjectUtils.isNull(children)) {
      return;
    }
    for (let i in children) {
      let child = children[i];
      array.push(child);
      this.dataHandler(child, array);
    }
  }

  /**
   * 判断解析XML后 单价、安文费、总价 清单是否有值，如果有值替换为xml中的数据
   * @param unit
   */
  async mergeMeasureProjectTables(unit) {
    let measureProjectTables = treeToArray(unit.measureProjectTables);//措施项目对象
    let djcs = measureProjectTables.filter(item => item.name.includes("单价措施项目"));
    if (ObjectUtils.isNotEmpty(djcs)) {
        djcs[0]["fxCode"] = "2";//为同广联达保持一致,给默认值2
    }
    let djMeasureProjectTableArray = unit.djMeasureProjectTableArray;//单价措施分布清单对象
    if (!ObjectUtils.isEmpty(djMeasureProjectTableArray)) {
      //判断单价措施下是否有子分部
      let filter = djMeasureProjectTableArray.filter(item => item.kind === BranchProjectLevelConstant.zfb);

      let djSequenceNbr;//单价措施标题主键
      let djIndex;//单价措施标题下标
      for (let k = 0; k < measureProjectTables.length; k++) {
        if (measureProjectTables[k].constructionMeasureType === 1) {
          djIndex = k;
          djSequenceNbr = measureProjectTables[k].sequenceNbr;
        }
      }

      if (ObjectUtils.isEmpty(filter)) {
        //没有子分部所有清单直接父id改为单价措施分布ID
        for (let k = 0; k < djMeasureProjectTableArray.length; k++) {
          djMeasureProjectTableArray[k].parentId = djSequenceNbr;
        }
      } else {
        let sequenceNbr = djSequenceNbr;
        for (let k = 0; k < djMeasureProjectTableArray.length; k++) {
          if (djMeasureProjectTableArray[k].kind == BranchProjectLevelConstant.zfb) {
            djMeasureProjectTableArray[k].parentId = djSequenceNbr;
            sequenceNbr = djMeasureProjectTableArray[k].sequenceNbr;
          } else {
            djMeasureProjectTableArray[k].parentId = sequenceNbr;
          }
        }
      }
      //第一个参数2表示要删除和插入元素的起始位置，第二个参数1表示要删除的元素数量，接下来的参数...djMeasureProjectTableArray表示要插入的元素
      measureProjectTables.splice(djIndex + 1, measureProjectTables.length, ...djMeasureProjectTableArray);
    }
    //判断解析后的xml中是否有安文费清单
    let awfMeasureProjectTableArray = unit.awfMeasureProjectTableArray;
    if (!ObjectUtils.isEmpty(awfMeasureProjectTableArray)) {
      let awfIndex;//安文费标题下标
      let awfSequenceNbr;//安文费标题主键
      let zjIndex;//总价措施标题下标
      for (let k = 0; k < measureProjectTables.length; k++) {
        if (measureProjectTables[k].constructionMeasureType === 2) {
          awfIndex = k;
          awfSequenceNbr = measureProjectTables[k].sequenceNbr;
        }
        if (measureProjectTables[k].constructionMeasureType === 3) {
          zjIndex = k;
        }
      }
      //安文费清单设置parentId
      for (let k = 0; k < awfMeasureProjectTableArray.length; k++) {
        awfMeasureProjectTableArray[k].parentId = awfSequenceNbr;
      }
      measureProjectTables.splice(awfIndex + 1, (zjIndex - awfIndex - 1), ...awfMeasureProjectTableArray);
    }
    //判断解析后的xml中是否有其他总价措施清单
    let zjMeasureProjectTableArray = unit.zjMeasureProjectTableArray;
    if (!ObjectUtils.isEmpty(zjMeasureProjectTableArray)) {
      let zjSequenceNbr;//总价措施标题主键
      let zjIndex;//总价措施标题下标
      let djSequenceNbr;//单价措施标题主键
      let djIndex;//单价措施标题下标
      for (let k = 0; k < measureProjectTables.length; k++) {
        if (measureProjectTables[k].constructionMeasureType === 3) {
          zjIndex = k;
          zjSequenceNbr = measureProjectTables[k].sequenceNbr;
          measureProjectTables[k].displaySign = BranchProjectDisplayConstant.open;
        }
        if (measureProjectTables[k].constructionMeasureType === 1) {
          djIndex = k;
          djSequenceNbr = measureProjectTables[k].sequenceNbr;
        }
      }
      //总价措施清单设置parentId
      for (let k = 0; k < zjMeasureProjectTableArray.length; k++) {
        zjMeasureProjectTableArray[k].parentId = zjSequenceNbr;
      }
      let length = measureProjectTables.length;
      measureProjectTables.splice(zjIndex + 1, (djIndex - zjIndex - 1), ...zjMeasureProjectTableArray);

    }
    unit.measureProjectTables = arrayToTree(measureProjectTables);
  }


  /**
   * 设置单位工程默认工程专业
   * @param constructId
   * @param projectStructTree
   */
  setUnitProjectDefaultMajorType(constructId, projectStructTree){
    if(ObjectUtils.isNotEmpty(projectStructTree)){
      let constructDeStandard = PricingFileFindUtils.getConstructDeStandard(constructId);

      // const majorTypeObject = ConstructProjectService.majorTypeObject;

      const nodeMap = new Map();
      for(let node of projectStructTree){
        nodeMap.set(node.id,node);
      }


      for(let node of projectStructTree){
        if(node.levelType != ConstantUtil.UNIT_LEVEL_TYPE){
          continue;
        }
        let unitId = node.id;
        let singleId = null;
        let parent = nodeMap.get(node.parentId);
        if(parent.levelType == ConstantUtil.SINGLE_LEVEL_TYPE){
          singleId = parent.id;
        }

        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        // let qdStartCodeMap = new Map();
        let zjAndAwfQd = [];
        if(ObjectUtils.isNotEmpty(unitProject.zjMeasureProjectTableArray)){
          zjAndAwfQd.push(...unitProject.zjMeasureProjectTableArray);
        }

        if(ObjectUtils.isNotEmpty(unitProject.awfMeasureProjectTableArray)){
          zjAndAwfQd.push(...unitProject.awfMeasureProjectTableArray);
        }


        node.defaultMajorType = this.getUnitDefaultMajorType(zjAndAwfQd, constructDeStandard);
        if(ObjectUtils.isNotEmpty(node.defaultMajorType)){
          let mainLib = this.service.unitProjectService.getMainDeLibrary(node.defaultMajorType, constructDeStandard == ConstantUtil.DE_STANDARD_22);
          node.libraryCode = ObjectUtils.isNotEmpty(mainLib) ? mainLib[0].libraryCode : undefined;
        }


        // for(let qd of zjAndAwfQd){
        //   if(qd.kind != ConstantUtil.QD_KIND){
        //     continue;
        //   }
        //   let code = qd.fxCode || qd.bdCode;
        //   qdStartCodeMap.set(code.substring(0,2), code.substring(0,4));
        // }
        //
        // if(qdStartCodeMap.size == 1){
        //   let twoStartCode = qdStartCodeMap.keys().next().value;
        //   // let majorCode = twoStartCode == "01" ? qdStartCodeMap.get(twoStartCode) : twoStartCode;
        //   let majorCode = twoStartCode
        //   node.defaultMajorType = majorTypeObject[constructDeStandard][majorCode];
        // }
      }
    }
  }

  getUnitDefaultMajorType(zjcjQds = [], constructDeStandard){
    const majorTypeObject = ConstructProjectService.majorTypeObject;
    let awfQd = zjcjQds.find(qd => qd.kind == ConstantUtil.QD_KIND && qd.name == '安全生产、文明施工费');

    // 如果存在安文费清单，以安文费为准
    if(ObjectUtils.isNotEmpty(awfQd)){
      let code = awfQd.fxCode || awfQd.bdCode
      return majorTypeObject[constructDeStandard][code.substring(0,2)]
    }else{
      let qdStartCodeMap = new Map();

      for(let qd of zjcjQds){
        if(qd.kind != ConstantUtil.QD_KIND){
          continue;
        }
        let code = qd.fxCode || qd.bdCode;

        let startTwoCode = code.substring(0,2)
        let cnt = qdStartCodeMap.get(startTwoCode) || 0;
        cnt++;
        qdStartCodeMap.set(startTwoCode, cnt);
      }

      if(qdStartCodeMap.size == 1){
        let twoStartCode = qdStartCodeMap.keys().next().value;
        let majorCode = twoStartCode
        return majorTypeObject[constructDeStandard][majorCode];
      }else if(qdStartCodeMap.size > 1){
        let codeCntArr = [];
        for (const [key, value] of qdStartCodeMap.entries()) {
          codeCntArr.push({twoStartCode: key, cnt: value});
        }

        // 降序排序
        codeCntArr.sort((a, b) => {
          return b.cnt - a.cnt;
        });

        // 以出现次数最多的编号为主，如果最高次数编码不唯一，返回null
        if(codeCntArr[0].cnt > codeCntArr[1].cnt){
          return majorTypeObject[constructDeStandard][codeCntArr[0].twoStartCode];
        }else{
          return null;
        }

      }else{
        return null;
      }
    }
  }

  getUnitDefaultMajorTypeBak(zjcjQds, constructDeStandard){
    let qdStartCodeMap = new Map();
    const majorTypeObject = ConstructProjectService.majorTypeObject;
    for(let qd of zjcjQds){
      if(qd.kind != ConstantUtil.QD_KIND){
        continue;
      }
      let code = qd.fxCode || qd.bdCode;
      qdStartCodeMap.set(code.substring(0,2), code.substring(0,4));
    }

    if(qdStartCodeMap.size == 1){
      let twoStartCode = qdStartCodeMap.keys().next().value;
      // let majorCode = twoStartCode == "01" ? qdStartCodeMap.get(twoStartCode) : twoStartCode;
      let majorCode = twoStartCode
      return majorTypeObject[constructDeStandard][majorCode];
    }
    return null;
  }

  async _reInitUnitBySingle(constructObj, single) {
    if (ObjectUtils.isNotEmpty(single.subSingleProjects)) {
      for (let i = 0; i < single.subSingleProjects.length; i++) {
        await this._reInitUnitBySingle(constructObj, single.subSingleProjects[i]);
      }
    } else if (ObjectUtils.isNotEmpty(single.unitProjects)) {


      let unitProjects = single.unitProjects;
      for (let j = 0; j < unitProjects.length; j++) {


        await this.reInitUnit(unitProjects[j], constructObj, single);

        // let unitProject = unitProjects[j];
        // //初始化措施项目基础数据
        // await this.service.stepItemCostService.initItemCost(constructObj.sequenceNbr, single.sequenceNbr, unitProject.sequenceNbr);
        //
        // //let unit = PricingFileFindUtils.getUnit(constructObj.sequenceNbr,single.sequenceNbr,unitProject.sequenceNbr);
        // //将单价措施 子分部和清单和默认数据合并
        // await this.mergeMeasureProjectTables(unitProject);
        //
        // //初始化单位的计税方式
        // await this.service.projectTaxCalculationService.initUnitTaxCalculationMethod(constructObj, unitProject);
        // //初始化费用代码
        // await this.initUnitCostCodePrice(unitProject);
        // //初始化费用汇总
        // await this.initUnitCostSummary(unitProject);
        // await this.service.management.afterImport("importData", [constructObj.sequenceNbr, unitProject.spId, unitProject.sequenceNbr]);
      }
    }

  }

  async reInitUnit(unitProject, constructObj) {
    //初始化措施项目基础数据
    await this.service.stepItemCostService.initItemCost(constructObj.sequenceNbr, unitProject.spId, unitProject.sequenceNbr);

    //let unit = PricingFileFindUtils.getUnit(constructObj.sequenceNbr,single.sequenceNbr,unitProject.sequenceNbr);
    //将单价措施 子分部和清单和默认数据合并
    await this.mergeMeasureProjectTables(unitProject);

    //初始化单位的计税方式
    await this.service.projectTaxCalculationService.initUnitTaxCalculationMethod(constructObj, unitProject);
    //初始化费用代码
    await this.initUnitCostCodePrice(unitProject);
    //初始化费用汇总
    await this.initUnitCostSummary(unitProject);
    await this.service.management.afterImport('importData', [constructObj.sequenceNbr, unitProject.spId, unitProject.sequenceNbr]);
  }


  //导入之后 编辑项目接口包含 根据项目初始化 措施项目 取费表 造价分析 费用汇总数据
  async editProjectStructureForImport(arg) {
    await this.editProjectLevelStructure(arg);
    //根据项目初始化 措施项目 取费表 造价分析 费用汇总数据
    let projectObjById = PricingFileFindUtils.getProjectObjById(arg.id);

    let singleProjects = projectObjById.singleProjects;

    //由于该行代码导致  导入数据之后清单特征内容重复  暂时注释掉 TODO
    // for (let i = 0; i < singleProjects.length; i++) {
    //   let singleProject = singleProjects[i];
    //   await this._reInitUnitBySingle(projectObjById, singleProject);
    // }
    //更新费用汇总
    await this.service.constructProjectService.countCostCodePriceAll(projectObjById);


    //获取线上项目
    //let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(projectObjById.constructName);
    let defaultStoragePath = await this.service.commonService.getSetStoragePath(projectObjById.constructName);
    let {extensions, dialogOptions} = await this.service.constructProjectService.defaultDialog(projectObjById.biddingType, defaultStoragePath);
    let result = dialog.showSaveDialogSync(null, dialogOptions);
    if (!ObjectUtils.isEmpty(result)) {
      let filePath = result;
      //查询是否覆盖路径
      this.isOverlayFile(filePath);
      // 在这里处理保存文件的操作
      if (!filePath.toUpperCase().endsWith(extensions[0])) {
        filePath += '.'+extensions[0];
      }
      projectObjById.path = filePath;
      //PricingFileWriteUtils.creatYsfFile(projectObjById);
      await this.service.ysfHandlerService.creatYsfFile(projectObjById);
      await this.service.systemService.openWindowForProject(filePath);
      return true;
    } else {
      //删除内存
      return false;

    }
  }

  async _editUnitLevelStructure(constructParam, singleId, unitParam, oldConstructFlatMap, parent, ifParentIsConstruct, constructObj) {

    let oldUnit = oldConstructFlatMap.get(unitParam.id);
    let newUnit = oldUnit;

    let constructId = constructParam.id;

    // let constructObj = PricingFileFindUtils.getProjectObjById(constructId);

    if (ObjectUtils.isEmpty(newUnit)) {// 单位不存在
      //let unitIs2022 = PricingFileFindUtils.is22De(constructId);
      let deStandard = await this.service.baseListDeStandardService.quotaStandardById(unitParam.deStandardId);
      let unitIs2022 = (deStandard.releaseYear == ConstantUtil.DE_STANDARD_22);

      let libraryCode = unitParam.libraryCode;
      if (!libraryCode && unitParam.constructMajorType) {
        libraryCode = this.service.unitProjectService.getMainDeLibrary(unitParam.constructMajorType, unitIs2022).filter(f => f.defaultDeFlag === 1)[0].libraryCode;
      }
      let secondInstallationProjectName = constructParam.secondInstallationProjectName ? constructParam.secondInstallationProjectName : unitParam.secondInstallationProjectName;
      newUnit = await this.initUnit(constructObj, unitParam.parentId, unitParam.id, unitParam.name, unitParam.constructMajorType, null, true, libraryCode, secondInstallationProjectName, unitParam.deStandardId);
    } else { // 单位存在

      let isConstruct22 = PricingFileFindUtils.is22De(constructId);
      let oldDeStandardId = newUnit.deStandardId;
      let oldConstructMajorType = newUnit.constructMajorType;


      if (ObjectUtils.isNotEmpty(unitParam.constructMajorType)) {
        unitParam.deStandardId = unitParam.deStandardId || constructObj.deStandardId;
      }

      newUnit.secondInstallationProjectName = unitParam.secondInstallationProjectName;
      newUnit.constructId = constructId;
      newUnit.spId = singleId;

      ConstructOperationUtil.updateUnitName(newUnit, unitParam.name);
      ConstructOperationUtil.updateUnitMajorType(newUnit, unitParam.constructMajorType);

      if (oldDeStandardId != unitParam.deStandardId) {
        //设置定额标准发布年份
        let deStandard = await this.service.baseListDeStandardService.quotaStandardById(unitParam.deStandardId);
        newUnit.deStandardReleaseYear = deStandard.releaseYear;
        newUnit.deStandardId = unitParam.deStandardId;
      }
      //凡新让这样改的
      if (oldConstructMajorType != unitParam.constructMajorType || !unitParam.constructMajorType) {
        let unitIs2022 = PricingFileFindUtils.is22Unit(newUnit);
        let libraryCode = newUnit.libraryCode || unitParam.libraryCode;
        if (!libraryCode && unitParam.constructMajorType) {
          libraryCode = this.service.unitProjectService.getMainDeLibrary(unitParam.constructMajorType, unitIs2022).filter(f => f.defaultDeFlag === 1)[0].libraryCode;
        }
        newUnit.mainDeLibrary = libraryCode;

        //初始化计税方式
        await this.service.projectTaxCalculationService.initUnitTaxCalculationMethod(constructObj, newUnit);
        //保存取费文件
        newUnit = await this.service.baseFeeFileService.initFeeFile(newUnit);


        await this.initFeeCsfyCalculateBase(newUnit);
        await this.reInitUnit(newUnit, constructObj);
      }


      /*if(newUnit.deStandardId != unitParam.deStandardId){
          //设置定额标准发布年份
          let deStandard = await this.service.baseListDeStandardService.quotaStandardById(unitParam.deStandardId);
          newUnit.deStandardReleaseYear = deStandard.releaseYear;
          newUnit.deStandardId = unitParam.deStandardId;
      }else{
          //这里默认12, 12没有下拉框选择,直接默认12
          newUnit.deStandardReleaseYear = '12';
          newUnit.deStandardId = '1657988044369526785';
      }
*/
      // //设置定额标准发布年份
      // let deStandard = await this.service.baseListDeStandardService.quotaStandardById(unitParam.deStandardId);
      // newUnit.deStandardReleaseYear = deStandard.releaseYear;
      // newUnit.deStandardId = unitParam.deStandardId;
      //
      // //初始化取费计算基数
      // await this.initFeeCsfyCalculateBase(newUnit);
      //
      // let unitIs2022 = PricingFileFindUtils.is22Unit(newUnit);
      //
      //
      // //导入项目之后再设置 专业 BUG 进行增加的代码
      // if (ObjectUtils.isNotEmpty(unitParam.constructMajorType)){
      //
      //     await this.reInitUnit(newUnit, constructObj);
      // }
      //
      //
      // newUnit.upName = unitParam.name;
      // newUnit.constructMajorType = unitParam.constructMajorType;
      // newUnit.secondInstallationProjectName = unitParam.secondInstallationProjectName;
      // newUnit.constructId = constructId;
      // newUnit.spId = singleId;
      //
      //
      // ConstructOperationUtil.updateUnitName(newUnit, unitParam.name);
      // ConstructOperationUtil.updateUnitMajorType(newUnit, unitParam.constructMajorType);
      //
      // let oldConstructMajorType = newUnit.constructMajorType;
      //
      // if (ObjectUtils.isNotEmpty(unitParam.constructMajorType)) {
      //     let libraryCode = newUnit.libraryCode || unitParam.libraryCode;
      //     if (!libraryCode && unitParam.constructMajorType) {
      //         libraryCode = this.service.unitProjectService.getMainDeLibrary(unitParam.constructMajorType, unitIs2022).filter(f => f.defaultDeFlag === 1)[0].libraryCode
      //     }
      //     newUnit.mainDeLibrary = libraryCode;
      //
      //     if (oldConstructMajorType != unitParam.constructMajorType) {
      //         //初始化计税方式
      //         await this.service.projectTaxCalculationService.initUnitTaxCalculationMethod(constructObj, newUnit);
      //         //保存取费文件
      //         newUnit = await this.service.baseFeeFileService.initFeeFile(newUnit);
      //         // let sql = "select default_qf_code as code from base_speciality_de_fee_relation where unit_project_name = ? and library_code = ?";
      //         // let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).get(oldUnit.constructMajorType, libraryCode);
      //         // if (sqlRes) {
      //         //     let mainFile = await this.service.baseFeeFileService.updateFeeFile(oldUnit, sqlRes.code);
      //         //     mainFile.defaultFeeFlag = 1;
      //         // } else {
      //         //     oldUnit.feeFiles[0].defaultFeeFlag = 1;
      //         // }
      //     }
      // }

    }

    // 将处理后的unit加入父级中
    if (ifParentIsConstruct) {
      constructObj.unitProjectArrayBak.push(newUnit);
    } else {
      parent.unitProjectsBak.push(newUnit);
    }

  }

  async _editUnitStructure(constructParam, singleId, unitParam, oldConstructFlatMap, parent, ifParentIsConstruct, constructObj) {

    let oldUnit = oldConstructFlatMap.get(unitParam.id);
    let newUnit = oldUnit;

    let constructId = constructParam.id;

    // let constructObj = PricingFileFindUtils.getProjectObjById(constructId);

    if (!ObjectUtils.isEmpty(unitParam.copyFromId)) {
      //代表是复制的单位
      let newUnit = await ConvertUtil.deepCopy(oldUnit);
      newUnit.sequenceNbr = Snowflake.nextId();
      newUnit.spId = singleId;
      newUnit.upName = unitParam.name;
      if (ifParentIsConstruct) {
        constructObj.unitProjectArrayBak.push(newUnit);
      } else {
        //重新赋值单位名称（有重复的话+1）
        await this.repeatInitUnitName(constructId, singleId, newUnit);
        //重新赋值分部分项等数据的unitId和spId
        await this.repeatInitUnitItemId(newUnit);
        //重新复制人材机ID和人材机子集ID
        await this.repeatRcj(newUnit.constructProjectRcjs,newUnit.rcjDetailList)



        parent.unitProjectsBak.push(newUnit);
      }
    } else {
      if (ObjectUtils.isEmpty(newUnit)) {// 单位不存在
        let deStandard = await this.service.baseListDeStandardService.quotaStandardById(unitParam.deStandardId);
        let unitIs2022 = (deStandard.releaseYear == ConstantUtil.DE_STANDARD_22);
        let libraryCode = unitParam.libraryCode;
        if (!libraryCode && unitParam.constructMajorType) {
          libraryCode = this.service.unitProjectService.getMainDeLibrary(unitParam.constructMajorType, unitIs2022).filter(f => f.defaultDeFlag === 1)[0].libraryCode;
        }
        let secondInstallationProjectName = constructParam.secondInstallationProjectName ? constructParam.secondInstallationProjectName : unitParam.secondInstallationProjectName;
        newUnit = await this.initUnit(constructObj, unitParam.parentId, unitParam.id, unitParam.name, unitParam.constructMajorType, null, true, libraryCode, secondInstallationProjectName, unitParam.deStandardId);
      } else { // 单位存在
        let oldConstructMajorType = newUnit.constructMajorType;
        let unitIs2022 = PricingFileFindUtils.is22Unit(newUnit);

        newUnit.upName = unitParam.name;
        newUnit.constructMajorType = unitParam.constructMajorType;
        newUnit.secondInstallationProjectName = unitParam.secondInstallationProjectName;
        newUnit.constructId = constructId;
        newUnit.spId = singleId;

        ConstructOperationUtil.updateUnitName(newUnit, unitParam.name);
        ConstructOperationUtil.updateUnitMajorType(newUnit, unitParam.constructMajorType);

        if (ObjectUtils.isNotEmpty(unitParam.constructMajorType)) {
          let libraryCode = newUnit.libraryCode || unitParam.libraryCode;
          if (!libraryCode && unitParam.constructMajorType) {
            libraryCode = this.service.unitProjectService.getMainDeLibrary(unitParam.constructMajorType, unitIs2022).filter(f => f.defaultDeFlag === 1)[0].libraryCode;
          }
          newUnit.mainDeLibrary = libraryCode;

          if (oldConstructMajorType != unitParam.constructMajorType) {
            //保存取费文件
            newUnit = await this.service.baseFeeFileService.initFeeFile(newUnit);
            // let sql = "select default_qf_code as code from base_speciality_de_fee_relation where unit_project_name = ? and library_code = ?";
            // let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).get(oldUnit.constructMajorType, libraryCode);
            // if (sqlRes) {
            //     let mainFile = await this.service.baseFeeFileService.updateFeeFile(oldUnit, sqlRes.code);
            //     mainFile.defaultFeeFlag = 1;
            // } else {
            //     oldUnit.feeFiles[0].defaultFeeFlag = 1;
            // }
          }
        }
      }
      // 将处理后的unit加入父级中
      if (ifParentIsConstruct) {
        constructObj.unitProjectArrayBak.push(newUnit);
      } else {
        parent.unitProjectsBak.push(newUnit);
      }
      ObjectUtils.updatePropertyValue(newUnit, 'spId', singleId);
      ObjectUtils.updatePropertyValue(newUnit, 'singleId', singleId);

    }
  }


  async repeatInitSingleName(ifParentIsConstruct, parent, oldConstructFlatMap, newSingle, singleId) {
    if (ifParentIsConstruct) {
      //单项
      let singleProjects = parent.singleProjects;
      if (ObjectUtils.isNotEmpty(singleProjects)) {
        let singleUnitNameList = singleProjects.map(obj => obj.projectName);
        let singleNewName = await this.repeatInitSingleNameCal(singleUnitNameList, newSingle.projectName);
        newSingle.projectName = singleNewName;
      }
    } else {
      //子单项
      let newVar = oldConstructFlatMap.get(singleId);
      let subSingleProjects = newVar.subSingleProjects;
      if (ObjectUtils.isNotEmpty(subSingleProjects)) {
        let singleUnitNameList = subSingleProjects.map(obj => obj.projectName);
        let singleNewName = await this.repeatInitSingleNameCal(singleUnitNameList, newSingle.projectName);
        newSingle.projectName = singleNewName;
      }
    }
  }


  async repeatInitSingleItemId(newSingle) {
    if (ObjectUtils.isNotEmpty(newSingle.unitProjects)) {
      //单项下有单位
      for (let i = 0; i < newSingle.unitProjects.length; i++) {
        let item = newSingle.unitProjects[i];
        item.sequenceNbr = Snowflake.nextId();
        item.spId = newSingle.sequenceNbr;
        //重新赋值分部分项等数据的unitId
        await this.repeatInitUnitItemId(item);
      }
    } else if (ObjectUtils.isNotEmpty(newSingle.subSingleProjects)) {
      //单项下有子单项
      for (let i = 0; i < newSingle.subSingleProjects.length; i++) {
        let item = newSingle.subSingleProjects[i];
        item.sequenceNbr = Snowflake.nextId();
        item.spId = newSingle.sequenceNbr;
        await this.repeatInitSingleItemId(item);
      }
    }
  }


  //单位和单项本层级有重复名称时重新计算名称
  async repeatInitSingleNameCal(singleUnitNameList, oldName) {
    let newName = oldName;
    if (singleUnitNameList.includes(newName)) {
      newName = newName + '_1';
      while (singleUnitNameList.includes(newName)) {
        let lastIndex = newName.lastIndexOf('_');
        let count = newName.slice(lastIndex + 1);
        // const regex = "^[1-9]*$";
        let number = parseInt(count);
        number = number + 1;
        newName = newName.slice(0, -1) + number;
      }
    }
    return newName;
  }


  async repeatInitUnitName(constructId, singleId, newUnit) {
    let singleProject = PricingFileFindUtils.getSingleProject(constructId, singleId);
    let unitProjects = singleProject.unitProjects;
    if (ObjectUtils.isNotEmpty(unitProjects)) {
      let singleUnitNameList = unitProjects.map(obj => obj.upName);
      let unitNewName = await this.repeatInitSingleNameCal(singleUnitNameList, newUnit.upName);
      newUnit.upName = unitNewName;
    }
  }

  async repeatInitUnitItemId(newUnit) {
    //重刷单位id
    ObjectUtils.updatePropertyValue(newUnit, 'unitId', newUnit.sequenceNbr);
    //重刷单项id
    ObjectUtils.updatePropertyValue(newUnit, 'spId', newUnit.spId);

    // if (ObjectUtils.isNotEmpty(newUnit.feeFiles)) {
    //     newUnit.feeFiles.forEach((item) => {
    //         if (ObjectUtils.isNotEmpty(item.unitId)) {
    //             item.unitId = newUnit.sequenceNbr;
    //         }
    //     });
    // }
    // if (ObjectUtils.isNotEmpty(newUnit.itemBillProjects)) {
    //     newUnit.itemBillProjects.forEach((item) => {
    //         if (ObjectUtils.isNotEmpty(item.unitId)) {
    //             item.unitId = newUnit.sequenceNbr;
    //         }
    //     });
    // }
    // if (ObjectUtils.isNotEmpty(newUnit.measureProjectTables)) {
    //     newUnit.measureProjectTables.forEach((item) => {
    //         if (ObjectUtils.isNotEmpty(item.unitId)) {
    //             item.unitId = newUnit.sequenceNbr;
    //         }
    //     });
    // }
    // if (ObjectUtils.isNotEmpty(newUnit.gfees)) {
    //     newUnit.gfees.forEach((item) => {
    //         if (ObjectUtils.isNotEmpty(item.unitId)) {
    //             item.unitId = newUnit.sequenceNbr;
    //         }
    //     });
    // }
    // if (ObjectUtils.isNotEmpty(newUnit.safeFees)) {
    //     newUnit.safeFees.forEach((item) => {
    //         if (ObjectUtils.isNotEmpty(item.unitId)) {
    //             item.unitId = newUnit.sequenceNbr;
    //         }
    //     });
    // }
    // if (ObjectUtils.isNotEmpty(newUnit.constructProjectRcjs)) {
    //     newUnit.constructProjectRcjs.forEach((item) => {
    //         if (ObjectUtils.isNotEmpty(item.unitId)) {
    //             item.unitId = newUnit.sequenceNbr;
    //         }
    //     });
    // }
    // if (ObjectUtils.isNotEmpty(newUnit.unitCostSummarys)) {
    //     newUnit.unitCostSummarys.forEach((item) => {
    //         if (ObjectUtils.isNotEmpty(item.unitId)) {
    //             item.unitId = newUnit.sequenceNbr;
    //         }
    //     });
    // }
    //
    // if (ObjectUtils.isNotEmpty(newUnit.organizationInstructions) && ObjectUtils.isNotEmpty(newUnit.organizationInstructions.unitId)) {
    //     newUnit.organizationInstructions.unitId = newUnit.sequenceNbr;
    // }
    // if (ObjectUtils.isNotEmpty(newUnit.projectTaxCalculation) && ObjectUtils.isNotEmpty(newUnit.projectTaxCalculation.unitId)) {
    //     newUnit.projectTaxCalculation.unitId = newUnit.sequenceNbr;
    // }
  }



  /**
   * 复制后替换人材机ID
   */
  async repeatRcj(constructProjectRcjs,rcjDetailList) {
    const oldIdToChildrenMap = new Map();
    // 1. 建立子级映射（旧父ID -> 子项数组）
    if (rcjDetailList) {
      for (const child of rcjDetailList) {
        if (!oldIdToChildrenMap.has(child.rcjId)) {
          oldIdToChildrenMap.set(child.rcjId, []);
        }
        oldIdToChildrenMap.get(child.rcjId).push(child);
      }
    }

    // 2. 处理父级并生成新旧ID映射
    if (constructProjectRcjs) {
      for (const parent of constructProjectRcjs) {
        const newId = Snowflake.nextId();

        parent.sequenceNbr = newId

        // 3. 处理对应子级（直接修改原对象）
        const children = oldIdToChildrenMap.get(parent.sequenceNbr) || [];
        for (const child of children) {
          // 生成新子级ID
          const newChildId = Snowflake.nextId();
          child.sequenceNbr = newChildId;
          child.rcjId = newId;
        }
      }
    }

  }

  // async deepCopy(obj) {
  //     if (typeof obj !== 'object' || obj === null) {
  //         return obj;
  //     }
  //
  //     let clone = Array.isArray(obj) ? [] : {};
  //
  //     for (let key in obj) {
  //         if (obj.hasOwnProperty(key)) {
  //             clone[key] = await this.deepCopy(obj[key]);
  //         }
  //     }
  //
  //     return clone;
  // }

  async _editSingleProjectLevelStructure(constructParam, singleParam, oldConstructFlatMap, parent, ifParentIsConstruct, parentSame) {
    let oldSingle = oldConstructFlatMap.get(singleParam.id);
    let newSingle = oldSingle;
    let constructId = constructParam.id;

    if (ObjectUtils.isEmpty(oldSingle)) {// 单项不存在
      let arg = {
        constructId: constructId,
        singleName: singleParam.name,
        singleId: singleParam.id,
        oldSingleId: ifParentIsConstruct ? null : parent.sequenceNbr
      };
      if (ifParentIsConstruct) { // 新增单项
        newSingle = this.service.singleProjectService.addSingleProject(arg, false);
      } else { // 新增子单项
        newSingle = this.service.singleProjectService.addSubSingleProject(arg, false);
      }
    }

    if (ifParentIsConstruct) {
      parent.singleProjectsBak.push(newSingle);
    } else {
      parent.subSingleProjectsBak.push(newSingle);
    }

    // 新增两个bak熟悉用于保存处理后的数据
    newSingle.unitProjectsBak = new Array();
    newSingle.subSingleProjectsBak = new Array();
    newSingle.projectName = singleParam.name;

    if (ObjectUtils.isNotEmpty(singleParam.children)) {
      for (const item of singleParam.children) {
        if (item.levelType == 2) {
          await this._editSingleProjectLevelStructure(constructParam, item, oldConstructFlatMap, newSingle, false, parentSame);
        } else if (item.levelType == 3) {
          await this._editUnitLevelStructure(constructParam, newSingle.sequenceNbr, item, oldConstructFlatMap, newSingle, false, parentSame);
        }
      }
    }

    //处理后的数据回填
    newSingle.unitProjects = newSingle.unitProjectsBak;
    newSingle.subSingleProjects = newSingle.subSingleProjectsBak;
    delete newSingle.unitProjectsBak;
    delete newSingle.subSingleProjectsBak;
  }

  async _editSingleProjectStructure(constructParam, singleParam, oldConstructFlatMap, parent, ifParentIsConstruct, parentSame) {
    let oldSingle = oldConstructFlatMap.get(singleParam.id);
    let newSingle = oldSingle;
    let constructId = constructParam.id;

    if (!ObjectUtils.isEmpty(singleParam.copyFromId)) {
      let newSingle = await ConvertUtil.deepCopy(oldSingle);
      newSingle.sequenceNbr = Snowflake.nextId();
      newSingle.spId = parent.id;
      // newSingle.constructId = constructId;
      newSingle.projectName = singleParam.name;

      //复制过来的校验名称是否有重复
      await this.repeatInitSingleName(ifParentIsConstruct, parent, oldConstructFlatMap, newSingle, singleParam.id);
      //重新赋值单项下的单位id和单项spId
      await this.repeatInitSingleItemId(newSingle);
      if (ifParentIsConstruct) {
        parent.singleProjectsBak.push(newSingle);
      } else {
        parent.subSingleProjectsBak.push(newSingle);
      }
    } else {
      if (ObjectUtils.isEmpty(oldSingle)) {// 单项不存在
        let arg = {
          constructId: constructId,
          singleName: singleParam.name,
          singleId: singleParam.id,
          oldSingleId: ifParentIsConstruct ? null : parent.sequenceNbr
        };
        if (ifParentIsConstruct) { // 新增单项
          newSingle = this.service.singleProjectService.addSingleProject(arg, false);
        } else { // 新增子单项
          newSingle = this.service.singleProjectService.addSubSingleProject(arg, false);
        }
      }

      //拖拽后如果重名前端修改名称
      newSingle.projectName = singleParam.name;

      if (ifParentIsConstruct) {
        parent.singleProjectsBak.push(newSingle);
      } else {
        parent.subSingleProjectsBak.push(newSingle);
      }

      // 新增两个bak熟悉用于保存处理后的数据
      newSingle.unitProjectsBak = new Array();
      newSingle.subSingleProjectsBak = new Array();
      // newSingle.projectName = singleParam.name;

      if (ObjectUtils.isNotEmpty(singleParam.children)) {
        for (const item of singleParam.children) {
          if (item.levelType == 2) {
            await this._editSingleProjectStructure(constructParam, item, oldConstructFlatMap, newSingle, false, parentSame);
          } else if (item.levelType == 3) {
            await this._editUnitStructure(constructParam, newSingle.sequenceNbr, item, oldConstructFlatMap, newSingle, false, parentSame);
          }
        }
      }

      //处理后的数据回填
      newSingle.unitProjects = newSingle.unitProjectsBak;
      newSingle.subSingleProjects = newSingle.subSingleProjectsBak;
      delete newSingle.unitProjectsBak;
      delete newSingle.subSingleProjectsBak;
    }
  }


// 编辑项目层级结构
  async editProjectLevelStructure(param) {
    let constructId = param.id;
    let oldConstructFlatMap = ConstructOperationUtil.flatConstructTreeToMapById(constructId);
    // 1. 工程项目处理
    let constructObj = PricingFileFindUtils.getProjectObjById(constructId);
    return this.reassign(param, oldConstructFlatMap, constructObj);
  }

  /**
   * 批量修改名称
   * @param args
   */
  batchModifyName(args) {
    let constructFlatMap = ConstructOperationUtil.flatConstructTreeToMapById(args.constructId);

    for (let item of args.data) {
      let node = constructFlatMap.get(item.id);
      if (node.levelType == ConstantUtil.CONSTRUCT_LEVEL_TYPE) {
        ConstructOperationUtil.updateConstructName(node, item.name);
      } else if (node.levelType == ConstantUtil.SINGLE_LEVEL_TYPE) {
        node.projectName = item.name;
      } else if (node.levelType == ConstantUtil.UNIT_LEVEL_TYPE) {
        ConstructOperationUtil.updateUnitName(node, item.name);
      }
    }
  }

  // 拖拽项目结构
  async dragDropProjectStructure(param) {
    let constructId = param.id;
    let oldConstructFlatMap = await ConstructOperationUtil.flatConstructTreeToMapById(constructId);
    // 1. 工程项目处理
    let constructObj = await PricingFileFindUtils.getProjectObjById(constructId);
    return await this.dragDropSortCopyDeleteProject(param, oldConstructFlatMap, constructObj);
  }

  /**
   * 工程项目重新赋值
   * @param param 前端的树
   * @param oldConstructFlatMap 内存中的数据平铺
   * @param constructObj  内存中的数据
   * @returns {Promise<ResponseData>}
   */
  async reassign(param, oldConstructFlatMap, constructObj) {

    ConstructOperationUtil.updateConstructName(constructObj, param.name);
    // 新增两个bak熟悉用于保存处理后的数据
    constructObj.unitProjectArrayBak = [];
    constructObj.singleProjectsBak = [];

    // 2. 单项工程处理
    if (ObjectUtils.isNotEmpty(param.children)) {
      for (const item of param.children) {
        if (item.levelType == 2) {
          await this._editSingleProjectLevelStructure(param, item, oldConstructFlatMap, constructObj, true, constructObj);
        } else if (item.levelType == 3) {
          await this._editUnitLevelStructure(param, null, item, oldConstructFlatMap, constructObj, true, constructObj);
        }
      }
    }

    //处理后的数据回填
    constructObj.unitProjectArray = constructObj.unitProjectArrayBak;
    constructObj.singleProjects = constructObj.singleProjectsBak;

    delete constructObj.unitProjectArrayBak;
    delete constructObj.singleProjectsBak;

    return ResponseData.success();
  }


  /**
   * 工程项目重新赋值
   * @param param 前端的树
   * @param oldConstructFlatMap 内存中的数据平铺
   * @param constructObj  内存中的数据
   * @returns {Promise<ResponseData>}
   */
  async dragDropSortCopyDeleteProject(param, oldConstructFlatMap, constructObj) {

    // ConstructOperationUtil.updateConstructName(constructObj, param.name);
    // 新增两个bak熟悉用于保存处理后的数据
    constructObj.unitProjectArrayBak = [];
    constructObj.singleProjectsBak = [];

    // 2. 单项工程处理
    if (ObjectUtils.isNotEmpty(param.children)) {
      for (const item of param.children) {
        if (item.levelType == 2) {
          await this._editSingleProjectStructure(param, item, oldConstructFlatMap, constructObj, true, constructObj);
        } else if (item.levelType == 3) {
          await this._editUnitStructure(param, null, item, oldConstructFlatMap, constructObj, true, constructObj);
        }
      }
    }

    //处理后的数据回填
    constructObj.unitProjectArray = constructObj.unitProjectArrayBak;
    constructObj.singleProjects = constructObj.singleProjectsBak;

    delete constructObj.unitProjectArrayBak;
    delete constructObj.singleProjectsBak;

    return ResponseData.success();
  }


  /**
   * 初始化政策文件
   * @param obj
   * @return {Promise<void>}
   */
  async initConstructPolicyDocument(obj) {
    let policyDocumentList = await this.app.appDataSource.getRepository(BasePolicyDocument).find({
      where: {
        fileType: In([PolicyDocumentTypeEnum.AFF.code, PolicyDocumentTypeEnum.GF.code])
      }
    });
    const groupByGender = policyDocumentList.reduce((groups, item) => {
      const group = groups[item.fileType] || [];
      return {
        ...groups,
        [item.fileType]: [...group, item]
      };
    }, {});
    let affPolicyDocument = groupByGender[PolicyDocumentTypeEnum.AFF.code];
    if (!ObjectUtils.isEmpty(affPolicyDocument)) {
      obj.awfId = affPolicyDocument[0].sequenceNbr;
    }
    let gfPolicyDocument = groupByGender[PolicyDocumentTypeEnum.GF.code];
    if (!ObjectUtils.isEmpty(gfPolicyDocument)) {
      obj.gfId = gfPolicyDocument[0].sequenceNbr;
    }

    let rgfPolicyDocumentList = await this.app.appDataSource.getRepository(BasePolicyDocument).find({
      where: {
        areaId: obj.ssCity,
        fileType: PolicyDocumentTypeEnum.RGF.code
      }
    });
    if (!ObjectUtils.isEmpty(rgfPolicyDocumentList)) {

      //时间倒叙排列
      rgfPolicyDocumentList.sort(function(a, b) {
        return b.fileDate.localeCompare(a.fileDate);
      });
      obj.rgfId = rgfPolicyDocumentList[0].sequenceNbr;
    }
  }


  /**
   *  全量计算费用代码和基数
   * @param constructProject
   * @returns {Promise<void>}
   */
  async countCostCodePriceAll(constructProject) {
    //此处增加 计算费用汇总和费用代码功能
    let singleProjects = constructProject.singleProjects;
    try {
      if (2 == constructProject.biddingType) {
        let args = {
          unitId: constructProject.unitProject.sequenceNbr
        };

        await this.service.unitCostCodePriceService.countCostCodePrice(args);
      } else {
        if (!ObjectUtils.isEmpty(singleProjects)) {
          for (let i = 0; i < singleProjects.length; i++) {
            let unitProjects = singleProjects[i].unitProjects;
            if (!ObjectUtils.isEmpty(unitProjects)) {
              for (let j = 0; j < unitProjects.length; j++) {
                let args = {
                  constructId: constructProject.sequenceNbr,
                  singleId: singleProjects[i].sequenceNbr,
                  unitId: unitProjects[j].sequenceNbr
                };
                await this.service.unitCostCodePriceService.countCostCodePrice(args);
              }
            }
          }
        } else {
          let unitProjectArray = constructProject.unitProjectArray;
          if (!ObjectUtils.isEmpty(unitProjectArray)) {
            for (let i = 0; i < unitProjectArray.length; i++) {
              let args = {
                unitId: unitProjectArray[i].sequenceNbr
              };
              await this.service.unitCostCodePriceService.countCostCodePrice(args);
            }
          }
        }
      }

    } catch (e) {
      console.log('导入后费用代码和费用汇总计算报错');
      console.log(e);
    }
  }


  getConstructMajorTypeByConstructId(args) {
    let unitList = PricingFileFindUtils.getUnitList(args.constructSequenceNbr);
    if (!ObjectUtils.isEmpty(unitList)) {
      for (let i = 0; i < unitList.length; i++) {
        let unit = unitList[i];
        if (ObjectUtils.isEmpty(unit.constructMajorType)) {
          return false;
        }
      }
    }
    return true;
  }

  getBottomSummary(args) {
    const { constructId, singleId, unitId } = args;
    //总造价
    let zzj = 0;
    //人工费
    let rgf = 0;
    //材料费
    let clf = 0;
    //机械费
    let jxf = 0;
    //管理费
    let glf = 0;
    //主材费
    let zcf = 0;
    //利润
    let lr = 0;
    //直接工程费
    let zjgcf = 0;

    let gdawf = 0;
    // 设备费及税金
    let sbfsj = 0;

    let unitList = [];
    if (ObjectUtils.isEmpty(unitId) && ObjectUtils.isEmpty(singleId)) {
      // 工程项目级别
      unitList = PricingFileFindUtils.getUnitList(constructId);
      const constructObj = PricingFileFindUtils.getProjectObjById(constructId);
      if (ObjectUtils.isNotEmpty(constructObj) && ObjectUtils.isNotEmpty(constructObj.securityFee)) {
        // 如果设置了固定安文费  那么工程项目级别需要加上固定安文费的值
        gdawf = constructObj.securityFee;
      }
    } else if (ObjectUtils.isEmpty(unitId) && ObjectUtils.isNotEmpty(singleId)) {
      // 单项级别
      unitList = PricingFileFindUtils.getUnitListBySingle(constructId, singleId);
    } else {
      // 单位级别
      unitList = [PricingFileFindUtils.getUnit(constructId, singleId, unitId)];
    }
    if (!ObjectUtils.isEmpty(unitList)) {
      for (let i = 0; i < unitList.length; i++) {
        let unit = unitList[i];

        sbfsj = NumberUtil.addParams(sbfsj, unit.sbfsj);

        let unitCostCodePrices = unit.unitCostCodePrices;
        if (!ObjectUtils.isEmpty(unitCostCodePrices)) {
          zzj = NumberUtil.add(zzj, NumberUtil.add(unit.gczj, unit.sbfsj));

          let fbfxrgf = unitCostCodePrices.find(item => item.code === 'RGF').price;
          let zjcsrgf = unitCostCodePrices.find(item => item.code === 'QTZJCS_RGF').price;
          let djcsrgf = unitCostCodePrices.find(item => item.code === 'DJCS_RGF').price;
          rgf = NumberUtil.addParams(rgf, fbfxrgf, zjcsrgf, djcsrgf);

          let fbfxclf = unitCostCodePrices.find(item => item.code === 'CLF').price;
          let zjcsclf = unitCostCodePrices.find(item => item.code === 'QTZJCS_CLF').price;
          let djcsclf = unitCostCodePrices.find(item => item.code === 'DJCS_CLF').price;
          clf = NumberUtil.addParams(clf, fbfxclf, zjcsclf, djcsclf);

          let fbfxjxf = unitCostCodePrices.find(item => item.code === 'JXF').price;
          let zjcsjxf = unitCostCodePrices.find(item => item.code === 'QTZJCS_JXF').price;
          let djcsjxf = unitCostCodePrices.find(item => item.code === 'DJCS_JXF').price;
          jxf = NumberUtil.addParams(jxf, fbfxjxf, zjcsjxf, djcsjxf);

          let fbfxglf = unitCostCodePrices.find(item => item.code === 'FBFX_GLF').price;
          let zjcsglf = unitCostCodePrices.find(item => item.code === 'QTZJCS_GLF').price;
          let djcsglf = unitCostCodePrices.find(item => item.code === 'DJCS_GLF').price;
          glf = NumberUtil.addParams(glf, fbfxglf, zjcsglf, djcsglf);

          let fbfxlr = unitCostCodePrices.find(item => item.code === 'FBFX_LR').price;
          let zjcslr = unitCostCodePrices.find(item => item.code === 'QTZJCS_LR').price;
          let djcslr = unitCostCodePrices.find(item => item.code === 'DJCS_LR').price;
          lr = NumberUtil.addParams(lr, fbfxlr, zjcslr, djcslr);

          let fbfxzcf = unitCostCodePrices.find(item => item.code === 'ZCF').price;
          let zjcszcf = unitCostCodePrices.find(item => item.code === 'QTZJCS_ZCF').price;
          let djcszcf = unitCostCodePrices.find(item => item.code === 'DJCS_ZCF').price;
          zcf = NumberUtil.addParams(zcf, fbfxzcf, zjcszcf, djcszcf);

          let fbfxzjf = unitCostCodePrices.find(item => item.code === 'ZJF').price;
          let zjcszjf = unitCostCodePrices.find(item => item.code === 'QTZJCS_ZJF').price;
          let djcszjf = unitCostCodePrices.find(item => item.code === 'DJCS_ZJF').price;
          zjgcf = NumberUtil.addParams(zjgcf, fbfxzjf, zjcszjf, djcszjf);
        }
      }
    }
    // 加上固定安文费的值   如果没有设置固定安文费  那么gdawf的值为0  不会有影响
    zzj = NumberUtil.addParams(zzj, gdawf);
    return {
      zzj: NumberUtil.numberScale2(zzj),
      rgf: NumberUtil.numberScale2(rgf),
      clf: NumberUtil.numberScale2(clf),
      jxf: NumberUtil.numberScale2(jxf),
      glf: NumberUtil.numberScale2(glf),
      zcf: NumberUtil.numberScale2(zcf),
      lr: NumberUtil.numberScale2(lr),
      zjgcf: NumberUtil.numberScale2(zjgcf),
      sbfsj: NumberUtil.numberScale2(sbfsj)
    };
  }


  /**
   * 通用便捷设置
   *  @param   column要修改的字段名称
   *  @param   value要修改的值
   */

  async projectConvenientSet(args) {
    let { constructId, column, value } = args;
    let project = PricingFileFindUtils.getProjectObjById(constructId);
    project[column] = value;
  }

  /**
   * 工程项目便捷设置查询
   *
   */
  async queryProjectConvenientSet(args) {
    let { constructId } = args;
    let project = PricingFileFindUtils.getProjectObjById(args.constructId);
    //便捷性设置里面需要什么最好是直接给前端设置上
    return {
      deGlTcFlag: project['deGlTcFlag']

    };
  }

  /**
   *
   * @param args
   */
  updateMainRcjShowFlag(args){
    let {constructId,mainRcjShowFlag} = args;
    let construct = PricingFileFindUtils.getProjectObjById(constructId);
    construct.mainRcjShowFlag=mainRcjShowFlag;
    return true;
  }


  /**
   *
   * @param args
   */
  updateStandardConversionShowFlag(args){
    let {constructId,standardConversionShowFlag} = args;
    let construct = PricingFileFindUtils.getProjectObjById(constructId);
    construct.standardConversionShowFlag=standardConversionShowFlag;
    return true;
  }


  queryConstructProjectMessage(args){
    let {constructId} = args;
    let construct = PricingFileFindUtils.getProjectObjById(constructId);
    return {
      mainRcjShowFlag: construct.mainRcjShowFlag,
      standardConversionShowFlag: construct.standardConversionShowFlag,
      exportConfig: construct.exportConfig

    };
  }

  /**
   * 查询工程项目级别 是否有单位工程未设置取费专业 true 为 全部设置了 false为最少有一个没设置
   * @param args
   * @returns {Promise<*>}
   */
  async getConstructUnitListTypeByConstructId(args) {

    let unitList = PricingFileFindUtils.getUnitList(args.constructId);
    if (!ObjectUtils.isEmpty(unitList)) {
      for (let unit of unitList) {
        if (ObjectUtils.isEmpty(unit.constructMajorType)) {
          return false;
        }
      }
    }
    return true;
  }


  async getConstructFileMsg(args) {
    let { constructId } = args;
    let project = PricingFileFindUtils.getProjectObjById(constructId);

    return {
      path: project.path
    };
  }

  /**
   * 标段结构保护
   * @returns {Promise<void>}
   */
  async optionLock(args){
    let { constructId,optionLock } = args;
    let project = PricingFileFindUtils.getProjectObjById(constructId);
    project.optionLock = optionLock;
  }

  /**
   * 展示设置
   * @returns {Promise<void>}
   */
  async setMainSettingShow(args){
      let {constructId} = args;
      let construct = PricingFileFindUtils.getProjectObjById(constructId);
      let m = construct.mainSetting;
      if(ObjectUtils.isEmpty(m)){
          m = new MainSetting();
      }
      m.bdCode = args.bdCode;
      construct.mainSetting = m;
      return true;
  }

  /**
   * 展示设置回显
   * @returns {Promise<void>}
   */
  async getMainSettingShow(args){
    let {constructId} = args;
    let construct = PricingFileFindUtils.getProjectObjById(constructId);
    return construct.mainSetting;
  }

  /**
   * 判断工程项目文件是否是 只读文件
   * @param args
   * @returns {Promise<void>}
   */
  async fsIsReadOnly (args){
    let projectObjById = PricingFileFindUtils.getProjectObjById(args.constructId);
    if (ObjectUtils.isEmpty(projectObjById)) {
      return;
    }

    let resultIsReadOnly;
    try {
      const isReadOnly = await this.isFileReadOnly(projectObjById.path)
      if (isReadOnly) {
        //console.log('该文件是只读文件。');
        resultIsReadOnly = true;
        return ResponseData.success(true);
      } else {
        //console.log('该文件不是只读文件。');
        resultIsReadOnly = false;
        return ResponseData.success(false);
      }
    } catch (e) {
      resultIsReadOnly = false;
      return ResponseData.success(true);
    }
  }

}

ConstructProjectService.toString = () => '[class ConstructProjectService]';
module.exports = ConstructProjectService;
