/* 
组	整数
宗	整数
元	小数2位
套	整数
台	整数
千米	小数3位
辆	整数
块	整数
公斤	小数2位
付	整数
处	整数
部	整数
千克	小数2位
kg	小数2位
吨	小数3位
t	小数3位
立方米	小数2位
m3	小数2位
米	小数2位
m	小数2位
平方米	小数2位
m2	小数2位
工日	整数
个	整数
台班	整数
座	整数
km	小数3位
樘	整数
件	整数
根	整数
系统	整数
株	整数
丛	整数
缸	整数
只	整数
支	整数
对	整数
份	整数
攒	整数
榀	整数
其他不在所列单位范围内的单位	小数点后3位


架	整数
条	整数
节	整数
口	整数
天	整数
间	整数
段	整数
单元	整数
组件	整数

*/

const QdUnitFormatEnum = Object.freeze({
    "架": {
        label: "整数",
        value: 0
    },
    "条": {
        label: "整数",
        value: 0
    },
    "节": {
        label: "整数",
        value: 0
    },
    "口": {
        label: "整数",
        value: 0
    },
    "天": {
        label: "小数2位",
        value: 2
    },
    "间": {
        label: "整数",
        value: 0
    },
    "段": {
        label: "整数",
        value: 0
    },
    "单元": {
        label: "整数",
        value: 0
    },
    "组件": {
        label: "整数",
        value: 0
    },
    "组": {
        label: "整数",
        value: 0
    },
    "宗": {
        label: "整数",
        value: 0
    },
    "元": {
        label: "小数2位",
        value: 2
    },
    "套": {
        label: "整数",
        value: 0
    },
    "台": {
        label: "整数",
        value: 0
    },
    "千米": {
        label: "小数3位",
        value: 3
    },
    "辆": {
        label: "整数",
        value: 0
    },
    "块": {
        label: "整数",
        value: 0
    },
    "公斤": {
        label: "小数2位",
        value: 2
    },
    "付": {
        label: "整数",
        value: 0
    },
    "处": {
        label: "整数",
        value: 0
    },
    "部": {
        label: "整数",
        value: 0
    },
    "千克": {
        label: "小数2位",
        value: 2
    },
    "kg": {
        label: "小数2位",
        value: 2
    },
    "吨": {
        label: "小数3位",
        value: 3
    },
    "t": {
        label: "小数3位",
        value: 3
    },
    "立方米": {
        label: "小数2位",
        value: 2
    },
    "m3": {
        label: "小数2位",
        value: 2
    },
    "米": {
        label: "小数2位",
        value: 2
    },
    "m": {
        label: "小数2位",
        value: 2
    },
    "平方米": {
        label: "小数2位",
        value: 2
    },
    "m2": {
        label: "小数2位",
        value: 2
    },
    "工日": {
        label: "小数2位",
        value: 2
    },
    "个": {
        label: "整数",
        value: 0
    },
    "台班": {
        label: "整数",
        value: 0
    },
    "座": {
        label: "整数",
        value: 0
    },
    "km": {
        label: "小数3位",
        value: 3
    },
    "樘": {
        label: "整数",
        value: 0
    },
    "件": {
        label: "整数",
        value: 0
    },
    "根": {
        label: "整数",
        value: 0
    },
    "系统": {
        label: "整数",
        value: 0
    },
    "株": {
        label: "整数",
        value: 0
    },
    "丛": {
        label: "整数",
        value: 0
    },
    "缸": {
        label: "整数",
        value: 0
    },
    "只": {
        label: "整数",
        value: 0
    },
    "支": {
        label: "整数",
        value: 0
    },
    "对": {
        label: "整数",
        value: 0
    },
    "份": {
        label: "整数",
        value: 0
    },
    "攒": {
        label: "整数",
        value: 0
    },
    "榀": {
        label: "整数",
        value: 0
    },
    "other": {
        label: "小数点后3位",
        value: 3
    }
});
const getUnitFormatEnum = (unit) => {
    return {
        label: "软件系统默认",
        value: 5
    };
}
/* 
%	小数点后2位
kg	小数点后3位
km	小数点后3位
m	小数点后2位
m2	小数点后2位
m3	小数点后2位
t	小数点后3位
丛	整数
个	整数
件	整数
份	整数
副	整数
只	整数
台	整数
台(块)	整数
台次	整数
吨	小数点后3位
块	整数
基	整数
处	整数
头	整数
套	整数
套天	整数
孔	整数
对	整数
座	整数
扇	整数
把	整数
支	整数
条	整数
架	整数
架次	整数
株	整数
根	整数
根次	整数
榀	整数
樘	整数
步	整数
段	整数
每户	整数
每昼夜	整数
水平投影面积m2	小数点后2位
点	整数
片	整数
站	整数
端	整数
系统	整数
组	整数
组件	整数	节
整数
芯	整数
道	整数
部	整数
链路	整数
间	整数
1个梯段	小数点后2位
1根天	小数点后2位
30路	小数点后2位
50根天	小数点后2位
10kg	小数点后3位
10m	小数点后3位
10m2	小数点后3位
10m3	小数点后3位
10t	小数点后3位
10丛	小数点后3位
10个	小数点后3位
10个口	小数点后3位
10串	小数点后3位
10件	小数点后3位
10副	小数点后3位
10口	小数点后3位
10只	小数点后3位
10只(套)	小数点后3位
10块	小数点后3位
10处	小数点后3位
10套	小数点后3位
10张	小数点后3位
10株	小数点后3位
10根	小数点后3位
10樘	小数点后3位
10片	小数点后3位
10盆	小数点后3位
10组	小数点后3位
100kg	小数点后3位
100m	小数点后3位
100m2	小数点后3位
100m3	小数点后3位
100t	小数点后3位
100个	小数点后3位
100块	小数点后3位
100工日	小数点后3位
100根	小数点后3位
100片	小数点后3位
1000m2	小数点后3位
1000m3	小数点后3位
100cm3	小数点后3位
其他不在所列单位范围内的单位	小数点后3位 */
const DeUnitFormatEnum = Object.freeze({
    "%": {
        label: "小数点后2位",
        value: 2
    },
    "kg": {
        label: "小数点后3位",
        value: 3
    },
    "km": {
        label: "小数点后3位",
        value: 3
    },
    "m": {
        label: "小数点后2位",
        value: 2
    },
    "m2": {
        label: "小数点后2位",
        value: 2
    },
    "m3": {
        label: "小数点后4位",
        value: 4
    },
    "t": {
        label: "小数点后3位",
        value: 3
    },
    "丛": {
        label: "整数",
        value: 0
    },
    "个": {
        label: "整数",
        value: 0
    },
    "件": {
        label: "整数",
        value: 0
    },
    "份": {
        label: "整数",
        value: 0
    },
    "副": {
        label: "整数",
        value: 0
    },
    "只": {
        label: "整数",
        value: 0
    },
    "台": {
        label: "整数",
        value: 0
    },
    "台(块)": {
        label: "整数",
        value: 0
    },
    "台次": {
        label: "整数",
        value: 0
    },
    "吨": {
        label: "小数点后3位",
        value: 3
    },
    "块": {
        label: "整数",
        value: 0
    },
    "基": {
        label: "整数",
        value: 0
    },
    "处": {
        label: "整数",
        value: 0
    },
    "头": {
        label: "整数",
        value: 0
    },
    "套": {
        label: "整数",
        value: 0
    },
    "套天": {
        label: "整数",
        value: 0
    },
    "孔": {
        label: "整数",
        value: 0
    },
    "对": {
        label: "整数",
        value: 0
    },
    "座": {
        label: "整数",
        value: 0
    },
    "扇": {
        label: "整数",
        value: 0
    },
    "把": {
        label: "整数",
        value: 0
    },
    "支": {
        label: "整数",
        value: 0
    },
    "条": {
        label: "整数",
        value: 0
    },
    "架": {
        label: "整数",
        value: 0
    },
    "架次": {
        label: "整数",
        value: 0
    },
    "株": {
        label: "整数",
        value: 0
    },
    "根": {
        label: "整数",
        value: 0
    },
    "根次": {
        label: "整数",
        value: 0
    },
    "榀": {
        label: "整数",
        value: 0
    },
    "樘": {
        label: "整数",
        value: 0
    },
    "步": {
        label: "整数",
        value: 0
    },
    "段": {
        label: "整数",
        value: 0
    },
    "每户": {
        label: "整数",
        value: 0
    },
    "每昼夜": {
        label: "整数",
        value: 0
    },
    "水平投影面积m2": {
        label: "小数点后2位",
        value: 2
    },
    "点": {
        label: "整数",
        value: 0
    },
    "片": {
        label: "整数",
        value: 0
    },
    "站": {
        label: "整数",
        value: 0
    },
    "端": {
        label: "整数",
        value: 0
    },
    "系统": {
        label: "整数",
        value: 0
    },
    "组": {
        label: "整数",
        value: 0
    },
    "组件": {
        label: "整数",
        value: 0
    },
    "节": {
        label: "整数",
        value: 0
    },
    "芯": {
        label: "整数",
        value: 0
    },
    "道": {
        label: "整数",
        value: 0
    },
    "部": {
        label: "整数",
        value: 0
    },
    "链路": {
        label: "整数",
        value: 0
    },
    "间": {
        label: "整数",
        value: 0
    },
    "1个梯段": {
        label: "小数点后2位",
        value: 2
    },
    "1根天": {
        label: "小数点后2位",
        value: 2
    },
    "30路": {
        label: "小数点后2位",
        value: 2
    },
    "50根天": {
        label: "小数点后2位",
        value: 2
    },
    "10kg": {
        label: "小数点后4位",
        value: 4
    },
    "10m": {
        label: "小数点后4位",
        value: 4
    },
    "10m2": {
        label: "小数点后4位",
        value: 4
    },
    "10m3": {
        label: "小数点后4位",
        value: 4
    },
    "10t": {
        label: "小数点后4位",
        value: 4
    },
    "10丛": {
        label: "小数点后4位",
        value: 4
    },
    "10个": {
        label: "小数点后4位",
        value: 4
    },
    "10个口": {
        label: "小数点后4位",
        value: 4
    },
    "10串": {
        label: "小数点后4位",
        value: 4
    },
    "10件": {
        label: "小数点后4位",
        value: 4
    },
    "10副": {
        label: "小数点后4位",
        value: 4
    },
    "10口": {
        label: "小数点后4位",
        value: 4
    },
    "10只": {
        label: "小数点后4位",
        value: 4
    },
    "10只(套)": {
        label: "小数点后4位",
        value: 4
    },
    "10块": {
        label: "小数点后4位",
        value: 4
    },
    "10处": {
        label: "小数点后4位",
        value: 4
    },
    "10套": {
        label: "小数点后4位",
        value: 4
    },
    "10张": {
        label: "小数点后4位",
        value: 4
    },
    "10株": {
        label: "小数点后4位",
        value: 4
    },
    "10根": {
        label: "小数点后4位",
        value: 4
    },
    "10樘": {
        label: "小数点后4位",
        value: 4
    },
    "10片": {
        label: "小数点后4位",
        value: 4
    },
    "10盆": {
        label: "小数点后4位",
        value: 4
    },
    "10组": {
        label: "小数点后4位",
        value: 4
    },
    "100kg": {
        label: "小数点后5位",
        value: 5
    },
    "100m": {
        label: "小数点后5位",
        value: 5
    },
    "100m2": {
        label: "小数点后5位",
        value: 5
    },
    "100m3": {
        label: "小数点后5位",
        value: 5
    },
    "100t": {
        label: "小数点后5位",
        value: 5
    },
    "100个": {
        label: "小数点后5位",
        value: 5
    },
    "100块": {
        label: "小数点后5位",
        value: 5
    },
    "100工日": {
        label: "小数点后5位",
        value: 5
    },
    "100根": {
        label: "小数点后5位",
        value: 5
    },
    "100片": {
        label: "小数点后5位",
        value: 5
    },
    "1000m2": {
        label: "小数点后6位",
        value: 6
    },
    "1000m3": {
        label: "小数点后6位",
        value: 6
    },
    "100cm3": {
        label: "小数点后5位",
        value: 5
    },
    "1000t.m": {
        label: "小数点后6位",
        value: 6
    },
    "100m/束": {
        label: "小数点后5位",
        value: 5
    },
    "100m2.天": {
        label: "小数点后5位",
        value: 5
    },
    "100m2斜面积": {
        label: "小数点后5位",
        value: 5
    },
    "100m2槽底面积": {
        label: "小数点后5位",
        value: 5
    },
    "100m2水平投影面积": {
        label: "小数点后5位",
        value: 5
    },
    "100m3空间体积": {
        label: "小数点后5位",
        value: 5
    },
    "100m单线": {
        label: "小数点后5位",
        value: 5
    },
    "100个井点": {
        label: "小数点后5位",
        value: 5
    },
    "10m/单相": {
        label: "小数点后4位",
        value: 4
    },  
    "10m2投影面积": {
        label: "小数点后4位",
        value: 4
    },
    "10m2斜面积": {
        label: "小数点后4位",
        value: 4
    },
    "10m2水平投影面积": {
        label: "小数点后4位",
        value: 4
    },
    "10m3实体积": {
        label: "小数点后4位",
        value: 4
    },
    "10t·km": {
        label: "小数点后4位",
        value: 4
    },
    "10t·m": {
        label: "小数点后4位",
        value: 4
    },
    "10个测试件": {
        label: "小数点后4位",
        value: 4
    },
    "10根·8h": {
        label: "小数点后4位",
        value: 4
    },
    "30根.8h": {
        label: "小数点后4位",
        value: 4
    },
    "3根/套": {
        label: "小数点后4位",
        value: 4
    },
    "每m3竣工木料": {
        label: "小数点后5位",
        value: 5
    },
    "每一试样": {
        label: "小数点后5位",
        value: 5
    },
    "跨/三相": {
        label: "小数点后5位",
        value: 5
    },
    "other": {
        label: "小数点后3位",
        value: 3
    }
});
/* 

1000t.m	小数点后6位
100m/束	小数点后5位
100m2.天	小数点后5位
100m2斜面积	小数点后5位
100m2槽底面积	小数点后5位
100m2水平投影面积	小数点后5位
100m3空间体积	小数点后5位
100m单线	小数点后5位
100个井点	小数点后5位
10m/单相	小数点后4位
10m2投影面积	小数点后4位
10m2斜面积	小数点后4位
10m2水平投影面积	小数点后4位
10m3实体积	小数点后4位
10t·km	小数点后4位
10t·m	小数点后4位
10个测试件	小数点后4位
10根·8h	小数点后4位
30根.8h	小数点后4位
3根/套	小数点后4位
每m3竣工木料	小数点后5位
每一试样	小数点后5位
跨/三相	小数点后5位
*/
const getDeUnitFormatEnum = (unit) => {
    return {
        label: "软件系统默认",
        value: 5
    };
}
module.exports = {getUnitFormatEnum,getDeUnitFormatEnum,QdUnitFormatEnum,DeUnitFormatEnum}