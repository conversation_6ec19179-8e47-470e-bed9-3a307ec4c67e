<!--
 * @Descripttion:
 * @Author:
 * @Date: 2024-07-05 15:09:05
 * @LastEditors: wangru
 * @LastEditTime: 2025-05-16 14:59:47
-->
<!--
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-11-15 16:56:26
 * @LastEditors: liuxia
 * @LastEditTime: 2024-12-24 16:50:05
-->
<template>
  <commonModal
    className="dialog-comm page-column-setting"
    v-model:modelValue="visible"
    title="设置"
    @show="openModal"
    @close="close"
  >
    <div class="page-setting">
      <div class="tree-box">
        <a-tree
          :tree-data="treeData"
          :defaultExpandAll="true"
          v-if="treeData.length"
          v-model:selectedKeys="tabKey"
          @select="treeSelect"
          :field-names="{
            children: 'children',
            title: 'name',
          }"
        >
        </a-tree>
      </div>
      <div class="setting-box">
        <div class="content-box">
          <div
            class="list-content"
            v-if="tabKey.includes('file')"
          >
            <div class="item">
              <span class="title">文件下载路径：</span>
              <div class="path">
                <button
                  class="change"
                  @click="setData('FILE_DOWNLOAD_PATH')"
                >
                  更改路径
                </button>
                <span>{{ FILE_DOWNLOAD_PATH }}</span>
              </div>
            </div>
            <div class="item">
              <span class="title">默认数据存储路径：</span>
              <div class="path">
                <button
                  class="change"
                  @click="setData('DEF_SAVE_PATH')"
                >
                  更改路径
                </button>
                <span>{{ DEF_SAVE_PATH }}</span>
              </div>
            </div>
          </div>
          <div
            class="list-content"
            v-if="tabKey.includes('setting')"
          >
            <a-radio-group
              v-if="projectAttrVisible"
              @click.stop="onChange($event, 'settingValue')"
              v-model:value="settingValue"
              name="radioGroup"
            >
              <a-radio value="1">推荐项目特征选中后自动关联组价方案</a-radio>
            </a-radio-group>

            <a-radio-group
              v-if="rgfInMeasureAndRPriceInMechanicalVisible"
              @click.stop="
                onChange($event, 'rgfInMeasureAndRPriceInMechanicalAction')
              "
              v-model:value="rgfInMeasureAndRPriceInMechanicalAction"
              name="radioGroup2"
              style="margin-top: 20px"
            >
              <a-radio value="1">以系数计算的措施项目和机械台班中的人工单价参与调整</a-radio>
            </a-radio-group>
            <a-radio-group
              @click.stop="onChange($event, 'standardConversionShowFlag')"
              v-model:value="standardConversionShowFlag"
              name="radioGroup2"
              style="margin-top: 20px"
            >
              <a-radio :value="true">单位工程中标准换算弹窗展示</a-radio>
            </a-radio-group>
            <a-radio-group
              @click.stop="onChange($event, 'mainRcjShowFlag')"
              v-model:value="mainRcjShowFlag"
              name="radioGroup2"
              style="margin-top: 20px; display: block"
            >
              <a-radio :value="true">单位工程中主材弹窗展示</a-radio>
            </a-radio-group>

            <a-radio-group
              @click.stop="onChange($event, 'deGlTcFlag')"
              v-model:value="deGlTcFlag"
              name="radioGroup3"
              style="margin-top: 20px; display: block"
            >
              <a-radio :value="true">展示定额关联子目弹窗</a-radio>
            </a-radio-group>
            <a-radio-group
              @click.stop="onChange($event, 'lockRcjResQty')"
              v-model:value="globalConfigInfo.budget.input.lockRcjResQty"
              name="radioGroup3"
              style="margin-top: 20px; display: block"
            >
              <a-radio :value="true">锁定人材机消耗量</a-radio>
            </a-radio-group>
          </div>
          <div
            class="list-content"
            v-else
          >
            <div
              :class="{
                'list-box': true,
                'list-group': options.constructor === Object,
              }"
              v-for="(options, index) of currentTreeInfo"
            >
              <template v-if="options.constructor === Object">
                <div class="title">
                  {{ options.title }}
                </div>
                <setItem :options="options.options"></setItem>
              </template>
              <template v-if="Array.isArray(options)">
                <setItem :options="options"></setItem>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="page-setting-foot">
      <div class="right">
        <a-button
          style="margin-right: 25px"
          @click="close"
        >取消</a-button>
        <a-button
          type="primary"
          @click="save"
        >确定</a-button>
      </div>
    </div>
  </commonModal>
</template>
<script setup>
import { ref } from 'vue';
import csProject from '@/api/csProject';
import api from '@/api/projectDetail';
import { message } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import setItem from './setItem.vue';
const projectStore = projectDetailStore();
let FILE_DOWNLOAD_PATH = ref();
let DEF_SAVE_PATH = ref();
let projectAttrVisible = ref(false);
let rgfInMeasureAndRPriceInMechanicalVisible = ref(false);
const emits = defineEmits(['closePopup']);

let treeData = ref([
  {
    key: 'file',
    name: '文件管理',
  },
  {
    key: 'setting',
    name: '便捷性设置',
  },
  // {
  //   key: 'global',
  //   name: '全局设置',
  //   children: [

  //   ],
  // },
  {
    key: 'jiSuan',
    name: '计算设置',
    options: [
      [
        {
          name: '工程量单位',
          field: 'gcldw',
          type: 'radio',
          value: '2',
          disabled: true,
          options: [
            {
              label: '自然单位',
              value: '1',
            },
            {
              label: '定额单位',
              value: '2',
            },
          ],
        },
      ],
      [
        {
          name: '主材、设备及区价差',
          field: 'zccj',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '补充人材机计取差价',
          field: 'bccj',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '暂估人材机计取差价',
          field: 'zgcj',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '修改市场价同步整个单位工程',
          field: 'xgscjtb',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
      ],
      [
        {
          name: '清单综合合价计算方式',
          field: 'qdzhjsfs',
          type: 'radio',
          value: '1',
          disabled: true,
          options: [
            {
              label: '清单综合合价=清单综合单价*清单工程量',
              value: '1',
            },
            {
              label: '清单综合合价=∑子目综合单价',
              value: '2',
            },
          ],
        },
      ],
      [
        {
          name: '综合单价计算方式',
          field: 'zhdjjsfs',
          type: 'radio',
          value: '2',
          disabled: true,
          options: [
            {
              label: '清单单价取费',
              value: '1',
            },
            {
              label: '子目单价取费',
              value: '2',
            },
          ],
        },
      ],
      [
        {
          name: '暂估合价计算方式',
          field: 'zfhjjsfs',
          type: 'radio',
          value: '2',
          disabled: true,
          options: [
            {
              label: '暂估合价=暂估单价*工程量（清单、子目）',
              value: '1',
            },
            {
              label: '暂估合价=∑暂估材料数量*暂估材料单价',
              value: '2',
            },
          ],
        },
      ],
      {
        title: '配比、机械台班设置',
        options: [
          {
            name: '商砼浆支持二次解析',
            field: 'stjecjx',
            type: 'checkbox',
            value: false,
            disabled: true,
          },
          {
            name: '现浇砼浆支持二次解析',
            field: 'xjtjecjx',
            type: 'checkbox',
            value: true,
            disabled: true,
          },
          {
            name: '现浇砼浆可直接修改市场价',
            field: 'xjtjxgscj',
            type: 'checkbox',
            value: false,
            disabled: true,
          },
          {
            name: '机械台班支持二次解析',
            field: 'jxtbecjx',
            type: 'checkbox',
            value: true,
            disabled: true,
          },
          {
            name: '机械台班可直接修改市场价',
            field: 'jxtbxgscj',
            type: 'checkbox',
            value: false,
            disabled: true,
          },
        ],
      },
    ],
  },
  {
    key: 'qdgclJingDu',
    name: '清单工程量精度设置',
    options: [
      [
        {
          name: '清单工程量精度设置',
          field: 'qdgclJingDu',
          type: 'checkbox',
          value: false,
          disabled: true,
        },
        {
          name: '',
          field: 'qdgclJingDu',
          type: 'table',
          columns: [
            { title: '序号', customRender: ({ text, record, index }) => index },
            { title: '计量单位', dataIndex: 'name' },
            {
              title: '精度',
              dataIndex: 'float',
              customRender: ({ text, record, index }) => {
                if (text === 0) {
                  return '整数';
                }
                return `小数${text}位`;
              },
            },
          ],
          dataSource: [
            {
              name: '个',
              float: 0,
            },
            {
              name: '件',
              float: 0,
            },
            {
              name: '工日',
              float: 0,
            },
            {
              name: '台班',
              float: 0,
            },
            {
              name: '座',
              float: 0,
            },
            { name: '樘', float: 0 },
            { name: '组', float: 0 },
            { name: '宗', float: 0 },
            { name: '套', float: 0 },
            { name: '台', float: 0 },
            { name: '辆', float: 0 },
            { name: '块', float: 0 },
            { name: '付', float: 0 },
            { name: '处', float: 0 },
            { name: '部', float: 0 },
            { name: '根', float: 0 },
            { name: '系统', float: 0 },
            { name: '株', float: 0 },
            { name: '丛', float: 0 },
            { name: '缸', float: 0 },
            { name: '只', float: 0 },
            { name: '支', float: 0 },
            { name: '对', float: 0 },
            { name: '份', float: 0 },
            { name: '攒', float: 0 },
            { name: '榀', float: 0 },
            { name: 'm', float: 3 },
            { name: '其余未列到单位', float: 2 },
          ],
        },
      ],
    ],
  },
  // {
  //   key: 'zmgclJingDu',
  //   name: '子目工程量精度设置',
  // },
  // {
  //   key: 'fyhzfyxjeJingDu',
  //   name: '费用汇总费用项金额精度设置',
  // },
  {
    key: 'zhanShi',
    name: '展示设置',
    options: [
      [
        {
          name: '分部编码展示方式',
          field: 'bdCode',
          type: 'radio',
          value: '1',
          disabled: false,
          options: [
            {
              label: '流水码（010101）',
              value: '1',
            },
            {
              label: '章节码（A.1.1）',
              value: '2',
            },
          ],
        },
      ],
      [
        {
          name: '插入定额数据时展示标准换算弹窗',
          field: 'crdezsbzhstk',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '插入定额数据时展示主要材料价格设置弹窗',
          field: 'crdezszycljgsztk',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '插入定额数据时展示定额关联子目弹窗',
          field: 'crdezsdeglzmtk',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
      ],
    ],
  },
  {
    key: 'shuRu',
    name: '输入设置',
    options: [
      [
        {
          name: '复制子目数据时，子目消耗量不变',
          field: 'fzzmsjxhlbb',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '直接输入标准子目时，弹出标准换算弹窗',
          field: 'zjsrbzzmtcbzhtk',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '通过修改子目名称完成砼/浆替换',
          field: 'xgzmmcwcgjth',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '直接输入编码时，复用当前单位工程中已有的清单或子目',
          field: 'zjsrbmfydqyyqdzm',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '输入名称，关联查询当前定额册中的子目或清单',
          field: 'srmcglcxdqdeczmqd',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '输入清单工程量回车跳到子目行',
          field: 'qdgclhctdzmh',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '输入清单后自动带出工作内容行',
          field: 'srqdhzddcgznrh',
          type: 'checkbox',
          value: false,
          disabled: true,
        },
        {
          name: '子目工程量自动等于清单工程量',
          field: 'zmgclzddyqdgcl',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '复制清单时，保留原清单编码不变',
          field: 'fzqdblyqdbmbb',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '定额工程量联动清单',
          field: 'degclldqd',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '锁定人材机消耗量',
          field: 'sdrcjxhl',
          type: 'checkbox',
          value: false,
          disabled: true,
        },
      ],
    ],
  },
  {
    key: 'qiTa',
    name: '其他设置',
    options: [
      [
        {
          name: '文件定额存储时间设置(分钟)',
          field: 'wjdeccsjsz',
          type: 'input',
          value: '5',
          disabled: true,
        },
        {
          name: '文件默认保存路径设置',
          field: 'wjmrbcljsz',
          type: 'file',
          value: '',
          disabled: true,
        },
      ],
    ],
  },
  {
    key: 'biaoZhunHuanSuan',
    name: '标准换算设置',
    options: [
      [
        {
          name: '厚度/运距换算后带出子目合并到主子目',
          field: 'hdyjhsdczmhbdzzm',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
      ],
    ],
  },
  {
    key: 'chengXianXuanXiang',
    name: '呈现选项设置',
    options: [
      [
        {
          name: '隐藏补差项',
          field: 'ycbcx',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '子目编码显示换算串',
          field: 'zmbmxshsc',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '子目名称显示换算信息',
          field: 'zmmcxshsxx',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
      ],
    ],
  },
  {
    key: 'baoBiaoXiangGuan',
    name: '报表相关设置',
    options: [
      [
        {
          name: '报表输出时，工程量为0的子目不显示',
          field: 'scgclw0zmbxs',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '报表单位工程名称显示单项名称+单位名称',
          field: 'dwgcmcxsdxmchdwmc',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
      ],
    ],
  },
  {
    key: 'diQuTeXing',
    name: '地区特性',
    options: [
      [
        {
          name: '以系数计算的措施项目和机械台班中的人工单价参与调整',
          field: 'yxsjsdcsxmhjxtbzdrgdjcytz',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '税率可编辑',
          field: 'slkbj',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
        {
          name: '除税系数是否可编辑',
          field: 'csxssfkbj',
          type: 'checkbox',
          value: true,
          disabled: true,
        },
      ],
    ],
  },
]);

/**
 * 修改设置值
 * @param key
 * @param subKey
 * @param value
 */
const setSettingValue = (key, subKey, value) => {
  const options = treeData.value.find(item => item.key === key)?.options || [];
  if (Array.isArray(options)) {
    for (let option of options) {
      let optionInfo = option.find(item => item.field === subKey);
      if (optionInfo) {
        optionInfo.value = value;
        break;
      }
    }
  }
  if (options.constructor === Object) {
    let optionInfo = options.options.find(item => item.field === subKey);
    if (optionInfo) {
      optionInfo.value = value;
    }
  }
  console.log('🌶setUpPopup.vue|645====>', treeData.value);
};
const defaultSettingAllValue = info => {
  for (let item of treeData.value) {
    let options = item?.options || [];
    for (let option of options) {
      if (Array.isArray(option)) {
        for (let opt of option) {
          if (Object.keys(info).includes(opt.field)) {
            opt.value = info[opt.field];
          }
        }
      }
      if (option.constructor === Object) {
        for (let opt of option.options) {
          if (Object.keys(info).includes(opt.field)) {
            opt.value = info[opt.field];
          }
        }
      }
    }
  }
};
let tabKey = ref(['file']);
let currentTreeInfo = ref([]);
const treeSelect = (keys, e) => {
  console.log('🌶setUpPopup.vue|262====>', keys);
  if (keys.length) {
    tabKey.value = keys;
    currentTreeInfo.value = e.node.options;
  }
};

const tabChange = key => {
  tabKey.value = key;
};

let settingValue = ref('1');
let rgfInMeasureAndRPriceInMechanicalAction = ref('1');
let mainRcjShowFlag = ref(true); // 设置主材弹框
let standardConversionShowFlag = ref(true); // 设置标准换算弹框
let deGlTcFlag = ref(false);
let globalConfigInfo = ref(null); // 获取全局设置信息
let lockRcjResQty = ref(false);
let beforeChangeParams = ref();

const onChange = (e, v) => {
  if (e.target.nodeName.toLowerCase() === 'input') {
    if (v == 'settingValue') {
      settingValue.value = settingValue.value === '1' ? '' : '1';
    } else if (v === 'deGlTcFlag') {
      deGlTcFlag.value = !deGlTcFlag.value;
    } else if (v === 'standardConversionShowFlag') {
      standardConversionShowFlag.value = !standardConversionShowFlag.value;
    } else if (v === 'mainRcjShowFlag') {
      mainRcjShowFlag.value = !mainRcjShowFlag.value;
    } else if (v === 'lockRcjResQty') {
      globalConfigInfo.value.budget.input.lockRcjResQty =
        !globalConfigInfo.value.budget.input.lockRcjResQty;
    } else {
      rgfInMeasureAndRPriceInMechanicalAction.value =
        rgfInMeasureAndRPriceInMechanicalAction.value === '1' ? '' : '1';
    }
    if (v === 'deGlTcFlag') {
      projectConvenientSetColl();
    } else if (v === 'standardConversionShowFlag') {
      standardConversionShowFlagColl();
    } else if (v === 'mainRcjShowFlag') {
      mainRcjShowFlagColl();
    } else if (v === 'lockRcjResQty') {
      resetGlobalConfig();
    } else {
      setRelateMergeScheme();
    }
  }
};

const getGlobalConfig = () => {
  api
    .getGlobalConfig({
      constructId: projectStore.currentTreeGroupInfo?.constructId,
    })
    .then(res => {
      console.log('getGlobalConfig', res);
      if (res.status === 200 && res.result) {
        globalConfigInfo.value = res.result;
      }
    });
};
const projectConvenientSetColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    column: 'deGlTcFlag',
    value: deGlTcFlag.value,
  };
  api.projectConvenientSetColl(apiData).then(res => {
    if (res.result) {
      message.success('设置成功');
      setTimeout(() => {
        location.reload();
      }, 1000);
    }
  });
};

const queryProjectConvenientSetColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  api.queryProjectConvenientSetColl(apiData).then(res => {
    console.log('查询便捷性设置', res);
    if (res.status === 200 && res.result) {
      deGlTcFlag.value = res.result.deGlTcFlag;
    }
  });
};

const setRelateMergeScheme = () => {
  const params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    projectAttrRelateMergeScheme: !!settingValue.value,
    rgfInMeasureAndRPriceInMechanicalAction:
      !!rgfInMeasureAndRPriceInMechanicalAction.value,
  };
  csProject.projectAttrRelateMergeSchemeSet(params).then(res => {
    if (res.result) {
      message.success('设置成功');
      setTimeout(() => {
        location.reload();
      }, 1000);
    } else {
      message.error('设置失败');
    }
  });
};

const resetGlobalConfig = () => {
  console.log(
    '更新参数',
    { ...JSON.parse(JSON.stringify(globalConfigInfo.value)) },
    lockRcjResQty.value
  );
  api
    .resetGlobalConfig({
      ...JSON.parse(JSON.stringify(globalConfigInfo.value)),
    })
    .then(res => {
      if (res.result) {
        message.success('设置成功');
        setTimeout(() => {
          location.reload();
        }, 1000);
      } else {
        message.error('设置失败');
      }
    });
};
const visible = ref(true);
const close = () => {
  emits('closePopup');
};
// key-value形式的所有参数
const getAllParams = () => {
  let params = {};
  for (let item of treeData.value) {
    console.log(item?.options);
    for (let option of item?.options || []) {
      let optionList = option;
      if (!Array.isArray(option)) {
        optionList = option.options || [];
      }
      for (let opt of optionList) {
        params[opt.field] = opt.value;
      }
    }
  }
  console.log('🌶setUpPopup.vue|791====>全局参数', params);
  return params;
};

// 值修改了的参数
const getChangeParams = () => {
  let params = getAllParams();
  let changeParams = {};
  for (let key in beforeChangeParams.value) {
    if (params[key] !== beforeChangeParams.value[key]) {
      changeParams[key] = params[key];
    }
  }
  return changeParams;
};
const save = () => {
  let params = getChangeParams();
  if (Object.keys(params).length > 0) {
    params.constructId = projectStore.currentTreeGroupInfo?.constructId;
    console.log('🚀 ~ save ~ params:', params);
    api.setMainSettingShow(params).then(res => {
      console.log('🚀 ~ api.setMainSettingShow ~ res:', res);
    });
  }
  close();
};
const getData = () => {
  csProject
    .getSetUp({ constructId: projectStore.currentTreeGroupInfo?.constructId })
    .then(res => {
      if (res.result) {
        console.log('返回数据', res.result);
        res.result.forEach(e => {
          switch (e.paramsTag) {
            case 'DEF_SAVE_PATH':
              DEF_SAVE_PATH.value = e.content;
              setSettingValue('qiTa', 'wjmrbcljsz', e.content);
              break;
            case 'FILE_DOWNLOAD_PATH':
              FILE_DOWNLOAD_PATH.value = e.content;
              break;
            case 'PROJECTATTR':
              projectAttrVisible.value = true;
              settingValue.value = e.content ? '1' : '';
              break;
            case 'RGFINMEASUREANDRPRICEINMECHANICALACTION':
              rgfInMeasureAndRPriceInMechanicalVisible.value = true;
              rgfInMeasureAndRPriceInMechanicalAction.value = e.content
                ? '1'
                : '';
              break;
            case 'mainRcjShowFlag':
              mainRcjShowFlag.value = e.content;
              break;
            case 'standardConversionShowFlag':
              standardConversionShowFlag.value = e.content;
              break;
          }
        });
        beforeChangeParams.value = JSON.parse(JSON.stringify(getAllParams()));
      }
    });
};

const setData = paramsTag => {
  csProject.setSetUp({ paramsTag }).then(res => {
    console.log(
      '🚀 ~ file: setUpPopup.vue:69 ~ csProject.setSetUp ~ res:',
      res
    );
    if (res.status == 200) {
      switch (res.result) {
        case 0:
          console.log('点击了取消');
          break;
        case 1:
          getData();
          break;
        default:
          break;
      }
    }
  });
};

// 设置主材
const mainRcjShowFlagColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    mainRcjShowFlag: mainRcjShowFlag.value,
  };
  api.mainRcjShowFlagColl(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('设置成功');
      setTimeout(() => {
        location.reload();
      }, 1000);
    } else {
      message.error('设置失败');
    }
  });
};

// 设置标准换算
const standardConversionShowFlagColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    standardConversionShowFlag: standardConversionShowFlag.value,
  };
  api.standardConversionShowFlagColl(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('设置成功');
      setTimeout(() => {
        location.reload();
      }, 1000);
    } else {
      message.error('设置失败');
    }
  });
};

const getMainSettingShow = () => {
  api
    .getMainSettingShow({
      constructId: projectStore.currentTreeGroupInfo?.constructId,
    })
    .then(res => {
      if (res.result) {
        defaultSettingAllValue(res.result);
        beforeChangeParams.value = JSON.parse(JSON.stringify(getAllParams()));
      }
      console.log('🚀 ~ getMainSettingShow ~ res:', res.result);
    });
};
const openModal = () => {
  getMainSettingShow();
};
getData();
queryProjectConvenientSetColl();
getGlobalConfig();
</script>

<style lang="scss" scoped>
:deep(.page-column-setting .vxe-modal--content) {
  background: #f5f5f5;
  padding: 20px !important;
}
:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  height: calc(60vh - 84px);
}
.page-setting {
  display: flex;
  height: 60vh;
  .tree-box {
    width: 160px;
    padding: 10px 0;
    border: 1px solid #e1e1e1;
    margin-right: 10px;
    background: rgba(255, 255, 255, 1);
  }
  .setting-box {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .content-box {
    flex: 1;
    padding: 12px;
    overflow: scroll;
    border: 1px solid #e1e1e1;
    background: rgba(255, 255, 255, 1);
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    height: calc(60vh - 84px);
    :deep(.ant-checkbox-wrapper) {
      margin-left: 0;
      padding: 0;
    }
  }
}
.list-content {
  padding: 0;
  .item {
    margin-bottom: 23px;
    .title {
      display: block;
      font-size: 14px;
      font-weight: 400;
      color: #2a2a2a;
      margin-bottom: 20px;
    }
    .path {
      display: flex;
      align-items: center;
      .change {
        background: rgba(255, 255, 255, 0.39);
        border: 1px solid #bfbfbf;
        opacity: 1;
        border-radius: 3px;
        font-size: 14px;
        font-weight: 400;
        color: #2a2a2a;
        outline: none;
        padding: 6px 10px;
      }
      span {
        font-size: 14px;
        color: #898989;
        margin-left: 15px;
      }
    }
  }
}
.list-box {
  margin-bottom: 10px;
  padding: 5px 10px;
  border: 1px solid #e1e1e1;
}
.list-group {
  position: relative;
  margin-top: 18px;
  padding: 15px 10px 5px 10px;
  .title {
    top: -15px;
    left: 10px;
    padding: 0 2px;
    position: absolute;
    background-color: #fff;
    font-size: 14px;
    line-height: 30px;
    color: #2a2a2a;
  }
}
.page-setting-foot {
  padding-top: 12px;
  display: flex;
  justify-content: flex-end;
}
</style>
