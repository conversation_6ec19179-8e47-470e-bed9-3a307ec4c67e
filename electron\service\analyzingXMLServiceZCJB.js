'use strict';



const {ConstructProject} = require("../model/ConstructProject");
const {Service} = require("../../core");
const {Snowflake} = require("../utils/Snowflake");
const fs = require('fs')
const xml2js = require('xml2js');
const ConstructBiddingTypeConstant = require("../enum/ConstructBiddingTypeConstant");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const BranchProjectDisplayConstant = require("../enum/BranchProjectDisplayConstant");
const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const PolicyDocumentTypeEnum = require("../enum/PolicyDocumentTypeEnum");
const {arrayToTree,treeToArray} = require("../main_editor/tree");
const {BasePolicyDocument} = require("../model/BasePolicyDocument");
const {NumberUtil} = require("../utils/NumberUtil");
const {ResponseData} = require("../utils/ResponseData");
const {MeasureProjectTable} = require("../model/MeasureProjectTable");
const {OtherProjectZygcZgj} = require("../model/OtherProjectZygcZgj");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {ItemBillProject} = require("../model/ItemBillProject");
const {OtherProjectDayWork} = require("../model/OtherProjectDayWork");
const {OtherProjectServiceCost} = require("../model/OtherProjectServiceCost");
const {OtherProjectZgj} = require("../model/OtherProjectZgj");
const {OtherProjectProvisional} = require("../model/OtherProjectProvisional");
const {OtherProject} = require("../model/OtherProject");
const {UnitProject} = require("../model/UnitProject");
const {SingleProject} = require("../model/SingleProject");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {ReadXmlUtil} = require("../utils/ReadXmlUtil");
const {getUnitFormatEnum} = require("../main_editor/rules/format");
const ConstantUtil = require("../enum/ConstantUtil");
class AnalyzingXMLServiceZCJB extends Service{
    constructor(ctx) {
        super(ctx);

        this.qbExtraTableArray = new Array();
        this.qdMap = new Map();
        this.dispNo = 1;

    }


    async  analysis(constructProject,data){
        // let 文件类别 = data.工程造价文件.$.文件类别;

        let 工程项目 = data.建设项目.$;
        let 单项工程 = data.建设项目.单项工程;
        //单项工程
        let singleProjects = new Array();

        if(ObjectUtils.isEmpty(data.建设项目.投标信息)){
            if(ObjectUtils.isEmpty(constructProject.biddingType)){
                constructProject.biddingType =ConstructBiddingTypeConstant.zbProject
            }
        }else {
            constructProject.biddingType =ConstructBiddingTypeConstant.tbProject
            return ResponseData.fail('文件类型有误，请重新选择');
        }

        let jsType ; //1 一般 0 简易

        if(工程项目.计税方式 === '一般计税'){
            jsType = 1;
        }else {
            jsType = 0;
        }
        //计税方式
        await this.service.projectTaxCalculationService.importXMLInitProjectTaxCalculation(constructProject,jsType);

        if(ObjectUtils.isEmpty(constructProject.sequenceNbr)){
            constructProject.sequenceNbr = Snowflake.nextId();
        }
        // constructProject.constructName = 工程项目.项目名称;
        constructProject.constructCode = 工程项目.项目编号;
        // constructProject.projectOverview = 工程项目.报价说明;
        // constructProject.total = 工程项目.金额;
        // constructProject.gfee = 工程项目.其中规费;
        // constructProject.safeFee = 工程项目.其中安全生产文明施工费;
        // constructProject.sbfsj = 工程项目.其中设备费及相关费用;
        // constructProject.fddbr = 工程项目.发包人法定代表人;
        // constructProject.constructionUnit = 工程项目.招投标单位名称;
        constructProject.gfId = '14';
        constructProject.awfId = '44';

        //人工费id
        let rgfPolicyDocumentList = await this.app.appDataSource.getRepository(BasePolicyDocument).find({
            where: {
                areaId:130100,
                fileType: PolicyDocumentTypeEnum.RGF.code,
            }
        });
        if (!ObjectUtils.isEmpty(rgfPolicyDocumentList)) {

            //时间倒叙排列
            rgfPolicyDocumentList.sort(function(a, b) {
                return b.fileDate.localeCompare(a.fileDate);
            });
            constructProject.rgfId=rgfPolicyDocumentList[0].sequenceNbr;
        }

        let map = this.convertConstructProjectJBXX(constructProject, data);
        // 编制说明 ---项目层级
        this.service.constructProjectService.initProjectOrUnitBZSM(1, constructProject);
        //解析单项工程
        await this.convertSingleProject(单项工程,constructProject,map);
        //放入内存
        PricingFileWriteUtils.writeToMemory(constructProject);
        return constructProject.sequenceNbr;
    }


    convertConstructProjectJBXX(constructProject, data) {
        //工程基本信息
        this.service.constructProjectService.initProjectOrUnitData(constructProject, 1);
        let constructProjectJBXX = constructProject.constructProjectJBXX;
        let 招标信息 = data.建设项目.招标信息[0].$;
        let map = new Map();

        for (let i = 0; i < constructProjectJBXX.length; i++) {
            switch (constructProjectJBXX[i].name) {
                case '工程名称':
                    constructProjectJBXX[i].remark = constructProject.constructName;
                    break;
                case '招标人(发包人)':
                    constructProjectJBXX[i].remark = 招标信息.招标人;
                    break;
                case '编制人':
                    constructProjectJBXX[i].remark = 招标信息.编制人;
                    break;
                case '编制时间':
                    constructProjectJBXX[i].remark = 招标信息.编制时间;
                    break;
                case '核对人(复核人)':
                    constructProjectJBXX[i].remark = 招标信息.复核人;
                    break;
                case '核对(复核)时间':
                    constructProjectJBXX[i].remark =招标信息.复核时间;
                    break;
                default:
                    // constructProjectJBXX[i].remark = map.get(constructProjectJBXX[i].name)
                    break;
            }

        }
        constructProject.constructProjectJBXX = constructProjectJBXX;
        return map;
    }

    /**
     * 解析单项工程
     * @param 单项工程
     * @param constructProject
     */
    async convertSingleProject(单项工程, constructProject,map) {
        if(!ObjectUtils.isObject(单项工程)){
            let singleProjects = new Array();
            for (let i = 0; i < 单项工程.length; i++) {
                let singleProject = new SingleProject();
                let model = 单项工程[i];
                let $ = model.$;
                singleProject.sequenceNbr = Snowflake.nextId();
                singleProject.constructId = constructProject.sequenceNbr;
                singleProject.projectName = $.名称;
                singleProject.total = $.金额;
                singleProject.safeFee = $.安全文明施工费;
                singleProject.gfee = $.规费;
                singleProject.sbf = $.设备费及其税金;
                //判断单项下是否还有单项
                if(model.单位工程 === undefined){
                    await this.recursionSingleProject(model.单项工程, singleProject, constructProject, map);
                }else{
                    //解析单位工程
                    await this.convertUnitProject(model.单位工程,singleProject,map,constructProject.rgfId);
                }
                singleProjects.push(singleProject);
            }
            constructProject.singleProjects = singleProjects
        }
    }

    /**
     * 递归处理子单项的问题
     * @param xmlSingleProjects
     * @param oldSingleProjects
     * @param constructProject
     * @param map
     * @returns {Promise<void>}
     */
    async recursionSingleProject(xmlSingleProjects, oldSingleProjects, constructProject, map) {
            let newSingleProjects = new Array();
            for (let i = 0; i < xmlSingleProjects.length; i++) {
                let singleProject = new SingleProject();
                let model = xmlSingleProjects[i];
                let $ = model.$;
                singleProject.sequenceNbr = Snowflake.nextId();
                singleProject.constructId = constructProject.sequenceNbr;
                singleProject.projectName = $.名称;
                singleProject.total = $.金额;
                singleProject.safeFee = $.安全文明施工费;
                singleProject.gfee = $.规费;
                singleProject.sbf = $.设备费及其税金;
                //判断单项下是否还有单项
                if(model.单位工程 === undefined){
                    await this.recursionSingleProject(model.单项工程, newSingleProjects, constructProject,map);
                }else{
                    //解析单位工程
                    await this.convertUnitProject(model.单位工程,singleProject,map,constructProject.rgfId);
                }
                newSingleProjects.push(singleProject);
            }
            oldSingleProjects.subSingleProjects = newSingleProjects;

    }

    /**
     * 解析单位工程
     * @param 单位工程
     * @param singleProject
     */
    async convertUnitProject(单位工程, singleProject,map,rgfId) {
            let unitProjects = new Array();
            for (let i = 0; i < 单位工程.length; i++) {
                let model = 单位工程[i].$;
                let unitProject = new UnitProject();
                unitProject.sequenceNbr = Snowflake.nextId();
                // unitProject.upCode = model.编码;
                unitProject.upName = model.名称;
                unitProject.uptotal = model.金额;
                unitProject.csxhj = model.措施项目费;
                unitProject.safeFee = model.安全文明施工费;
                unitProject.gfee = model.规费;
                unitProject.sbf = model.其中设备费及相关费用;
                unitProject.spId = singleProject.sequenceNbr;
                unitProject.constructId = singleProject.constructId;
                unitProject.rgfId = rgfId;
                // 添加工程基本信息 ---单位层级
                this.service.constructProjectService.initProjectOrUnitData(unitProject, 3,map);
                // 编制说明 ---单位层级
                this.service.constructProjectService.initProjectOrUnitBZSM(3, unitProject);
                //单位工程费用汇总（包含单位工程部分数据）
                await this.convertUnitProjectSummary(单位工程[i].费用汇总表,unitProject);
                //分部分项
                this.dispNo = 1;
                await this.convertItemBill(单位工程[i].分部分项,unitProject);
                this.dispNo = 1;
                //单价措施
                await this.convertMeasureTableDJ(单位工程[i].措施项目,unitProject);
                //总价措施
                await this.convertMeasureTableZJ(单位工程[i].措施项目,unitProject);

                this.qbExtraTableArray = new Array();

                //其他项目清单
                await this.convertOtherProjects(单位工程[i].其他项目[0].其他项目清单,unitProject);
                //暂列金额
                await this.convertProvisional(单位工程[i].其他项目[0].暂列金额,unitProject);
                //暂估价
                await this.convertZgjSums(单位工程[i].其他项目[0].材料暂估价,单位工程[i].其他项目[0].设备暂估价,单位工程[i].其他项目[0].专业工程暂估价,unitProject);
                //总承包服务费
                await this.convertServiceCosts(单位工程[i].其他项目[0].总承包服务费,unitProject);
                //计日工
                await this.convertDayWorks(单位工程[i].其他项目[0].计日工表, unitProject);
                //设备和材料暂估价
                await this.convertZgj(单位工程[i].其他项目, unitProject);
                //承包人
                await this.convertCbr(单位工程[i].承包人主要材料和设备表[0].承包人主要材料和设备,unitProject);

                //其他项目 签证与索赔计价表 初始化
                let otherProjectQzSpJjbList = await this.service.otherProjectService.getInitOtherProjectQzSpJjb(unitProject);
                unitProject.otherProjectQzAndSuoPeis = otherProjectQzSpJjbList;
                //保存其他项目
                unitProject.otherProjects= ObjectUtils.isNotEmpty(this.qbExtraTableArray)?this.qbExtraTableArray:unitProject.otherProjects;;
                unitProject.constructProjectRcjs = [];
                unitProject.rcjDetailList = [];
                unitProjects.push(unitProject);
            }
            singleProject.unitProjects = unitProjects;

    }

    /**
     * 费用汇总
     * @param 费用汇总表
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertUnitProjectSummary(费用汇总表, unitProject) {
        if(!ObjectUtils.isEmpty(费用汇总表)){
            let 费用项 = 费用汇总表[0].费用项;

            for (let i = 0; i < 费用项.length; i++) {
                let model = 费用项[i].$;
                switch(model.序号){
                    //分部分项工程量清单计价合计
                    case "1": {
                        unitProject.fbfxhj  = model.金额;
                        break;
                    }

                    //措施项目清单计价合计
                    case "2": {
                        unitProject.csxhj  = model.金额;
                        break;
                    }

                    //单价措施项目工程量清单计价合计
                    case "2.1": {
                        unitProject.djcsxhj = model.金额;
                        break;
                    }

                    //其他总价措施项目清单计价合计
                    case "2.2": {
                        unitProject.zjcsxhj = model.金额;
                        break;
                    }

                    //其他项目清单计价合计
                    case "3": {
                        unitProject.qtxmhj = model.金额;
                        break;
                    }

                    //规费
                    case "4": {
                        unitProject.gfee= model.金额;
                        break;
                    }

                    //安全生产、文明施工费
                    case "5": {
                        unitProject.safeFee= model.金额;
                        break;
                    }

                    //税前工程造价
                    case "6": {
                        unitProject.sqgczj= model.金额;
                        break;
                    }

                    //进项税额
                    case "6.1": {
                        unitProject.jxse= model.金额;
                        break;
                    }

                    //销项税额
                    case "7": {
                        unitProject.xxse= model.金额;
                        break;
                    }

                    //增值税应纳税额
                    case "8": {
                        unitProject.zzsynse= model.金额;
                        break;
                    }

                    //附加税费
                    case "9": {
                        unitProject.fjse= model.金额;
                        break;
                    }

                    //税金
                    case "10": {
                        unitProject.sj= model.金额;
                        break;
                    }
                    //工程造价
                    case "11": {
                        unitProject.uptotal= model.金额;
                        break;
                    }
                }
            }
        }


        
    }

    /**
     * 暂列金额
     * @param 暂列金额
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertProvisional(暂列金额, unitProject) {
        if(ObjectUtils.isEmpty(暂列金额)){
            //调用插入暂列金额默认值
            //调用插入暂列金额默认值
            unitProject.otherProjectProvisionals = await this.service.otherProjectProvisionalService.importInitProjectProvisional();
            return
        }
        if(!ObjectUtils.isEmpty(暂列金额)){
            let qtJxTotal = 0;
            let qtCsTotal = 0;

            if(!ObjectUtils.isEmpty(暂列金额[0].暂列金额明细)){
                let 暂列金额明细 = 暂列金额[0].暂列金额明细;
                let otherProjectProvisionalArray = new Array();
                for (let i = 0; i < 暂列金额明细.length; i++) {
                    let $ = 暂列金额明细[i].$;
                    let otherProjectProvisional = new OtherProjectProvisional();
                    otherProjectProvisional.sequenceNbr = Snowflake.nextId();
                    otherProjectProvisional.name = $.项目名称;
                    otherProjectProvisional.unit = $.计量单位;
                    otherProjectProvisional.provisionalSum = NumberUtil.costPriceAmountFormat($.暂定金额);
                    otherProjectProvisional.sortNo = i+1;
                    otherProjectProvisional.dispNo = $.序号;
                    otherProjectProvisional.description = $.备注;
                    otherProjectProvisional.constructId = unitProject.constructId;
                    otherProjectProvisional.spId = unitProject.spId;
                    otherProjectProvisional.unitId = unitProject.sequenceNbr;


                    otherProjectProvisional.amount = 1 ;
                    otherProjectProvisional.price = otherProjectProvisional.provisionalSum;//单价 没有单价所以直接默认赋值暂定金额
                    otherProjectProvisional.taxRemoval = 3 ; //除税系数(%)
                    // 进项合计 暂定金额*除税系数
                    otherProjectProvisional.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectProvisional.provisionalSum,otherProjectProvisional.taxRemoval/100)) ;
                    otherProjectProvisional.csPrice = NumberUtil.subtract(otherProjectProvisional.provisionalSum,otherProjectProvisional.jxTotal);
                    otherProjectProvisional.csTotal = otherProjectProvisional.csPrice;

                    qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectProvisional.jxTotal);
                    qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectProvisional.csTotal);
                    otherProjectProvisionalArray.push(otherProjectProvisional);
                }
                unitProject.otherProjectProvisionals = otherProjectProvisionalArray;
                
            }
            this.qbExtraTableArray[0].jxTotal =qtJxTotal;
            this.qbExtraTableArray[0].csTotal =qtCsTotal;

        }



    }

    /**
     * 暂估价
     * @param 暂估价
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertZgjSums(材料暂估价,设备暂估价, 专业工程暂估价,unitProject) {


            let otherProjectZgjArray =new Array();
            let model ;

            let sortNo= 0;
            if(!ObjectUtils.isEmpty(材料暂估价)){
                model = 材料暂估价[0].$;
                let otherProjectZgj = new OtherProjectZgj();
                let 暂估价材料明细 = 材料暂估价[0].暂估价材料明细;
                if(!ObjectUtils.isEmpty(暂估价材料明细)){
                    for (let i = 0; i < 暂估价材料明细.length; i++) {
                        model = 暂估价材料明细[i].$;
                        otherProjectZgj = new OtherProjectZgj();
                        otherProjectZgj.sequenceNbr = Snowflake.nextId();
                        otherProjectZgj.price   = NumberUtil.costPriceAmountFormat(model.市场价);
                        otherProjectZgj.name   = model.材料名称;
                        otherProjectZgj.attr  =model.规格型号;
                        otherProjectZgj.unit  =model.计量单位;
                        otherProjectZgj.taxRemoval  =model.除税系数;
                        otherProjectZgj.jxTotal  = NumberUtil.costPriceAmountFormat(model.进项税额合计);
                        otherProjectZgj.csPrice  = NumberUtil.costPriceAmountFormat(model.除税市场价);
                        otherProjectZgj.csTotal  = NumberUtil.costPriceAmountFormat(model.除税合价);
                        // otherProjectZgj.parentId = id;
                        otherProjectZgj.dispNo   = ''+(i+1);
                        otherProjectZgj.sortNo   = sortNo++;
                        otherProjectZgj.description = model.备注;
                        otherProjectZgj.constructId = unitProject.constructId;
                        otherProjectZgj.spId = unitProject.spId;
                        otherProjectZgj.unitId = unitProject.sequenceNbr;
                        otherProjectZgjArray.push(otherProjectZgj);
                    }
                }
            }
            unitProject.otherProjectClZgjs = otherProjectZgjArray;
            otherProjectZgjArray = new Array();
            if(!ObjectUtils.isEmpty(设备暂估价)){
                model = 设备暂估价[0].$;
                let otherProjectZgj = new OtherProjectZgj();

                let 暂估设备明细 = 设备暂估价[0].暂估设备明细;
                if(!ObjectUtils.isEmpty(暂估设备明细)){
                    for (let i = 0; i < 暂估设备明细.length; i++) {
                        model = 暂估设备明细[i].$;
                        otherProjectZgj = new OtherProjectZgj();
                        otherProjectZgj.sequenceNbr = Snowflake.nextId();
                        otherProjectZgj.price   = NumberUtil.costPriceAmountFormat(model.金额);
                        otherProjectZgj.name   = model.名称;
                        otherProjectZgj.attr  =model.规格型号;
                        otherProjectZgj.unit  =model.计量单位;
                        otherProjectZgj.taxRemoval  =model.除税系数;
                        otherProjectZgj.jxTotal  = NumberUtil.costPriceAmountFormat(model.进项税额合计);
                        otherProjectZgj.csPrice  = NumberUtil.costPriceAmountFormat(model.除税市场价);
                        otherProjectZgj.csTotal  = NumberUtil.costPriceAmountFormat(model.除税合价);
                        // otherProjectZgj.parentId = id;
                        otherProjectZgj.dispNo   = ''+(i+1);
                        otherProjectZgj.sortNo   = sortNo++;
                        otherProjectZgj.description = model.备注;
                        otherProjectZgj.constructId = unitProject.constructId;
                        otherProjectZgj.spId = unitProject.spId;
                        otherProjectZgj.unitId = unitProject.sequenceNbr;
                        otherProjectZgjArray.push(otherProjectZgj);
                    }
                }
            }
            unitProject.otherProjectSbZgjs = otherProjectZgjArray;
            otherProjectZgjArray = new Array();
            let qtJxTotal = 0;
            let qtCsTotal = 0;
            if(!ObjectUtils.isEmpty(专业工程暂估价)){
                model = 专业工程暂估价[0].$;
                let otherProjectZgj = new OtherProjectZygcZgj();
                let 专业工程暂估明细 = 专业工程暂估价[0].专业工程暂估明细;
                if(!ObjectUtils.isEmpty(专业工程暂估明细)){
                    for (let i = 0; i < 专业工程暂估明细.length; i++) {
                        model = 专业工程暂估明细[i].$;
                        otherProjectZgj = new OtherProjectZygcZgj();
                        otherProjectZgj.sequenceNbr = Snowflake.nextId();
                        otherProjectZgj.total   = NumberUtil.costPriceAmountFormat(model.金额);
                        otherProjectZgj.name   = model.工程名称;
                        otherProjectZgj.content   = model.工程内容;
                        // otherProjectZgj.unit  =model.单位;
                        // otherProjectZgj.parentId = id;
                        otherProjectZgj.dispNo   = ''+(i+1);
                        otherProjectZgj.sortNo   = sortNo++;
                        otherProjectZgj.description = model.备注;
                        otherProjectZgj.constructId = unitProject.constructId;
                        otherProjectZgj.spId = unitProject.spId;
                        otherProjectZgj.unitId = unitProject.sequenceNbr;

                        otherProjectZgj.amount = 1 ;
                        otherProjectZgj.price = otherProjectZgj.total;//单价 没有单价所以直接默认赋值暂定金额
                        otherProjectZgj.taxRemoval = 3 ; //除税系数(%)
                        // 进项合计 暂定金额*除税系数
                        otherProjectZgj.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectZgj.total,otherProjectZgj.taxRemoval/100)) ;
                        otherProjectZgj.csPrice = NumberUtil.subtract(otherProjectZgj.total,otherProjectZgj.jxTotal);
                        otherProjectZgj.csTotal = otherProjectZgj.csPrice;

                        qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectZgj.jxTotal);
                        qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectZgj.csTotal);

                        otherProjectZgjArray.push(otherProjectZgj);
                    }
                }
                unitProject.otherProjectZygcZgjs = otherProjectZgjArray;
            }
            if(ObjectUtils.isEmpty(unitProject.otherProjectZygcZgjs)){
                //调用插入专业工程额默认值
                unitProject.otherProjectZygcZgjs = await this.service.otherProjectZgjService.importInitOtherProjectZygcZgj()
            }
        this.qbExtraTableArray[1].jxTotal =qtJxTotal;
        this.qbExtraTableArray[1].csTotal =qtCsTotal;
        this.qbExtraTableArray[4].jxTotal =qtJxTotal;
        this.qbExtraTableArray[4].csTotal =qtCsTotal;

    }


    /**
     * 总承包服务费
     * 许红成说招财进宝的总承包服务费依据序号进行判断
     * 招标人另行发包专业工程 1开头
     * 招标人供应材料 2开头
     * 招标人供应设备 3开头
     * @param 总承包服务费
     * @param unitProject
     * @returns {Promise<void>}
     */
    async  convertServiceCosts(总承包服务费, unitProject) {


        if(ObjectUtils.isEmpty(总承包服务费)){
            return;
        }
        let 总承包服务费项 = 总承包服务费[0].总承包服务费项;

        if(ObjectUtils.isEmpty(总承包服务费项)){
            unitProject.otherProjectServiceCosts =await this.service.otherProjectService.getInitOtherProjectZcbfwfList();
            return;
        }
        // 根据类型分组

        let zygcList = new Array();
        let clList   = new Array();
        let sbList   = new Array();
        for (let i = 0; i < 总承包服务费项.length; i++) {
            if(总承包服务费项[i].$.序号.substring(0,2) === '1.'){
                zygcList.push(总承包服务费项[i].$);
            }else if(总承包服务费项[i].$.序号.substring(0,2) === '2.'){
                clList.push(总承包服务费项[i].$);
            }else if(总承包服务费项[i].$.序号.substring(0,2) === '3.'){
                sbList.push(总承包服务费项[i].$);
            }
        }


        unitProject.otherProjectServiceCosts = new Array();
        await this.setServiceCosts(unitProject, zygcList, '1', '招标人另行发包专业工程');
        await this.setServiceCosts(unitProject, clList, '2', '招标人供应材料');
        await this.setServiceCosts(unitProject, sbList, '3', '招标人供应设备');
        let ts = unitProject.otherProjectServiceCosts.filter(item =>item.dispNo.indexOf('.')!== -1);
        this.qbExtraTableArray[5].total = Number(ts.reduce((accumulator, item) => {
            return NumberUtil.add(accumulator,item.fwje)  ;
        }, 0).toFixed(2))
    }

    /**
     * 总承包服务费
     * @param unitProject
     * @param list
     * @param dispNo
     * @param name
     * @returns {Promise<void>}
     */
    async  setServiceCosts(unitProject, list, dispNo, name) {
        let serviceCostsArray = new Array();
        let parentsUuid = Snowflake.nextId();
        let serviceCostsParents = new OtherProjectServiceCost();
        serviceCostsParents.sequenceNbr = parentsUuid;
        serviceCostsParents.dispNo = dispNo;
        serviceCostsParents.fxName = name;
        serviceCostsParents.sortNo = 0;
        serviceCostsParents.constructId = unitProject.constructId;
        serviceCostsParents.spId = unitProject.spId;
        serviceCostsParents.unitId = unitProject.sequenceNbr;
        serviceCostsParents.dataType = 1;

        serviceCostsArray.push(serviceCostsParents);

        if (!ObjectUtils.isEmpty(list)) {
            for (let i = 0; i < list.length; i++) {
                let model = list[i];
                let uuid = Snowflake.nextId();
                let otherProjectServiceCost = new OtherProjectServiceCost();
                otherProjectServiceCost.sequenceNbr  = uuid;
                otherProjectServiceCost.xmje  = NumberUtil.costPriceAmountFormat(model.项目价值);
                otherProjectServiceCost.dispNo  = dispNo+'.'+(i+1);
                otherProjectServiceCost.fxName  = model.项目名称;
                otherProjectServiceCost.fwje  = NumberUtil.costPriceAmountFormat(model.金额);
                otherProjectServiceCost.rate  = model.费率;
                otherProjectServiceCost.parentId  = parentsUuid;
                otherProjectServiceCost.constructId = unitProject.constructId;
                otherProjectServiceCost.spId = unitProject.spId;
                otherProjectServiceCost.unitId = unitProject.sequenceNbr;
                otherProjectServiceCost.sortNo  = i + 1;
                otherProjectServiceCost.amount  = 1;
                otherProjectServiceCost.dataType = 2;
                serviceCostsArray.push(otherProjectServiceCost);
            }
        }else {
            let myNumber = 0;
            let formattedNumber = NumberUtil.costPriceAmountFormat(myNumber);
            let otherProjectServiceCost = new OtherProjectServiceCost();
            otherProjectServiceCost.sequenceNbr = Snowflake.nextId();
            otherProjectServiceCost.dispNo=dispNo+'.1';
            otherProjectServiceCost.xmje = formattedNumber;
            otherProjectServiceCost.rate= formattedNumber;
            otherProjectServiceCost.fwje = formattedNumber;
            otherProjectServiceCost.dataType =2 ;
            otherProjectServiceCost.parentId = parentsUuid;
            serviceCostsArray.push(otherProjectServiceCost);
        }
        unitProject.otherProjectServiceCosts.push(...serviceCostsArray);

    }

    /**
     * 计日工
     * @param 计日工
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertDayWorks(计日工, unitProject) {
        if (ObjectUtils.isEmpty(计日工)) {
            return;
        }
        const dayWorksArray = [];
        let sort = 0;

        let qtJxTotal = 0;
        let qtCsTotal = 0;

        for (let i = 0; i < 计日工[0].计日工标题.length; i++) {
            let 计日工标题 = 计日工[0].计日工标题[i];
            let $ = 计日工标题.$;

            let otherProjectDayWork = new OtherProjectDayWork();
            let id = Snowflake.nextId();
            otherProjectDayWork.sequenceNbr = id;
            otherProjectDayWork.worksName = $.名称;
            otherProjectDayWork.dispNo = $.序号;
            otherProjectDayWork.sortNo = sort++;
            otherProjectDayWork.constructId = unitProject.constructId;
            otherProjectDayWork.spId = unitProject.spId;
            otherProjectDayWork.unitId = unitProject.sequenceNbr;
            otherProjectDayWork.dataType = 1;
            dayWorksArray.push(otherProjectDayWork);
            if(!ObjectUtils.isEmpty(计日工标题.计日工项)){
                for (let j = 0; j < 计日工标题.计日工项.length; j++) {
                    $ = 计日工标题.计日工项[j].$;
                    otherProjectDayWork = new OtherProjectDayWork();
                    otherProjectDayWork.sequenceNbr = Snowflake.nextId();
                    otherProjectDayWork.worksName =  $.名称;
                    otherProjectDayWork.specification =  $.型号规格;
                    otherProjectDayWork.unit = $.单位;
                    otherProjectDayWork.tentativeQuantity = NumberUtil.qtxmAmountFormat($.暂定数量);


                    otherProjectDayWork.price = NumberUtil.costPriceAmountFormat($.综合单价);
                    otherProjectDayWork.total = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.tentativeQuantity, otherProjectDayWork.price));
                    otherProjectDayWork.taxRemoval = 0;
                    if(计日工标题.$.名称 === '材料'){
                        otherProjectDayWork.taxRemoval =  11.28;
                    }
                    if(计日工标题.$.名称 === '机械'){
                        otherProjectDayWork.taxRemoval =  8.66;
                    }
                    otherProjectDayWork.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.total,otherProjectDayWork.taxRemoval/100));
                    otherProjectDayWork.csPrice = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.price,(100-otherProjectDayWork.taxRemoval)/100)) ;
                    otherProjectDayWork.csTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.total,(100-otherProjectDayWork.taxRemoval)/100)) ;


                    otherProjectDayWork.dispNo = $.编号;
                    otherProjectDayWork.sortNo = sort++;
                    otherProjectDayWork.parentId = id;
                    otherProjectDayWork.constructId = unitProject.constructId;
                    otherProjectDayWork.spId = unitProject.spId;
                    otherProjectDayWork.unitId = unitProject.sequenceNbr;
                    otherProjectDayWork.dataType = 2;
                    dayWorksArray.push(otherProjectDayWork);

                    qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectDayWork.jxTotal);
                    qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectDayWork.csTotal);
                }
            }
        }
        this.qbExtraTableArray[6].jxTotal =qtJxTotal;
        this.qbExtraTableArray[6].csTotal =qtCsTotal;
        unitProject.otherProjectDayWorks = dayWorksArray;
    }

    async convertZgj(暂估价, unitProject){


        if (ObjectUtils.isEmpty(暂估价) || ObjectUtils.isEmpty(unitProject)){
            return ;
        }
        let zgjRcjList = [];
        let model1;
        let model2;
        let model;
        if (ObjectUtils.isNotEmpty(暂估价[0].材料暂估价)){
            model1 = 暂估价[0].材料暂估价[0].暂估价材料明细;
            if (!ObjectUtils.isEmpty(model1)){
                for (let model1Element of model1) {
                    model1Element.$.类型 = 2;
                }
            }
            model = model1;
        }
        if (ObjectUtils.isNotEmpty(暂估价[0].设备暂估价)){
            model2 = 暂估价[0].设备暂估价[0].暂估价设备明细;
            if (!ObjectUtils.isEmpty(model2)){
                for (let model1Element of model2) {
                    model1Element.$.类型 = 4;
                }
            }

            if (model){
                model = model1.concat(model2);
            }else {
                model = model2;
            }

        }

        if (ObjectUtils.isEmpty(model)){
            return;
        }

        for (let i = 0; i<model.length; i++){
            if (ObjectUtils.isEmpty(model[i])){
                continue;
            }
            if (!ObjectUtils.isEmpty(model[i].$.材料名称) && model[i].$.材料名称.includes("名称")){
                continue;
            }

            let zgjRcj = {};
            //主键
            //zgjRcj.sequenceNbr = Snowflake.nextId();

            //编码
            zgjRcj.materialCode = model[i].$.招标材料号;
            //名称
            zgjRcj.materialName = model[i].$.材料名称;
            //类型
            zgjRcj.kind = model[i].$.类型;
            if (ObjectUtils.isEmpty(model[i].$.类型)){
                zgjRcj.kind = -1;
            }

            //规格型号
            zgjRcj.specification = model[i].$.规格型号;
            //单位
            zgjRcj.unit = model[i].$.计量单位;
            //来源
            zgjRcj.source = "文件导入";
            //产地
            zgjRcj.producer =model[i].$.产地;
            //厂家
            zgjRcj.manufactor = model[i].$.厂家;
            //备注
            zgjRcj.remark = model[i].$.备注;
            //关联材料编码
            zgjRcj.relevancyMaterialCodeList = null;
            //锁定
            zgjRcj.lock = 0;
            //市场价
            zgjRcj.marketPrice = NumberUtil.costPriceAmountFormat(model[i].$.暂定价);
            if (ObjectUtils.isEmpty(zgjRcj.marketPrice)){
                zgjRcj.marketPrice = 0;
            }
            zgjRcj.totalNumber = NumberUtil.qtxmAmountFormat(model[i].$.数量);
            //zgjRcjList.push(zgjRcj);
            await this.service.rcjProcess.addZgjList(unitProject,zgjRcj,"文件导入");
        }
        //unitProject.zgjRcjList = zgjRcjList;

    }

    async convertCbr(cbr,unitProject){
        if (ObjectUtils.isEmpty(cbr) || ObjectUtils.isEmpty(unitProject)){
            return ;
        }

        for (let zyclsbMx of cbr) {

            if (ObjectUtils.isEmpty(zyclsbMx)){
                continue;
            }
            let cbrRcj = {};
            //编码
            cbrRcj.materialCode = zyclsbMx.$.招标材料号;
            //名称
            cbrRcj.materialName = zyclsbMx.$.名称;
            //规格型号
            cbrRcj.specification = zyclsbMx.$.规格型号;
            //单位
            cbrRcj.unit = zyclsbMx.$.单位;
            //来源
            cbrRcj.source = "文件导入";
            //合计数量
            cbrRcj.totalNumber = zyclsbMx.$.数量;
            //单价
            cbrRcj.marketPrice = zyclsbMx.$.投标单价;
            //备注
            cbrRcj.remark = zyclsbMx.$.备注;
            //基础单价
            cbrRcj.benchmarkUnitPrice = zyclsbMx.$.基准单价;
            //风险系数
            cbrRcj.riskCoefficient = zyclsbMx.$.风险系数;
            //产地
            //cbrRcj.producer = zyclsbMx.$.产地;
            //厂家
            //cbrRcj.manufactor = zyclsbMx.$.厂家;

            await this.service.rcjProcess.addCbrList(unitProject,cbrRcj,"文件导入");
        }

    }

    /**
     * 分部分项
     * @param 分部分项工程
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertItemBill(分部分项工程, unitProject) {
        let itemBillProjectArray = [];
        let itemBillProject = new ItemBillProject();
        let topId = Snowflake.nextId();
        itemBillProject.sequenceNbr = topId;
        itemBillProject.name = '单位工程';
        itemBillProject.kind = BranchProjectLevelConstant.top;
        itemBillProject.constructId = unitProject.constructId;
        itemBillProject.spId = unitProject.spId;
        itemBillProject.unitId = unitProject.sequenceNbr;
        itemBillProject.displaySign = BranchProjectDisplayConstant.open;
        itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
        itemBillProjectArray.push(itemBillProject);

        let 分部 = 分部分项工程[0].分部;
        if (ObjectUtils.isEmpty(分部)) {
            let qd = 分部分项工程[0].清单;
            if(!ObjectUtils.isEmpty(qd)){
                await this.convertItemBillQd(qd, topId, unitProject, itemBillProjectArray);
            }
            unitProject.itemBillProjects = arrayToTree(itemBillProjectArray);
            return;
        }

        let kind = BranchProjectLevelConstant.fb;
        // 递归处理子项目
        const createSubProjects = async (subProjects, parentId,kind) => {
            for (let i = 0; i < subProjects.length; i++) {
                let subProjectElement = subProjects[i];
                let $ = subProjectElement.$;
                itemBillProject = new ItemBillProject();
                let id = Snowflake.nextId();
                itemBillProject.sequenceNbr = id;
                itemBillProject.name = $.名称;
                itemBillProject.bdCode = $.编号;
                // itemBillProject.total = $.金额;
                itemBillProject.kind = kind;
                itemBillProject.parentId = parentId;
                itemBillProject.constructId = unitProject.constructId;
                itemBillProject.spId = unitProject.spId;
                itemBillProject.unitId = unitProject.sequenceNbr;
                itemBillProject.displaySign = BranchProjectDisplayConstant.open;
                itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
                itemBillProjectArray.push(itemBillProject);

                if (ObjectUtils.isEmpty(subProjectElement.清单)) {
                    await  createSubProjects(subProjectElement.分部, id,BranchProjectLevelConstant.zfb);
                } else {
                    let 清单 = subProjectElement.清单;
                    await this.convertItemBillQd(清单, id, unitProject, itemBillProjectArray);

                }
            }
        };

        await createSubProjects(分部, topId,kind);
        unitProject.itemBillProjects = arrayToTree(itemBillProjectArray);
    }


    async convertItemBillQd(qd, id, unitProject, itemBillProjectArray) {
        for (let t = 0; t < qd.length; t++) {
            let $ = qd[t].$;
            let itemBillProject = new ItemBillProject();
            itemBillProject.sequenceNbr = Snowflake.nextId();

            itemBillProject.name = $.名称;
            itemBillProject.name = $.名称;
            itemBillProject.bdCode = $.编码;
            itemBillProject.fxCode = $.编码;

            itemBillProject.unit = $.单位;
            // itemBillProject.quantity = $.数量;
            itemBillProject.quantity = NumberUtil.numberScale($.数量,getUnitFormatEnum(itemBillProject.unit).value) + "";
            //清单工程量=清单工程量表达式/单位符号前数值
            let 单位num = $.单位.replace(/[^0-9].*/ig, '') !== '' ? $.单位.replace(/[^0-9].*/ig, '') : 1;
            itemBillProject.quantityExpression = NumberUtil.multiplyToStringExEnd0(单位num, $.数量, ConstantUtil.QD_DE_DECIMAL_POINT);
            itemBillProject.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, $.数量, ConstantUtil.QD_DE_DECIMAL_POINT);
            itemBillProject.projectAttr = $.项目特征;
            itemBillProject.kind = BranchProjectLevelConstant.qd;
            itemBillProject.dispNo = (this.dispNo++) + '';
            itemBillProject.parentId = id;
            itemBillProject.constructId = unitProject.constructId;
            itemBillProject.spId = unitProject.spId;
            itemBillProject.unitId = unitProject.sequenceNbr;
            itemBillProject.displaySign = BranchProjectDisplayConstant.noSign;
            itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
            if (ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode))) {
                //如果map中没有去查数据库
                let res = await this.service.baseListService.queryQdByCode(itemBillProject.fxCode);
                if (!ObjectUtils.isEmpty(res)) {
                    this.qdMap.set(itemBillProject.fxCode, res)
                }
            }
            itemBillProject.standardId = !ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode)) ? this.qdMap.get(itemBillProject.fxCode).sequenceNbr : '';
            itemBillProject.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode)) ? 0 : 1;
            itemBillProjectArray.push(itemBillProject);
        }

    }

    /**
     * 单价措施项目（目前只处理单价措施清单）
     * @param 措施项目
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertMeasureTableDJ(措施项目, unitProject) {
        if(ObjectUtils.isEmpty(措施项目)){
            return ;
        }
        let 措施标题 = 措施项目[0].措施项目标题;
        if(!ObjectUtils.isEmpty(措施标题)){
            for (let i = 0; i < 措施标题.length; i++) {
                let 措施标题Element = 措施标题[i];

                let $ = 措施标题Element.$;
                if($.名称==='单价措施项目'){
                    let djMeasureProjectTableArray = new Array();

                    let 单价措施标题 = 措施标题Element.措施项目标题;
                    let dispNo =1;
                    if(!ObjectUtils.isEmpty(单价措施标题)){
                        //遍历标题
                        for (let j = 0; j < 单价措施标题.length; j++) {
                            $ = 单价措施标题[j].$;
                            //存放标题
                            let measureProjectTableBt = new MeasureProjectTable();
                            let btId = Snowflake.nextId();
                            measureProjectTableBt.sequenceNbr = btId;
                            // measureProjectTableBt.fxCode = $.编码;
                            measureProjectTableBt.name = $.名称;
                            // measureProjectTableBt.total = $.金额;
                            measureProjectTableBt.constructId = unitProject.constructId;
                            measureProjectTableBt.spId = unitProject.spId;
                            measureProjectTableBt.unitId = unitProject.sequenceNbr;
                            measureProjectTableBt.kind = BranchProjectLevelConstant.zfb;
                            measureProjectTableBt.displaySign = BranchProjectDisplayConstant.open;
                            measureProjectTableBt.displayStatu = BranchProjectDisplayConstant.displayMax;
                            measureProjectTableBt.adjustmentCoefficient = 1;
                            djMeasureProjectTableArray.push(measureProjectTableBt)
                            //存放标题下的清单
                            let 措施项目计价表 = 单价措施标题[j].措施项目计价表;
                            if(!ObjectUtils.isEmpty(措施项目计价表)){
                                for (let k = 0; k < 措施项目计价表.length; k++) {
                                    let $ = 措施项目计价表[k].$ ;
                                    let measureProjectTable = new MeasureProjectTable();
                                    measureProjectTable.sequenceNbr =Snowflake.nextId();
                                    measureProjectTable.dispNo = (dispNo++)+'';

                                    measureProjectTable.name = $.名称;
                                    measureProjectTable.name = $.名称;
                                    measureProjectTable.bdCode = $.编码;
                                    measureProjectTable.fxCode = $.编码;

                                    measureProjectTable.projectAttr = $.项目特征;
                                    measureProjectTable.unit = $.单位;
                                    // measureProjectTable.quantity = $.数量;
                                    measureProjectTable.quantity = NumberUtil.numberScale($.数量,getUnitFormatEnum(measureProjectTable.unit).value);
                                    let 单位num = $.单位.replace(/[^0-9].*/ig,'')!==''?$.单位.replace(/[^0-9].*/ig,''): 1;
                                    measureProjectTable.quantityExpression =NumberUtil.multiplyToStringExEnd0(单位num, $.数量,ConstantUtil.QD_DE_DECIMAL_POINT);
                                    measureProjectTable.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, $.数量,ConstantUtil.QD_DE_DECIMAL_POINT);
                                    measureProjectTable.kind = BranchProjectLevelConstant.qd;
                                    //单价措施类型
                                    measureProjectTable.parentId =btId;
                                    measureProjectTable.constructId = unitProject.constructId;
                                    measureProjectTable.spId = unitProject.spId;
                                    measureProjectTable.unitId = unitProject.sequenceNbr;
                                    measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                                    measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                                    measureProjectTable.adjustmentCoefficient = 1;
                                    if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                        //如果map中没有去查数据库
                                        let res =await this.service.baseListService.queryQdByCode(measureProjectTable.fxCode);
                                        if(!ObjectUtils.isEmpty(res)){
                                            this.qdMap.set(measureProjectTable.fxCode,res)
                                        }
                                    }
                                    if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                        if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                                            measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                                        }
                                    }
                                    measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr:'';
                                    measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                                    djMeasureProjectTableArray.push(measureProjectTable);
                                }
                            }
                        }
                    }

                    unitProject.measureProjectTables = djMeasureProjectTableArray;
                    unitProject.djMeasureProjectTableArray = djMeasureProjectTableArray;
                }
            }
        }
    }

    /**
     *
     * @param 总价措施措施项目
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertMeasureTableZJ(措施项目, unitProject) {
        if(ObjectUtils.isEmpty(措施项目)){
            return ;
        }
        let 措施项目标题 = 措施项目[0].措施项目标题;
        if(!ObjectUtils.isEmpty(措施项目标题)){
            for (let i = 0; i < 措施项目标题.length; i++) {
                let 措施标题Element = 措施项目标题[i];
                let $ = 措施标题Element.$;
                if($.名称==='其他总价措施项目'){
                    let 总价措施标题 = 措施标题Element.措施项目计价表;
                    if(!ObjectUtils.isEmpty(总价措施标题)){
                        let zjMeasureProjectTableArray = new Array();
                        for (let j = 0; j < 总价措施标题.length; j++) {
                            let 总价措施标题Element = 总价措施标题[j];
                            $ = 总价措施标题Element.$;

                            let measureProjectTable = new MeasureProjectTable();
                            measureProjectTable.sequenceNbr =Snowflake.nextId();
                            measureProjectTable.dispNo = (j+1)+'';

                            measureProjectTable.name = $.名称;
                            measureProjectTable.name = $.名称;
                            measureProjectTable.bdCode = $.编号;
                            measureProjectTable.fxCode = $.编号;


                            measureProjectTable.projectAttr = $.项目特征;
                            measureProjectTable.unit = '项';
                            // measureProjectTable.quantity = $.数量;
                            measureProjectTable.quantity = NumberUtil.numberScale($.数量,getUnitFormatEnum(measureProjectTable.unit).value);
                            let 单位num = $.单位.replace(/[^0-9].*/ig,'')!==''?$.单位.replace(/[^0-9].*/ig,''): 1;
                            measureProjectTable.quantityExpression =NumberUtil.multiplyToStringExEnd0(单位num, $.数量,ConstantUtil.QD_DE_DECIMAL_POINT);
                            measureProjectTable.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, $.数量,ConstantUtil.QD_DE_DECIMAL_POINT);
                            measureProjectTable.quantityExpression = '1';
                            measureProjectTable.quantityExpressionNbr = '1' ;
                            measureProjectTable.kind = BranchProjectLevelConstant.qd;
                            measureProjectTable.constructId = unitProject.constructId;
                            measureProjectTable.spId = unitProject.spId;
                            measureProjectTable.unitId = unitProject.sequenceNbr;
                            measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                            measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                            measureProjectTable.adjustmentCoefficient = 1;
                            if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                //如果map中没有去查数据库
                                let res =await this.service.baseListService.queryQdByCode(measureProjectTable.fxCode);
                                if(!ObjectUtils.isEmpty(res)){
                                    this.qdMap.set(measureProjectTable.fxCode,res)
                                }
                            }
                            if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                                    measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                                }
                            }
                            measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr:'';
                            measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                            zjMeasureProjectTableArray.push(measureProjectTable);
                        }
                        unitProject.measureProjectTables = zjMeasureProjectTableArray;
                        unitProject.zjMeasureProjectTableArray = zjMeasureProjectTableArray;
                    }
                }
            }
        }
    }

    async convertOtherProjects(其他项目清单, unitProject) {

        if(!ObjectUtils.isEmpty(其他项目清单)){
            for (let i = 0; i < 其他项目清单.length; i++) {
                let model = 其他项目清单[i].$;
                let otherProject = new OtherProject();
                otherProject.sequenceNbr = Snowflake.nextId();
                otherProject.constructId = unitProject.constructId;
                otherProject.spId = unitProject.spId;
                otherProject.unitId = unitProject.sequenceNbr;
                otherProject.sortNo = 1;
                otherProject.extraName = model.名称;
                otherProject.dispNo = model.序号;
                otherProject.total = model.金额;
                otherProject.unit = model.单位;
                otherProject.description = model.备注;
                if('暂列金额' === model.名称){
                    otherProject.type = OtherProjectCalculationBaseConstant.zljr;
                    otherProject.markSj = 1;
                    otherProject.markSafa = 1;
                }else if('暂估价' === model.名称){
                    otherProject.markSj = 1;
                    otherProject.markSafa = 1;
                }else if('专业工程暂估价' === model.名称){
                    otherProject.type = OtherProjectCalculationBaseConstant.zygczgj;
                    otherProject.markSj = 1;
                    // otherProject.markSafa = 1;
                }else if('总承包服务费' === model.名称){
                    otherProject.type = OtherProjectCalculationBaseConstant.zcbfwf;
                    otherProject.markSj = 1;
                    otherProject.markSafa = 1;
                }else if('计日工' === model.名称){
                    otherProject.type = OtherProjectCalculationBaseConstant.jrg;
                    otherProject.markSj = 1;
                    otherProject.markSafa = 1;
                }else {
                    otherProject.markSj = 0;
                    otherProject.markSafa = 0;
                }
                this.qbExtraTableArray.push(otherProject);
            }
        }

    }
}


AnalyzingXMLServiceZCJB.toString = () => '[class AnalyzingXMLServiceZCJB]';
module.exports = AnalyzingXMLServiceZCJB;