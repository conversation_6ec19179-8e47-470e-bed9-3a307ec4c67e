const _ = require('lodash');

class ObjectUtils{


    constructor() {
        function isType(type) {
            return function(obj) {
                return Object.prototype.toString.call(obj) === "[object " + type + "]";
            };
        }
        this.isObject = isType("Object");
        this.isString = isType("String")
        this.isArray = Array.isArray || isType("Array")
        this.isFunction = isType("Function")
        this.isUndefined = isType("Undefined")
        this.isNull = isType("Null")
        this.isNumber = isType("Number")
    }

    isEmpty(obj){
        return this.isNull(obj)
            || this.isUndefined(obj)
            || (this.isArray(obj) && obj.length === 0)
            || (this.isString(obj) && obj === '')
    }

    isNotEmpty(obj){
        return !this.isEmpty(obj);
    }

    is_Undefined(obj){
        return this.isUndefined(obj)

    }

    toJsonString(obj){
        return JSON.stringify(obj,(key, value) => typeof value === 'undefined' ? null : value);
    }


    updatePropertyValue(obj, propertyKey, newValue) {
        for (let key in obj) {
            if (key == "parent" || key == "prev" || key == "next") {
                continue;
            }
            if (typeof obj[key] === 'object') {
                if (Array.isArray(obj[key])) {
                    // 如果属性的值是数组，则循环遍历数组并递归调用更新函数
                    obj[key].forEach((item) => {
                        this.updatePropertyValue(item, propertyKey, newValue);
                    });
                } else {
                    // 如果属性的值是对象，则递归调用更新函数
                    this.updatePropertyValue(obj[key], propertyKey, newValue);
                }
            } else if (key === propertyKey && !this.isEmpty(obj[key])) {
                // 如果属性的键等于目标属性键，并且属性具有值，则更新属性的值
                obj[key] = newValue;
            }
        }
    }

     compareJSON(json1, json2) {
        var obj1 = JSON.parse(json1);
        var obj2 = JSON.parse(json2);

        return this.deepEqual(obj1, obj2);
    }

     deepEqual(obj1, obj2) {

         obj1 = obj1 === undefined ? null :obj1;
         obj2 = obj2 === undefined ? null :obj2;

        if (obj1 === obj2) {
            return true;
        }
        if (typeof obj1 !== 'object' || typeof obj2 !== 'object' || obj1 === null || obj2 === null) {
            return false;
        }

        var keys1 = Object.keys(obj1);
        var keys2 = Object.keys(obj2);

        if (keys1.length !== keys2.length) {
            return false;
        }

        for (var key of keys1) {
            if (!keys2.includes(key) || !this.deepEqual(obj1[key], obj2[key])) {
                return false;
            }
        }

        return true;
    }


  isNumberStr(numStr) {
    return !isNaN(parseFloat(numStr));
  }


  cloneDeep(obj){
    return _.cloneDeep(obj);
  }

}

module.exports = {
    ObjectUtils: new ObjectUtils()
}
