const {Controller} = require("../../core");
const {BaseRcj} = require("../model/BaseRcj");
const {ResponseData} = require("../utils/ResponseData");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {NumberUtil} = require("../utils/NumberUtil");
const TypConstant = require("../rcj_handle/TypConstant");
const RcjDeleteStrategy = require("../rcj_handle/remove/removeRcjStrategy");
const FindRcjStrategy = require("../rcj_handle/find/findRcjStrategy");
const InsertRcjStrategy = require("../rcj_handle/insert/insertRcjStrategy");
const RcjInsertOptimizeStrategy = require("../rcj_new/insert/rcjInsertOptimizeStrategy");
const {EditAreaSupplementDe} = require("../rcj_handle/insert/editArea/EditAreaRcjHandler");

/**
 * 人材机 controller
 */
class RcjController extends Controller {


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    baseRcjService = this.service.baseRcjService;
    rcjProcess = this.service.rcjProcess;


    /**
     * 根据类型查询人材机数据 -人材机汇总
     * @param args type   数据类型 1 工程项目层级  2 单项工程 3单位工程
     * @param args kind   人材机类型 0所有人才机  1：人工；2：材料；3：机械；4：设备；5：主材； 6 预拌混凝土
     * @param args constructId   工程项目id
     * @param args singleId 单项工程id
     * @param args unitId 单位工程id
     */
    queryConstructRcjByDeId(args){
        let {type, kind, constructId, singleId,unitId} = args;
        const result = this.rcjProcess.queryConstructRcjByDeIdNew(type, kind, constructId, singleId,unitId,args);
        return ResponseData.success(result);
    }


    /**
     * 单位工程人材机修改
     * @param args
     * @returns {ResponseData}
     */
    async changeRcj(args){

        try {
            const result = await this.rcjProcess.changeRcjNew(args);


            await this.service.management.sycnTrigger("unitDeChange");
            await this.service.management.trigger("itemChange");

            return ResponseData.success(result);
        } catch (e) {
            return ResponseData.fail("操作失败");
        }
    }

    /**
     * 单项工程人材机修改
     * @param args
     * @returns {ResponseData}
     */
    async changeRcjSingleProject(args){

        try {
            const result = await this.rcjProcess.changeRcjSingleProject(args);
            return ResponseData.success(result);
        } catch (e) {
            return ResponseData.fail("操作失败");
        }
    }

    /**
     * 工程项目人材机修改
     * @param args
     * @returns {ResponseData}
     */
    async changeRcjConstructProject(args){

        try {
            const result = await this.rcjProcess.changeRcjConstructProject(args);
            return ResponseData.success(result);
        } catch (e) {
            return ResponseData.fail("操作失败");
        }
    }

    /**
     * 人材机索引-人材机目录树
     * @param libraryCode 清单册code
     * @returns {Promise<*>}
     */
    async listTree(args) {
        /*// 自测参数
        libraryCode = "2012-JZGC-DEY";*/

        // 原12定额标准逻辑 const result = await this.baseRcjService.listTreeByLibraryCode(libraryCode);

        // 22定额标准逻辑，兼容12
        const result = await this.rcjProcess.listTreeByLibraryCode(args);

        return ResponseData.success(result);
    }

    /**
     * 批量删除人材机
     * @param libraryCode
     */
    async batchDeleteRcj(args) {
        await this.rcjProcess.batchDeleteRcj(args);
        return ResponseData.success(true);
    }

    /**
     * 获取补充定额下的人材机数据
     * @param args
     * @return {Promise<ResponseData>}
     */
    async getSupplementDeByRcj(args){
        return ResponseData.success(await this.rcjProcess.getSupplementDeByRcj(args));
    }


    /**
     * 人材机索引-人材机模糊搜索
     * @param baseRcjModel 材料名称（模糊搜索）、定额册编码、level1~7
     * @return {BaseRcj[]|BaseRcj2022[]}
     */
    async listLike(params) {
        let {baseRcj, page, limit,taxMade,constructId, singleId, unitId} = params;
        /*// 自测参数
        baseRcj = new BaseRcj();
        baseRcj.libraryCode = "2012-JZGC-DEY";
        //baseRcjModel.level1 = "";
        //baseRcj.materialName = "盲板";*/

        page = ObjectUtils.isEmpty(page) ? 1 : page;
        limit = ObjectUtils.isEmpty(limit) ? 10000 : limit;

        // 原12定额标准逻辑 const result = await this.baseRcjService.listLike(baseRcj, page, limit);

        // 22定额标准逻辑，兼容12
        const result = await this.rcjProcess.listLike(baseRcj, page, limit,taxMade,constructId, singleId, unitId);
        await this.rcjProcess.rcjType(result.list);
        return ResponseData.success(result);
    }

    /**
     * 人材机索引-人材机模糊搜索
     * @param baseRcjModel 材料名称（模糊搜索）、定额册编码、level1~7
     * @return {Promise<BaseRcj[]>}
     */
    async listLike2(params) {
        let {baseRcj, page, limit,taxMade,constructId, singleId, unitId} = params;
        page = ObjectUtils.isEmpty(page) ? 1 : page;
        limit = ObjectUtils.isEmpty(limit) ? 10000 : limit;

        // 原12定额标准逻辑 const result = await this.baseRcjService.listLike2(baseRcj, page, limit);

        // 22定额标准逻辑，兼容12
        const result = await this.rcjProcess.listLikeZxz(baseRcj, page, limit,taxMade,constructId, singleId, unitId);
        await this.rcjProcess.rcjType(result.list);
        return ResponseData.success(result);
    }





    /**
     * 插入，将base人材机添加到分部分项定额下
     *    todo 此接口优化后已不再使用
     * @param fbFxDeId
     * @param baseRcjModel
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {Promise<string>} 人材机id
     */
    async addRcjData(args) {
        let {fbFxDeId, baseRcjModel, constructId, singleId, unitId} = args;

        const result = await this.rcjProcess.addRcjData(fbFxDeId, baseRcjModel, constructId, singleId, unitId);

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");

        return ResponseData.success(result);
    }

    // 此接口应在分部分项添加定额时调用，这里是做自测
    // 批量插入，根据定额id查人材机，将人材机加到分部分项定额下
    async batchSaveRcjData(args) {
        let {deId, fbFxDeId, constructId, singleId, unitId} = args;

        /*// 自测参数
        deId="1565640928113332240";
        fbFxDeId = "fbFxDeId";
        global.constructProject = [];
        global.constructProject[1] = {"proJectData":{"biddingType":"0","constructName":"ysb测试项目","fileCode":"1660814140593278976","path":"C:\\Users\\<USER>\\.xilidata\\ysb测试项目.ysf","singleProjects":[{"sequenceNbr":"1","unitProjects":[{"itemBillProjects":[{"bdName":"单位工程","kind":"0","sequenceNbr":"1659383061407444994","unitId":"1658367326819016727"},{"bdName":"分部","kind":"01","parentId":"1659383061407444994","sequenceNbr":"1659384089636773889","unitId":"1658367326819016727"},{"bdName":"子分部","kind":"02","parentId":"1659384089636773889","sequenceNbr":"1659384255106260994","unitId":"1658367326819016727"},{"bdName":"清单1","kind":"03","parentId":"1659384255106260994","sequenceNbr":"1660579763674288128","unitId":"1658367326819016727"},{"bdName":"定额1","kind":"04","parentId":"1660579763674288128","sequenceNbr":"1660579957178503168","unitId":"1658367326819016727"},{"bdName":"清单2","kind":"03","parentId":"1659384255106260994","sequenceNbr":"1660580315070074880","unitId":"1658367326819016727"},{"bdName":"定额1","kind":"04","parentId":"1660580315070074880","sequenceNbr":"1660581320901922816","unitId":"1658367326819016727"},{"bdName":"定额2","kind":"04","parentId":"1660580315070074880","sequenceNbr":"1660581517463785472","unitId":"1658367326819016727"}],"sequenceNbr":"1658367326819016727"}]}]}};
        constructId = "1";
        singleId = "1";
        unitId = "1658367326819016727";*/


        const result = await this.rcjProcess.batchSaveRcjData(deId, fbFxDeId, constructId, singleId, unitId);
        await this.service.unitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
        });
        return ResponseData.success(result);
    }

    /**
     * 删除人材机及其下配比明细
     * @param rcjId 分部分项定额下的人材机id
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {boolean}
     */
    async delConstructRcj(args) {

        let {rcjId, constructId, singleId, unitId} = args;

        /* // 自测参数
        rcjId = "cpRcjId";
        constructId = "1";
        singleId = "1";
        unitId = "1658367326819016727";*/
        // args.sequenceNbr = rcjId;
        // args.type = 2;
        // let rcjDeleteStrategy = new RcjDeleteStrategy(constructId, singleId, unitId,PricingFileFindUtils.getProjectObjById(constructId),null);
        // rcjDeleteStrategy.execute(args);
        // return ResponseData.success(true);

        //const result = this.rcjProcess.delRcjAndRcjDetail(rcjId, constructId, singleId, unitId);

        // await this.service.management.sycnTrigger("unitDeChange");
        // await this.service.management.trigger("itemChange");
        //
        // return ResponseData.success(result);
    }


    /**
     * 删除人材机 配比
     * @param args
     * @returns {ResponseData}
     */
    async delDetail(args) {
        let {sequenceNbr, constructId, singleId, unitId,de} = args;
        args.type = 2;
        let rcjDeleteStrategy = new RcjDeleteStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
        await rcjDeleteStrategy.execute({sequenceNbr:sequenceNbr,type:2,tempDeleteFlag:args.tempDeleteFlag,de:de,rcjSource:1});
        await this.service.autoCostMathService.autoCostMath({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        });
        //重新计算费用汇总
        await this.service.unitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
        });
        this.service.conversionDeService.saveDelRcjRuleIds({rcjId: sequenceNbr, deId: de.sequenceNbr})
        return ResponseData.success(true);



        // const result = await this.rcjProcess.delDetail(args);
        //
        // await this.service.management.sycnTrigger("unitDeChange");
        // await this.service.management.trigger("itemChange");
        // return ResponseData.success(result);
    }

    /**
     * 添加人材机 明细中的配比材料
     * @param args
     * @returns {ResponseData}
     */
    async addDetail(args) {
        let {constructId,singleId,unitId,de} = args;
        args.rcjDetail.resQty = 0;
        let insertRcjStrategy = new InsertRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId)});

        let newRcj = await insertRcjStrategy.execute({de:de,pointLine:args.pointLine,rcj:args.rcjDetail});

        //await new RcjInsertOptimizeStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId)}).execute({de: de, pointLine: args.pointLine, rcj: args.rcjDetail})
        //处理人材机换算信息
        this.service.rcjProcess.updateRcjSyncDeConversionInfo(constructId, singleId, unitId,de.sequenceNbr,newRcj,"add",null,null,1);
        await this.service.autoCostMathService.autoCostMath({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        });
        await this.service.unitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        });
        return ResponseData.success(newRcj);



        // const result =await this.rcjProcess.addDetail(args);
        //
        // await this.service.management.sycnTrigger("unitDeChange");
        // await this.service.management.trigger("itemChange");
        //
        // return ResponseData.success(result);
    }

    /**
     * 明细区补充人材机
     * @param args
     * @return {Promise<ResponseData>}
     */
    async detailAreaSupplementRcj(args) {
        let {constructId,singleId,unitId,de} = args;
        let insertRcjStrategy = new InsertRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
        let newRcj = await insertRcjStrategy.execute({de:de,pointLine:args.pointLine,rcj:args.rcjDetail});
        //处理人材机换算信息
        this.service.rcjProcess.updateRcjSyncDeConversionInfo(constructId, singleId, unitId,de.sequenceNbr,newRcj,"add",null,null,1);
        return ResponseData.success(newRcj);



        // const result =await this.rcjProcess.addDetail(args);
        //
        // await this.service.management.sycnTrigger("unitDeChange");
        // await this.service.management.trigger("itemChange");
        //
        // return ResponseData.success(result);
    }





    /**
     * 人材机 明细区 数据编辑
     * @param args
     * @returns {ResponseData}
     */
    async updateConstructRcj(args){
        const result = await this.service.constructProjectRcjService.updateConstructAndDetailRcj(args);

        await this.service.autoCostMathService.autoCostMath({
            constructId: args.constructId,
            singleId: args.singleId,
            unitId: args.unitId
        });

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");

        return ResponseData.success(result);
    }


    /**
     * 添加定额批量修改主材价格
     * @param args
     * @returns {ResponseData}
     */
    async updateConstructRcjZcList(args){
        const result = await this.service.constructProjectRcjService.rcjZcChangePrice(args);

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");

        return ResponseData.success(result);
    }

    /**
     * 分部分项/措施项目 人材机明细列表
     * @param id 分部分项/措施项目id
     * @param branchType 1分部分项、2措施项目
     * @param constructId
     * @param singleId
     * @param unitId
     * @return {ConstructProjectRcj[]}
     */
    queryRcjDataByDeId(args) {

        let {branchType, id, constructId, singleId, unitId} = args;
        let allData = PricingFileFindUtils.getUnit(constructId, singleId, unitId)[branchType == 1 ? "itemBillProjects" : "measureProjectTables"];
        if (!allData.getNodeById(id)) {
            return [];
        }
        /*// 自测参数
        branchType = 1;
        id = "fbFxDeId";
        constructId = "1";
        singleId = "1";
        unitId = "1658367326819016727";*/

        let findRcjStrategy = new FindRcjStrategy({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId)});
        args.pageType = branchType == 1?"fbfx":"csxm"
        let res = findRcjStrategy.execute(args);
        res.forEach(e => this.service.itemBillProjectOptionService.dataDeal(e,2));
        return ResponseData.success(res);
        // let result = this.rcjProcess.listRcjData(branchType, id, constructId, singleId, unitId);
        // if (result && result.length > 0) {
        //     let all = [];
        //     all = all.concat(PricingFileFindUtils.getFbFx(constructId, singleId, unitId)).concat(PricingFileFindUtils.getCSXM(constructId, singleId, unitId));
        //     let qdLine = this._findLineById(all, id);
        //     if (qdLine.rcjFlag === 1 && result[0].rcjDetailsDTOs /*&& result[0].rcjDetailsDTOs.length > 0*/) {
        //         result = result[0].rcjDetailsDTOs;
        //     }
        // }
        // if (result && result.length>0) {
        //     for (let i = 0 ; i < result.length ; ++i) {
        //         if (result[i].resQty && result[i].resQty != "") {
        //             result[i].resQty = NumberUtil.numberScale(Number(result[i].resQty), 6);
        //         }
        //         if (result[i].totalNumber && result[i].totalNumber != "") {
        //             result[i].totalNumber = NumberUtil.numberScale(Number(result[i].totalNumber), 4);
        //         }
        //     }
        // }
        // result.sort((r1, r2) => Number.parseInt(r1.kind) - Number.parseInt(r2.kind));
        // return ResponseData.success(result);
    }







    /**
     * 单位工程项目 人材机汇总 人材机替换
     * @param args
     * @returns {ResponseData}
     */
    async replaceRcjToUnit(args){

        const result = await this.rcjProcess.replaceRcjToUnit(args);

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");

        return ResponseData.success(result);
    }

    /**
     * 单位工程 人材机汇总 导入 Excel 修改人材机汇总 市场价
     * @param args
     * @returns {Promise<void>}
     */
    async useUnitExcelRcjPrice(args){

        const result = await this.rcjProcess.useUnitExcelRcjPrice(args);

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");

        return ResponseData.success(result);

    }

    /**
     * 工程项目 人材机汇总 导入 Excel 修改人材机汇总 市场价
     * @param args
     * @returns {Promise<void>}
     */
    async useConstructExcelRcjPrice(args){

        const result = await this.rcjProcess.useConstructExcelRcjPrice(args);
        return ResponseData.success(result);

    }

    /**
     * 单项工程 人材机汇总 导入 Excel 修改人材机汇总 市场价
     * @param args
     * @returns {Promise<void>}
     */
    async useSingleExcelRcjPrice(args){

        const result = await this.rcjProcess.useSingleExcelRcjPrice(args);
        return ResponseData.success(result);

    }


    /**
     * 单位工程调整市场价系数
     * @param args
     * @returns {Promise<void>}
     */
    async unitAdjustmentCoefficient(args){

        const result = await this.rcjProcess.unitAdjustmentCoefficient(args);

        await this.service.management.sycnTrigger("unitDeChange");
        await this.service.management.trigger("itemChange");
        return ResponseData.success(result);

    }


    /**
     * 工程项目 调整市场价系数
     * @param args
     * @returns {Promise<void>}
     */
    async constructAdjustmentCoefficient(args){

        const result = await this.rcjProcess.constructAdjustmentCoefficient(args);
        return ResponseData.success(result);
    }


    /**
     * 工程项目人材机汇总 数据来源分析
     * @param args
     * @returns {Promise<void>}
     */
    async rcjFromUnit(args){

        const result = await this.rcjProcess.rcjFromUnit(args);
        return ResponseData.success(result);
    }

    /**
     * 单项工程人材机汇总 数据来源分析
     * @param args
     * @returns {Promise<void>}
     */
    async singleRcjFromUnit(args){

        const result = await this.rcjProcess.singleRcjFromUnit(args);
        return ResponseData.success(result);
    }

    /**
     * 工程项目人材机汇总 数据来源查询 后修改
     * @param args
     * @returns {Promise<void>}
     */
    async rcjFromUnitUpdate(args){
        const result = await this.rcjProcess.rcjFromUnitUpdate(args);
        return ResponseData.success(result);
    }


    /**
     * 下载人材机汇总模板
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async downloadExcelRcjTotal(args){
        const result = await this.rcjProcess.downloadExcelRcjTotal(args);
        return ResponseData.success(result);
    }



    /**
     * 合并人材机查询 单位工程人材机汇总
     * @returns {Promise<ResponseData>}
     */
    async rcjUnitHbQuery(args){
        const result = await this.rcjProcess.rcjUnitHbQuery(args);
        return ResponseData.success(result);
    }


    /**
     * 合并人材机应用   单位工程人材机汇总
     * @returns {Promise<ResponseData>}
     */
    async rcjUnitUseHb(args){
        const result = await this.rcjProcess.rcjUnitUseHb(args);
        return ResponseData.success(result);
    }

    /**
     * 单位工程人材机汇总  取消排序
     * @returns {Promise<ResponseData>}
     */
    async unitRcjCancelSort(args){
        const result = await this.rcjProcess.unitRcjCancelSort(args);
        return ResponseData.success(result);
    }

    /**
     * 获取人材机汇总 排序状态
     * @param args
     * @returns {Promise<void>}
     */
    async getSortState(args){
        const result = await this.rcjProcess.getSortState(args);
        return ResponseData.success(result);
    }

    /**
     * 单项工程人材机汇总  取消排序
     * @returns {Promise<ResponseData>}
     */
    async singleRcjCancelSort(args){
        const result = await this.rcjProcess.singleRcjCancelSort(args);
        return ResponseData.success(result);
    }

    /**
     * 工程项目 人材机汇总  取消排序
     * @returns {Promise<ResponseData>}
     */
    async constructRcjCancelSort(args){
        const result = await this.rcjProcess.constructRcjCancelSort(args);
        return ResponseData.success(result);
    }

    /**
     * 单位工程人材机汇总 暂估价 编辑
     * @param args
     * @returns {Promise<void>}
     */
    async updateZgjRcj(args){

        const result = await this.rcjProcess.updateZgjRcj(args);
        return ResponseData.success(result);
    }

    /**
     * 单位工程人材机汇总 暂估价 新增
     * @param args
     * @returns {Promise<void>}
     */
    async addZgjRcj(args){

        const result = await this.rcjProcess.addZgjRcj(args);
        return ResponseData.success(result);
    }

    /**
     * 单位工程人材机汇总 暂估价 删除
     * @param args
     * @returns {Promise<void>}
     */
    async deleteZgjRcj(args){

        const result = await this.rcjProcess.deleteZgjRcj(args);
        return ResponseData.success(result);
    }

    /**
     * 查询 暂估材料关联明细
     * @param args
     * @returns {Promise<void>}
     */
    async selectZgjRelevancyRcj(args){
        const result = await this.rcjProcess.selectZgjRelevancyRcj(args);
        return ResponseData.success(result);
    }

    /**
     * 勾选暂估价关联明细
     * @param args
     * @returns {Promise<void>}
     */
    async rcjToZgjRcj(args){
        const result = await this.rcjProcess.rcjToZgjRcj(args);
        return ResponseData.success(result);
    }

    /**
     * 暂估价 自动关联
     * @param args
     * @returns {Promise<void>}
     */
    async zgjAutoRelate(args){
        const result = await this.rcjProcess.zgjAutoRelate(args);
        return ResponseData.success(result);
    }

    /**
     * 暂估价 上移下移
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async zgjRcjMove(args){
        const result = await this.rcjProcess.zgjRcjMove(args);
        return ResponseData.success(result);
    }

    /**
     * 暂估价 批量插入
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async zgjRcjBatchInsert(args){
        const result = await this.rcjProcess.zgjRcjBatchInsert(args);
        return ResponseData.success(result);
    }



    /**
     * 单位工程人材机汇总 承包人 编辑
     * @param args
     * @returns {Promise<void>}
     */
    async updateCbrRcj(args){

        const result = await this.rcjProcess.updateCbrRcj(args);
        return ResponseData.success(result);
    }

    /**
     * 单位工程人材机汇总 承包人 新增
     * @param args
     * @returns {Promise<void>}
     */
    async addCbrRcj(args){

        const result = await this.rcjProcess.addCbrRcj(args);
        return ResponseData.success(result);
    }

    /**
     * 单位工程人材机汇总 承包人 删除
     * @param args
     * @returns {Promise<void>}
     */
    async deleteCbrRcj(args){

        const result = await this.rcjProcess.deleteCbrRcj(args);
        return ResponseData.success(result);
    }

    /**
     * 查询 承包人材料关联明细
     * @param args
     * @returns {Promise<void>}
     */
    async selectCbrRelevancyRcj(args){
        const result = await this.rcjProcess.selectCbrRelevancyRcj(args);
        return ResponseData.success(result);
    }

    /**
     * 勾选承包人关联明细
     * @param args
     * @returns {Promise<void>}
     */
    async rcjToCbrRcj(args){
        const result = await this.rcjProcess.rcjToCbrRcj(args);
        return ResponseData.success(result);
    }

    /**
     * 承包人 自动关联
     * @param args
     * @returns {Promise<void>}
     */
    async cbrAutoRelate(args){
        const result = await this.rcjProcess.cbrAutoRelate(args);
        return ResponseData.success(result);
    }

    /**
     * 承包人 上移下移
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async cbrRcjMove(args){
        const result = await this.rcjProcess.cbrRcjMove(args);
        return ResponseData.success(result);
    }


    /**
     * 承包人 重置承包人材料号
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async cbrRcjCzCode(args){
        const result = await this.rcjProcess.cbrRcjCzCode(args);
        return ResponseData.success(result);
    }

    /**
     * 承包人 批量插入
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async cbrRcjBatchInsert(args){
        const result = await this.rcjProcess.cbrRcjBatchInsert(args);
        return ResponseData.success(result);
    }


    /**
     * 单位工程 人材机无价差
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async unitRcjWjc(args){
        const result = await this.rcjProcess.unitRcjWjc(args);
        return ResponseData.success(result);
    }

    /**
     * 单项工程 所有人材机无价差
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async singleRcjWjc(args){
        const result = await this.rcjProcess.singleRcjWjc(args);
        return ResponseData.success(result);
    }

    /**
     * 工程项目 所有人材机无价差
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async constructRcjWjc(args){
        const result = await this.rcjProcess.constructRcjWjc(args);
        return ResponseData.success(result);
    }

    /**
     * 人材机无价差 查询状态修改
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updateRcjWjcState(args){
        const result = await this.rcjProcess.updateRcjWjcState(args);
        return ResponseData.success(result);
    }

    /**
     * 人材机无价差 查询状态查询
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async queryRcjWjcState(args){
        const result = await this.rcjProcess.queryRcjWjcState(args);
        return ResponseData.success(result);
    }

    /**
     * 人材机汇总 批注状态修改
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updatePostilState(args){
        const result = await this.rcjProcess.updatePostilState(args);
        return ResponseData.success(result);
    }

    /**
     * 人材机汇总 批注修改
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updatePostil(args){
        const result = await this.rcjProcess.updatePostil(args);
        return ResponseData.success(result);
    }

    /**
     * 人材机汇总 批注全部删除
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async deletePostilState(args){
        const result = await this.rcjProcess.deletePostilState(args);
        return ResponseData.success(result);
    }



    /**
     * 单位工程人材机汇总 下拉修改
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async unitRcjBatchUpdate(args){
        const result = await this.rcjProcess.unitRcjBatchUpdate(args);
        return ResponseData.success(result);
    }


    _findLineById(allData, id) {
        return allData.getNodeById(id);
        /*for (let i = 0; i<allData.length;++i) {
            if (id === allData[i].sequenceNbr) {
                return allData[i];
            }
        }*/
    }

    codeExistInUnit(args) {
        let {constructId, singleId, unitId, code} = args;
        let rcjs = PricingFileFindUtils.getRcjList(constructId, singleId, unitId);
        if (!rcjs) {
            return ResponseData.success(false);
        }
        let res = rcjs.filter(f=>f.materialCode === code);
        if (res) {
            return ResponseData.success(res.length > 0);
        } else {
            return ResponseData.success(false);
        }
    }
}

RcjController.toString = () => '[class RcjController]';
module.exports = RcjController;
