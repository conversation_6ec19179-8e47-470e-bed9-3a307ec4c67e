const {ResponseData} = require("../../utils/ResponseData");
const {Controller} = require("../../../core");
class SelfTestController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 检查项列表
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async checkItems(args){
        const res = await this.service.selfTestService.checkItems(args);
        return ResponseData.success(res);
    }

    remove(args){
        const res =  this.service.selfTestService.remove(args);
    }

    /**
     * 项目检查
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async projectCheck(args){
        const res = await this.service.selfTestService.projectCheck(args);
        return ResponseData.success(res);
    }
    async checkFfExistQdUnitDifferent(args){
        const res = await this.service.selfTestService.checkFfExistQdUnitDifferent(args);
        return ResponseData.success(res);
    }

    //编码刷新
    async refreshCode(args){
        const res = await this.service.selfTestService.refreshCode(args);
        return ResponseData.success(res);
    }

    /**
     * 查询项目检查结果
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async selectCheckResult(args){
        const res = await this.service.selfTestService.selectCheckResult(args);
        return ResponseData.success(res);
    }


    /**
     * 查询自检设置检查范围
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async selectCheckRange(args){
        const res = await this.service.selfTestService.selectCheckRange(args);
        return ResponseData.success(res);
    }

    /**
     * 定位数据是否存在
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async locate(args){
        return await this.service.selfTestService.locate(args);

    }

}

SelfTestController.toString = () => '[class SelfTestController]';
module.exports = SelfTestController;
