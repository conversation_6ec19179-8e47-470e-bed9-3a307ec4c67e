const {Service, Log} = require('../../core');
const {BaseDeJobContent} = require("../model/BaseDeJobContent");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const TaxCalculationMethodEnum = require("../enum/TaxCalculationMethodEnum");
const BuChongDataEnum = require("../enum/BuChongDataEnum");
const {SqlUtils} = require("../utils/SqlUtils");
const {NumberUtil} = require("../utils/NumberUtil");
const {BaseDe, BaseDe2022} = require("../model/BaseDe");
const {BaseList} = require("../model/BaseList");
const ConstantUtil = require('../enum/ConstantUtil');
const ReplaceStrategy = require("../main_editor/replace/replaceStrategy");
const InsertStrategy = require("../main_editor/insert/insertStrategy");
const {ConvertUtil} = require("../utils/ConvertUtils");
const {UnitRcjCacheUtil} = require("../rcj_handle/cache/UnitRcjCacheUtil");
const { Snowflake } = require('../utils/Snowflake');
const {RcjCalculateHandler} = require("../rcj_handle/calculate/RcjCalculateHandler");
/**
 * 定额工作内容表
 * @class
 */
class SupplementService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 判断清单 定额 的编码是否存在
     * @param constructId
     * @param singleId
     * @param unitId
     * @param code
     * @return {boolean}
     */
    isCodeExist(constructId, singleId, unitId, code) {
        let csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        let fres = csxm.filter(f=>f.fxCode === code);
        if (fres && fres.length > 0) {
            return true;
        }

        let fbfx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        let ffres = fbfx.filter(f=>f.bdCode === code);
        if (ffres && ffres.length > 0) {
            return true;
        }

        return false;
    }





    testPatternFun1(str) {
        const pattern = /^\d(-\d){3}$/;
        return pattern.test(str);
    }

    testPatternFun2(str) {
        const pattern = /^0\d(-\d){3}$/;
        return pattern.test(str);
    }

    testPatternFun3(str) {
        const pattern = /^0\d0\d(-\d{1,2})(-\d{1,3})$/;
        return pattern.test(str);
    }

    testPatternFun4(str) {
        const pattern = /^\d{1,2}(-\d{1,2})(-\d{1,2})(-\d{1,3})$/;
        return pattern.test(str);
    }


    /**
     * 处理清单编码
     * @param code
     * @returns {*}
     */
    changeQdByCode(code,constructId, singleId, unitId){
        let initCode=code;
        // 1-1-1-1
        // 01-1-1-1
        // 0101-1-1
        // 010101-1
        // 1-01-01-01
        // 1-01-01-001
        // 1-01-1-001

        //获取编码中的-
        const matches  = [...code.matchAll(/-/g)].map(match => match.index);
        const count = matches ? matches.length : 0;

        if(count==1){
            let indexOf = code.indexOf("-");
            let slice = code.slice(indexOf + 1);
            if(slice.length==1){
                code=code.slice(0, indexOf) + "00" + code.slice(indexOf + 1);
            }
            if(slice.length==2){
                code=code.slice(0, indexOf) + "0" + code.slice(indexOf + 1);
            }
            if(slice.length==3){
                code=code.slice(0, indexOf)+code.slice(indexOf + 1);
            }

        }else if (count==2){


            if(this.testPatternFun3(code)) {
                //获取中间的值
                let slice1 = code.slice(matches[0]+1,matches[1]);
                if(slice1.length==1){
                    code=code.slice(0, matches[0]) + "0" + code.slice(matches[0] + 1);
                }
                if(slice1.length==2){
                    code=code.slice(0, matches[0])+ code.slice(matches[0] + 1);
                }
                //处理第二个-
                let indexOf = code.indexOf("-");
                let slice = code.slice(indexOf + 1);
                if(slice.length==1){
                    code=code.slice(0, indexOf) + "00" + code.slice(indexOf + 1);
                }
                if(slice.length==2){
                    code=code.slice(0, indexOf) + "0" + code.slice(indexOf + 1);
                }
                if(slice.length==3){
                    code=code.slice(0, indexOf)+code.slice(indexOf + 1);
                }

            }


        }else if (count==3){

            if(this.testPatternFun4(code)){
                //获取所有的-以外的数据
                let split = code.split('-');
                //1
                let slice1 = code.slice(0, matches[0]).length==1?"0"+code.slice(0, matches[0]):code.slice(0, matches[0]);
                let slice2 = code.slice(matches[0]+1, matches[1]).length==1?"0"+code.slice(matches[0]+1, matches[1]):code.slice(matches[0]+1, matches[1]);
                let slice3 = code.slice(matches[1]+1, matches[2]).length==1?"0"+ code.slice(matches[1]+1, matches[2]): code.slice(matches[1]+1, matches[2]);
                let slice4 = code.slice(matches[2]+1 ).length==1?"00"+code.slice(matches[2]+1 ):code.slice(matches[2]+1 ).length==2?"0"+code.slice(matches[2]+1 ):code.slice(matches[2]+1 );
                let newCode="";
                for(let i=0;i<split.length;i++){
                    if(i==0){
                        newCode=newCode+slice1;
                    }
                    if(i==1){
                        newCode=newCode+slice2;
                    }
                    if(i==2){
                        newCode=newCode+slice3;
                    }
                    if(i==3){
                        newCode=newCode+slice4;
                    }
                }

                code =newCode;
            }


            // if(this.testPatternFun1(code)){
            //     while(flag){
            //         let indexOf = code.indexOf("-");
            //         if(indexOf!=-1){
            //             index++;
            //             if(index==3){
            //                 newChar="00"
            //             }
            //             code= code.slice(0, indexOf) + newChar + code.slice(indexOf + 1);
            //         }else {
            //             flag=false;
            //         }
            //     }
            //     code ="0"+code;
            // }
            //
            // //01-1-1-1
            // if(this.testPatternFun2(code)){
            //     while(flag){
            //         let indexOf = code.indexOf("-");
            //         if(indexOf!=-1){
            //             index++;
            //             if(index==3){
            //                 newChar="00"
            //             }
            //             code= code.slice(0, indexOf) + newChar + code.slice(indexOf + 1);
            //         }else {
            //             flag=false;
            //         }
            //     }
            // }

            //处理其他的

        }

        if(initCode!=code){
            //判断是不是标准编码
            let standQdCode = this.isStandQdCode(constructId, singleId, unitId,code);
            if(ObjectUtils.isEmpty(standQdCode)){
                code= initCode
            }
        }
        return code;
    }

    /**
     * 判断是否是 标准定额
     * @param constructId
     * @param singleId
     * @param unitId
     * @param code
     */
    isMainStandDeCode(constructId, singleId, unitId, code) {
        let flag = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        let sql = "";
        let sqlRes = 0
        if (flag) {
            sql = "select count(0) as count from base_de_2022 where de_code = ?";
            sqlRes = this.app.betterSqlite3DataSource.prepare(sql).get(code).count;
        } else {
            sql = "select count(0) as count from base_de where de_code = ?";
            sqlRes = this.app.betterSqlite3DataSource.prepare(sql).get(code).count;
        }
        if (sqlRes >0) {
            return {"code":code};
        } else {
            return null;
        }
    }

    /**
     * 判断是否是 标准定额
     * @param constructId
     * @param singleId
     * @param unitId
     * @param code
     */
    isStandDeCode(constructId, singleId, unitId, code) {
        let unitIs2022 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        let sql ;
        //22标准   查22
        if(unitIs2022){
            sql = "select count(0) as count from base_de_2022 where de_code = ?";
        }else {
            sql = "select count(0) as count from base_de where de_code = ?";
        }

        let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).get(code).count;

        if (sqlRes >0) {
            return {"code":code};
        } else {
            return null;
        }
    }

    /**
     * 判断是否是 标准清单
     * @param constructId
     * @param singleId
     * @param unitId
     * @param code
     */
    isStandQdCode(constructId, singleId, unitId, code) {
        // 仅认为9位或者12位数是标准编码
        if (code.length !== 9 && code.length !== 12) {
            return false;
        }
        let sql = "select count(0) as num from base_list where bd_code_level04 = ?";
        let itemSql = "select * from base_list where bd_code_level04 = ?";
        if (code.length === 9) {
            let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).get(code).num;
            if (sqlRes >0) {
                let item = this.app.betterSqlite3DataSource.prepare(itemSql).get(code);
                let convertRes = SqlUtils.convertToModel([item])[0];
                return convertRes;
            } else {
                return null;
            }
        }
        if (code.length === 12) {
            let cCode = code.slice(0, 9);
            let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).get(cCode).num;
            if (sqlRes > 0) {
                let item = this.app.betterSqlite3DataSource.prepare(itemSql).get(cCode);
                let convertRes = SqlUtils.convertToModel([item])[0];
                return convertRes;
            } else {
                return null;
            }
        }

        return null;
    }

    /**
     * 根据编码模糊查询5条数据
     */
    vagueSearchBaseQds(code) {
        let selectSql = "select * from base_list where bd_code_level04 like ? || '%' limit 5";
        let sqlRes = this.app.betterSqlite3DataSource.prepare(selectSql).all(code);
        let convertRes = SqlUtils.convertToModel(sqlRes);

        if (!ObjectUtils.isEmpty(convertRes)) {
            for (let convertResKey in convertRes) {
                if (ObjectUtils.isEmpty(convertRes[convertResKey].unit)) {
                    convertRes[convertResKey].unit = "项";
                }
            }
        }

        return convertRes;
    }


    /**
     * 根据名称模糊查询5条数据
     */
    vagueSearchBaseQdByName(name) {
        // let selectSql = "select * from base_list where bd_name_level04 like ? || '%' limit 5";
        let selectSql = "select * from base_list where bd_name_level04  like  '%' ||  ?  || '%' limit 5";
        let sqlRes = this.app.betterSqlite3DataSource.prepare(selectSql).all(name);
        let convertRes = SqlUtils.convertToModel(sqlRes);

        if (!ObjectUtils.isEmpty(convertRes)) {
            for (let convertResKey in convertRes) {
                if (ObjectUtils.isEmpty(convertRes[convertResKey].unit)) {
                    convertRes[convertResKey].unit = "项";
                }
            }
        }

        return convertRes;
    }


    async supplementDeByCode(constructId, singleId, unitId, type, pointLine, code,type1) {

        let {mainDeLibrary:mainLibCode,itemBillProjects,measureProjectTables} = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        let is2022=PricingFileFindUtils.is22Unit( PricingFileFindUtils.getUnit(constructId, singleId, unitId));
        let  getBaseDe= async is2022=>{
            let dao = this.app.appDataSource.manager.getRepository(is2022? BaseDe2022:BaseDe);
            //先查询主定额册  在查询其他定额册
            let result = await  dao.findOne({
                where: {deCode:code,libraryCode:mainLibCode}
            });
            if(ObjectUtils.isEmpty(result)){
                result = await  dao.findOne({
                    where: {deCode:code}
                });
            }
            return result;
        }
        let de =await getBaseDe(is2022);
        if(!de){
            de = await getBaseDe(!is2022);
            is2022= (!is2022);
        }
        let baseId = de.sequenceNbr;
        //pointLine.bdCode
        let allData = type==1?itemBillProjects:measureProjectTables;
         pointLine =allData.getNodeById(pointLine.sequenceNbr);
         if(pointLine.bdCode){
             let handler =new ReplaceStrategy({constructId, singleId, unitId:unitId,pageType:type==2?"csxm":"fbfx"});
             await handler.execute({selectId:baseId,replaceId:pointLine.sequenceNbr,unit:null,kind:BranchProjectLevelConstant.de,libraryCode:de.libraryCode});
         }else {
             let handler = new InsertStrategy({constructId, singleId, unitId:unitId,pageType:type==2?"csxm":"fbfx"});
             await handler.execute({ pointLine: pointLine, newLine: {kind:pointLine.kind}, indexId:baseId,option:"insert", libraryCode:de.libraryCode});
         }

    }
    /**
     * 由编码补充定额
     * 工程量，工程量明细保留原来的
     * @param constructId
     * @param singleId
     * @param unitId
     * @param type 类型      1 fbfx ； 2 csxm
     * @param pointLine
     * @param code
     */
    async supplementDeByCodeOld(constructId, singleId, unitId, type, pointLine, code,type1) {
        let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        if (type === 2 || type === "2") {
            allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        }

        let mainLibCode = PricingFileFindUtils.getUnit(constructId, singleId, unitId).mainDeLibrary;
        let is2022=PricingFileFindUtils.is22Unit( PricingFileFindUtils.getUnit(constructId, singleId, unitId));
        let  getBaseDe= async is2022=>{
            let dao = this.app.appDataSource.manager.getRepository(is2022? BaseDe2022:BaseDe);
            let result = await  dao.findOne({
                where: {deCode:code}
            });
            return result;
        }
        let de =await getBaseDe(is2022);
        if(!de){
            de = await getBaseDe(!is2022);
            is2022= (!is2022);
        }
        let baseId = de.sequenceNbr;


        pointLine = allData.find(f=>f.sequenceNbr === pointLine.sequenceNbr);
        await this.service.baseBranchProjectOptionService.replaceFronIndexPage(allData, constructId, singleId, unitId,
            baseId, pointLine.sequenceNbr, null, null, BranchProjectLevelConstant.de, null,null,null,type1,is2022);
        pointLine.isSupplement = 1;
        pointLine.isStandard = 1;
        // pointLine.standardId = null;
        pointLine.isCostDe = this.service.constructCostMathService.costDeByDe(pointLine);

        this.afterSup(constructId, singleId, unitId, pointLine, 4);
        //处理临时锁定数据  新增的是定额需要处理
        this.service.itemBillProjectOptionService.addDeTempDel(constructId, singleId, unitId,pointLine);
    }

    async supplementDeByPage(constructId, singleId, unitId, type, pointLine, pageInfo) {
        let handler = new ReplaceStrategy({constructId, singleId, unitId, pageType:type==2?"csxm":"fbfx"});
       await handler.supplement({pointLine,pageInfo});
        return true;
    }
    /**
     * 由页面补充数据
     * @param constructId
     * @param singleId
     * @param unitId
     * @param type  类型      1 fbfx ； 2 csxm
     * @param pointLine
     * @param pageInfo
     */
    async supplementDeByPageOld(constructId, singleId, unitId, type, pointLine, pageInfo) {
        let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        if (type === 2 || type === "2") {
            allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        }
        pointLine = allData.find(f => f.sequenceNbr === pointLine.sequenceNbr);
        // 1.删除原来数据的人材机关系
        this.service.rcjProcess.delRel(constructId, singleId, unitId, pointLine.sequenceNbr);
        // 2.当前行数据填充
        await this._pageFillDeLine(constructId, singleId, unitId, pointLine, pageInfo);
        // 3.人材机数据填充
        this.service.rcjProcess.supplementToDe(constructId, singleId, unitId, pointLine, pageInfo);
        // 4.处理单价构成
        this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitId, pointLine.sequenceNbr, true, allData)

        pointLine.isSupplement = 1;
        pointLine.isStandard = 0;
        pointLine.standardId = null;
        pointLine.isCostDe = 0;

        this.afterSup(constructId, singleId, unitId, pointLine, 4);
        //处理临时锁定数据  新增的是定额需要处理
        this.service.itemBillProjectOptionService.addDeTempDel(constructId, singleId, unitId,pointLine);
        return true;
    }

    /**
     * 由编码补充清单
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     * @param code
     */
    supplementQdByCode(constructId, singleId, unitId, pointLine, code, isSortQdCode, unit) {
        let baseCode = code;
        // baseCode代表 数据的数据库中编码 首先把用户编码切成数据库编码
        if (code.length > 9) {
            baseCode = code.slice(0,9);
        }
        // 判断是否需要自增
        if (isSortQdCode) {
            //空清单行会出现pointLine.fxCode 为空
            let pointLineCode = ObjectUtils.isEmpty(pointLine.fxCode)?baseCode:pointLine.fxCode;
            let icf = true;
            if (!pointLineCode || pointLineCode === "") {
                pointLineCode = pointLine.bdCode;
            }
            if (pointLineCode.slice(0,9) !== baseCode) {
                icf = false;
            }
            code = this.service.baseBranchProjectOptionService.getQdCode(constructId, singleId, unitId, code, icf, pointLine.sequenceNbr);
        }
        // 判断是否 是标准编码及是否 需要自增
        if(code.length == 9) {
            code = this.service.baseBranchProjectOptionService.getQdCode(constructId, singleId, unitId, code, false, pointLine.sequenceNbr);
        }
        // code = this.service.baseBranchProjectOptionService.getQdCode(constructId, singleId, unitId, code, false, pointLine.sequenceNbr)

        let lineData = this.findLine(constructId, singleId, unitId, pointLine);
        let selectSql = "select * " +
            "from base_list   " +
            "where bd_code_level04 = ?";
        let sqlRes = this.app.betterSqlite3DataSource.prepare(selectSql).all(baseCode);
        let baseQd = sqlRes[0];
        //查询数据
        // let baseQd =await this.app.appDataSource.getRepository(BaseList).findOne({
        //     where: { bdCodeLevel04: baseCode }
        // });


        // 删除清单的特征
        lineData.projectAttr = "";
        this.service.listFeatureService.cleanFeature(constructId, singleId, unitId, lineData.sequenceNbr, true);
        // 清除standid
        lineData.standardId = null;
        this._fillqdByBase(constructId, singleId, unitId, lineData, baseQd, code);
        if (this.service.baseBranchProjectOptionService._isBaseQdCode(code)) {  //为标准清单
            lineData.isSupplement = 0;
        }else {
            lineData.isSupplement = 1;
        }
        // 特征及项目
        this.service.listFeatureProcess.saveBatchToFbFxQdFeature(lineData.libraryCode, code,
            lineData.sequenceNbr, constructId, singleId, unitId);

        if (!isSortQdCode) {
            lineData.fxCode = code;
            lineData.bdCode = code;
        }

        lineData.unit = unit;

        this.afterSup(constructId, singleId, unitId, lineData, 3);
        return true;
    }
    async supplementQdByPage(constructId, singleId, unitId, pointLine, pageInfo,pageType) {
       let handler = new ReplaceStrategy({constructId, singleId, unitId, pageType});
         await handler.supplement({pointLine,pageInfo})
    }
    /**
     * 由界面补充清单
     * @param  constructId
     * @param  singleId
     * @param  unitId
     * @param  pointLine {编码 单位 名称 表达式}
     * @param  type      类型      1 fbfx ； 2 csxm
     * @return {MeasureProjectTable}
     */
    supplementQdByPageOld(constructId, singleId, unitId, pointLine, pageInfo) {
        pageInfo.code = pageInfo.bdCode;
        pageInfo.name = pageInfo.bdName;
        let lineData = this.findLine(constructId, singleId, unitId, pointLine);
        if (this.isStandQdCode(constructId, singleId, unitId, pageInfo.code)) {
            this.supplementQdByCode(constructId, singleId, unitId, pointLine, pageInfo.code);
        }

        // 删除清单的特征
        lineData.projectAttr = "";
        this.service.listFeatureService.cleanFeature(constructId, singleId, unitId, lineData.sequenceNbr, true);
        // 清除standid
        lineData.standardId = null;

        lineData.fxCode = pageInfo.code;
        lineData.bdCode = pageInfo.code;

        lineData.name = pageInfo.name;
        lineData.bdName = pageInfo.name;

        lineData.unit = pageInfo.unit; // 单位
        lineData.unitList = pageInfo.unit; // 单位 前端展示需要保留此字段

        if (!pageInfo.quantityExpression) {
            pageInfo.quantityExpression = "0";
        }
        lineData.quantityExpression = pageInfo.quantityExpression;
        lineData.quantity = pageInfo.quantityExpression;

        lineData.projectAttr = "";
        this.service.listFeatureService.cleanFeature(constructId, singleId, unitId, lineData.sequenceNbr, true);

        let {varMap, variables} = this._getVariables(constructId, singleId, unitId, pointLine);
        let evalStr = pointLine.quantityExpression;
        for (let j = 0 ; j<variables.length; ++j) {
            let reg = new RegExp("\\b"+variables[j]+"\\b", "g");
            evalStr = evalStr.replaceAll(reg, varMap.get(variables[j]));
        }

        let unitNum = Number.isNaN(Number.parseInt(pointLine.unit))?1:Number.parseInt(pointLine.unit);
        let res = eval(evalStr)/unitNum;
        pointLine.quantity = res;

        let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        if (pageInfo.type === 2) {
            allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        }
        this.service.unitPriceService.caculateQDUnitPrice(constructId, singleId, unitId, lineData.sequenceNbr, true, allData);

        lineData.isSupplement = 1;
        this.afterSup(constructId, singleId, unitId, lineData, 3);
        return true;
    }

    findLine(constructId, singleId, unitId, pointLine) {
        let csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        let res = csxm.filter(f=>f.sequenceNbr === pointLine.sequenceNbr);
        if (!res || res.length === 0) {
            let fbfx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
            res = fbfx.filter(f=>f.sequenceNbr === pointLine.sequenceNbr);
        }
        return res[0];
    }

    _fillqdByBase(constructId, singleId, unitId, currentUpdateLine, baseQdOrDe, cusCode) {
        currentUpdateLine.unit = baseQdOrDe.unit; // 单位
        currentUpdateLine.unitList = baseQdOrDe.unit; // 单位 前端展示需要保留此字段
        // 同时适配分部分项和措施项目
        let code = this.service.baseBranchProjectOptionService.getQdCode(constructId, singleId, unitId, baseQdOrDe.level4);
        if (cusCode) {
            code = cusCode;
        }
        currentUpdateLine.fxCode = this.service.baseBranchProjectOptionService.getQdCode(constructId, singleId, unitId, code, null, currentUpdateLine.sequenceNbr);
        currentUpdateLine.name = baseQdOrDe.bd_name_level04;
        currentUpdateLine.bdCode = currentUpdateLine.fxCode;
        currentUpdateLine.bdName = baseQdOrDe.bd_name_level04; // 同时适配措施项目和分部分项

        currentUpdateLine.isStandard = 1;
        currentUpdateLine.standardId = baseQdOrDe.sequence_nbr;
        currentUpdateLine.libraryCode = baseQdOrDe.library_code;
        currentUpdateLine.zjcsClassName = baseQdOrDe.zjcs_label_name;
        currentUpdateLine.zjcsClassCode = baseQdOrDe.zjcs_class_code;
        currentUpdateLine.zjcsLabelName = baseQdOrDe.zjcs_class_name;
        currentUpdateLine.unitId = unitId;
    }

    _getVariables(constructId, singleId, unitWorkId, pointLine) {
        let quantities = pointLine.quantities;

        let res = 0;
        for (let i = 0; i < quantities.length; ++i) {
            if (quantities[i].mathResult && quantities[i].accumulateFlag === 1) {
                res += quantities[i].mathResult;
            }
        }

        let varMap = new Map();
        varMap.set("GCLMXHJ", res);

        return {
            "varMap": varMap,
            "variables": ["GCLMXHJ"]
        }

    }

    _isSameUnit(unitQd, unitDe) {
        if (!unitQd || !unitDe) {
            return false;
        }
        return unitQd.endsWith(unitDe) || unitDe.endsWith(unitQd);
    }

    /**
     * 填充定额行
     * @param pointLine
     * @param pageInfo
     * @private
     */
    async _pageFillDeLine(constructId, singleId, unitId, pointLine, pageInfo) {
        pointLine.isSupplement = 1;
        pointLine.fxCode = pageInfo.bdCode;
        pointLine.bdCode = pageInfo.bdCode;
        pointLine.code = pageInfo.bdCode;
        pointLine.unit = pageInfo.unit;

        if(pageInfo.quantityExpression && pageInfo.quantityExpression !== "") {
            pointLine.quantityExpression = pageInfo.quantityExpression;
        } else {
            let qdLine = this.findLine(constructId, singleId, unitId, {sequenceNbr: pointLine.parentId});
            if(this._isSameUnit(qdLine.unit, pointLine.unit)) {
                pointLine.quantityExpression = qdLine.quantityExpression;
            } else {
                pointLine.quantityExpression = "0";
                pointLine.quantity = 0;
            }
        }


        pointLine.quantityExpressionNbr = this.service.baseBranchProjectOptionService.getQuantityExpressionNbr(constructId, singleId, unitId, pointLine); // 重新计算
        pointLine.quantity = NumberUtil.divide(pointLine.quantityExpressionNbr, this.service.baseBranchProjectOptionService.getUnitNum(pointLine));       // 根据unit 重新计算
        pointLine.name = pageInfo.bdName;
        pointLine.fxName = pageInfo.bdName;
        pointLine.bdName = pageInfo.bdName;
        pointLine.classifyLevel1 = pageInfo.classifyLevel1; // 1
        pointLine.classifyLevel2 = pageInfo.classifyLevel2; // 2
        pointLine.classifyLevel3 = pageInfo.classifyLevel3; // 3
        pointLine.classifyLevel4 = pageInfo.classifyLevel4; // 4
        pointLine.rfee = pageInfo.rfee; //人工费
        pointLine.cfee = pageInfo.cfee; //材料费
        pointLine.jfee = pageInfo.jfee; //机械费
        pointLine.zcfee = pageInfo.zcfee; //主材费
        pointLine.sbfee = pageInfo.sbfee; //设备费

        //取费文件   当前单位的主取费文件
        let unitWork = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let mainDeLib = unitWork.mainDeLibrary;
        let constructMajorType = unitWork.constructMajorType;
        let feeFile = await this._getDefaultFeeFile(constructId, singleId, unitId, mainDeLib, constructMajorType);
        pointLine.costMajorName = feeFile.feeFileName;
        pointLine.costFileCode = feeFile.feeFileCode;
        pointLine.feeFileId = feeFile.feeFileId;
        pointLine.measureType = unitWork.secondInstallationProjectName?unitWork.secondInstallationProjectName:feeFile.rateName;

        pointLine.appendType = [];
    }

    async _getDefaultFeeFile(constructId, singleId, unitWorkId, mainDeLib, constructMajorType) {
        // 根据 主定额库 和 工程专业 获取取费文件code
        let querySql = "select default_qf_code as feecode\n" +
            (mainDeLib.startsWith(ConstantUtil.YEAR_2022)? "from base_speciality_de_fee_relation_2022\n":"from base_speciality_de_fee_relation\n") +
            "where library_code = ?\n" +
            "  and unit_project_name = ?";
        let sqlRes = this.app.betterSqlite3DataSource.prepare(querySql).all(mainDeLib, constructMajorType);
        let feeCode = sqlRes[0].feecode;
        // 根据 feeCode 查询取费文件
        let feeFile = await this.service.baseFeeFileService.updateFBFXFeeFile(constructId, singleId, unitWorkId, feeCode);

        return feeFile;
    }

    // <!-- --------------------人材机 -------------------------->
    /**
     * 是否是标准人材机
     */
    isStandRcj(constructId, singleId, unitId, code) {
        let flag = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        let sql ;
        if(flag){
            sql = "select * from base_rcj_2022 where  material_code = ? limit 1";
        }else {
            sql = "select * from base_rcj where  material_code = ? limit 1";
        }
        let res = this.app.betterSqlite3DataSource.prepare(sql).get(code);
        if (undefined === res) {
            res = null;
        } else {
            res = SqlUtils.convertToModel([res])[0];
        }
        return res;
    }

    /**
     * 是否是住定额库标准人材机
     */
    isMainLibStandRcj(constructId, singleId, unitId, code) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let mainLibCode = unit.mainDeLibrary;
        let flag = PricingFileFindUtils.is22Unit(unit);
        let sql ;
        if(flag){
             sql = "select * from base_rcj_2022 where library_code = ? and material_code = ? limit 1";
        }else {
            sql = "select * from base_rcj where library_code = ? and material_code = ? limit 1";
        }
        let res = this.app.betterSqlite3DataSource.prepare(sql).get(mainLibCode, code);

        if (!res) {
            res = null;
        } else {
            res = SqlUtils.convertToModel([res])[0];
        }
        return res;
    }


    /**
     * 获取缓存中的补充人材机数据
     */
    getCacheSRcj(constructId, singleId, unitId, code) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let res = UnitRcjCacheUtil.getByCode(unit,code);
        return res;
    }

    /**
     * 单位下是否存在同code人材机
     */
    rcjCodeExist(constructId, singleId, unitId, code) {
        let sameCodeRcjs = PricingFileFindUtils.getUnit(constructId, singleId, unitId).constructProjectRcjs.filter(f=>f.materialCode === code);
        if (!sameCodeRcjs) {
            return false;
        }
        return sameCodeRcjs.length > 0;
    }

    _findDe(constructId, singleId, unitId, deId) {
        let fbFxs = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        let csxms = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        for (let i = 0 ; i < fbFxs.length ; ++i) {
            if (deId === fbFxs[i].sequenceNbr) {
                return fbFxs[i];
            }
        }
        for (let i = 0 ; i < csxms.length ; ++i) {
            if (deId === csxms[i].sequenceNbr) {
                return csxms[i];
            }
        }
        return undefined;
    }

    /**
     * 由code补充人材机
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     * @param code
     * @param region  region 0 操作区  1 明细区
     */
    async supplementRcjByCode(constructId, singleId, unitId, pointLine, code, region) {
        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //从缓存中获取人材机  没有的话去标准库取
        let newVar = UnitRcjCacheUtil.getByCodeAndLibraryCode(unitProject,code,unitProject.mainDeLibrary);
        let rcj;
        let pb=[];
        let baseIndex;
        let deId;
        if (ObjectUtils.isEmpty(pointLine.deId)){
            deId = pointLine.sequenceNbr;
        }else {
            deId = pointLine.deId;
        }
        if(ObjectUtils.isNotEmpty(newVar)){
            rcj=ObjectUtils.cloneDeep(newVar);
            rcj.sequenceNbr= pointLine.sequenceNbr;
            rcj.deId=deId;
            rcj.unitId=pointLine.unitId;
            rcj.resQty = 0;
            rcj.initResQty = 0;
            baseIndex=newVar.standardId;
            let queryChildrenRcj = UnitRcjCacheUtil.queryChildrenRcj(unitProject,rcj);
            if(ObjectUtils.isNotEmpty(queryChildrenRcj)){
                queryChildrenRcj.forEach(p=>{
                    p.sequenceNbr=Snowflake.nextId();
                    p.rcjId= rcj.sequenceNbr;
                    p.deId= rcj.deId;
                    if(ObjectUtils.isNotEmpty(newVar.childrenRcjCodeList)){
                        let childrenRcjCodeList = newVar.childrenRcjCodeList;
                        childrenRcjCodeList.forEach(c=>{
                            if(c.materialCode == p.materialCode){
                                p.resQty = c.resQty;
                                p.initResQty = c.initResQty;
                            }
                        })
                    }

                    pb.push(ObjectUtils.cloneDeep(p));
                });
            }
        }else {
            let is2022 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
            let baseName=is2022?"base_rcj_2022":"base_rcj";
            let sql = "select library_code = ? as isMain, b.*\n" +
                "from  "+  baseName  +" b \n"+
                "where material_code = ? \n" +
                "order by isMain desc\n" +
                "limit 1";
            let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).get(unitProject.mainDeLibrary, code);
            baseIndex = sqlRes.sequence_nbr;
            let obj = await this.service.rcjProcess.addRcjLineOnOptionMenu(constructId, singleId, unitId, baseIndex, pointLine.sequenceNbr,is2022);
            rcj=obj.rcj;
            pb=obj.pb;

            rcj.sequenceNbr= Snowflake.nextId();
            rcj.deId=deId;
            rcj.unitId=pointLine.unitId;
            rcj.resQty = 0;
            rcj.initResQty = 0;

            if(ObjectUtils.isNotEmpty(pb)){
                pb.forEach(p=>{
                    p.sequenceNbr=Snowflake.nextId();
                    p.rcjId= rcj.sequenceNbr;
                    p.deId= rcj.deId;
                });
            }
        }



        rcj.markSum = 1;
        //rcj.supplementDeRcjFlag = 1;
        // 明细区
        if (region === 1) {
            let noSucjRcj = true;
            for (let i = 0 ; i < unitProject.constructProjectRcjs.length ; ++i) {
                if (unitProject.constructProjectRcjs[i].sequenceNbr === pointLine.sequenceNbr) {
                    noSucjRcj = false;
                    // 删除明细 替换当前
                    if (ObjectUtils.isNotEmpty(unitProject.rcjDetailList)) {
                        unitProject.rcjDetailList = unitProject.rcjDetailList.filter(f=>f.rcjId !== pointLine.sequenceNbr);
                    }
                    let oldRcj=ConvertUtil.deepCopy(unitProject.constructProjectRcjs[i]);
                    let standId = unitProject.constructProjectRcjs[i].standardId;
                    let qt = unitProject.constructProjectRcjs[i].resQty;
                    let initResQty = unitProject.constructProjectRcjs[i].initResQty;

                    unitProject.constructProjectRcjs[i] = rcj;
                    unitProject.constructProjectRcjs[i].resQty = qt;
                    unitProject.constructProjectRcjs[i].standId = standId;
                    unitProject.constructProjectRcjs[i].initResQty = initResQty;
                    if (!unitProject.rcjDetailList) {
                        unitProject.rcjDetailList = [];
                    }
                    if(ObjectUtils.isNotEmpty(pb)){
                        unitProject.rcjDetailList = unitProject.rcjDetailList.concat(pb);
                    }

                    // 获取分部分项定额的工程量
                    let itemBillDe = this.findLine(constructId, singleId, unitId, {sequenceNbr: deId});
                    let constructProjectRcj = unitProject.constructProjectRcjs[i];
                    if (!ObjectUtils.isEmpty(itemBillDe)) {
                        //合计数量
                        if (!itemBillDe) {
                            constructProjectRcj.totalNumber = 0;
                        } else {
                            let baseNum = 1;
                            if (itemBillDe.baseNum) {
                                if (itemBillDe.baseNum["def"]) {
                                    baseNum = itemBillDe.baseNum["def"];
                                }
                                if (itemBillDe.baseNum[constructProjectRcj.kind]) {
                                    baseNum = itemBillDe.baseNum[constructProjectRcj.kind];
                                }
                            }
                            let percent = 1;
                            if (constructProjectRcj.unit === "%") {
                                percent = 0.01;
                            }
                            constructProjectRcj.totalNumber = NumberUtil.multiply(NumberUtil.multiply(constructProjectRcj.resQty, itemBillDe.quantity), baseNum);
                            constructProjectRcj.totalNumber = NumberUtil.multiply(constructProjectRcj.totalNumber,percent);
                        }
                        //合价
                        constructProjectRcj.total = NumberUtil.numberScale(NumberUtil.multiply(constructProjectRcj.totalNumber, constructProjectRcj.marketPrice));
                    }
                    //生成换算信息
                    let conversionInfo = this.service.rcjProcess.updateRcjSyncDeConversionInfo(constructId, singleId, unitId,deId,oldRcj,"replace",constructProjectRcj,null,1);
                    let {line:deReal} = this.service.baseBranchProjectOptionService.findLineOnlyById(deId);
                    deReal.name = `${deReal.name} ${conversionInfo.conversionNameExplain}`;
                    deReal.nameSuffixHistory = deReal.nameSuffixHistory || [];
                    deReal.nameSuffixHistory.push(conversionInfo.conversionNameExplain)
                }
            }
            // 处理明细区子
            if (noSucjRcj) {
                let rcjChild = unitProject.rcjDetailList.filter(f=>f.sequenceNbr === pointLine.sequenceNbr)[0];
                let libCode = PricingFileFindUtils.getUnit(constructId, singleId, unitId).mainDeLibrary;
                let materialCode = code;
                let sql = "select t.library_code = ? as tc, t.* from \n" +
                    "(select c.*, r.library_code\n" +
                    "from base_jxpb c\n" +
                    "         left join "+  baseName  +" r on c.rcj_id = r.sequence_nbr\n" +
                    "where c.material_code = ?\n" +
                    "group by library_code) t\n" +
                    "order by tc desc\n" +
                    "limit 1";
                let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all(libCode, materialCode);
                if (!sqlRes || sqlRes.length === 0) {
                    let sql = "select t.library_code = ? as tc, t.* from \n" +
                        "(select c.*, r.library_code\n" +
                        "from base_clpb c\n" +
                        "         left join "+  baseName  +" r on c.rcj_id = r.sequence_nbr\n" +
                        "where c.material_code = ?\n" +
                        "group by library_code) t\n" +
                        "order by tc desc\n" +
                        "limit 1";
                    sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all(libCode, materialCode);
                }
                let convertRes = SqlUtils.convertToModel(sqlRes);
                if (!convertRes || convertRes.length === 0) {
                    let sql = "select * from base_rcj where material_code = ?";
                    sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all(materialCode);
                    convertRes = SqlUtils.convertToModel(sqlRes);
                }
                let findLine = convertRes[0];
                for (let attr in findLine) {
                    if (attr === "rcjId") continue;
                    if (attr === "resQty") continue;
                    rcjChild[attr] = findLine[attr];
                }

                await this.service.rcjProcess.rcjUseUnitRcj(constructId, singleId, unitId,rcjChild);


                pointLine = PricingFileFindUtils.getRcjList(constructId, singleId, unitId).filter(f => f.sequenceNbr === rcjChild.rcjId)[0];
                await this.service.constructProjectRcjService.changeChildRcjMaterialCode(PricingFileFindUtils.getRcjList(constructId, singleId, unitId),
                    PricingFileFindUtils.getRcjDetailList(constructId, singleId, unitId),
                    rcjChild);
            }
            let deLine = this.findLine(constructId, singleId, unitId, {sequenceNbr:pointLine.deId});
            let rcjCalculateHandler = new RcjCalculateHandler({
                constructId,
                singleId,
                unitId,
                projectObj: PricingFileFindUtils.getProjectObjById(constructId)
            });
            await rcjCalculateHandler.calculate(deLine);
            let allData = this.findAllDatas(constructId, singleId, unitId, {sequenceNbr:pointLine.deId});
            this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitId, pointLine.deId, true, allData);

            // 换
            let nowRcjs = this.service.rcjProcess.queryRcjDataByDeId(pointLine.deId, constructId, singleId, unitId);
            let baseRcjs = this.service.rcjProcess.getBaseRcjInfoByDeId(deLine.standardId);
            deLine.appendType = deLine.appendType.filter(a=>a!=="换");
            if (!this.service.rcjProcess.isSameRcj(baseRcjs, nowRcjs)) {
                if (!deLine.appendType) {
                    deLine.appendType = [];
                } else {
                    deLine.appendType = deLine.appendType.filter(f=>f!== "换");
                }
                deLine.appendType.push("换");
            }
        }
        // 操作区 pointLine代表一行操作区数据  ： 替换原数据 保留工程量， 删除元数数据人材机，明细 入新的
        else
        {
            // 删除下挂
            let orgRcj = unitProject.constructProjectRcjs.find(r=>r.deId===pointLine.sequenceNbr);
            unitProject.constructProjectRcjs = unitProject.constructProjectRcjs.filter(r=>r.deId!==pointLine.sequenceNbr);
            if (ObjectUtils.isNotEmpty(unitProject.rcjDetailList)) {
                unitProject.rcjDetailList = unitProject.rcjDetailList.filter(rd=>rd.rcjId !== orgRcj.sequenceNbr);
            }
            // 1.当前行内数据替换
            pointLine = this.findLine(constructId, singleId, unitId, pointLine);
            this.service.baseBranchProjectOptionService.wfillRcjLine(pointLine, rcj, constructId, singleId, unitId, baseIndex);
            // 2.下挂人材机替换
            unitProject.constructProjectRcjs.push(rcj);
            if (!unitProject.rcjDetailList) {
                unitProject.rcjDetailList = [];
            }
            unitProject.rcjDetailList.concat(pb);
            pointLine.isSupplement = 1;
            let rcjCalculateHandler = new RcjCalculateHandler({
                constructId,
                singleId,
                unitId,
                projectObj: PricingFileFindUtils.getProjectObjById(constructId)
            });
            await rcjCalculateHandler.calculate(pointLine);
            let allData = this.findAllDatas(constructId, singleId, unitId, {sequenceNbr:pointLine.sequenceNbr});
            this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitId, pointLine.sequenceNbr, true, allData);
        }

        this.afterSup(constructId, singleId, unitId, pointLine, 5);
        // 费用代码
        this.service.management.trigger("itemChange");
    }

    supplementRcjDetailByPage(constructId, singleId, unitId, pointLine, pageInfo) {
        // 1. 找内存数据
        let detail = PricingFileFindUtils.getRcjDetailList(constructId, singleId, unitId);
        let d = detail.filter(f=>f.sequenceNbr === pointLine.sequenceNbr)[0];
        // 2. 改内存数据
        // 自身修改
        d.materialCode = pageInfo.materialCode;
        d.kind = pageInfo.kind;
        d.type = pageInfo.type;
        d.materialName = pageInfo.materialName;
        d.unit = pageInfo.unit;
        d.resQty = pageInfo.resQty;
        d.specification = pageInfo.specification;
        d.dePrice = pageInfo.dePrice;
        d.marketPrice = pageInfo.marketPrice;
        d.taxRemoval = pageInfo.taxRemoval;
        d.markSum = 1;
        // 数量 合价
        let rcj = PricingFileFindUtils.getRcjList(constructId, singleId, unitId).filter(f=>f.sequenceNbr === d.rcjId)[0];
        let deId = rcj.deId;
        let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        let findres = allData.filter(f=>f.sequenceNbr === deId);
        if (!findres || findres.length === 0) {
            allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        }
        let deLine = allData.filter(f=>f.sequenceNbr === deId)[0];
        this.service.rcjProcess.reCaculate(constructId, singleId, unitId, deLine);
        // 单价构成
        this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitId, deLine.sequenceNbr, true, allData);

        return {
            rcjLine: rcj,
            deLine: deLine
        }
    }

    async supplementRcjByPage(constructId, singleId, unitId, pointLine, pageInfo, type) {
        let handler =new ReplaceStrategy({constructId, singleId, unitId,pageType:type==2?"csxm":"fbfx"});
        await handler.supplement({pointLine,pageInfo});
    }
    /**
     *
     * @param constructId
     * @param singleId
     * @param unitId
     * @param pointLine
     * @param code
     * @param region   0 操作区  1 明细区
     */
    async supplementRcjByPageOld(constructId, singleId, unitId, pointLine, pageInfo, region) {
        if (!pageInfo.resQty) {
            pageInfo.resQty = 0;
        }
        if (pointLine.rcjId && pointLine.rcjId !== "") {
            let {rcjLine, deLine} = this.supplementRcjDetailByPage(constructId, singleId, unitId, pointLine, pageInfo, region);

            //编码联动
            let maxMaterialCode =  await this.service.constructProjectRcjService.getMaxMaterialCode(PricingFileFindUtils.getRcjList(constructId, singleId, unitId),
                PricingFileFindUtils.getRcjDetailList(constructId, singleId, unitId));
            await this.service.constructProjectRcjService.parentMaterialCodeChange(PricingFileFindUtils.getRcjList(constructId, singleId, unitId),
                PricingFileFindUtils.getRcjDetailList(constructId, singleId, unitId),
                rcjLine,maxMaterialCode);

            //父级价钱联动
            await this.service.rcjProcess.parentMaterialPrice(PricingFileFindUtils.getUnit(constructId, singleId, unitId),rcjLine);

            // 处理定额 换
            if (!deLine.rcjFlag || deLine.rcjFlag!== 1) {
                let nowRcjs = this.service.rcjProcess.queryRcjDataByDeId(rcjLine.deId, constructId, singleId, unitId);
                let baseRcjs = this.service.rcjProcess.getBaseRcjInfoByDeId(deLine.standardId);
                if (!deLine.appendType) {
                    deLine.appendType = [];
                }
                deLine.appendType = deLine.appendType.filter(a=>a!=="换");
                if (!this.service.rcjProcess.isSameRcj(baseRcjs, nowRcjs)) {
                    deLine.appendType.push("换");
                }
            }
        } else {//人材机进来
            let rcj;
            let dequity;
            if (region === 0) {
                rcj = this.service.rcjProcess.getRcjListByDeId(pointLine.sequenceNbr, constructId, singleId, unitId)[0];
                dequity = pointLine.quantity?pointLine.quantity:0;
            } else {
                rcj = PricingFileFindUtils.getUnit(constructId, singleId, unitId)
                    .constructProjectRcjs.filter(f => f.sequenceNbr === pointLine.sequenceNbr)[0];
                let deId = rcj.deId;
                let deItemFind = this.findLine(constructId, singleId, unitId, {sequenceNbr: deId});
                dequity = deItemFind.quantity;
            }
            // 删除详情+修改自身
            this.doSupplementRcjByPage(constructId, singleId, unitId, rcj, pageInfo, dequity);

            // 如果是操作区的rcj，还要把当前选中行的数据改掉
            if (region === 0) {
                let RCJKIN = ["", "人工费", "材料费", "机械费", "设备费", "主材费", "商砼", "砼", "浆", "商浆", "配比"];
                pointLine = this.findLine(constructId, singleId, unitId, pointLine);
                pointLine.unit = rcj.unit;
                pointLine.unitCoefficient = rcj.unitCoefficient;
                pointLine.quantity = rcj.quantity;
                ``
                pointLine.bdCode = rcj.materialCode;
                pointLine.bdName = rcj.materialName;
                pointLine.fxCode = rcj.materialCode;
                pointLine.name = rcj.materialName;
                pointLine.remark = PricingFileFindUtils.getUnit(constructId, singleId, unitId).mainDeLibrary;
                pointLine.price = rcj.dePrice;
                pointLine.rcjKind = RCJKIN[rcj.kind];
                pointLine.isSupplement = 1;
                pointLine.levelMark =rcj.levelMark;
                pointLine.appendType = [];
                // 处理工程量
                this.service.baseBranchProjectOptionService.dealQuantityExpression(constructId, singleId, unitId, pointLine.sequenceNbr);
                // 计算工程量后重新计算人材机的合计数量，合价
                this.service.rcjProcess.reCaculate(constructId, singleId, unitId, pointLine);
                // 单价构成
                let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
                let findres = allData.filter(f => f.sequenceNbr === pointLine.sequenceNbr);
                if (!findres || findres.length === 0) {
                    allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
                }
                //取费文件   当前单位的主取费文件
                let unitWork = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
                let mainDeLib = unitWork.mainDeLibrary;
                let constructMajorType = unitWork.constructMajorType;
                let feeFile = await this._getDefaultFeeFile(constructId, singleId, unitId, mainDeLib, constructMajorType);
                pointLine.costMajorName = feeFile.feeFileName;
                pointLine.costFileCode = feeFile.feeFileCode;
                pointLine.feeFileId = feeFile.feeFileId;
                pointLine.measureType = unitWork.secondInstallationProjectName ? unitWork.secondInstallationProjectName : feeFile.rateName;
                //处理其他材料 其他机械
                this.service.rcjProcess.handleSpecialRcj(constructId, singleId, unitId,rcj.deId);
                this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitId, pointLine.sequenceNbr, true, allData);
            } else {
                // 如果是明细区 处理换, 单价构成
                let allData = this.findAllDatas(constructId, singleId, unitId, {sequenceNbr: pointLine.deId});
                //处理其他材料 其他机械
                this.service.rcjProcess.handleSpecialRcj(constructId, singleId, unitId,rcj.deId);
                this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitId, pointLine.deId, true, allData);
                let deLine = this.findLine(constructId, singleId, unitId, {sequenceNbr: pointLine.deId});
                if (!deLine.appendType) {
                    deLine.appendType = [];
                } else {
                    deLine.appendType = deLine.appendType.filter(f => f != "换");
                }
                deLine.appendType.push("换");

            }

            this.afterSup(constructId, singleId, unitId, pointLine, 5);
        }
    }

    doSupplementRcjByPage(constructId, singleId, unitId, rcj, pageInfo, dequity) {
        // 删除详情
        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        if (unitProject.rcjDetailList) {
            unitProject.rcjDetailList = unitProject.rcjDetailList.filter(r=>r.rcjId !== rcj.sequenceNbr);
        }
        //xhc说补充人材机的 libraryCode 取新建单位的 主定额册编码
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let mainDeLibrary = unit.mainDeLibrary;
        //获取计税方式 '1'?'一般计税':'简易计税'
        let taxCalculationMethod = unit.projectTaxCalculation.taxCalculationMethod;

        // 自身修改
        rcj.libraryCode = mainDeLibrary;
        rcj.materialCode = pageInfo.materialCode;
        rcj.kind = pageInfo.kind;
        rcj.type = pageInfo.type;
        rcj.materialName = pageInfo.materialName;
        rcj.unit = pageInfo.unit;
        rcj.resQty = pageInfo.resQty;
        rcj.specification = pageInfo.specification;

        //判断当前人材机数据是 22还是12
        const is2022 = rcj.libraryCode.startsWith(ConstantUtil.YEAR_2022);
        if(is2022){
            if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
                //含税
                rcj.priceBaseJournalTax  = pageInfo.dePrice;
                rcj.priceMarketTax  = pageInfo.marketPrice;
            }else {
                rcj.priceBaseJournal   = pageInfo.dePrice;
                rcj.priceMarket   = pageInfo.marketPrice;
            }
        }else {
            rcj.dePrice = pageInfo.dePrice;
            rcj.marketPrice = pageInfo.marketPrice;
        }


        rcj.taxRemoval = pageInfo.taxRemoval;
        rcj.initResQty = pageInfo.resQty;
        rcj.levelMark = 0;
        rcj.markSum = 1;
        rcj.supplementDeRcjFlag = 1;
        //合计数量
        let baseNum = 1;
        let itemBillDe = this.findLine(constructId, singleId, unitId, {sequenceNbr: rcj.deId});
        let constructProjectRcj = rcj;
        if (itemBillDe.baseNum) {
            if (itemBillDe.baseNum["def"]) {
                baseNum = itemBillDe.baseNum["def"];
            }
            if (itemBillDe.baseNum[constructProjectRcj.kind]) {
                baseNum = itemBillDe.baseNum[constructProjectRcj.kind];
            }
        }
        let percent = 1;
        if (constructProjectRcj.unit === "%") {
            percent = 0.01;
        }
        constructProjectRcj.totalNumber = NumberUtil.multiply(NumberUtil.multiply(constructProjectRcj.resQty, itemBillDe.quantity), baseNum);
        constructProjectRcj.totalNumber = NumberUtil.multiply(constructProjectRcj.totalNumber, percent);
        let specialRcjCode = this.service.rcjProcess.specialRcjCode;
        //判断是否为特殊人材机 （“其他材料”、“其他材料费”、“其他机械费”、“其他机械“ 特殊编码）
        let  roleExists  =specialRcjCode.includes(constructProjectRcj.materialCode);

        if(roleExists){
            constructProjectRcj.totalNumber =1;
        }

        //合价
        if(is2022) {
            if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
                //含税
                constructProjectRcj.total = NumberUtil.numberScale(NumberUtil.multiply(constructProjectRcj.totalNumber, constructProjectRcj.priceMarketTax), 2);
            }else {
                constructProjectRcj.total = NumberUtil.numberScale(NumberUtil.multiply(constructProjectRcj.totalNumber, constructProjectRcj.priceMarket), 2);
            }
        }else {
            constructProjectRcj.total = NumberUtil.numberScale(NumberUtil.multiply(constructProjectRcj.totalNumber, constructProjectRcj.marketPrice), 2);
        }

    }

    /**
     *
     * @param constructId
     * @param singleId
     * @param unitId
     * @param lineData
     * @param type   清单 3，定额 4，人材机 5
     */
    afterSup(constructId, singleId, unitId, lineData, type) {
        if ((lineData.kind === "03" || lineData.kind === "04") && lineData.rcjFlag !== 1) {
            this.recaculateQuaty(constructId, singleId, unitId, lineData);
        }
    }

    recaculateQuaty(constructId, singleId, unitId, lineData) {
        let unit = lineData.unit;
        if (unit) {
            let unitNum = Number.parseInt(unit);
            if (Number.isNaN(unitNum)) {
                unitNum = 1;
            }

            lineData.quantityExpressionNbr = this.service.baseBranchProjectOptionService.getQuantityExpressionNbr(constructId, singleId, unitId, lineData);
            lineData.quantity = NumberUtil.divide(lineData.quantityExpressionNbr, unitNum);
        }
    }

    findAllDatas(constructId, singleId, unitId, pointLine) {
        let fbfxs = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        let res = fbfxs.filter(f=>f.sequenceNbr === pointLine.sequenceNbr);
        if (res && res.length > 0) {
            return fbfxs;
        }
        return PricingFileFindUtils.getCSXM(constructId,singleId,unitId);
    }


    /**
     * 获取所有省的下拉框
     * @param type  类型 1 清单  2 定额  3 人材机
     * @param clKind  类型 1材料  2 人工  3 机械 4 主材 5设备
     */

    async defaultCode(args) {
        let {constructId, singleId, unitId, type, clKind} = args;
        //判断定额标准是否是22定额
        let is22de = PricingFileFindUtils.is22UnitById(constructId,singleId,unitId);;
        //获取单位
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let code;
        if (type == 1) {
            if(is22de){
                code = BuChongDataEnum.QINGDAN["22"].get(unit.constructMajorType);
            }else {
                code = BuChongDataEnum.QINGDAN["12"].get(unit.constructMajorType);
            }
            let exist = this.isCodeExist(constructId, singleId, unitId,code);
            if(exist){
                code = this.querySameCodeQd(constructId, singleId, unitId, code.substring(0,9));
            }
            return code;
        }

        if (type == 2) {
            code = "补充定额001";
            let exist = this.isCodeExist(constructId, singleId, unitId,code);
            if(exist){
                code = this.querySameCodeDe(constructId, singleId, unitId, code.substring(0,4));
            }

            return code;
        }

        if (type == 3) {

            if (clKind == 1) {
                code = "补充人工001";
            } else if (clKind == 3) {
                code = "补充机械001";
            } else if (clKind == 4) {
                code = "补充设备001";
            } else if (clKind == 5) {
                code = "补充主材001";
            } else {
                code = "补充材料001";
            }
            code = this.querySameCodeRcj(constructId, singleId, unitId, code);
            return code;
        }

        return null;

    }


    /**
     * 处理补充清单默认编码
     * @param constructId
     * @param singleId
     * @param unitId
     * @param qdCode
     * @returns {*}
     */
    querySameCodeQd(constructId, singleId, unitId, code) {

        //获取分部分项数据
        let fbFx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        const collect = fbFx.filter(obj => {
            const regex = new RegExp(`^${code}`);
            return regex.test(obj.bdCode);
        }).map(obj => obj.bdCode);

        let csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        const collect2 = csxm.filter(obj => {
            const regex = new RegExp(`^${code}`);
            return regex.test(obj.fxCode);
        }).map(obj => obj.fxCode);

        if (collect2 && collect2.length > 0) {
            collect.push(...collect2);
        }

        if (collect.length > 0) {
            const collect1 = [...new Set(collect)].filter(str=>ObjectUtils.isNotEmpty(str)).sort();
            const s = collect1[collect1.length - 1];
            if (s.length > 9) {
                const substring = s.substring(9);
                const integer = parseInt(substring, 10) + 1;

                if (integer < 10) {
                    code += "00" + integer;
                } else if (integer < 100) {
                    code += "0" + integer;
                } else {
                    code += integer;
                }
            } else {
                code += "001";
            }
        } else {
            code += "001";
        }

        return code;
    }

    /**
     * 处理补充清单默认编码
     * @param constructId
     * @param singleId
     * @param unitId
     * @param qdCode
     * @returns {*}
     */
    querySameCodeDe(constructId, singleId, unitId, code) {

        //获取分部分项数据
        let fbFx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        const collect = fbFx.filter(obj => {
            const regex = new RegExp(`^${code}`);
            return regex.test(obj.bdCode);
        }).map(obj => obj.bdCode);

        let csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        const collect2 = csxm.filter(obj => {
            const regex = new RegExp(`^${code}`);
            return regex.test(obj.fxCode);
        }).map(obj => obj.fxCode);

        if (collect2 && collect2.length > 0) {
            collect.push(...collect2);
        }

        if (collect.length > 0) {
            const collect1 = [...new Set(collect)].filter(str=>ObjectUtils.isNotEmpty(str)).sort();
            const s = collect1[collect1.length - 1];
            if (s.length > 4) {
                const substring = s.substring(4);
                const integer = parseInt(substring, 10) + 1;

                if (integer < 10) {
                    code += "00" + integer;
                } else if (integer < 100) {
                    code += "0" + integer;
                } else {
                    code += integer;
                }
            } else {
                code += "001";
            }
        } else {
            code += "001";
        }

        return code;
    }


    /**
     * 处理补充人材机默认编码
     * @param constructId
     * @param singleId
     * @param unitId
     * @param code
     * @returns {*}
     */
    querySameCodeRcj(constructId, singleId, unitId, code) {
        code=code.substring(0,4);
          //获取所有的人材机数据
        let rcjList = ObjectUtils.isNotEmpty(PricingFileFindUtils.getRcjList(constructId, singleId, unitId))?PricingFileFindUtils.getRcjList(constructId, singleId, unitId):[];
        let rcjDetailList = ObjectUtils.isNotEmpty(PricingFileFindUtils.getRcjDetailList(constructId, singleId, unitId))?PricingFileFindUtils.getRcjDetailList(constructId, singleId, unitId):[];
        const collect = rcjList.filter(obj => {
            const regex = new RegExp(`^${code}`);
            return regex.test(obj.materialCode);
        }).map(obj => obj.materialCode);

        const collect1 = rcjDetailList.filter(obj => {
            const regex = new RegExp(`^${code}`);
            return regex.test(obj.materialCode);
        }).map(obj => obj.materialCode);

        if (collect1 && collect1.length > 0) {
            collect.push(...collect1);
        }

        if (collect.length > 0) {
            const collect1 = [...new Set(collect)].filter(str=>ObjectUtils.isNotEmpty(str)).sort();
            const s = collect1[collect1.length - 1];
            if (s.length > 4) {
                const substring = s.substring(4);
                const integer = parseInt(substring, 10) + 1;
                if (integer < 10) {
                    code += "00" + integer;
                } else if (integer < 100) {
                    code += "0" + integer;
                } else {
                    code += integer;
                }
            } else {
                code += "001";
            }
        } else {
            code += "001";
        }

        return code;
    }


    /**
     * 补充人材机特殊处理
     */
    async bcrcjtscl(constructId, singleId, unitId,pageInfo,pageType){
        if (ObjectUtils.isEmpty(pageInfo) || ObjectUtils.isEmpty(pageInfo.rcjList)){
            return;
        }

        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        if (ObjectUtils.isEmpty(unit)){
            return;
        }
        let priceKey = await this.service.rcjProcess.getPriceKey(constructId);
        let page = pageType == 1 ? "fbfx" : "csxm"
        let type = 2;
        let pk ;
        if (priceKey ==  "marketPrice"){
            pk = "marketPricelastEdit";
        }else if (priceKey ==  "priceMarketTax"){
            pk = "priceMarketTaxlastEdit";
        }else if (priceKey ==  "priceMarket"){
            pk = "priceMarketlastEdit";
        }

        const params = {
            constructId:constructId,
            singleId: singleId,
            unitId: unitId,
            type: type,
            pageType: page
        };
        for (let rcjListElement of pageInfo.rcjList) {
            params.sequenceNbr = rcjListElement.sequenceNbr;

            let constructProjectRcj ={};
            constructProjectRcj[priceKey] = rcjListElement[pk];
            params.constructProjectRcj=constructProjectRcj;
            await this.service.constructProjectRcjService.updateConstructAndDetailRcj(params);
        }

        await this.service.autoCostMathService.autoCostMath({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        });
    }



}

SupplementService.toString = () => '[class SupplementService]';
module.exports = SupplementService;
