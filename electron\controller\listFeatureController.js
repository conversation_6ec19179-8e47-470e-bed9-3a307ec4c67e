const {Controller} = require("../../core");
const {ListFeature} = require("../model/ListFeature");
const {ResponseData} = require("../utils/ResponseData");

/**
 * 清单特征 controller
 */
class ListFeatureController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    listFeatureProcess = this.service.listFeatureProcess;
    listFeatureService = this.service.listFeatureService;

    /**
     *  清单项目特征列表
     * @param fbFxQdId  分部分项清单id
     * @param constructId
     * @param singleId
     * @param unitId
     * @return ListFeature[]
     */
    async listQdFeature(args) {
        let {fbFxQdId, constructId, singleId, unitId} = args;

        const result = await this.listFeatureService.getQdFeatureListByQdId(fbFxQdId, constructId, singleId, unitId);
        return ResponseData.success(result);
    }

    /**
     * 根据项目特征 查询 项目特征 对应的下拉选项
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async listFeatureDownPullMenu(args){
        let {qdCode, featureName,constructId, singleId, unitId} = args;

        const result = await this.listFeatureService.listFeatureDownPullMenu(qdCode, featureName,constructId, singleId, unitId);
        return ResponseData.success(result);

    }



    /**
     * 编辑特征
     * @param qdId 清单id
     * @param upDateLine {
     *     sequenceNbr
     *     val
     *     checkFlag 0 没选 1 选了
     * }
     * @param constructId
     * @param singleId
     * @param unitId
     */
    editQdFeature(args) {
        let {upDateLine, qdId, constructId, singleId, unitId} = args;
        const result =  this.listFeatureService.editQdFeature(upDateLine, qdId, constructId, singleId, unitId);
        return ResponseData.success(true);
    }

    /**
     * constructId, singleId, unitId c/s/u Id
     * fbFxQdId  清单的id
     * pointLine 选中的特征行
     * direction 方向  0下  1上
     */
    move(args) {
        let {fbFxQdId, constructId, singleId, unitId, pointLine, direction} = args;
        if (direction == 1) {
            this.listFeatureService.moveUp(fbFxQdId, constructId, singleId, unitId, pointLine);
        } else {
            this.listFeatureService.moveDown(fbFxQdId, constructId, singleId, unitId, pointLine);
        }

        return ResponseData.success(true);
    }

    /**
     * fbFxQdId  分部分项id
     * constructId, singleId, unitId  c/s/u id
     * pointLine 选中的特征行
     * @param args
     * @return {ResponseData}
     */
    addLine(args) {
        let {fbFxQdId, constructId, singleId, unitId, pointLine} = args;
        let res = this.listFeatureService.createFeature(fbFxQdId, constructId, singleId, unitId, pointLine)

        return ResponseData.success(res);
    }

    /**
     * 删除行 参数同新增
     * @param args
     * @return {ResponseData}
     */
    removeFeature(args) {
        let {fbFxQdId, constructId, singleId, unitId, pointLine} = args;
        let res = this.listFeatureService.removeFeature(fbFxQdId, constructId, singleId, unitId, pointLine)

        return ResponseData.success(true);
    }

}

ListFeatureController.toString = () => '[class ListFeatureController]';
module.exports = ListFeatureController;




