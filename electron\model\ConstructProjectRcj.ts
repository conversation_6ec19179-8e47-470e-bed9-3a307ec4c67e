import {BaseModel} from "./BaseModel";
import {RcjDetails} from "./RcjDetails";


/**
 * 工程项目人材机表(JieSuanConstructProjectRcj)DTO类
 * 与BS保持一致
 */
export class ConstructProjectRcj extends BaseModel {


    /**
     * 编码
     */
    public materialCode: string;

    /**
     * base rcj ID
     */
    public standardId: string

    /**
     * 名称
     */
    public materialName: string;

    /**
     * 规格及型号
     */
    public specification: string;

    /**
     * 单位
     */
    public unit: string;

    /**
     * 单位id
     */
    public unitId: string;


    /**
     * 信息价
     */
    public informationPrice:number;

    /**
     * 序号
     */
    public dispNo:number;

    /**
     * 推荐价
     */
    public recommendPrice:number;

    /**
     * 精准市场价的备份 调整优先级后要用
     */
    public marketPriceOrigin: number;

    /**
     * 精准市场价的价格来源备份
     */
    public marketSourcePriceOrigin:number;

    /**
     * 精准信息价的备份
     */
    public informationPriceOrigin:number;

    /**
     * 精准信息价的价格来源备份
     */
    public informationSourcePriceOrigin:number;

    /**
     * 精准推荐价的备份
     */
    public recommendPriceOrigin:number;

    /**
     * 精准推荐价的价格来源备份
     */
    public recommendSourcePriceOrigin:number;

    /**
     * 载价前原来人材机的市场价
     */
    public marketPriceBeforeLoading:number;

    /**
     * 载价前原来人材机的市场价价格来源
     */
    public marketSourcePriceBeforeLoading:number;

    /**
     * 价格来源
     */
    public sourcePrice: string;

    /**
     * 是否汇总
     */
    public iscount: number;

    /**
     * 价差
     */
    public priceDifferenc: number;

    /**
     * 价差合计
     */
    public priceDifferencSum: number;

    /**
     * 品牌
     */
    public brand: string;

    /**
     * 厂家
     */
    public manufactor: string;

    /**
     * 产地
     */
    public producer: string;

    /**
     * 送达地点
     */
    public deliveryLocation: string;

    /**
     * 质量等级
     */
    public qualityGrade: string;

    /**
     * 除税系数
     */
    public taxRemoval: number;

    /**
     * 除税系数备份
     */
    public taxRemovalBackUp: number;

    /**
     * 企业除税价格
     */
    public csMarketPrice: number;

    /**
     * kind(0：其他费；1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比)
     */
    public kind: number;

    /**
     * 定额册编码
     */
    public libraryCode: string;

    /**
     * 材料类别(1：人工材料；2：计价材料；3：未计价材料；4：机械台班；5：仪器仪表；6：配合比材料；7：设备；8：子定额；9：自定义材料；10：其他；11：新补充材料；20：定额人工；21：定额材料；22：定额机械；23：定额设备；24：定额主材）
     */
    public materialCategory: string;

    /**
     * 三材类别
     */
    public threeMaterialCategories: string;

    /**
     * 地区
     */
    public area: string;

    /**
     * 类别
     */
    public type: string;

    /**
     * 材料价格年月
     */
    public priceDate: string;

    /**
     * 更新时间-记录时间
     */
    public updateDate: Date;

    /**
     * 创建者
     */
    public recUserId: string;

    /**
     * 工程id
     */
    public constructId: string;

    /**
     * 甲供价格
     */
    public donorMaterialPrice: number;

    /**
     * 甲供名称
     */
    public donorMaterialName: string;

    /**
     * 甲供规格型号
     */
    public donorSpecification: string;

    /**
     * 是否是甲供(0:不是，1：是 甲供, 2是甲定已供)
     */
    public ifDonorMaterial: number;

    /**
     * 甲供数量
     */
    public donorMaterialNumber: number;

    /**
     * 甲供 数量特殊 状况处理字段 一直情况 给-1 不一致给具体数量
     */
    public donorMaterialNumberManage: number;



    /**
     * kind备份
     */
    public kindBackUp: number;

    /**
     * 甲供材料ID
     */
    public materialSequenceNbr: string;

    /**
     * 是否锁定材料价格(0：否  1：是)
     */
    public ifLockStandardPrice: number;


    /**
     * 是否锁定工程量(0:不是，1：是)
     */
    public ifLockQuantity: number;

    /**
     * 暂估名称
     */
    public provisionalEstimateName: string;

    /**
     * 暂估价格
     */
    public provisionalEstimatePrice: number;

    /**
     * 暂估型号
     */
    public provisionalEstimateSpecification: string;

    /**
     * 暂估ID
     */
    public provisionalEstimateSequenceNbr: string;

    /**
     * 是否是暂估(非1:不是，1：是)
     */
    public ifProvisionalEstimate: number;

    /**
     * 记录勾选暂估之前的价格
     */
    public beforeZgjPrice: number;



    /**
     *所属定额id
     */
    public deId: string;
    /**
     * 材料消耗量
     */
    public resQty: number;

    /**
     * 措施项目清单系数与材料消耗量计算初始值
     */
    public confficientInitQty: number;



    /**
     * 原始含量
     */
    public initResQty: number;

    /**
     * 是否下沉标识（2：下沉机械；1：下沉配比；0：无需下沉）
     */
    public levelMark: string;

    /**
     * 合计数量
     */
    private totalNumber: number;

    /**
     * 合价
     */
    private total: number;

    /**
     * 人材机明细数据
     */
    public rcjDetailsDTOs: Array<RcjDetails>;



    /**
     * 是否汇总(解析) 1代表解析 0代表不解决  默认是1 解析
     */
    private markSum: number;

    /**
     * 进项合计
     */
    public jxTotal: number;


    /**
     * 编辑记录
     */
    public referenceRecord: string;


    /**
     * 标记 是否是定额部分人材机 ture 表示是定额 人材机
     */
    public labelDe: boolean;


    /**
     * 1代表 费用定额人材机 (无法编辑 类型，名称，规格型号，单位，市场价，市场价锁定 )
     *
     */
    public edit: number;

    /**
     * 1代表 补充人材机
     *
     */
    public supplementDeRcjFlag: number;
    /**
     * 是否为定额人材机 1是0 否
     */
    public rcjFlag: number;


    /**
     * 1代表 添加的人材机
     *
     */
    public addRcjType: number;

    /**
     * 1代表 人材机消耗量进行过修改
     */
    public resQtyChangeType: number;


    /**
     * 1代表 人材机进行过kind3D的标准换算
     */
    public kind3dType: number;

    /**材料替换历史记录 用于勾选时替换材料 取消勾选后恢复 */
    public materialReplaceHistory?: {
        materialCode: string,
        materialName: string
        dePrice: number
    }

    /**
     * 是否勾选执行载价  true为执行
     */
    public isExecuteLoadPrice:boolean;

    /**
     * 待载价格
     */
    public loadPrice:number;

    /**
     * 载价编辑弹窗的待载价格是否高亮
     */
    public highlight:boolean;

    /**
     * 信息价的价格来源  用于载价编辑页面手动双击价格时应用
     */
    public informationSourcePrice:string;

    /**
     * 市场价价格来源
     */
    public marketSourcePrice:string;

    /**
     * 推荐价价格来源
     */
    public recommendSourcePrice:string;

    public consumerResQty: string


    // /**
    //  * 不含税基期价 一般计税 (12 dePrice)
    //  */
    // public priceBaseJournal:number;
    //
    // /**
    //  * 含税基期价  简易计税 (12 dePrice)
    //  */
    // public priceBaseJournalTax:number;
    //
    // /**
    //  * 不含税市场价 一般计税 (12 市场价)
    //  */
    // public priceMarket:number;
    //
    // /**
    //  * 含税市场价 简易计税
    //  */
    // public priceMarketTax:number;


    /**
     * 12定额 : 定额价
     * 22定额 一般计税 : 不含税基期价
     *       简易计税 : 含税基期价
     */
    public dePrice: number;

    /**
     * 12定额 : 市场价
     * 22定额 一般计税 : 不含税市场价
     *       简易计税 : 含税市场价
     */
    public marketPrice: number;


    /**
     * 12定额 : 市场价公式
     * 22定额 一般计税 : 不含税市场价公式
     *       简易计税 : 含税市场价公式
     */
    public marketPriceFormula: string;

    /**
     * 临时删除标识  true 临时删除  false 不临时删除
     */
    public tempDeleteFlag: boolean;

    /**
     * 临时删除备份消耗量
     */
    public tempDeleteBackupResQty: number;


    /**
     * '是否费用人材机(0:是，1否)'
     */
    public isFyrcj: number;

    /**
     * 如果为 0 22定额 人材机 市场价展示为-
     * 22定额中部分材料的基期价、市场价为“-”，该部分数据的价格列不可编辑，不可勾选“是否暂估”，且该部分数据同材料需在人材机明细、人材机汇总、主要材料设备明细表中展示；
     * 当前22定额标准中存在部分配比数据，其基期价为“-”（注：“-”与“0”不同），该部分数据的市场价不可调整，其类别无法修改为“主材”/“设备”，且无法设置为“暂估”
     */
    public isChangeAva: number;

    /**是否是由标准换算添加的人材机 */
    public addFromConversionRuleId?: string

    /**人材机被哪些规则ID修改消耗量 */
    public changeResQtyRuleIds?: string[]

    /**人材机被哪些规则ID影响 */
    public ruleIds?: string[]

    /**人材机修改消耗量后与哪些规则ID绑定并冻结消耗量 */
    public freezeRuleIds?: string[]

    /**默认的材料编码 标准换算会替换材料编码 这里保存最初的编码 用来判断 */
    defaultMaterialCode?: string

    //三材类别
    public kindSc: string;

    //三材系数
    public transferFactor: string;


    // '不含税基期价',
    public priceBaseJournal: number;

    // '含税基期价',
    public priceBaseJournalTax: number;

    // '不含税市场价' 一般计税
    public priceMarket: number;

    // '不含税市场价' 一般计税计算公式
    public priceMarketFormula: string;

    // '含税市场价' 简易计税
    public priceMarketTax: number;

    // '含税市场价' 简易计税计算公式
    public priceMarketTaxFormula: string;

    //税率
    public taxRate: number;

    //代表补充人材机
    public isSupplement : number;
    // 应用人材机定额及计数
    public rcjDeNumberMap: Map<string, number>;

    //消耗量是否锁定
    public isLock : boolean;

    //单位工程 载价 条件记录
    public loadPriceCache:any;

    //工程项目人材机汇总排序 设置记录
    public rcjKindSort:any;

    //人材机 主材类型
    public kind7Sort:any;

    //是否是承包人材料  (非1:不是，1：是)
    public isCbrRcj :number;

    //单位批注
    public unitPostil:any;

    //人材机单位批注 批注展示状态 0展示,1不展示
    public unitPostilState:any;

    //单项批注
    public singlePostil:any;

    //人材机单项批注 批注展示状态 0展示,1不展示
    public singlePostilState:any;

    //工程项目批注
    public constructPostil:any;

    //人材机工程项目批注 批注展示状态 0展示,1不展示
    public constructPostilState:any;


    constructor(sequenceNbr: string, recUserCode: string, recStatus: string, recDate: string, extend1: string, extend2: string, extend3: string, description: string, materialCode: string, standardId: string, materialName: string, specification: string, unit: string, unitId: string, informationPrice: number, dispNo: number, recommendPrice: number, marketPriceOrigin: number, marketSourcePriceOrigin: number, informationPriceOrigin: number, informationSourcePriceOrigin: number, recommendPriceOrigin: number, recommendSourcePriceOrigin: number, marketPriceBeforeLoading: number, marketSourcePriceBeforeLoading: number, sourcePrice: string, iscount: number, priceDifferenc: number, priceDifferencSum: number, brand: string, manufactor: string, producer: string, deliveryLocation: string, qualityGrade: string, taxRemoval: number, taxRemovalBackUp: number, csMarketPrice: number, kind: number, libraryCode: string, materialCategory: string, threeMaterialCategories: string, area: string, type: string, priceDate: string, updateDate: Date, recUserId: string, constructId: string, donorMaterialPrice: number, donorMaterialName: string, donorSpecification: string, ifDonorMaterial: number, donorMaterialNumber: number, donorMaterialNumberManage: number, kindBackUp: number, materialSequenceNbr: string, ifLockStandardPrice: number, ifLockQuantity: number, provisionalEstimateName: string, provisionalEstimatePrice: number, provisionalEstimateSpecification: string, provisionalEstimateSequenceNbr: string, ifProvisionalEstimate: number, deId: string, resQty: number, confficientInitQty: number, initResQty: number, levelMark: string, totalNumber: number, total: number, rcjDetailsDTOs: Array<RcjDetails>, markSum: number, jxTotal: number, referenceRecord: string, labelDe: boolean, edit: number, supplementDeRcjFlag: number, rcjFlag: number, addRcjType: number, resQtyChangeType: number, kind3dType: number, materialReplaceHistory: {
        materialCode: string;
        materialName: string;
        dePrice: number
    // }, isExecuteLoadPrice: boolean, loadPrice: number, kind7Sort:any,isCbrRcj :number,highlight: boolean, informationSourcePrice: string, marketSourcePrice: string, recommendSourcePrice: string, consumerResQty: string, dePrice: number, marketPrice: number, tempDeleteFlag: boolean, tempDeleteBackupResQty: number, isFyrcj: number, isChangeAva: number, addFromConversionRuleId: string, changeResQtyRuleIds: string[], ruleIds: string[], freezeRuleIds: string[], defaultMaterialCode: string, kindSc: string, transferFactor: string, priceBaseJournal: number, priceBaseJournalTax: number, priceMarket: number, priceMarketTax: number, taxRate: number, isSupplement: number, rcjDeNumberMap: Map<string, number>, isLock: boolean) {
    }, isExecuteLoadPrice: boolean, loadPrice: number, kind7Sort:any, unitPostil:any,unitPostilState:any,singlePostil:any,singlePostilState:any,constructPostil:any,constructPostilState:any,isCbrRcj :number,highlight: boolean, informationSourcePrice: string, marketSourcePrice: string, recommendSourcePrice: string, consumerResQty: string, dePrice: number, marketPrice: number,marketPriceFormula: string, tempDeleteFlag: boolean, tempDeleteBackupResQty: number, isFyrcj: number, isChangeAva: number, addFromConversionRuleId: string, changeResQtyRuleIds: string[], ruleIds: string[], freezeRuleIds: string[], defaultMaterialCode: string, kindSc: string, transferFactor: string, priceBaseJournal: number, priceBaseJournalTax: number, priceMarket: number, priceMarketTax: number,postilState:any, priceMarketFormula: string, priceMarketTaxFormula: string,taxRate: number, isSupplement: number, rcjDeNumberMap: Map<string, number>, isLock: boolean) {
        super(sequenceNbr, recUserCode, recStatus, recDate, extend1, extend2, extend3, description);
        this.materialCode = materialCode;
        this.standardId = standardId;
        this.materialName = materialName;
        this.specification = specification;
        this.unit = unit;
        this.unitId = unitId;
        this.informationPrice = informationPrice;
        this.dispNo = dispNo;
        this.recommendPrice = recommendPrice;
        this.marketPriceOrigin = marketPriceOrigin;
        this.marketSourcePriceOrigin = marketSourcePriceOrigin;
        this.informationPriceOrigin = informationPriceOrigin;
        this.informationSourcePriceOrigin = informationSourcePriceOrigin;
        this.recommendPriceOrigin = recommendPriceOrigin;
        this.recommendSourcePriceOrigin = recommendSourcePriceOrigin;
        this.marketPriceBeforeLoading = marketPriceBeforeLoading;
        this.marketSourcePriceBeforeLoading = marketSourcePriceBeforeLoading;
        this.sourcePrice = sourcePrice;
        this.iscount = iscount;
        this.priceDifferenc = priceDifferenc;
        this.priceDifferencSum = priceDifferencSum;
        this.brand = brand;
        this.manufactor = manufactor;
        this.producer = producer;
        this.deliveryLocation = deliveryLocation;
        this.qualityGrade = qualityGrade;
        this.taxRemoval = taxRemoval;
        this.taxRemovalBackUp = taxRemovalBackUp;
        this.csMarketPrice = csMarketPrice;
        this.kind = kind;
        this.libraryCode = libraryCode;
        this.materialCategory = materialCategory;
        this.threeMaterialCategories = threeMaterialCategories;
        this.area = area;
        this.type = type;
        this.priceDate = priceDate;
        this.updateDate = updateDate;
        this.recUserId = recUserId;
        this.constructId = constructId;
        this.donorMaterialPrice = donorMaterialPrice;
        this.donorMaterialName = donorMaterialName;
        this.donorSpecification = donorSpecification;
        this.ifDonorMaterial = ifDonorMaterial;
        this.donorMaterialNumber = donorMaterialNumber;
        this.donorMaterialNumberManage = donorMaterialNumberManage;
        this.kindBackUp = kindBackUp;
        this.materialSequenceNbr = materialSequenceNbr;
        this.ifLockStandardPrice = ifLockStandardPrice;
        this.ifLockQuantity = ifLockQuantity;
        this.provisionalEstimateName = provisionalEstimateName;
        this.provisionalEstimatePrice = provisionalEstimatePrice;
        this.provisionalEstimateSpecification = provisionalEstimateSpecification;
        this.provisionalEstimateSequenceNbr = provisionalEstimateSequenceNbr;
        this.ifProvisionalEstimate = ifProvisionalEstimate;
        this.deId = deId;
        this.resQty = resQty;
        this.confficientInitQty = confficientInitQty;
        this.initResQty = initResQty;
        this.levelMark = levelMark;
        this.totalNumber = totalNumber;
        this.total = total;
        this.rcjDetailsDTOs = rcjDetailsDTOs;
        this.markSum = markSum;
        this.jxTotal = jxTotal;
        this.referenceRecord = referenceRecord;
        this.labelDe = labelDe;
        this.edit = edit;
        this.supplementDeRcjFlag = supplementDeRcjFlag;
        this.rcjFlag = rcjFlag;
        this.addRcjType = addRcjType;
        this.resQtyChangeType = resQtyChangeType;
        this.kind3dType = kind3dType;
        this.materialReplaceHistory = materialReplaceHistory;
        this.isExecuteLoadPrice = isExecuteLoadPrice;
        this.loadPrice = loadPrice;
        this.highlight = highlight;
        this.informationSourcePrice = informationSourcePrice;
        this.marketSourcePrice = marketSourcePrice;
        this.recommendSourcePrice = recommendSourcePrice;
        this.consumerResQty = consumerResQty;
        this.dePrice = dePrice;
        this.marketPrice = marketPrice;
        this.tempDeleteFlag = tempDeleteFlag;
        this.tempDeleteBackupResQty = tempDeleteBackupResQty;
        this.isFyrcj = isFyrcj;
        this.isChangeAva = isChangeAva;
        this.addFromConversionRuleId = addFromConversionRuleId;
        this.changeResQtyRuleIds = changeResQtyRuleIds;
        this.ruleIds = ruleIds;
        this.freezeRuleIds = freezeRuleIds;
        this.defaultMaterialCode = defaultMaterialCode;
        this.kindSc = kindSc;
        this.transferFactor = transferFactor;
        this.priceBaseJournal = priceBaseJournal;
        this.priceBaseJournalTax = priceBaseJournalTax;
        this.priceMarket = priceMarket;
        this.priceMarketTax = priceMarketTax;
        this.priceMarketFormula = priceMarketFormula;
        this.priceMarketTaxFormula = priceMarketTaxFormula;
        this.marketPriceFormula = marketPriceFormula;
        this.taxRate = taxRate;
        this.isSupplement = isSupplement;
        this.rcjDeNumberMap = rcjDeNumberMap;
        this.isLock = isLock;
        this.kind7Sort = kind7Sort;
        this.isCbrRcj = isCbrRcj;
        this.unitPostil = unitPostil;
        this.unitPostilState = unitPostilState;
        this.singlePostil = singlePostil;
        this.singlePostilState = singlePostilState;
        this.constructPostil = constructPostil;
        this.constructPostilState = constructPostilState;
    }
}
