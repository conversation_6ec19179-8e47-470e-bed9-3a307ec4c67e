const BranchProjectLevelConstant = require("../../../enum/BranchProjectLevelConstant");
const EE = require('../../../../core/ee');
const _ = require("lodash");
const {NumberUtil} = require("../../../utils/NumberUtil");
const {CupmuteContext} = require("../../../unit_price_composition/compute/ComputingCore");
const {keysQdMap,qdBaseFn,qdRules} = require("../../rules/qd");
const {keysSupplementQdMap,qdSupplementBaseFn} = require("../../rules/supplementQd");
const {ObjectUtils} = require("../../../utils/ObjectUtils");
const {getUnitFormatEnum} = require("../../rules/format");
class SupplementQdHandler extends CupmuteContext{
    constructor(ctx,pageInfo) {
        super();
        this.ctx = ctx;
        this.allData = ctx.allData;
        this.pageInfo=pageInfo;
    }
    async prepare() {
        let {pointLine} = this.pageInfo;
        this.pointLine = this.allData.getNodeById(pointLine.sequenceNbr);
        this.analyzeBaseFn(qdSupplementBaseFn);
    }
    getValue({type, kind, cloumn}){
        //获取方式留给子类实现
        let value = "";
        switch (type) {
            case "from": {
                //基础定额
                if (typeof cloumn == "function") {
                    value = cloumn(this.pageInfo);
                } else {
                    value = this.pageInfo[cloumn] || "";
                }
                break;
            }
            default:{
                if (typeof cloumn == "function") {
                    value = cloumn(this);
                }
            }
        }
        return value;

    }
    async replace() {
        this.prepare();
        this._fillQDLineData();
        this.pointLine.quantity = NumberUtil.numberScale(this.pointLine.quantity,getUnitFormatEnum(this.pointLine.unit).value);
        return this.pointLine;
    }
    async _fillQDLineData() {
        this.pointLine.isSupplement = 1;//代表室补充定额
        this.pointLine.appendType = ['补清'] //1. 清单也需要针对类别进行处理，需增加【补】，若清单通过用户补充插入，则类别列展示为【补清】
        for (const key of keysSupplementQdMap) {
            this.pointLine[key] = this.parseParams(key);
        }
        this.pointLine.isEmpData=1;
    }
}
class QdHandler extends CupmuteContext {
    constructor(ctx, upDateInfo) {
        super();
        this.ctx = ctx;
        this.pointLine = null;
        this.allData = ctx.allData;
        this.upDateInfo = upDateInfo;
        this.upDateInfo.pointLine.adjustmentCoefficient=1;
        this.pointConfig={
            "rcjDetailAmount": 2,   //人材机明细区：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
            "rcjSummaryAmount": 2,   //人材机汇总：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
            "qDDeAmount": 2,  // 工程量，数量类：小数点后3位，第4位四舍五入
            "costPrice": 2, // 金额、合计，金额类：小数点后2位，第3位四舍五入
            "rate": 2,  // 费率、指数、比率(%)：小数点后2位，第3位四舍五入
      
          }
    }

    async prepare() {
        let {pointLine, indexId, is2022, unit, rcjFlag,selectedLine} = this.upDateInfo;
        let {service} = EE.app;
        this.pointLine = this.allData.getNodeById(this.upDateInfo.pointLine.sequenceNbr);
        if (ObjectUtils.isNotEmpty(indexId)) { //indexId为null 属于特殊情况,更新信息是补充清单（补充清单要替换其他清单这种情况）
            this.baseQdOrDe = await service.baseQdDeProcess.selectBaseQdOrDe(indexId, BranchProjectLevelConstant.qd);
        }else {
            this.baseQdOrDe = selectedLine;
        }
        this.analyzeBaseFn(qdBaseFn);
        this.analyzeCoreRules(qdRules);
        this.pointConfig=service.globalConfigurationService.getDecimalPointConfig()
    }
    getValue({type, kind, cloumn}) {
        //获取方式留给子类实现
        let value = "";
        switch (type) {
            case "QD": {
                //基础定额
                if (typeof cloumn == "function") {
                    value = cloumn(this.baseQdOrDe);
                } else {
                    value = this.baseQdOrDe[cloumn] || "";
                }
                break;
            }
            default:{
                if (typeof cloumn == "function") {
                    value = cloumn(this);
                }
            }
        }
        return value;
    }

    async replace() {
        await this.prepare();
        await this._fillQDLineData();
        await this.handlerQuantity();
        await this._fillQdFeature();
        return this.pointLine;
    }

    async _fillQDLineData() {
        this.pointLine.isSupplement = 0;
        for (const key of keysQdMap) {
            this.pointLine[key] = this.parseParams(key);
        }
        this.pointLine.isEmpData=1;
    }
    convertValue(value) {
        return NumberUtil.numberScale(value, this.pointConfig.costPrice);
    }
    async handlerQuantity() {
        //1. 填充 工程量表达式，工程量 默认值  若清单 表达式 = 1， 工程量 = 1； 若是定额 表达式 继承清单的,  工程量  从 UnitConversion根据单位取
        let {constructId, singleId, unitId} = this.ctx;
        let {service} = EE.app;
        this.pointLine.quantityExpression = this.pointLine.quantityExpression || this.pointLine.quantityExpression == 0 ? this.pointLine.quantityExpression : "1";
        let unitNum =  Number.parseInt(this.pointLine.unit);
        if (Number.isNaN(unitNum)) {
            unitNum = 1;
        }
        this.pointLine.quantityExpressionNbr = this.pointLine.quantityExpressionNbr || this.pointLine.quantityExpressionNbr === 0 ?
            this.pointLine.quantityExpressionNbr : service.baseBranchProjectOptionService.getQuantityExpressionNbr(constructId, singleId, unitId, this.pointLine);
        //如果替换的时候工程量明细不为空的情况工程量不需要初始化赋值
        let count = _.filter(this.pointLine.quantities, (item) => !_.isEmpty(item.mathFormula));
        if (!count.length) {
            this.pointLine.quantity = NumberUtil.numberScale( NumberUtil.multiply(this.pointLine.quantityExpressionNbr, unitNum),getUnitFormatEnum(this.pointLine.unit).value);
        }
        this.pointLine.quantity = NumberUtil.numberScale(this.pointLine.quantity,getUnitFormatEnum(this.pointLine.unit).value);
    }
    //处理清单特征
    async _fillQdFeature() {
        let {service} = EE.app;
        let {libraryCode, sequenceNbr, fxCode} = this.pointLine;
        let {constructId, singleId, unitId} = this.ctx;
        if (ObjectUtils.isNotEmpty(libraryCode) && ObjectUtils.isNotEmpty(fxCode)) {
            await service.listFeatureProcess.saveBatchToFbFxQdFeature(libraryCode, fxCode,
                sequenceNbr, constructId,
                singleId, unitId);
        }
    }

    /**
     * 获取清单编码
     */
    getQdCode(qdCode) {
        let strCode = "" + qdCode;
        if (this._isBaseQdCode(strCode)) {
            let baseCode = strCode.substring(0, 9);
            // 标准清单
            let maxCodeNum = this._getMaxQdCodeNum(baseCode)
            let newCode = maxCodeNum + 1;
            return baseCode + _.padStart(newCode, 3, '0');
        } else {
            return qdCode;
        }
    }

    /**
     * 根据清单标准编码获取最大的清单编码
     * 获取到当前单位的 分部分项措施项目 匹配到标准的清单编码 获取最大的
     * @param baseCode
     * @returns {number}
     * @private
     */
    _getMaxQdCodeNum(baseCode) {
        //这里的 itemBillProjects 和measureProjectTables都是树形结构
        let {unit: {itemBillProjects, measureProjectTables}} = this.ctx;
        let codeList = [];
        let matchListA = itemBillProjects.getAllNodes().filter(item => item.bdCode && item.bdCode.startsWith(baseCode));
        let max = null;
        //条件获取到相对应的清单集合
        //根据清单编码排序
        if (matchListA && matchListA.length > 0) {
            max = _.maxBy(matchListA, function (o) {
                return o.bdCode;
            }).bdCode;
        }
        let matchListB = measureProjectTables.getAllNodes().filter(item => item.fxCode && item.fxCode.startsWith(baseCode));
        if (matchListB && matchListB.length > 0) {
            let b = _.maxBy(matchListB, function (o) {
                return o.fxCode;
            }).fxCode;
            if (max && max < b) max = b;
            if (!max) max = b;
        }
        if (!max) return 0;
        return max.length > 9 ? Number.parseInt(max.substring(9)) : 0;
    }

    /**
     * 判断是否是标准清单
     * @private
     */
    _isBaseQdCode(strCode) {

        if (strCode.length < 9) {
            return false;
        }
        if (strCode.length > 9) {
            strCode = strCode.substring(0, 9);
        }
        let {betterSqlite3DataSource} = EE.app;
        let querySql = "select bd_code_level04 from base_list where bd_code_level04 = ?";
        let sqlRes = betterSqlite3DataSource.prepare(querySql).all(strCode);
        return sqlRes.length > 0;
    }

}


module.exports = {
    QdHandler, SupplementQdHandler
}
