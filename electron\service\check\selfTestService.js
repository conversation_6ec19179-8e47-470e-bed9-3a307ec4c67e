const CheckPlayground = require("../../check/palyground");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const {checkContext} = require("../../check/context");


const unitProjectPrivateCheckList =  require( "../../check/checklist").unitProjectPrivateCheckList;
const zbProjectPrivateCheckList =  require( "../../check/checklist").zbProjectPrivateCheckList;
const tbProjectPrivateCheckList =  require( "../../check/checklist").tbProjectPrivateCheckList;
const CheckListKey =  require( "../../check/checklist").CheckListKey;
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");


const {Service} = require("../../../core");
const _ = require("lodash");
const BranchProjectLevelConstant = require("../../enum/BranchProjectLevelConstant");
const {arrayToTree} = require("../../main_editor/tree");
const {treeToArray} = require("../../main_editor/tree");
const {ResponseData} = require("../../utils/ResponseData");



class SelfTestService extends Service {

    constructor(ctx) {
        super(ctx);
        this.standardMap = new Map();
    }


    /**
     * 检查项列表
     * @param args
     */
    async checkItems(args) {
        let arr=[];
        for (const projectObjKey in CheckListKey) {
            CheckListKey[projectObjKey].checkFlag = 1
            arr.push(CheckListKey[projectObjKey]);
        }


        let resArr = [];
        let projectObj =  PricingFileFindUtils.getProjectObjById(args.constructId);
        if(projectObj.biddingType ===0){

            resArr = arr;
            resArr.pop();

        }else if(projectObj.biddingType ===1){
            resArr = arr;
        }else {

            resArr.push(arr[0]);//检查项目清单编码重复
            resArr.push(arr[1]);//相同清单单位不一致
            resArr.push(arr[4]);//清单项目特征为空
            resArr.push(arr[5]);//清单单位为空
            resArr.push(arr[6]);//清单工程量为空或为零
            resArr.push(arr[7]);//清单综合单价为零
            resArr.push(arr[25]);//未组价清单
            resArr.push(arr[8]);//定额工程量为零
            resArr.push(arr[9]);//定额单价为零
            resArr.push(arr[10]);//同一人材机有多个价格
            resArr.push(arr[11]);//人材机消耗量为零
            resArr.push(arr[12]);//人材机单价为零
            resArr.push(arr[28]);//暂估材料是否关联
        }

        //判断之前有没有调过自检
        let result = checkContext.getResult(args.constructId);
        if(!ObjectUtils.isEmpty(result)){
            let selectCheck = result.selectCheck;

            for (let i = 0; i < resArr.length; i++) {
                resArr[i].checkFlag = 0
            }
            for (let i = 0; i < selectCheck.length; i++) {
                resArr.find(item => item.checkType ===selectCheck[i] ).checkFlag = 1;
            }

        }

        return  resArr;
    }
    //编码刷新
    async refreshCode(args){
      const {isGlobal,isSkip,constructId} =args;
      try {
          //获取项目数据
          let projectObj =  PricingFileFindUtils.getProjectObjById(constructId);
          //获取自检结果
          let result = checkContext.getResult(constructId);
          if(result){
              let {unitSequenceNbrArray,resultMap}=result;
              let checkResult = resultMap.find(item=>item.checkType==CheckListKey.QD_UNIT_INCONSISTENT.checkType);
              let skipArray = [];
              if(checkResult&&checkResult.childrenList.length>0&&isSkip){
                  checkResult.childrenList.forEach(item=>{
                      item.childrenList.forEach(i=>{
                          skipArray.push(i.sequenceNbr);
                      });
                  });
              }
              this.standardMap.clear();
              const {singleProjects, unitProject} = projectObj;
              let unitProjects = [];
              if (singleProjects) {
                  unitProjects = singleProjects.flatMap(singleProject => singleProject.unitProjects);
              }
              if (unitProject) {
                  unitProjects.push(unitProject);
              }
              for (let i = 0; i < unitProjects.length; i++) {
                  let item = unitProjects[i];
                  if(unitSequenceNbrArray.includes(item.sequenceNbr)){
                      if(!isGlobal){
                          this.standardMap.clear();
                      }
                      await this.batchRefresh(item,skipArray);
                  }
              }
          }
      }catch (e){
          console.log(e);
          return  false;
      }
        return  true;
    }
    async batchRefresh(unit,skipArray){
        //分布分项              措施项目
        let {itemBillProjects,measureProjectTables} =  unit;
        itemBillProjects = treeToArray(itemBillProjects);
        measureProjectTables = treeToArray(measureProjectTables);
        //获取所有标准的分部分项清单项目
        let filterItemBillProjects =_.filter(itemBillProjects,(item)=>item.kind==BranchProjectLevelConstant.qd&&!_.isEmpty(item.standardId));
        if(!_.isEmpty(filterItemBillProjects)){
            let groupItemBillProjects = _.groupBy(filterItemBillProjects,(item)=>item.standardId);
            for (const groupItemBillProjectsKey in groupItemBillProjects) {
                if(this.standardMap.has(groupItemBillProjectsKey))continue;
                let item =groupItemBillProjects[groupItemBillProjectsKey];
                //截取前9位
                let standardcode = _.truncate(item[0].bdCode,{length:9,separator:"",omission:""});
                //设置初始化编码  从1开始
                this.standardMap.set(groupItemBillProjectsKey,{standardcode,number:1})
            }
            for (let i = 0; i < itemBillProjects.length ; i++) {
                let item =itemBillProjects[i];
                if(item.kind==BranchProjectLevelConstant.qd&&!_.isEmpty(item.standardId)){
                    let value = this.standardMap.get(item.standardId);
                    let newFxcode = value.standardcode+_.padStart(value.number+"",3,"0");
                    //使用后重置编码 编码+1
                    this.standardMap.set(item.standardId,{"standardcode":value.standardcode,"number":value.number+1})
                    if(skipArray.includes(item.sequenceNbr))continue;
                    itemBillProjects[i].bdCode = newFxcode;
                    itemBillProjects[i].fxCode= newFxcode;

                }
            }
        }
        //获取所有的标准的措施项目清单
        let filterMeasureProjectTables =_.filter(measureProjectTables,(item)=>item.kind==BranchProjectLevelConstant.qd&&!_.isEmpty(item.standardId));
        if(!_.isEmpty(filterMeasureProjectTables)){
            let groupMeasureProjectTables = _.groupBy(filterMeasureProjectTables,(item)=>item.standardId);
            for (const groupMeasureProjectsKey in groupMeasureProjectTables) {
                //如果已经存在项目的 code  则不需要处理
                if(this.standardMap.has(groupMeasureProjectsKey))continue;
                let item =groupMeasureProjectTables[groupMeasureProjectsKey];
                //截取前9位
                let standardcode = _.truncate(item[0].bdCode,{length:9,separator:"",omission:""});
                //设置初始化编码  从1开始
                this.standardMap.set(groupMeasureProjectsKey,{standardcode,number:1})
            }
            for (let i = 0; i < measureProjectTables.length ; i++) {
                let item =measureProjectTables[i];
                if(item.kind==BranchProjectLevelConstant.qd&&!_.isEmpty(item.standardId)){
                    let value = this.standardMap.get(item.standardId);
                    let newFxcode = value.standardcode+_.padStart(value.number+"",3,"0");
                    //使用后重置编码 编码+1
                    this.standardMap.set(item.standardId,{"standardcode":value.standardcode,"number":value.number+1})
                    if(skipArray.includes(item.sequenceNbr))continue;
                    measureProjectTables[i].bdCode =newFxcode;
                }
            }
        }
        unit.itemBillProjects = arrayToTree(itemBillProjects);
        unit.measureProjectTables = arrayToTree(measureProjectTables);
        return true;
    }

    remove(args){
         checkContext.remove(args.constructId);
    }
    async checkFfExistQdUnitDifferent(args){
        if(ObjectUtils.isEmpty(args.rangeTree)){
            return false;
        }
        let projectObj =  PricingFileFindUtils.getProjectObjById(args.constructId);
        let check = new CheckPlayground(projectObj,args.rangeTree).selectCheckKey([2]).build();
        return check.checkFfExistQdUnitDifferent();
    }

    async projectCheck(args){
        if(ObjectUtils.isEmpty(args.rangeTree) || ObjectUtils.isEmpty(args.checkValues)){
            return []
        }
        let projectObj =  PricingFileFindUtils.getProjectObjById(args.constructId);
        let check = new CheckPlayground(projectObj,args.rangeTree).selectCheckKey(args.checkValues).build();
        await check.check();
        let result = checkContext.getResult(args.constructId);
        return result?result.resultMap:null;
    }

    async selectCheckResult(args){
        let result = checkContext.getResult(args.constructId);
        if(!ObjectUtils.isEmpty(result)){
            return result.resultMap;
        }

    }
    async selectCheckRange(args){
        let result = checkContext.getResult(args.constructId);
        if(!ObjectUtils.isEmpty(result)){
            return result.unitSequenceNbrArray;
        }else {
            return null;
        }

    }

    async locate(args){
        let constructId = args.constructId;
        let spId = args.spId;
        let upId = args.upId;
        let sequenceNbr = args.sequenceNbr;//业务数据主键
        let bizType = args.bizType;//业务数据主键

        let unit = PricingFileFindUtils.getUnit(constructId,spId,upId);

        if(bizType==='fbfx'){

            let itemBillProjects = unit.itemBillProjects;
            if(ObjectUtils.isEmpty(itemBillProjects)){
                return  ResponseData.fail('关联数据已被删除');
            }else {
                let find = itemBillProjects.find(item =>item.sequenceNbr === sequenceNbr);
                if(ObjectUtils.isEmpty(find)){
                    return  ResponseData.fail('关联数据已被删除');
                }else {
                    //执行展开
                    this.service.baseBranchProjectOptionService.openLineAndParent(find,itemBillProjects);
                }
            }
        }

        if(bizType==='csxm'){

            let measureProjectTables = unit.measureProjectTables;
            if(ObjectUtils.isEmpty(measureProjectTables)){
                return  ResponseData.fail('关联数据已被删除');
            }else {
                let find = measureProjectTables.find(item =>item.sequenceNbr === sequenceNbr);
                if(ObjectUtils.isEmpty(find)){
                    return  ResponseData.fail('关联数据已被删除');
                }else {
                    //执行展开
                    this.service.baseBranchProjectOptionService.openLineAndParent(find,measureProjectTables);
                }
            }
        }

        if(bizType==='rcj'){

            let constructProjectRcjs = unit.constructProjectRcjs;
            if(ObjectUtils.isEmpty(constructProjectRcjs)){
                return  ResponseData.fail('关联数据已被删除');
            }else {
                let find = constructProjectRcjs.find(item =>item.sequenceNbr === sequenceNbr);
                let rcjDetailList = unit.rcjDetailList;
                let findSon = rcjDetailList.find(item =>item.sequenceNbr === sequenceNbr);
                if(ObjectUtils.isEmpty(find) && ObjectUtils.isEmpty(findSon) ){
                    return  ResponseData.fail('关联数据已被删除');
                }
                let deLine ;
                if(args.moduleType==='fbfx'){
                    if(!ObjectUtils.isEmpty(find)){
                        deLine = unit.itemBillProjects.find(item => item.sequenceNbr === args.deId)
                    }
                    if(!ObjectUtils.isEmpty(findSon)){
                        deLine = unit.itemBillProjects.find(item => item.sequenceNbr === args.deId)
                    }

                    //执行展开
                    this.service.baseBranchProjectOptionService.openLineAndParent(deLine,unit.itemBillProjects);
                }else {
                    //执行展开

                    if(!ObjectUtils.isEmpty(find)){
                        deLine = unit.measureProjectTables.find(item => item.sequenceNbr === args.deId)
                    }
                    if(!ObjectUtils.isEmpty(findSon)){
                        deLine = unit.measureProjectTables.find(item => item.sequenceNbr === args.deId)
                    }
                    this.service.baseBranchProjectOptionService.openLineAndParent(deLine,unit.measureProjectTables);
                }


            }

        }
        if(bizType==='jrg'){

            let otherProjectDayWorks = unit.otherProjectDayWorks;
            if(ObjectUtils.isEmpty(otherProjectDayWorks)){
                return  ResponseData.fail('关联数据已被删除');
            }else {
                let find = otherProjectDayWorks.find(item =>item.sequenceNbr === sequenceNbr);
                if(ObjectUtils.isEmpty(find)){
                    return  ResponseData.fail('关联数据已被删除');
                }
            }
        }


        if(bizType==='zcbfwf'){

            let otherProjectServiceCosts = unit.otherProjectServiceCosts;
            if(ObjectUtils.isEmpty(otherProjectServiceCosts)){
                return  ResponseData.fail('关联数据已被删除');
            }else {
                let find = otherProjectServiceCosts.find(item =>item.sequenceNbr === sequenceNbr);
                if(ObjectUtils.isEmpty(find)){
                    return  ResponseData.fail('关联数据已被删除');
                }
            }
        }


        if(bizType==='qtxm'){

            let otherProjects = unit.otherProjects;
            if(ObjectUtils.isEmpty(otherProjects)){
                return  ResponseData.fail('关联数据已被删除');
            }else {
                let find = otherProjects.find(item =>item.sequenceNbr === sequenceNbr);
                if(ObjectUtils.isEmpty(find)){
                    return  ResponseData.fail('关联数据已被删除');
                }
            }
        }


        // let resObj ={
        //     constructId:constructId,
        //     spId:spId,
        //     upId:upId,
        //     sequenceNbr:sequenceNbr,
        //     bizType:bizType
        // }
        return ResponseData.success(args);
    }

}

SelfTestService.toString = () => '[class SelfTestService]';
module.exports = SelfTestService;
