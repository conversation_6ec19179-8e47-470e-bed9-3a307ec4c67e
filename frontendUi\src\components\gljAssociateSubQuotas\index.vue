<template>
  <common-modal
    className="dialog-comm"
    v-model:modelValue="show"
    :title="title"
    :mask="false"
    :lockView="false"
    :lockScroll="false"
    width="1000px"
    @cancel="cancel"
    @close="cancel"
  >
    <div class="associate-sub-quotas">
      <div class="left">
        <div class="variable-table">
          <s-table
            size="small"
            class="s-table"
            bordered
            :columns="variableGridOptions.columns"
            :scroll="{ y: 180 }"
            :pagination="false"
            rowKey="id"
            :data-source="zmAssociateDataCopy.zmVariableRuleList"
            @closeEditor="zmCalculateVariable"
          >
            <template #bodyCell="{ text }">
              {{ text }}
            </template>
            <template
              #cellEditor="{
                column,
                modelValue,
                closeEditor,
                record: row,
                editorRef,
                getPopupContainer,
              }"
            >
              <template v-if="column.key === 'resultValue'">
                <a-input
                  :ref="editorRef"
                  :bordered="false"
                  :value="modelValue.value"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row[column.field]
                      );
                      closeEditor();
                    }
                  "
                >
                </a-input>
              </template>
            </template>
          </s-table>
        </div>

        <div class="guide">
          <div class="title">子目指引</div>
          <div class="guide-content">
            <div
              class="list"
              v-for="item of zmAssociateDataCopy.zmPointList"
              :key="item"
              :class="{ active: item === guideValue }"
              @click="guideClick(item)"
            >
              {{ item }}
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <s-table
          size="small"
          ref="stableRef"
          class="s-table"
          bordered
          :defaultExpandAllRows="true"
          :expandedRowKeys="expandedRowKeys"
          :expandIconColumnIndex="3"
          :custom-cell="customCell"
          :rowSelection="{
            columnTitle: '关联',
            checkStrictly: false,
            selectedRowKeys: state.selectedRowKeys,
            getCheckboxProps: record => {
              return {
                disabled: [1, 2].includes(record?.levelType),
              };
            },
            onSelect: onSelectChange,
          }"
          :columns="tableOptions.columns"
          :scroll="{ y: 435 }"
          :pagination="false"
          rowKey="sequenceNbr"
          :data-source="displayZmDeList"
          @expand="expand"
          @openEditor="openEditor"
        >
          <template
            #bodyCell="{
              text,
              record: row,
              index,
              column,
              key,
              openEditor,
              closeEditor,
            }"
          >
            <div v-if="column.dataIndex === 'associate'">
              <span v-if="[1, 2].includes(row?.levelType)">{{ row.name }}</span>
            </div>
            <template v-if="column.key === 'quantityExpression'">
              {{ decimalFormat(row.quantity, 'EDIT_DEZS_QUANTITY_PATH') }}
            </template>
            <template v-if="column.dataIndex === 'deCodeZ'">
              {{ row.children?.length > 0 ? row.relationContent : row.deCodeZ }}
            </template>
          </template>
          <template
            #cellEditor="{
              column,
              modelValue,
              save,
              closeEditor,
              record: row,
              editorRef,
              getPopupContainer,
            }"
          >
            <template v-if="column.key === 'quantityExpression'">
              <a-input
                :bordered="false"
                :ref="el => setInputRef(el, column.dataIndex)"
                :value="modelValue.value"
                :get-popup-container="getPopupContainer"
                @update:value="
                  v => {
                    modelValue.value = v;
                  }
                "
                @blur="
                  () => {
                    quantityEditClosedEvent(
                      { row, column },
                      modelValue.value,
                      row[column.key]
                    );
                    closeEditor();
                  }
                "
              >
              </a-input>
            </template>
          </template>
        </s-table>
      </div>
    </div>
    <div class="btn-list">
      <div class="checkbox">
        <a-checkbox v-model:checked="showZmModel" @change="changeShowZmModel"
          >不再显示该窗口</a-checkbox
        >
        <a-checkbox v-model:checked="isRelationed" @change="getDisplayZmDeList"
          >当前项已关联子目</a-checkbox
        >
      </div>
      <a-button type="primary" ghost @click="cancel" style="margin-right: 12px"
        >取消</a-button
      >
      <a-button type="primary" @click="batchSaveChildrenDeListColl"
        >确定</a-button
      >
    </div>
  </common-modal>
</template>

<script setup>
import { computed, reactive, ref, watch, nextTick } from 'vue';
import { projectDetailStore } from '@/store/projectDetail.js';
import proDetApi from '@gongLiaoJi/api/projectDetail.js';
import { cloneDeep } from 'lodash';
import { setConvenienceSettings } from '@gongLiaoJi/hooks/common.js';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
const { decimalFormat } = useDecimalPoint();

const projectStore = projectDetailStore();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  currentInfo: {
    type: Object,
    default: () => null,
  },
  selectMaterialRow: {
    type: Object,
    default: () => null,
  },
  type: {
    type: Number,
    default: () => null,
  },
  zmAssociateData: {
    type: Object,
    default: () => null,
  },
});

const ShowAllZmDeListFlg = '显示所有';
const guideValue = ref(ShowAllZmDeListFlg);
const expandedRowKeys = ref([]);
const zmAssociateDataCopy = reactive({ ...props.zmAssociateData });
const displayZmDeList = ref([]);
const isRelationed = ref(false);
const showZmModel = ref(false);
const allZmDeList = ref([]);
const inputRefs = reactive({});
const setInputRef = (el, field) => {
  inputRefs[field + 'Input'] = el;
};

watch(
  () => props.zmAssociateData,
  newVal => {
    Object.assign(zmAssociateDataCopy, newVal);
    expandedRowKeys.value = [];
    console.log('~~~~~~~~~~zmAssociateDataCopy', zmAssociateDataCopy);
    expandedRowKeys.value.push(
      zmAssociateDataCopy.zmDeList[0].sequenceNbr,
      zmAssociateDataCopy.zmDeList[0]?.children[0]?.sequenceNbr
    );
    allZmDeList.value = zmAssociateDataCopy.zmDeList;
    getDisplayZmDeList();
  },
  { deep: true }
);
const getDisplayZmDeListWithGuideValue = () => {
  expandedRowKeys.value = []; // 清空之前的 row keys
  displayZmDeList.value = [];
  if (guideValue.value === ShowAllZmDeListFlg) {
    expandedRowKeys.value.push(
      zmAssociateDataCopy?.zmDeList?.[0]?.sequenceNbr,
      zmAssociateDataCopy?.zmDeList?.[0]?.children[0]?.sequenceNbr
    );
    return zmAssociateDataCopy.zmDeList;
  } else {
    // 否则执行过滤操作
    return zmAssociateDataCopy.zmDeList.map(item => {
      const children = [...item.children].filter(zmPointItem => {
        const showItem = zmPointItem.relationContent === guideValue.value;
        if (showItem) {
          expandedRowKeys.value.push(
            zmAssociateDataCopy.zmDeList[0]?.sequenceNbr,
            zmPointItem.sequenceNbr
          );
        }
        return showItem;
      });
      return { ...item, children };
    });
  }
};

const getDisplayZmDeList = () => {
  if (isRelationed.value) {
    const res = getDisplayZmDeListWithGuideValue();
    displayZmDeList.value = filterDataByIsRelationed(res);
  } else {
    displayZmDeList.value = getDisplayZmDeListWithGuideValue();
  }
  console.log('~~~~~~~~~~displayZmDeList', displayZmDeList.value);
};

watch(
  () => guideValue.value, // 监听 guideValue 的变化
  () => {
    getDisplayZmDeList();
  },
  { immediate: true } // 初始值也会触发一次 watch
);

const emits = defineEmits([
  'update:visible',
  'successHandler',
  'quotasCancel',
  'insertZmDe',
  'updateZmData',
]);
const state = reactive({
  selectedRowKeys: [],
  selectedRows: [],
});
const updateDeList = ref([]);
let originalDataList = ref([]);

const show = computed({
  get: () => {
    return props.visible;
  },
  set: val => {
    emits('update:visible', val);
  },
});

const title = computed(() => {
  return `${props.currentInfo?.deCode} 的关联子目`;
});
const cancel = () => {
  variableInit();
  emits('quotasCancel');
};
const customCell = ({ column, record: row }) => {
  let className = '';
  if (['associate'].includes(column.dataIndex)) {
    className += `Virtual-pdLeft-s${row?.levelType} `;
  }
  return { class: className };
};
let variableGridOptions = reactive({
  columns: [
    { dataIndex: 'index', width: 50, title: '序号', align: 'center' },
    { dataIndex: 'variableName', title: '变量描述' },
    {
      dataIndex: 'resultValue',
      title: '值',
      key: 'resultValue',
      editable: ({ record: row }) => {
        //四项面积系数 体积系数的值仅展示，不支持手动编辑
        if (['S', 'S1', 'S2', 'V'].includes(row.variableCode)) {
          return false;
        } else {
          return 'cellEditorSlot';
        }
      },
    },
  ],
  data: zmAssociateDataCopy.zmVariableRuleList,
});

let tableOptions = reactive({
  columns: [
    {
      title: '序号',
      dataIndex: 'dispNo',
      width: 50,
    },
    // {
    //   title: '关联',
    //   dataIndex: 'relationContent',
    //   slot: true,
    //   autoHeight: true,
    //   width: 50,
    //   customRender: ({ record }) => {
    //     if (record?.children?.length > 0) {
    //       return {
    //         props: {
    //           colSpan: 3,
    //         },
    //       };
    //     }
    //     return;
    //   },
    // },
    {
      title: '项目编码',
      dataIndex: 'deCodeZ',
      autoHeight: true,
      width: 130,
      customRender: ({ record }) => {
        if (record?.children?.length > 0) {
          return {
            props: {
              colSpan: 4,
            },
          };
        }
      },
    },
    {
      title: '名称',
      dataIndex: 'deNameZ',
      autoHeight: true,
      width: 220,
    },
    {
      title: '单位',
      dataIndex: 'unitZ',
      width: 60,
    },
    {
      title: '工程量',
      dataIndex: 'quantityExpression',
      key: 'quantityExpression',
      editable: 'cellEditorSlot',
    },
  ],
  data: [],
});

const guideClick = value => {
  guideValue.value = value;
};

const zmCalculateVariable = () => {
  const params = {
    zmVariableRuleList: zmAssociateDataCopy.zmVariableRuleList,
    standardId: props.selectMaterialRow
      ? props.selectMaterialRow.sequenceNbr
      : props.currentInfo?.standardId,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  console.log(
    'zmCalculateVariable params',
    params,
    'selectMaterialRow',
    props.selectMaterialRow
  );
  const apiParams = JSON.parse(JSON.stringify(params));
  proDetApi.zmCalculateVariable(apiParams).then(res => {
    console.log('子目规则计算返回', res);
    if (res.status === 200) {
      emits('updateZmData', res.result);
    }
  });
};

const variableInit = () => {
  isRelationed.value = false;
  showZmModel.value = false;
  state.selectedRowKeys = [];
  state.selectedRows = [];
  guideValue.value = ShowAllZmDeListFlg;
};

function findNodesBFS(treeList, targetSequenceNbrs) {
  if (targetSequenceNbrs.length == 0) {
    return [];
  }
  const result = [];
  const queue = [...treeList]; // 初始队列 = dataList 的所有根节点
  const targetSet = new Set(targetSequenceNbrs);

  while (queue.length > 0) {
    const node = queue.shift(); // 取出队首节点
    if (targetSet.has(node.sequenceNbr)) {
      result.push(node); // 匹配则加入结果
    }
    if (node.children) {
      queue.push(...node.children); // 子节点入队
    }
  }

  return result;
}

const batchSaveChildrenDeListColl = () => {
  // console.log('displayZmDeList.value', displayZmDeList.value);
  // const targetSequenceNbrs = state.selectedRows.map(row => row.sequenceNbr);
  const foundNodesBFS = findNodesBFS(allZmDeList.value, state.selectedRowKeys);

  emits('insertZmDe', foundNodesBFS, zmAssociateDataCopy.zmVariableRuleList);
  variableInit();
};

const onSelectChange = (record, selected, selectedRows, nativeEvent) => {
  if (selected) {
    state.selectedRowKeys = Array.from(
      new Set([...state.selectedRowKeys, record.sequenceNbr])
    );
  } else {
    state.selectedRowKeys = Array.from(
      new Set(
        state.selectedRowKeys.filter(
          item => ![record.sequenceNbr].includes(item)
        )
      )
    );
  }
};

const expand = (expanded, record) => {
  console.log('expanded1111111111', expanded, record);
  if (expanded) {
    expandedRowKeys.value.push(record.sequenceNbr);
  } else {
    let index = expandedRowKeys.value.findIndex(x => x === record.sequenceNbr);
    expandedRowKeys.value.splice(index, 1);
  }
};

const editClosedEvent = ({ row }, newValue, oldValue) => {
  console.log('newValue', newValue, oldValue);
  row.resultValue = newValue;
};

const quantityEditClosedEvent = ({ row }, newValue, oldValue) => {
  console.log('1111111111', newValue, oldValue);
  if (newValue !== oldValue) {
    row.quantityExpression = newValue;
    zmCalculateQuantity(row);
  }
};

const zmCalculateQuantity = row => {
  const params = {
    zmVariableRuleList: zmAssociateDataCopy.zmVariableRuleList,
    zmDe: row,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  console.log('改变工程量计算 params', params, row);
  const apiParams = JSON.parse(JSON.stringify(params));
  proDetApi.zmCalculateQuantity(apiParams).then(res => {
    console.log('改变工程量计算返回', res);
    if (res.status === 200) {
      // quantity 使用接口返回的值
      zmAssociateDataCopy.zmDeList[0].children.forEach(item => {
        if (item.children?.length) {
          item.children.forEach(deItem => {
            if (deItem.sequenceNbr === row.sequenceNbr) {
              deItem.quantity = res.result.quantity;
            }
          });
        }
      });
    }
  });
};

const filterDataByIsRelationed = data => {
  const cloneData = cloneDeep(data);
  const filterData = cloneData
    .map(item => {
      // 如果有 children，则递归处理子级
      if (item.children) {
        item.children = filterDataByIsRelationed(item.children);
      }
      // 筛选当前节点：如果 children 存在并且有符合条件的子级，或者当前节点自身符合条件
      if (item.children && item.children.length > 0) {
        return item;
      }
      // 如果当前节点是最底层，且自身符合条件，则保留
      if (state.selectedRowKeys?.includes(item.sequenceNbr)) {
        return item;
      }
      // 如果没有符合条件的子级，且自身也不符合条件，则剔除
      return null;
    })
    .filter(item => item !== null); // 过滤掉 null 值
  // 数据展开
  expandedRowKeys.value = [];
  filterData.forEach(item => {
    expandedRowKeys.value.push(item.sequenceNbr);
    if (item.children) {
      item.children.forEach(pointItem => {
        expandedRowKeys.value.push(pointItem.sequenceNbr);
      });
    }
  });
  return filterData;
};

const changeShowZmModel = () => {
  setConvenienceSettings('RELATION_DE', { relationDePop: !showZmModel.value });
};

const sTableState = ref({
  prevDbTime: null,
});
// 打开编辑自动获得焦点
const openEditor = cellInfos => {
  sTableState.isOpenEditor = true;
  sTableState.prevDbTime = new Date().getTime();
  setTimeout(() => {
    // 清除sTableState.time
    sTableState.prevDbTime = null;
  }, 300);
  nextTick(() => {
    const inputRef = inputRefs[cellInfos.column.dataIndex + 'Input'];
    if (inputRef) {
      inputRef.focus();
      if (inputRef && typeof inputRef.select === 'function') {
        inputRef?.select();
      }
    }
  });
};
</script>
<style lang="scss" scoped>
.btn-list {
  margin-top: 25px;
  display: flex;
  .checkbox {
    flex: 1;
  }
}
.s-table {
  ::v-deep(.surely-table) {
    border-radius: 0;
  }
  ::v-deep(.surely-table-body) {
    border-right: 1px solid #b9b9b9;
  }
}
.variable-table {
  height: 220px;
  overflow-y: hidden;
  border-bottom: 1px solid #b9b9b9;
}
.guide {
  .title {
    font-size: 14px;
    line-height: 24px;
    padding: 10px 0 5px;
    color: #000;
  }
  .guide-content {
    padding: 10px 6px;
    border: 1px solid #b9b9b9;
    overflow-y: auto;
    height: 220px;
    .list {
      padding: 0 12px;
      font-size: 14px;
      line-height: 22px;
      color: #000;
      cursor: pointer;
      &.active {
        background: rgba(192, 217, 255, 0.39);
      }
    }
  }
}
.associate-sub-quotas {
  display: flex;
  .left {
    width: 330px;
    margin-right: 10px;
  }
  .right {
    width: calc(100% - 340px);
    ::v-deep(.surely-table-cell-content) {
      height: 22px !important;
      line-height: 22px !important;
      padding: 0 8px;
    }
  }
}
.ant-input {
  height: 100%;
  font-size: 12px;
  resize: none;
  padding: 0;
}
</style>
