'use strict';

const fs = require("fs");
const os = require('os');

const { Service, } = require('../../core');
const {ObjectUtils} = require("../utils/ObjectUtils");
const UtilsPs = require('../../core/ps');
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {ConvertUtil} = require("../utils/ConvertUtils");

/**
 * 全局配置处理service
 */
class GlobalConfigurationService extends Service {

    constructor(ctx) {
        super(ctx);
        this.configFilePath = `${os.homedir()}\\.xilidata\\global_config.json`;
        this.defaultConfigPath = UtilsPs.getExtraResourcesDir() + '/global_config.default.json';
        console.log("--------%^&*(^%^&*________", this.defaultConfigPath)
        console.log("--------%^&*(^%^&*________", this.getGlobalConfig())
    }

    /**
     * 获取全局配置配置信息
     */
    getGlobalConfig () {
        if(ObjectUtils.isNotEmpty(global.globalConfig)){
            return global.globalConfig;
        }else{
            let globalConfig = {};
            let defaultConfig = {};
            if(fs.existsSync(this.configFilePath)){
                globalConfig = require(this.configFilePath);
            }

            if(fs.existsSync(this.defaultConfigPath)){
                defaultConfig = require(this.defaultConfigPath);
            }

            global.globalConfig = ConvertUtil.recursiveCopyTo(globalConfig, defaultConfig);

            return global.globalConfig;
        }
    }

    /**
     * 获取全局配置配置信息
     */
    getProjectAllConfig(constructProject){
        return {

        }
    }


    /**
     * 设置配置信息
     * @param param
     * @return {boolean}
     */
    async resetGlobalConfig (param){
        let {constructId} = param;
        let globalConfig = this.getGlobalConfig();
        let lockRcjResFlag = true;//人材机锁定消耗量标识
        if (globalConfig.budget.input.lockRcjResQty && param.budget.input.lockRcjResQty){
            lockRcjResFlag = false;
        }
        Object.assign(globalConfig, param);
        fs.writeFileSync(this.configFilePath, JSON.stringify(globalConfig, null, 4));
        global.globalConfig = globalConfig;

        //全局修改人材机消耗量
        if (lockRcjResFlag){
            let constructProjects = Object.values(global.constructProject);
            for (let project of constructProjects) {
                let unitList = PricingFileFindUtils.getUnitList(project.proJectData.sequenceNbr);
                unitList.forEach(unit =>{
                    let {constructProjectRcjs,rcjDetailList} = unit;
                    if (ObjectUtils.isNotEmpty(constructProjectRcjs)){
                        constructProjectRcjs.forEach( k=> k.isLock = param.budget.input.lockRcjResQty);
                    }
                    if (ObjectUtils.isNotEmpty(rcjDetailList)){
                        rcjDetailList.forEach( k=> k.isLock = param.budget.input.lockRcjResQty);
                    }
                })
            }
            // let unitList = PricingFileFindUtils.getUnitList(constructId);
            // for (let unit of unitList) {
            //     let fbFx = PricingFileFindUtils.getFbFx(constructId, unit.spId, unit.sequenceNbr);
            //     let csxm = PricingFileFindUtils.getCSXM(constructId, unit.spId, unit.sequenceNbr);
            //     await this.service.unitPriceService.reCacaulateAll(constructId, unit.spId, unit.sequenceNbr, csxm, null);
            //     await this.service.unitPriceService.reCacaulateAll(constructId, unit.spId, unit.sequenceNbr, fbFx, null);
            // }

        }
        return true;
    }

    /**
     * 小数点保留位数默认配置
     * @return {{rcjDetailAmount: number,rcjSummaryAmount: number, qDDeAmount: number, rate: number, costPrice: number}}
     */
    getDecimalPointConfig(){
        return this.getGlobalConfig().project;
        // return {
        //     "rcjDetailAmount": 4,   //人材机明细区：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
        //     "rcjSummaryAmount": 4,   //人材机汇总：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
        //     "qDDeAmount": 3,  // 工程量，数量类：小数点后3位，第4位四舍五入
        //     "costPrice": 2, // 金额、合计，金额类：小数点后2位，第3位四舍五入
        //     "rate": 2,  // 费率、指数、比率(%)：小数点后2位，第3位四舍五入
        // };
    }

}

GlobalConfigurationService.toString = () => '[class GlobalConfigurationService]';
module.exports = GlobalConfigurationService;
