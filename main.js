const { Application } = require('./core');
const EE = require('./core/ee');
const { createConnection,getConnectionOptions,getConnection } = require('typeorm');
const fs = require('fs');
const os = require('os');
const { getDataSource, initDataSource} = require('./data-source');
const UtilsPs = require('./core/ps');
const Database = require('better-sqlite3');
const { app, BrowserWindow} = require('electron');
const {PricingFileFindUtils} = require("./electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("./electron/utils/ObjectUtils");
const path = require("path");
const {UPCContext} = require("./electron/unit_price_composition/core/UPCContext");
const worker = require("./packages/worker.json");
const {UPCTemplate} = require("./electron/model/UPCTemplate");
const Fixs = require("./electron/fixs");
const ConstantUtil = require("./electron/enum/ConstantUtil");
const {IpcWinUtils} = require("./electron/utils/IpcWinUtils");
const {UnitCostSummaryService} = require("./electron/service/unitCostSummaryService");
const {dialog} = require("electron");
const { NumberUtil } = require('./electron/utils/NumberUtil');
global.NumberUtil = NumberUtil;

class Main extends Application {

  constructor() {
    super();
    // this === eeApp;
  }
  async initialize () {
    let path = UtilsPs.getExtraResourcesDir();
    let defaultDataSource =await getDataSource(path+"/pricing.db");
    this.appDataSource = defaultDataSource;
    let gsDefaultDataSource =await getDataSource(path+"/PreliminaryEstimate.db");
    this.gsAppDataSource = gsDefaultDataSource;
    this.betterSqlite3DataSource = new Database(path+"/pricing.db"/*, { verbose: console.log }*/);
    this.gsSqlite3DataSource = new Database(path+"/PreliminaryEstimate.db", { verbose: console.log });
    let gljDefaultDataSource =await getDataSource(path+"/gongLiaoJiProject.db");
    this.gljAppDataSource = gljDefaultDataSource;
    this.gljSqlite3DataSource = new Database(path+"/gongLiaoJiProject.db", { verbose: console.log });
    await initDataSource(this,worker,path);
    this.db.default=defaultDataSource;
    await UPCContext.init(defaultDataSource);
    await this.service.unitCostSummaryService.initTemplate();
    super.initialize();
    await this.service.autoCostMathService.initData(defaultDataSource);
    await this.service.pumpingAddFeeService.initData(defaultDataSource);

    try{
      let softConfPath = path+"/software.conf.json";
      if(fs.existsSync(softConfPath)){
        let softwareConf = require(softConfPath);
        this.showDebugger = (softwareConf.showDebugger == true) || !UtilsPs.isPackaged();
      }else{
        this.showDebugger = !UtilsPs.isPackaged();
      }
    }catch (e){

    }

  }

  /**
   * core app have been loaded
   */
  async ready () {
    // do some things
    //创建系统文件下载默认存放路径
    //获取到软件安装目录
    let homeDir =  path.dirname(app.getPath('exe'));
    homeDir = `${homeDir}\\download`
    await this.creatBaseDataDir(homeDir);

    //默认数据存放路径
    let defSavePath = `${os.homedir()}\\.xilidata`;
    await this.creatBaseDataDir(defSavePath);

    //读取数据
    const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
    //如果不存在创建文件
    let obj = {};
    if (!fs.existsSync(baseDataDir)) {
       obj = {
        key:'key1为用户主键_身份 存历史信息  key2 为主键 存对应身份 ',
        DEF_SAVE_PATH:`${os.homedir()}\\.xilidata`,
        FILE_DOWNLOAD_PATH:homeDir
      };
    }else {
      //读取数据
      let data = fs.readFileSync(baseDataDir, 'utf8');
      let userHistoryData = {};

      if (ObjectUtils.isEmpty(data)){
        userHistoryData = {
          key:'key1为用户主键_身份 存历史信息  key2 为主键 存对应身份 ',
          DEF_SAVE_PATH:`${os.homedir()}\\.xilidata`,
          FILE_DOWNLOAD_PATH:homeDir
        };
      }else {
        userHistoryData = JSON.parse(data)||{};
        if (ObjectUtils.isEmpty(userHistoryData.DEF_SAVE_PATH)){
          userHistoryData.DEF_SAVE_PATH=`${os.homedir()}\\.xilidata`;
        }
        if (ObjectUtils.isEmpty(userHistoryData.FILE_DOWNLOAD_PATH)){
          userHistoryData.FILE_DOWNLOAD_PATH=homeDir;
        }
      }
      obj = userHistoryData;
    }
    let result = ObjectUtils.toJsonString(obj);
    try {
      fs.writeFileSync(baseDataDir, result);
      console.log('创建文件后写入文件成功！');
    } catch (err) {
      console.error('写入文件时发生错误：', err);
    }

    // 判断启动应用程序时是否有传递参数
    if (process.argv.length >= 2) {
      //双击文件打开应用程序的情况
      const filePath = process.argv[1];
      if (filePath !== "."){
        //检测是否已经登录
        if (ObjectUtils.isEmpty(global.idInformation)){
          global.loginPath = filePath;
          console.log("文件的打开路径是",global.loginPath)
        }else {
          //双击文件打开的情况
          //首先获取到文件内容
          let obj = await PricingFileFindUtils.getProjectObjByPath(filePath);
          await  new Fixs(obj,obj.version).fix();
          this.service.constructProjectService.openProject({"sequenceNbr":obj.sequenceNbr,"path":filePath});
        }
      }
    }


    app.on('second-instance',  async (event, commandLine, workingDirectory) => {
      const args = commandLine;
      function endsWithAny(string, endings) {
        return endings.some(ending => new RegExp(ending + '$').test(string));
      }
      let route = null;
      const endings = ConstantUtil.YUSUAN_FILE_SUFFIX_LIST;
      args.forEach(k =>{
        if (endsWithAny(k, endings)){
          route = k;
        }
      });


        if (!ObjectUtils.isEmpty(route)){
        //首先获取到文件内容
        if (ObjectUtils.isEmpty(global.idInformation)){
          global.loginPath = route;
          console.log("文件的打开路径是",global.route)
        } else {
          let obj =  await PricingFileFindUtils.getProjectObjByPath(route);
          // // 若用户手动将其他业务线产生的文件后缀修改为预算文件后缀（如将审核成果文件修改为“.YSF”），将直接弹窗拦截提示用户“文件已被篡改，无法打开”；
          // const suffixToBiddingType = {YSFZ: 0, YSF: 1, YSFD: 2, YJS: 3, YSH: 5, YGS: 4, YSFG: 7,};
          // // 获取文件后缀
          // let pathSuffix = obj.path.match(/[^.]+$/)[0];
          // // 检查文件后缀是否在映射表中
          // if (suffixToBiddingType.hasOwnProperty(pathSuffix)) {
          //   // 检查 biddingType 是否匹配
          //   if (obj.biddingType != suffixToBiddingType[pathSuffix]) {
          //     const win = this.electron.mainWindow;
          //     win.webContents.send('judgeSuffix', '文件已被篡改，无法打开');
          //     return
          //   }
          // }
          await  new Fixs(obj,obj.version).fix();
          this.service.constructProjectService.openProject({"sequenceNbr":obj.sequenceNbr,"path":route});
        }
      }
    });
  }

  /**
   * 项目启动是创建初始的文件夹
   * @param path
   * @returns {Promise<void>}
   */
  async creatBaseDataDir (path) {
    if (fs.existsSync(path)) {
      console.log(`${path} already exists, skip creating.`);
    }
    fs.mkdir(path, { recursive: true }, (err) => {
      if (err) {
        console.error(err);
      } else {
        console.log(`Directory ${path} created successfully`);
      }
    });
  }

  /**
   * electron app ready
   */
  async electronAppReady () {
    // do some things
    // let check = new CheckPlayground({name:"test",biddingType:0}).selectCheckKey([1,2,3,4,5,6,7,8,9]).build();
    // await check.check();
  }

  /**
   * main window have been loaded
   */
  async windowReady () {
    // do some things
    // 延迟加载，无白屏
    const winOpt = this.config.windowsOption;
   let loginKeys = ["access_token","token_type","refresh_token","expires_in","scope"]
    let loginResult={};
    let token = app.commandLine.hasSwitch("access_token");
    loginKeys.forEach((key)=>{
      if(app.commandLine.hasSwitch(key)){
        loginResult[key]=app.commandLine.getSwitchValue(key);
      }
    })
    console.log(loginResult);

    const win = this.electron.mainWindow;
    win.once('ready-to-show', () => {
      if (winOpt.show == false) {
        win.show();
      }
      //添加定时只执行一次
      const timer = setInterval(() => {
        if(token){
          win.send("token",loginResult);
        }
        clearInterval(timer);
      },3000)

    })
    this.service.softdogService.initNotificationTask();
  }

  /**
   * before app close
   */
  async beforeClose () {
    // do some things

  }
}
app.commandLine.appendSwitch("no-sandbox");
// Instantiate an app object
EE.app = new Main();

