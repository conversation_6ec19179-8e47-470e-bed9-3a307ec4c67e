const Conf = require("../core/config");
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme,BrowserWindow
} = require('electron');
const EE = require('../core/ee');

// 对于Node.js
const sysPath = require('path');
const {ObjectUtils} = require("../electron/utils/ObjectUtils");

/**
 * 项目窗口操作
 */
class WinManageUtils {


    /**
     * 获取项目所有的窗口信息
     */
    async getAllWindow(){
        return  BrowserWindow.getAllWindows();
    }


    /**
     * 获取指定的窗口信息
     * @param winId 窗口ID
     */
    async getWin(winId){
        return BrowserWindow.fromId(winId);
    }



    /**
     * 获取项目所有的窗口ID信息
     * @return {*}
     */
    getAllWindowIdCache(){
        if (!global.windowMap) {
            global.windowMap = new Map();
        }
        return global.windowMap;
    }

    /**
     * 根据项目ID获取当前项目的窗口ID
     * @param projectId 项目ID
     */
    async getChildWinIdByProjectId(projectId){
        let winIds = this.getAllWindowIdCache();
        return winIds.get(projectId);
    }


    /**
     * 关闭所有项目窗口
     */
    async closeAllWin(){
        let wins = this.getAllWindow();
        wins.forEach((win) => {
            win.close();
        });
    }


    /**
     * 关闭指定项目窗口
     * @param winId
     */
    async closeWin(winId){
        let win = this.getWin(winId);
        win.close();
    }


    /**
     * 判断项目窗口是否被打开
     * @param projectId 项目ID
     */
    projectIsOpen(projectId){
        let winIds = this.getAllWindowIdCache();
        if (winIds.has(projectId)){
            return true;
        }
        return false;
    }


    /**
     * 创建一个新的项目窗口
     * @param windowName
     * @param windowId
     * @return {*}
     */
    // createWindow(windowName,windowId) {
    //     let addr = 'http://localhost:8080'
    //     const config = Conf.all();
    //     if (config.env == 'prod') {
    //         const mainServer = config.mainServer;
    //         addr = mainServer.protocol + mainServer.host + ':' + mainServer.port;
    //     }
    //     let opt = {
    //         title: windowName
    //     }
    //     const addonWindow = EE.app.addon.window;
    //     const win = addonWindow.create(windowId, opt);
    //     //const winContentsId = win.webContents.id;
    //     let content = addr + '#/projectDetail/customize?constructSequenceNbr='+windowId;
    //     win.loadURL(content);
    //     return win;
    // }


    /**
     * 创建一个新的项目窗口
     * @param 项目路径
     * @param windowId
     * @return {*}
     */
    createWindow(path, windowId,route) {
        if (ObjectUtils.isEmpty(route))throw new Error("路由不能为空");
        if (this.projectIsOpen(windowId)) {
            // 获取对应的窗口引用
            let win = BrowserWindow.fromId(
                this.getAllWindowIdCache().get(windowId)
            );
            if (win.isMinimized()) {
                win.restore();
            }
            //将窗口移动到顶部
            win.moveTop();
            return;
        }
        let addr = 'http://localhost:8080';
        const config = Conf.all();
        if (config.env == 'prod') {
            const mainServer = config.mainServer;
            addr =
                mainServer.protocol + mainServer.host + ':' + mainServer.port;
        }
        let opt = {
            title: sysPath.basename(path),
            minWidth: 980,
            minHeight: 650
        };
        const addonWindow = EE.app.addon.window;
        const win = addonWindow.create(windowId, opt);
        let content =
            addr + route + windowId;
        //全局map
        this.getAllWindowIdCache().set(windowId, win.id);
        win.loadURL(content);
        if(EE.app.showDebugger){
            win.openDevTools();
        }
        return win;
    }




}

module.exports = {
    WinManageUtils: new WinManageUtils()
};

