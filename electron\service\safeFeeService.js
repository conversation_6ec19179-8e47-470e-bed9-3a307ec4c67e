

const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const {NumberUtil} = require("../utils/NumberUtil");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {Snowflake} = require("../utils/Snowflake");
const {SafeFee} = require("../model/SafeFee");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const {Service} = require('../../core');

class SafeFeeService  extends Service{
    constructor(ctx) {
        super(ctx);
        this.decimalPointConfig = this.service.globalConfigurationService.getDecimalPointConfig()
    }

    async getSafeFee(args){
        await this.countSafeFee(args);

        let unit =await PricingFileFindUtils.getUnit(args.constructId,args.singleId, args.unitId);
        return  unit.safeFees;
    }

    /**
     * 计算安文费
     */
    countSafeFee(args){
        let unit = PricingFileFindUtils.getUnit(args.constructId,args.singleId, args.unitId);
        let measureProjectTables = unit.measureProjectTables;
        //安文费定额
        let awfDe = measureProjectTables.filter(item => item.kind === BranchProjectLevelConstant.de && item.isCostDe  ===1);

        // //考虑到复制安文费定额的情况 将数据取费专业进行分组求和
        let group = ArrayUtil.group(awfDe,'costMajorName');
        let groupAwfDe = new Array();
        for ( let [key, value] of group.entries()) {
            let item = {};
            item.costMajorName = key;
            item.formula = value[0].formula;//安文费明细不考虑复制后的定额
            item.total = value[0].total;
            // item.formula = value.reduce((accumulator, i) => {
            //     return accumulator + i.formula;
            // }, 0);
            // item.total = value.reduce((accumulator, i) => {
            //     return accumulator + i.total;
            // }, 0);
            groupAwfDe.push(item);
        }

        let feeFiles = unit.feeFiles;
        if(!ObjectUtils.isObject(groupAwfDe)){
            let safeFees = new Array();
            for (let i = 0; i < groupAwfDe.length; i++) {
                let item = groupAwfDe[i];
                // 当前定额取费专业
                let feeFile = feeFiles.find(obj => obj.feeFileName === item.costMajorName);
                let safeFee = new SafeFee();
                safeFee.sequenceNbr = Snowflake.nextId();
                safeFee.unitId = unit.sequenceNbr;
                safeFee.costMajorName = item.costMajorName;
                safeFee.costFeeBase = ObjectUtils.isEmpty(item.formula)?item.formula:NumberUtil.numberScale(item.formula,this.decimalPointConfig.costPrice) ;
                safeFee.basicRate =feeFile.anwenRateBase
                safeFee.addRate =feeFile.anwenRateAdd;
                // safeFee.feeAmount =!ObjectUtils.isEmpty(item.total)?item.total:NumberUtil.numberScale2(NumberUtil.multiply(safeFee.costFeeBase,safeFee.basicRate/100)) ;
                safeFee.feeAmount =item.total ;
                safeFees.push(safeFee);
            }
            unit.safeFees =safeFees;
        }
    }
}
SafeFeeService.toString = () => '[class SafeFeeService]';
module.exports = SafeFeeService;