const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");


class GlobalConfigurationController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 获取配置信息
     */
    getGlobalConfig () {
        return ResponseData.success(
            this.service.globalConfigurationService.getGlobalConfig()
        );
    }


/**
     * 设置配置信息
     * @param param
     * @return {ResponseData}
     */
   async resetGlobalConfig (param){
        return ResponseData.success(
            await this.service.globalConfigurationService.resetGlobalConfig(param)
        );
    }

}

GlobalConfigurationController.toString = () => '[class ConfigurationController]';
module.exports = GlobalConfigurationController;