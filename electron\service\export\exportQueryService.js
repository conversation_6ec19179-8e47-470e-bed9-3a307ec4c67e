const {Gfee} = require("../../model/Gfee");
const {Service} = require("../../../core");
const {app} = require('electron');
const {ResponseData} = require("../../utils/ResponseData");
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const {ExcelUtil} = require("../../utils/ExcelUtil.js");
const {ZhaoBiaoUtil} = require("../../utils/ZhaoBiaoUtil.js");
const {TouBiaoUtil} = require("../../utils/TouBiaoUtil.js");
const {ProjectQdUtil} = require("../../utils/ProjectQdUtil.js");
const path = require('path');
const _ = require('lodash');
const ExcelJS = require('exceljs');
const BranchProjectLevelConstant = require("../../enum/BranchProjectLevelConstant");
const ConstructionMeasureTypeConstant = require("../../enum/ConstructionMeasureTypeConstant");
const ProjectLevelConstant = require("../../enum/ProjectLevelConstant");
const {ConstructProjectRcj} = require("../../model/ConstructProjectRcj");
const {ConvertUtil} = require("../../utils/ConvertUtils");
const {NumberUtil} = require("../../utils/NumberUtil");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const UtilsPs = require('../../../core/ps');
const AdmZip = require('adm-zip');
const fs = require('fs');
const TaxCalculationMethodEnum = require("../../enum/TaxCalculationMethodEnum");
const ConstantUtil = require("../../enum/ConstantUtil");
const RcjLevelMarkConstant = require("../../enum/RcjLevelMarkConstant");
const {OtherProjectZygcZgj} = require("../../model/OtherProjectZygcZgj");
const {ClJxSbZzsUtil} = require("../../utils/ClJxSbZzsUtil");
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const {ObjectUtil} = require("../../../common/ObjectUtil");
const ExportSheetTypeConstant = require("../../enum/ExportSheetTypeConstant");
const {DataTemplate} = require("../../reportDesign/dataTemplate");
const {ExcelOperateUtil} = require("../../utils/ExcelOperateUtil");
const DXGCFHZ1_5 = require("../../template/export/DXGCFHZ1_5.json");
const zhaobiao12General = require("../../template/export/zhaobiao/12/一般/ExportSheetNameEnum12");
const zhaobiao12Simple = require("../../template/export/zhaobiao/12/简易/ExportSheetNameEnum12");
const zhaobiao22General = require("../../template/export/zhaobiao/22/一般/ExportSheetNameEnum22");
const zhaobiao22Simple = require("../../template/export/zhaobiao/22/简易/ExportSheetNameEnum22");
const toubiao12General = require("../../template/export/toubiao/12/一般/ExportSheetNameEnum12");
const toubiao12Simple = require("../../template/export/toubiao/12/简易/ExportSheetNameEnum12");
const toubiao22General = require("../../template/export/toubiao/22/一般/ExportSheetNameEnum22");
const toubiao22Simple = require("../../template/export/toubiao/22/简易/ExportSheetNameEnum22");
const gclqd12General = require("../../template/export/gclqd/12/一般/ExportSheetNameEnum12");
const gclqd12Simple = require("../../template/export/gclqd/12/简易/ExportSheetNameEnum12");
const gclqd22General = require("../../template/export/gclqd/22/一般/ExportSheetNameEnum22");
const gclqd22Simple = require("../../template/export/gclqd/22/简易/ExportSheetNameEnum22");
//针对测评
const DXGCFHZ1_5Ceping = require("../../template/export/cepingResponse/DXGCFHZ1_5.json");
const zhaobiao12GeneralCeping = require("../../template/export/cepingResponse/zhaobiao/12/一般/ExportSheetNameEnum12");
const zhaobiao12SimpleCeping = require("../../template/export/cepingResponse/zhaobiao/12/简易/ExportSheetNameEnum12");
const zhaobiao22GeneralCeping = require("../../template/export/cepingResponse/zhaobiao/22/一般/ExportSheetNameEnum22");
const zhaobiao22SimpleCeping = require("../../template/export/cepingResponse/zhaobiao/22/简易/ExportSheetNameEnum22");
const toubiao12GeneralCeping = require("../../template/export/cepingResponse/toubiao/12/一般/ExportSheetNameEnum12");
const toubiao12SimpleCeping = require("../../template/export/cepingResponse/toubiao/12/简易/ExportSheetNameEnum12");
const toubiao22GeneralCeping = require("../../template/export/cepingResponse/toubiao/22/一般/ExportSheetNameEnum22");
const toubiao22SimpleCeping = require("../../template/export/cepingResponse/toubiao/22/简易/ExportSheetNameEnum22");
const gclqd12GeneralCeping = require("../../template/export/cepingResponse/gclqd/12/一般/ExportSheetNameEnum12");
const gclqd12SimpleCeping = require("../../template/export/cepingResponse/gclqd/12/简易/ExportSheetNameEnum12");
const gclqd22GeneralCeping = require("../../template/export/cepingResponse/gclqd/22/一般/ExportSheetNameEnum22");
const gclqd22SimpleCeping = require("../../template/export/cepingResponse/gclqd/22/简易/ExportSheetNameEnum22");

const moreTemplate = require("../../template/export/more/moreTemplate");
const ExcelEnum = require("../../enum/ExcelEnum");
const {ExportConfig} = require("../../model/ExportConfig");

class ExportQueryService extends Service{
    constructor(ctx) {
        super(ctx);
    }
    //展示报表查看的表名列表
    async showExportHeadLine (itemLevel,args) {
        let result = [];
        //定义一个存放数据值和cell的对象
        function HeadLineList(desc, baoBiaoList) {
            this.desc = desc;//存放大标题
            this.baoBiaoList = baoBiaoList;
        }

        let object = await this.initReportStructureObject(itemLevel,args.constructId,args.singleId,args.unitId);
        let headLineList1 = new HeadLineList("招标项目报表",object.招标项目报表);
        let headLineList2 = new HeadLineList("投标项目报表",object.投标项目报表);
        let headLineList3 = new HeadLineList("工程量清单报表",object.工程量清单报表);
        // let headLineList4 = new HeadLineList("测评响应招标项目报表",object.测评响应招标项目报表);
        // let headLineList5 = new HeadLineList("测评响应投标项目报表",object.测评响应投标项目报表);
        // let headLineList6 = new HeadLineList("测评响应工程量清单报表",object.测评响应工程量清单报表);

        result.push(headLineList1)
        result.push(headLineList2)
        result.push(headLineList3)
        // result.push(headLineList4)
        // result.push(headLineList5)
        // result.push(headLineList6)

        return result;
    }

    async moreTemplateQuery(args) {
        //定义一个存放数据值和cell的对象
        function HeadLineList(desc, baoBiaoList) {
            this.desc = desc;//存放大标题
            this.baoBiaoList = baoBiaoList;
        }
        let {constructId,singleId,unitId,itemLevel} = args;

        let report =await this.initKindsOfBaoBiaoTemplate(itemLevel,constructId,singleId,unitId);

        let result = [];
        let headLineList招标 = new HeadLineList("招标项目报表",report.zhaoBiaoList);
        let headLineList投标 = new HeadLineList("投标项目报表",report.touBiaoList);
        let headLineList工程量 = new HeadLineList("工程量清单报表",report.gongChengLiangList);

        result.push(headLineList招标);
        result.push(headLineList投标);
        result.push(headLineList工程量);

        if (itemLevel == "unit") {
            let sheet1_6 = moreTemplate.filter(item=> item.headLine=="表1-6 分部分项工程量清单与计价表(含分部小计)")[0];
            let index1 = await this.getIndexForHeadLineList(headLineList招标.baoBiaoList,"表1-6 分部分项工程量清单与计价表");
            let index2 = await this.getIndexForHeadLineList(headLineList投标.baoBiaoList,"表1-6 分部分项工程量清单与计价表");
            headLineList招标.baoBiaoList.splice( index1, 0, sheet1_6);
            headLineList投标.baoBiaoList.splice( index2, 0, sheet1_6);

            let sheet1_16 = moreTemplate.filter(item=> item.headLine=="表1-16 分部分项工程量清单综合单价分析表(含分部小计)")[0];
            let index3 = await this.getIndexForHeadLineList(headLineList招标.baoBiaoList,"表1-16 分部分项工程量清单综合单价分析表");
            let index4 = await this.getIndexForHeadLineList(headLineList投标.baoBiaoList,"表1-16 分部分项工程量清单综合单价分析表");
            headLineList招标.baoBiaoList.splice( index3, 0, sheet1_16);
            headLineList投标.baoBiaoList.splice( index4, 0, sheet1_16);
        }
        return result;
    }

    async getIndexForHeadLineList(headLineList,headLine) {
        let index = 0;
        for (let i = 0; i < headLineList.length; i++) {
            index++;
            if (headLineList[i].headLine == headLine) {
                return index;
            }
        }
    }

    //初始化单位、单项及工程项目的报表结构树对象
    async initReportStructureObject(itemLevel,constructId,singleId,unitId) {
        let object = null;//该对象
        if (itemLevel == "project") {
            object = PricingFileFindUtils.getProjectObjById(constructId);
        }
        if (itemLevel == "single") {
            object = PricingFileFindUtils.getSingleProject(constructId,singleId);
        }
        if (itemLevel == "unit") {
            object = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        }
        //如果对象的模板不为空并且版本一致时 直接返回  否则需要重新初始化模板
        if (!(await this.judgeProjectLevelToUpdateTemplate(object))) return object.reportViewObject;
        let result =await this.initKindsOfBaoBiaoTemplate(itemLevel,constructId,singleId,unitId);
        //如果不是手动新建的  并且 没有被表格设计过 则更新该sheet的模板  （保留上一个版本表格设计过的模板）
        if (ObjectUtils.isNotEmpty(object['reportViewObject'])) {
            await this.updateTemplateNotDesigned(object['reportViewObject']['招标项目报表'],result.zhaoBiaoList);
            await this.updateTemplateNotDesigned(object['reportViewObject']['投标项目报表'],result.touBiaoList);
            await this.updateTemplateNotDesigned(object['reportViewObject']['工程量清单报表'],result.gongChengLiangList);
            if (ObjectUtils.isNotEmpty(object['reportViewObject']['测评响应招标项目报表'])) {
                await this.updateTemplateNotDesigned(object['reportViewObject']['测评响应招标项目报表'],result.zhaoBiaoListCeping);
            }else {
                object['reportViewObject']['测评响应招标项目报表'] = result.zhaoBiaoListCeping;
            }
            if (ObjectUtils.isNotEmpty(object['reportViewObject']['测评响应投标项目报表'])) {
                await this.updateTemplateNotDesigned(object['reportViewObject']['测评响应投标项目报表'],result.touBiaoListCeping);
            }else {
                object['reportViewObject']['测评响应投标项目报表'] = result.touBiaoListCeping;
            }
            if (ObjectUtils.isNotEmpty(object['reportViewObject']['测评响应工程量清单报表'])) {
                await this.updateTemplateNotDesigned(object['reportViewObject']['测评响应工程量清单报表'],result.gongChengLiangListCeping);
            }else {
                object['reportViewObject']['测评响应工程量清单报表'] = result.gongChengLiangListCeping;
            }
        }else {
            object['reportViewObject'] = {};
            object['reportViewObject']['招标项目报表'] = result.zhaoBiaoList;
            object['reportViewObject']['投标项目报表'] = result.touBiaoList;
            object['reportViewObject']['工程量清单报表'] = result.gongChengLiangList;
            object['reportViewObject']['测评响应招标项目报表'] = result.zhaoBiaoListCeping;
            object['reportViewObject']['测评响应投标项目报表'] = result.touBiaoListCeping;
            object['reportViewObject']['测评响应工程量清单报表'] = result.gongChengLiangListCeping;
        }
        object['reportViewObject']['version'] = ExcelEnum.templateVersion;
        return object.reportViewObject;
    }

    //更新工程中没有被设计过的模板
    async updateTemplateNotDesigned(baoBiaoListOrigin,baoBiaoList) {
        for (let i = 0; i < baoBiaoListOrigin.length; i++) {
            if (ObjectUtils.isNotEmpty(baoBiaoListOrigin[i].isManual)) {  //如果是手动新建的报表不进行更新
                continue;
            }
            let filter = baoBiaoList.filter(item => item.headLine==baoBiaoListOrigin[i].headLine);
            if (ObjectUtils.isEmpty(baoBiaoListOrigin[i].isUpdated) || !baoBiaoListOrigin[i].isUpdated) {
                if (ObjectUtils.isNotEmpty(filter)) {
                    baoBiaoListOrigin[i].excelDataTemplate = filter[0].excelDataTemplate;
                }
            }
            if (ObjectUtils.isNotEmpty(baoBiaoListOrigin[i].bakReportConfig) && ObjectUtils.isNotEmpty(filter)) {
                baoBiaoListOrigin[i].bakReportConfig = filter[0].bakReportConfig;
            }
        }

        //将新版本多出来的新增模板 添加进去
        let elementList = baoBiaoList.filter(itemReport => ObjectUtils.isEmpty(baoBiaoListOrigin.filter(item => item.headLine==itemReport.headLine)));
        baoBiaoListOrigin.push(...elementList);
    }

    /**
     * 判断单项、单位、工程项目层级的报表模板是否需要重新初始化
     * @param object
     * @returns true 表示需要重新初始化
     */
    async judgeProjectLevelToUpdateTemplate(object) {
        let version = ExcelEnum.templateVersion;
        //如果对象的模板不为空并且版本一致时 直接返回  否则需要重新初始化模板
        if (ObjectUtils.isNotEmpty(object.reportViewObject) && (ObjectUtils.isNotEmpty(object.reportViewObject.version)&& ObjectUtils.isNotEmpty(version)
            && object.reportViewObject.version==version)
        ) return false;
        return true;
    }


    async initKindsOfBaoBiaoTemplate(itemLevel,constructId,singleId,unitId) {
        let constructIs2022= PricingFileFindUtils.is22De(constructId);
        let ExportSheetNameEnumZhaoBiao = {};
        let ExportSheetNameEnumTouBiao = {};
        let ExportSheetNameEnumGclQd = {};

        let ExportSheetNameEnumZhaoBiaoCeping = {};
        let ExportSheetNameEnumTouBiaoCeping = {};
        let ExportSheetNameEnumGclQdCeping = {};

        let args = {};
        if (itemLevel == "project"||itemLevel == "single") {  //
            args['levelType'] = 1;
            args['constructId'] = constructId;
        }else if (itemLevel == "unit") {
            args['levelType'] = 3;
            args['constructId'] = constructId;
            args['singleId'] = singleId;
            args['unitId'] = unitId;
            let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
            constructIs2022 = PricingFileFindUtils.is22Unit(unit);
        }
        let taxCalculation = await this.service.baseFeeFileService.taxCalculation(args);
        if (taxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            if (constructIs2022) {
                ExportSheetNameEnumZhaoBiao = zhaobiao22Simple;
                ExportSheetNameEnumTouBiao = toubiao22Simple;
                ExportSheetNameEnumGclQd = gclqd22Simple;

                ExportSheetNameEnumZhaoBiaoCeping = zhaobiao22SimpleCeping;
                ExportSheetNameEnumTouBiaoCeping = toubiao22SimpleCeping;
                ExportSheetNameEnumGclQdCeping = gclqd22SimpleCeping;
            }else {
                ExportSheetNameEnumZhaoBiao = zhaobiao12Simple;
                ExportSheetNameEnumTouBiao = toubiao12Simple;
                ExportSheetNameEnumGclQd = gclqd12Simple;

                ExportSheetNameEnumZhaoBiaoCeping = zhaobiao12SimpleCeping;
                ExportSheetNameEnumTouBiaoCeping = toubiao12SimpleCeping;
                ExportSheetNameEnumGclQdCeping = gclqd12SimpleCeping;
            }
        }else {
            if (constructIs2022) {
                ExportSheetNameEnumZhaoBiao = zhaobiao22General;
                ExportSheetNameEnumTouBiao = toubiao22General;
                ExportSheetNameEnumGclQd = gclqd22General;

                ExportSheetNameEnumZhaoBiaoCeping = zhaobiao22GeneralCeping;
                ExportSheetNameEnumTouBiaoCeping = toubiao22GeneralCeping;
                ExportSheetNameEnumGclQdCeping = gclqd22GeneralCeping;
            }else {
                ExportSheetNameEnumZhaoBiao = zhaobiao12General;
                ExportSheetNameEnumTouBiao = toubiao12General;
                ExportSheetNameEnumGclQd = gclqd12General;

                ExportSheetNameEnumZhaoBiaoCeping = zhaobiao12GeneralCeping;
                ExportSheetNameEnumTouBiaoCeping = toubiao12GeneralCeping;
                ExportSheetNameEnumGclQdCeping = gclqd12GeneralCeping;
            }
        }


        let zhaoBiaoList = _.cloneDeep(ExportSheetNameEnumZhaoBiao.招标项目报表).filter(function (element) {
            if (element.projectLevel==itemLevel)
                return element;
        });
        let touBiaoList = _.cloneDeep(ExportSheetNameEnumTouBiao.投标项目报表).filter(function (element) {
            if (element.projectLevel==itemLevel)
                return element;
        });
        let gongChengLiangList = _.cloneDeep(ExportSheetNameEnumGclQd.工程量清单报表).filter(function (element) {
            if (element.projectLevel==itemLevel)
                return element;
        });

        let zhaoBiaoListCeping = _.cloneDeep(ExportSheetNameEnumZhaoBiaoCeping.测评响应招标项目报表).filter(function (element) {
            if (element.projectLevel==itemLevel)
                return element;
        });
        let touBiaoListCeping = _.cloneDeep(ExportSheetNameEnumTouBiaoCeping.测评响应投标项目报表).filter(function (element) {
            if (element.projectLevel==itemLevel)
                return element;
        });
        let gongChengLiangListCeping = _.cloneDeep(ExportSheetNameEnumGclQdCeping.测评响应工程量清单报表).filter(function (element) {
            if (element.projectLevel==itemLevel)
                return element;
        });

        if (itemLevel == "single") {
            let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);
            if (ObjectUtils.isNotEmpty(singleProject.subSingleProjects)) {
                let elementZb = zhaoBiaoList.filter(item => item.headLine=="表1-5 单项工程费汇总表")[0];
                elementZb.excelDataTemplate = DXGCFHZ1_5.招标项目报表;
                let elementTb = touBiaoList.filter(item => item.headLine=="表1-5 单项工程费汇总表")[0];
                elementTb.excelDataTemplate = DXGCFHZ1_5.投标项目报表;
                let elementGcl = gongChengLiangList.filter(item => item.headLine=="表1-5 单项工程费汇总表")[0];
                elementGcl.excelDataTemplate = DXGCFHZ1_5.工程量清单报表;

                let elementZbCeping = zhaoBiaoListCeping.filter(item => item.headLine=="表1-5 单项工程费汇总表")[0];
                elementZbCeping.excelDataTemplate = DXGCFHZ1_5Ceping.测评响应招标项目报表;
                let elementTbCeping = touBiaoListCeping.filter(item => item.headLine=="表1-5 单项工程费汇总表")[0];
                elementTbCeping.excelDataTemplate = DXGCFHZ1_5Ceping.测评响应投标项目报表;
                let elementGclCeping = gongChengLiangListCeping.filter(item => item.headLine=="表1-5 单项工程费汇总表")[0];
                elementGclCeping.excelDataTemplate = DXGCFHZ1_5Ceping.测评响应工程量清单报表;
            }
        }

        if (itemLevel == "unit") {
            if (constructIs2022) {  //如果是22定额标准项目
                zhaoBiaoList = zhaoBiaoList.filter(function (element) {
                    if (!element.headLine.includes("增值税报表")) {
                        return element;
                    }
                });
                touBiaoList = touBiaoList.filter(function (element) {
                    if (!element.headLine.includes("增值税报表")) {
                        return element;
                    }
                });
                gongChengLiangList = gongChengLiangList.filter(function (element) {
                    if (!element.headLine.includes("增值税报表")) {
                        return element;
                    }
                });
                zhaoBiaoListCeping = zhaoBiaoListCeping.filter(function (element) {
                    if (!element.headLine.includes("增值税报表")) {
                        return element;
                    }
                });
                touBiaoListCeping = touBiaoListCeping.filter(function (element) {
                    if (!element.headLine.includes("增值税报表")) {
                        return element;
                    }
                });
                gongChengLiangListCeping = gongChengLiangListCeping.filter(function (element) {
                    if (!element.headLine.includes("增值税报表")) {
                        return element;
                    }
                });
            }else {
                let args = {};
                args['levelType'] = 3;
                args['constructId'] = constructId;
                args['singleId'] = singleId;
                args['unitId'] = unitId;
                let taxCalculation = await this.service.baseFeeFileService.taxCalculation(args);
                //简易计税  去掉增值税表
                if (taxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
                    zhaoBiaoList = zhaoBiaoList.filter(item => !(item.headLine=="增值税报表"));
                    touBiaoList = touBiaoList.filter(item => !(item.headLine=="增值税报表"));
                    gongChengLiangList = gongChengLiangList.filter(item => !(item.headLine=="增值税报表"));

                    zhaoBiaoListCeping = zhaoBiaoListCeping.filter(item => !(item.headLine=="增值税报表"));
                    touBiaoListCeping = touBiaoListCeping.filter(item => !(item.headLine=="增值税报表"));
                    gongChengLiangListCeping = gongChengLiangListCeping.filter(item => !(item.headLine=="增值税报表"));
                } else { //12的一般计税  临时删除
                    let zhaoBiaoZZS = zhaoBiaoList.filter(item => item.headLine=="增值税报表")[0];
                    let touBiaoZZS = touBiaoList.filter(item => item.headLine=="增值税报表")[0];
                    let gongChengLiangZZS = gongChengLiangList.filter(item => item.headLine=="增值税报表")[0];
                    zhaoBiaoZZS.children = zhaoBiaoZZS.children.filter(item => !(item.headLine=="材料、机械、设备增值税计算表（实体）"
                        ||item.headLine=="材料、机械、设备增值税计算表（措施）"));
                    touBiaoZZS.children = touBiaoZZS.children.filter(item => !(item.headLine=="材料、机械、设备增值税计算表（实体）"
                        ||item.headLine=="材料、机械、设备增值税计算表（措施）"));
                    gongChengLiangZZS.children = gongChengLiangZZS.children.filter(item => !(item.headLine=="材料、机械、设备增值税计算表（实体）"
                        ||item.headLine=="材料、机械、设备增值税计算表（措施）"));

                    let zhaoBiaoZZSCeping = zhaoBiaoListCeping.filter(item => item.headLine=="增值税报表")[0];
                    let touBiaoZZSCeping = touBiaoListCeping.filter(item => item.headLine=="增值税报表")[0];
                    let gongChengLiangZZSCeping = gongChengLiangListCeping.filter(item => item.headLine=="增值税报表")[0];
                    zhaoBiaoZZSCeping.children = zhaoBiaoZZSCeping.children.filter(item => !(item.headLine=="材料、机械、设备增值税计算表（实体）"
                        ||item.headLine=="材料、机械、设备增值税计算表（措施）"));
                    touBiaoZZSCeping.children = touBiaoZZSCeping.children.filter(item => !(item.headLine=="材料、机械、设备增值税计算表（实体）"
                        ||item.headLine=="材料、机械、设备增值税计算表（措施）"));
                    gongChengLiangZZSCeping.children = gongChengLiangZZSCeping.children.filter(item => !(item.headLine=="材料、机械、设备增值税计算表（实体）"
                        ||item.headLine=="材料、机械、设备增值税计算表（措施）"));
                }
            }
        }

        //去除父级标题项  其他栏目 不做处理
        await this.removeParentHeadLine(zhaoBiaoList);
        await this.removeParentHeadLine(touBiaoList);
        await this.removeParentHeadLine(gongChengLiangList);
        await this.removeParentHeadLine(zhaoBiaoListCeping);
        await this.removeParentHeadLine(touBiaoListCeping);
        await this.removeParentHeadLine(gongChengLiangListCeping);
        //初始化横竖版状态
        await this.setIsLandScapeTrue(zhaoBiaoList.filter(item => item.headLine.includes("增值税计算表")));
        await this.setIsLandScapeTrue(zhaoBiaoList.filter(item => item.headLine.includes("单项工程造价分析表")));

        await this.setIsLandScapeTrue(touBiaoList.filter(item => item.headLine.includes("增值税计算表")));
        await this.setIsLandScapeTrue(touBiaoList.filter(item => item.headLine.includes("单项工程造价分析表")));

        await this.setIsLandScapeTrue(gongChengLiangList.filter(item => item.headLine.includes("增值税计算表")));
        await this.setIsLandScapeTrue(gongChengLiangList.filter(item => item.headLine.includes("单项工程造价分析表")));

        await this.setIsLandScapeTrue(zhaoBiaoListCeping.filter(item => item.headLine.includes("增值税计算表")));
        await this.setIsLandScapeTrue(zhaoBiaoListCeping.filter(item => item.headLine.includes("单项工程造价分析表")));

        await this.setIsLandScapeTrue(touBiaoListCeping.filter(item => item.headLine.includes("增值税计算表")));
        await this.setIsLandScapeTrue(touBiaoListCeping.filter(item => item.headLine.includes("单项工程造价分析表")));

        await this.setIsLandScapeTrue(gongChengLiangListCeping.filter(item => item.headLine.includes("增值税计算表")));
        await this.setIsLandScapeTrue(gongChengLiangListCeping.filter(item => item.headLine.includes("单项工程造价分析表")));

        await this.bakOrginalReport(zhaoBiaoList);
        await this.bakOrginalReport(touBiaoList);
        await this.bakOrginalReport(gongChengLiangList);
        await this.bakOrginalReport(zhaoBiaoListCeping);
        await this.bakOrginalReport(touBiaoListCeping);
        await this.bakOrginalReport(gongChengLiangListCeping);

        return {"zhaoBiaoList":zhaoBiaoList,"touBiaoList":touBiaoList,"gongChengLiangList":gongChengLiangList,
        "zhaoBiaoListCeping":zhaoBiaoListCeping,
        "touBiaoListCeping":touBiaoListCeping,
        "gongChengLiangListCeping":gongChengLiangListCeping,
        };
    }

    /**
     * 备份原始的报表模板及对应配置  以备快速设计进行恢复默认设置
     * @returns {Promise<void>}
     */
    async bakOrginalReport(headLineList) {
        if (ObjectUtils.isEmpty(headLineList)) return;
        for (let i = 0; i < headLineList.length; i++) {
            headLineList[i].bakReportConfig = {};
            headLineList[i].bakReportConfig.excelDataTemplate = headLineList[i].excelDataTemplate;
            headLineList[i].bakReportConfig.isLandScape = headLineList[i].isLandScape;
        }
    }

    async setIsLandScapeTrue(headLineList) {
        if (ObjectUtils.isEmpty(headLineList)) return;
        for (let i = 0; i < headLineList.length; i++) {
            headLineList[i].isLandScape = true;
        }
    }

    async removeParentHeadLine(headLineList) {
        for (let i = 0; i < headLineList.length; i++) {
            if (ObjectUtils.isNotEmpty(headLineList[i].children)) {
                let parent = headLineList[i];
                for (let j = 0; j < parent.children.length; j++) {
                    let child = parent.children[j];
                    headLineList.splice(i+j,0,child);
                }
                let indexOf = headLineList.indexOf(parent);
                headLineList.splice(indexOf,1);
                await this.removeParentHeadLine(headLineList);
                break;
            }
        }
    }


    async calListSheetType(sheetList){
        if(ObjectUtils.isNotEmpty(sheetList)){
            sheetList.forEach(o=>{
                if(ObjectUtils.isEmpty(o.sheetType)){
                    o.sheetType = ExportSheetTypeConstant.SHEET_TYPE_AUTO;
                }
            })
        }
    }


    async showSheetStyle(itemLevel,lanMuName,sheetName,sheetType,args) {
        let {constructId,singleId,unitId,jsonData} = args;
        let object = null;//该对象
        if (itemLevel == "project") {
            object = PricingFileFindUtils.getProjectObjById(constructId);
        }
        if (itemLevel == "single") {
            object = PricingFileFindUtils.getSingleProject(constructId,singleId);
        }
        if (itemLevel == "unit") {
            object = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        }
        let branchCondition = true;
        let sheetObject = {};
        if (ObjectUtils.isNotEmpty(object.reportViewObject)) {
            sheetObject = await this.traverseGetHeadLineObject(object.reportViewObject[lanMuName],sheetName);
            if (ObjectUtils.isNotEmpty(sheetObject) && ObjectUtils.isNotEmpty(sheetObject.excelDataTemplate)) { //如果有对应的数据模板
                branchCondition = false;
            }
        }
        let workbook = new ExcelJS.Workbook();
        if (ObjectUtils.isNotEmpty(jsonData)){  //系统报表的展示走这里
            let worksheet = workbook.addWorksheet(sheetName);
            let result = [];
            let dataTemplate = new DataTemplate();
            try {
                if (lanMuName.includes("测评")) {
                    await this.dealWithJsonData(sheetObject.excelDataTemplate);
                }
                await this.service.exportDesignService.accordingTemplateToSheet(jsonData,args,worksheet,itemLevel,false,dataTemplate);
            } catch (e) {
                console.log("根据数据模板加载数据异常");
            }
            // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test-Before.xlsx");
            try {
                result = await ExcelOperateUtil.findCellStyleList(workbook.getWorksheet(sheetName),false,dataTemplate);
            } catch (e) {
                console.log("workSheet转换前端数据渲染异常");
            }
            return result;
         } else if (branchCondition){  //根据excel模板加载生成sheet
            workbook = await this.basedOnExcelTemplate(itemLevel,lanMuName,sheetName,sheetType,args);
            let result;
            try {
                result = await ExcelUtil.findCellStyleList(workbook.getWorksheet(sheetName));
            } catch (e) {
                console.log("报表填充数据异常");
            }
            return result;
        } else { //走根据数据模板生成sheet的逻辑
            let worksheet = workbook.addWorksheet(sheetName);
            let result = [];
            let dataTemplate = new DataTemplate();
            args["numberZero"] = sheetObject.numberZero;
            args["emptyField"] = sheetObject.emptyField;
            args["containsTaxCalculation"] = sheetObject.containsTaxCalculation;
            try {
                if (lanMuName.includes("测评")) {
                    await this.dealWithJsonData(sheetObject.excelDataTemplate);
                }
                await this.service.exportDesignService.accordingTemplateToSheet(sheetObject.excelDataTemplate,args,worksheet,itemLevel,sheetObject.isLandScape,dataTemplate);
            } catch (e) {
                console.log("根据数据模板加载数据异常");
            }
            // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test-Before.xlsx");
            try {
                result = await ExcelOperateUtil.findCellStyleList(workbook.getWorksheet(sheetName),sheetObject.isLandScape,dataTemplate);
            } catch (e) {
                console.log("workSheet转换前端数据渲染异常");
            }
            result["numberZero"] = sheetObject.numberZero;
            result["emptyField"] = sheetObject.emptyField;
            return result;
        }

    }

    //处理测评模板数据jsonData的cell数据 位数默认保留问题
    async dealWithJsonData(jsonData) {
        // jsonData
        let cellDataObject = jsonData.sheets.sheet1.cellData
        for (let cellDataKey in cellDataObject) {
            let rowObject = cellDataObject[cellDataKey];
            for (let rowObjectKey in rowObject) {
                let cellElement = rowObject[rowObjectKey];
                if (ObjectUtils.isEmpty(cellElement.custom)) {
                    cellElement.custom = {};
                }
                if (ObjectUtils.isEmpty(cellElement.custom.precision)) {
                    cellElement.custom.precision = "2";//价格类默认保留2位
                }
            }
        }
    }

    //拿到list中 没有children的元素
    async traverseGetHeadLineObject(headLineList,sheetName) {
        for (let i = 0; i < headLineList.length; i++) {
            let element = headLineList[i];

            if (element.headLine == sheetName) {
                return element;
            }

            if (ObjectUtils.isNotEmpty(element.children)) {
                let object = await this.traverseGetHeadLineObject(element.children,sheetName);
                if (ObjectUtils.isNotEmpty(object)) {
                    return object;
                }
            }
        }
        return null;
    }


    async basedOnExcelTemplate(itemLevel,lanMuName,sheetName,sheetType,args) {
        let unitIs2022= PricingFileFindUtils.getConstructDeStandard(args.constructId)==ConstantUtil.DE_STANDARD_22;
        let project = PricingFileFindUtils.getProjectObjById(args.constructId);
        //计税方式
        let taxCalculationMethodPath = "";
        let taxCalculationMethod = project.projectTaxCalculation.taxCalculationMethod;
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            taxCalculationMethodPath = "简易";
        }else {
            taxCalculationMethodPath = "一般";
        }
        let other = "";
        let touBiao = "";
        let unit = "";
        let zhaoBiao = "";
        if (unitIs2022) {
            other = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\22\\other\\招标通模板.xlsx";
            touBiao = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\22\\touBiao";
            unit = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\22\\unit";
            zhaoBiao = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\22\\zhaoBiao";
        }else {
            other = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\12\\other\\招标通模板.xlsx";
            touBiao = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\12\\touBiao";
            unit = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\12\\unit";
            zhaoBiao = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\12\\zhaoBiao";
        }
        if (itemLevel == "project") {
            touBiao = touBiao+"\\工程项目层级.xlsx";
            unit  = unit+"\\工程项目层级.xlsx";
            zhaoBiao = zhaoBiao+"\\工程项目层级.xlsx";
        }else if (itemLevel == "single") {
            touBiao = touBiao+"\\单项工程层级.xlsx";
            unit  = unit+"\\单项工程层级.xlsx";
            zhaoBiao = zhaoBiao+"\\单项工程层级.xlsx";
        }else if (itemLevel == "unit") {
            touBiao = touBiao+"\\单位工程层级.xlsx";
            unit  = unit+"\\单位工程层级.xlsx";
            zhaoBiao = zhaoBiao+"\\单位工程层级.xlsx";
        }
        let loadPath = "";
        if (lanMuName == "招标项目报表"||lanMuName == "测评响应招标项目报表") {
            loadPath = zhaoBiao;
        }
        if (lanMuName == "投标项目报表"||lanMuName == "测评响应投标项目报表") {
            loadPath = touBiao;
        }
        if (lanMuName == "工程量清单报表"||lanMuName == "测评响应工程量清单报表") {
            loadPath = unit;
        }
        if (lanMuName == "其他") {
            loadPath =other;
        }
        let workbook = await ExcelUtil.readToWorkBook(loadPath);
        args["workbook"] = workbook;
        try {
            if (sheetType === ExportSheetTypeConstant.SHEET_TYPE_HAND_RCJ) {
                await this.getWorkSheetWithDataHandRcj(workbook, itemLevel, sheetName, lanMuName, args);
            } else {
                await this.switchWorkSheet(itemLevel, lanMuName, workbook.getWorksheet(sheetName), args);
            }
        } catch (e) {
            console.log("报表填充数据异常");
        }
        return workbook;
    }

    async queryLanMuData(lanMuName,constructId) {
        let constructIs2022 = PricingFileFindUtils.getConstructDeStandard(constructId)==ConstantUtil.DE_STANDARD_22;
        let result = {};
        let construct= PricingFileFindUtils.getProjectObjById(constructId);
        if (construct.biddingType == 2) {
            await this.initReportStructureObject("unit",constructId);
            //则为单位工程
            result["headLine"] = construct.constructName;
            result["childrenList"] = construct.unitProject.reportViewObject[lanMuName];
            result["id"] = construct.sequenceNbr;
            result['biddingType'] = 2;
            //对result进行递归遍历  增加唯一序号
            let object = {};
            object['id'] = 1;
            await this.addOrderNum(result,object);
            return result;
        }
        let newProject = await this.deepCopy(construct.reportViewObject[lanMuName]);
        let constructChildrenList = await this.traverseGetHeadLineAndLeaf(newProject);
        result["headLine"] = construct.constructName;
        result["childrenList"] = constructChildrenList;
        result["id"] = construct.sequenceNbr;

        for (let i = 0; i < construct.singleProjects.length; i++) {
            await this.traverseSingleForExport(construct.sequenceNbr,construct.singleProjects[i],constructChildrenList,lanMuName,constructIs2022);
        }

        //对result进行递归遍历  增加唯一序号
        let object = {};
        object['id'] = 1;
        await this.addOrderNum(result,object);

        return result;
    }

    // async removeUnitTaxReport(levelType,constructId,singleId,unitId,report,lanMuName) {
    //
    //     let args = {};
    //     args['levelType'] = levelType;
    //     args['constructId'] = constructId;
    //     args['singleId'] = singleId;
    //     args['unitId'] = unitId;
    //     let taxCalculation = await this.service.baseFeeFileService.taxCalculation(args);
    //     //---------12定额标准下  判断是否需要展示增值税相关报表---------------------------------
    //     //简易计税
    //     if (taxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
    //         if (lanMuName == "招标项目报表" || lanMuName == "招标项目报表" || lanMuName == "工程量清单报表") {
    //             report.splice(24,4);
    //         }else {
    //             //如果是其他栏目 报表  舍掉最后两个元素
    //             report.pop();
    //             report.pop();
    //         }
    //     }
    // }

    //增加子单项及其单位的结构
    async traverseSingleForExport(constructId,singleProject,result,lanMuName,is2022) {
        await this.initReportStructureObject("single",constructId,singleProject.sequenceNbr,null);
        let singleObject = {};
        singleObject["headLine"] = singleProject.projectName;
        let singleChildren = await this.deepCopy(singleProject.reportViewObject[lanMuName]);
        singleChildren = await this.traverseGetHeadLineAndLeaf(singleChildren);
        singleObject["childrenList"] = singleChildren;
        singleObject["id"] = singleProject.sequenceNbr;
        result.push(singleObject);

        if (!ObjectUtils.isEmpty(singleProject.unitProjects)) {
            for (let i = 0; i < singleProject.unitProjects.length; i++) {
                let unitObject = {};
                unitObject["headLine"] = singleProject.unitProjects[i].upName;
                await this.initReportStructureObject("unit",constructId,singleProject.sequenceNbr,singleProject.unitProjects[i].sequenceNbr);
                let unitChildrenList = await this.deepCopy(singleProject.unitProjects[i].reportViewObject[lanMuName]);
                unitChildrenList = await this.traverseGetHeadLineAndLeaf(unitChildrenList);
                unitObject["childrenList"] = unitChildrenList;

                // let rcjCreateClassList = await this.service.rcjProcess.getUseRcjClassificationTableListToExport(constructId, singleProject.sequenceNbr, singleProject.unitProjects[i].sequenceNbr);
                // if (ObjectUtils.isNotEmpty(rcjCreateClassList)) {
                //     for (let item of rcjCreateClassList) {
                //         let obj = {};
                //         obj.headLine = item.name;
                //         obj.projectLevel = "unit";
                //         obj.sheetType = ExportSheetTypeConstant.SHEET_TYPE_HAND_RCJ;
                //         unitObject["childrenList"].push(obj);
                //     }
                // }
                // await this.calListSheetType(unitObject["childrenList"]);

                unitObject["id"] = singleProject.unitProjects[i].sequenceNbr;
                singleChildren.push(unitObject);
            }
        }

        if (!ObjectUtils.isEmpty(singleProject.subSingleProjects)) {
            for (let i = 0; i < singleProject.subSingleProjects.length; i++) {
                await this.traverseSingleForExport(constructId,singleProject.subSingleProjects[i],singleChildren,lanMuName,is2022);
            }
        }
    }

    async addOrderNum(result,object) {
        if (result['id'] == null) {
            result['id'] = object.id++;
        }
        if (result.childrenList != null) {
            for (let i = 0; i < result.childrenList.length; i++) {
                await this.addOrderNum(result.childrenList[i],object);
            }
        }
    }
    //非公有方法
    async deepCopy(obj) {
        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }

        let clone = Array.isArray(obj) ? [] : {};

        for (let key in obj) {
            if (obj.hasOwnProperty(key) && key!="excelDataTemplate") {
                clone[key] =await this.deepCopy(obj[key]);
            }
        }

        return clone;
    }

    async exportExcel(lanMuName,params,startPage,totalPage) {
        // let {constructName} = params.headLine;
        // let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(constructName);
        let construct = PricingFileFindUtils.getProjectObjById(params.id);
        if (ObjectUtils.isEmpty(construct.exportConfig)) {  //导出设置的配置
            construct.exportConfig = new ExportConfig(null,null,"excelWithLevel");
        }
        let flagShow = construct.exportConfig.batchExport=="excelWithAll"?true:false;
        const dialogOptions = {
            title: '保存文件',
            defaultPath: params.headLine,
            filters: [{name: flagShow?'xlsx':'zip', extensions: [flagShow?'xlsx':'zip']}]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        //弹出弹框确定路径以后 走下面
        if (result && !result.canceled) {
            let filePath = result;
            console.log(filePath);
            await this.exportExcelZip(lanMuName,params,filePath,startPage,totalPage);
            return true;
            // this.service.systemService.openWindowForProject(constructName,sequenceNbr);
        }else {
            return false;
        }
    }

    async traverseParams(params) {
        if (ObjectUtils.isNotEmpty(params) && params.selected) {
            return params.headLine;
        }
        if (ObjectUtils.isNotEmpty(params) && ObjectUtils.isNotEmpty(params.childrenList)) {
            for (let i = 0; i < params.childrenList.length; i++) {
                 let result = await this.traverseParams(params.childrenList[i]);
                 if (ObjectUtils.isNotEmpty(result)) return result;
            }
        }
        return ;
    }

    async exportSingleSheetExcel(args) {
        let {lanMuName,params,startPage,totalPage} = args;
        let headLine = await this.traverseParams(params);
        const dialogOptions = {
            title: '导出excel',
            defaultPath: headLine,
            filters: [{name: 'excel文件', extensions: ['xlsx']}]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        //弹出弹框确定路径以后 走下面
        if (result && !result.canceled) {
            let filePath = result;
            console.log(filePath);
            await this.exportExcelSingleSheet(lanMuName,params,filePath,startPage,totalPage);
            return true;
        }else {
            return false;
        }
    }

    async exportExcelSingleSheet(lanMuName,params,filePath,startPage,totalPage) {
        let constructIs2022= PricingFileFindUtils.getConstructDeStandard(params.id)==ConstantUtil.DE_STANDARD_22;
        let construct = PricingFileFindUtils.getProjectObjById(params.id);
        if (ObjectUtils.isEmpty(construct.exportConfig)) {  //导出设置的配置
            construct.exportConfig = new ExportConfig(null,null,"excelWithLevel");
        }
        let taxCalculationMethodPath = "";
        let taxCalculationMethod = construct.projectTaxCalculation.taxCalculationMethod;
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            taxCalculationMethodPath = "简易";
        }else {
            taxCalculationMethodPath = "一般";
        }
        let project = await this.initWorkBook(lanMuName,"project",constructIs2022,taxCalculationMethodPath);
        let single = await this.initWorkBook(lanMuName,"single",constructIs2022,taxCalculationMethodPath);
        let unit = await this.initWorkBook(lanMuName,"unit",constructIs2022,taxCalculationMethodPath);

        let fileDir = this.getProjectRootPath()+"\\excelTemplate\\export\\"+params.headLine;
        let workBookList = [];
        let args = {};
        args['constructId'] = params.id;
        args['startPage'] = startPage;
        args['totalPage'] = totalPage;
        args['containsTaxCalculation'] = construct.exportConfig.containsTaxCalculation;
        args['printPageFootAndEyeBrow'] = construct.exportConfig.exportPageEyeBrowPageFoot;
        await this.parseParams(params,project,single,unit,lanMuName,fileDir,args,workBookList,constructIs2022,taxCalculationMethodPath);

        if (construct.exportConfig.batchExport=="excelWithLevel") {
            for (let i = 0; i < workBookList.length; i++) {
                await this.createDirectory(workBookList[i].fileDir);
                await workBookList[i].sheet.xlsx.writeFile(workBookList[i].filename);
            }
        }else if (construct.exportConfig.batchExport=="excelWithAll") { //所有sheet到一个excel中

            await this.createDirectory(workBookList[0].fileDir);
            //合成一个大excel文件
            //先对workBookList[0]._worksheets 按照 worksheets的顺序进行重排  worksheets的属性orderNo 始终是有序的
            for (let i = 0; i < workBookList[0].sheet.worksheets.length; i++) {
                //按照worksheets的元素排列  确定在_worksheets 中当前的索引  及id相同的对应索引 进行位置交换 使当前索引上的sheet是想要的对应id的sheet
                let indexCur = await this.getIndexIn_worksheets(i+1,workBookList[0].sheet);
                let indexId = await this.getIndexOfSameId(workBookList[0].sheet.worksheets[i].id,workBookList[0].sheet);
                [workBookList[0].sheet._worksheets[indexCur], workBookList[0].sheet._worksheets[indexId]] = [workBookList[0].sheet._worksheets[indexId],workBookList[0].sheet._worksheets[indexCur]];
            }

            //更新表名 增加格式 【单位工程名称】
            for (let i = 0; i < workBookList[0].sheet.worksheets.length; i++) {
                let worksheet1 = workBookList[0].sheet.worksheets[i];
                let levelName = workBookList[0].filename.replace(workBookList[0].fileDir+"\\","").replace(".xlsx","");
                worksheet1.name = worksheet1.name+"【"+levelName+"】";
            }
            for (let i = 1; i < workBookList.length; i++) {
                let bookElement = workBookList[i].sheet;
                for (let j = 0; j < bookElement.worksheets.length; j++) {
                    let worksheet = bookElement.worksheets[j];
                    if (worksheet != null) {
                        let levelName = workBookList[i].filename.replace(workBookList[i].fileDir+"\\","").replace(".xlsx","");
                        worksheet.name =worksheet.name+"【"+levelName+"】";
                        workBookList[0].sheet._worksheets.push(worksheet);
                    }
                }
            }
            //excel表格乱序 展示顺序是按照 worksheets数组的顺序来的 而不是  _worksheets
            //如果这里不重置id和orderNo 会导致sheet名称和实际内容对不上  因为会有重复的id和orderNo
            let orderNo = 0;
            let map = new Map();//键为sheet名称  值为重复的累加次数
            for (let i = 0; i < workBookList[0].sheet._worksheets.length; i++) {
                let worksheetSam = workBookList[0].sheet._worksheets[i];
                if (worksheetSam != null) {
                    let replace = worksheetSam.name.replace(/【.*?】/g, '');
                    let levelName = worksheetSam.name.replace(replace,'').replace("【","").replace("】","");
                    if (map.has(replace)) {
                        map.set(replace, map.get(replace)+1);
                    }else {
                        map.set(replace,1);
                    }
                    let wholeChar = replace+"【"+levelName+"_"+map.get(replace)+"】";
                    if (wholeChar.length > 31) {
                        levelName = levelName.substring(0,levelName.length-(wholeChar.length-32));//去掉】  则名称多延长一位
                        worksheetSam.name = replace+"【"+levelName+"_"+map.get(replace);
                    }else {
                        worksheetSam.name = replace+"【"+levelName+"_"+map.get(replace)+"】";
                    }
                    worksheetSam.id = ++orderNo;
                    worksheetSam.orderNo = orderNo;
                }
            }
            await workBookList[0].sheet.xlsx.writeFile(workBookList[0].fileDir+"\\"+construct.constructName+".xlsx");
        }else if (construct.exportConfig.batchExport=="excelWithSingleSheet") {
            for (let i = 0; i < workBookList.length; i++) {
                await this.createDirectory(workBookList[i].fileDir);
                let sheet = workBookList[i].sheet;
                for (let j = 0; j < sheet.worksheets.length; j++) {
                    let worksheet = sheet.worksheets[j];
                    let workbook = new ExcelJS.Workbook();
                    await ExcelOperateUtil.addWorkSheet(workbook,worksheet,worksheet.name);
                    await workbook.xlsx.writeFile(workBookList[i].fileDir+"\\"+worksheet.name+".xlsx");
                }
            }
        }
        workBookList[0].sheet.xlsx.writeFile(filePath);
        function deleteDirectory(dirPath) {
            if (fs.existsSync(dirPath)) {
                fs.readdirSync(dirPath).forEach(file => {
                    const filePath = path.join(dirPath, file);

                    if (fs.lstatSync(filePath).isDirectory()) {
                        deleteDirectory(filePath); // 递归删除子目录
                    } else {
                        fs.unlinkSync(filePath); // 删除文件
                    }
                });

                fs.rmdirSync(dirPath); // 删除空目录
                console.log('目录删除成功');
            } else {
                console.log('目录不存在');
            }
        }
        deleteDirectory(fileDir);
    }

    async exportExcelZip(lanMuName,params,filePath,startPage,totalPage) {
        let constructIs2022= PricingFileFindUtils.getConstructDeStandard(params.id)==ConstantUtil.DE_STANDARD_22;
        let construct = PricingFileFindUtils.getProjectObjById(params.id);
        if (ObjectUtils.isEmpty(construct.exportConfig)) {  //导出设置的配置
            construct.exportConfig = new ExportConfig(null,null,"excelWithLevel");
        }
        let taxCalculationMethodPath = "";
        let taxCalculationMethod = construct.projectTaxCalculation.taxCalculationMethod;
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            taxCalculationMethodPath = "简易";
        }else {
            taxCalculationMethodPath = "一般";
        }
        let project = await this.initWorkBook(lanMuName,"project",constructIs2022,taxCalculationMethodPath);
        let single = await this.initWorkBook(lanMuName,"single",constructIs2022,taxCalculationMethodPath);
        let unit = await this.initWorkBook(lanMuName,"unit",constructIs2022,taxCalculationMethodPath);

        let fileDir = this.getProjectRootPath()+"\\excelTemplate\\export\\"+params.headLine;
        let workBookList = [];
        let args = {};
        args['constructId'] = params.id;
        args['startPage'] = startPage;
        args['totalPage'] = totalPage;
        args['containsTaxCalculation'] = construct.exportConfig.containsTaxCalculation;
        args['printPageFootAndEyeBrow'] = construct.exportConfig.exportPageEyeBrowPageFoot;
        await this.parseParams(params,project,single,unit,lanMuName,fileDir,args,workBookList,constructIs2022,taxCalculationMethodPath);

        let isNeedZip = true;//导出是否需要压缩到zip文件中
        if (construct.exportConfig.batchExport=="excelWithLevel") {
            for (let i = 0; i < workBookList.length; i++) {
                await this.createDirectory(workBookList[i].fileDir);
                await workBookList[i].sheet.xlsx.writeFile(workBookList[i].filename);
            }
        }else if (construct.exportConfig.batchExport=="excelWithAll") { //所有sheet到一个excel中
            isNeedZip = false;
            await this.createDirectory(workBookList[0].fileDir);
            //合成一个大excel文件
            //先对workBookList[0]._worksheets 按照 worksheets的顺序进行重排  worksheets的属性orderNo 始终是有序的
            for (let i = 0; i < workBookList[0].sheet.worksheets.length; i++) {
                //按照worksheets的元素排列  确定在_worksheets 中当前的索引  及id相同的对应索引 进行位置交换 使当前索引上的sheet是想要的对应id的sheet
                let indexCur = await this.getIndexIn_worksheets(i+1,workBookList[0].sheet);
                let indexId = await this.getIndexOfSameId(workBookList[0].sheet.worksheets[i].id,workBookList[0].sheet);
                [workBookList[0].sheet._worksheets[indexCur], workBookList[0].sheet._worksheets[indexId]] = [workBookList[0].sheet._worksheets[indexId],workBookList[0].sheet._worksheets[indexCur]];
            }

            //更新表名 增加格式 【单位工程名称】
            for (let i = 0; i < workBookList[0].sheet.worksheets.length; i++) {
                let worksheet1 = workBookList[0].sheet.worksheets[i];
                let levelName = workBookList[0].filename.replace(workBookList[0].fileDir+"\\","").replace(".xlsx","");
                worksheet1.name = worksheet1.name+"【"+levelName+"】";
            }
            for (let i = 1; i < workBookList.length; i++) {
                let bookElement = workBookList[i].sheet;
                for (let j = 0; j < bookElement.worksheets.length; j++) {
                    let worksheet = bookElement.worksheets[j];
                    if (worksheet != null) {
                        let levelName = workBookList[i].filename.replace(workBookList[i].fileDir+"\\","").replace(".xlsx","");
                        worksheet.name =worksheet.name+"【"+levelName+"】";
                        workBookList[0].sheet._worksheets.push(worksheet);
                    }
                }
            }
            //excel表格乱序 展示顺序是按照 worksheets数组的顺序来的 而不是  _worksheets
            //如果这里不重置id和orderNo 会导致sheet名称和实际内容对不上  因为会有重复的id和orderNo
            let orderNo = 0;
            let map = new Map();//键为sheet名称  值为重复的累加次数
            for (let i = 0; i < workBookList[0].sheet._worksheets.length; i++) {
                let worksheetSam = workBookList[0].sheet._worksheets[i];
                if (worksheetSam != null) {
                    let replace = worksheetSam.name.replace(/【.*?】/g, '');
                    let levelName = worksheetSam.name.replace(replace,'').replace("【","").replace("】","");
                    if (map.has(replace)) {
                        map.set(replace, map.get(replace)+1);
                    }else {
                        map.set(replace,1);
                    }
                    let wholeChar = replace+"【"+levelName+"_"+map.get(replace)+"】";
                    if (wholeChar.length > 31) {
                        levelName = levelName.substring(0,levelName.length-(wholeChar.length-32));//去掉】  则名称多延长一位
                        worksheetSam.name = replace+"【"+levelName+"_"+map.get(replace);
                    }else {
                        worksheetSam.name = replace+"【"+levelName+"_"+map.get(replace)+"】";
                    }
                    worksheetSam.id = ++orderNo;
                    worksheetSam.orderNo = orderNo;
                }
            }
            await workBookList[0].sheet.xlsx.writeFile(filePath);
        }else if (construct.exportConfig.batchExport=="excelWithSingleSheet") {
            for (let i = 0; i < workBookList.length; i++) {
                await this.createDirectory(workBookList[i].fileDir);
                let sheet = workBookList[i].sheet;
                for (let j = 0; j < sheet.worksheets.length; j++) {
                    let worksheet = sheet.worksheets[j];
                    let workbook = new ExcelJS.Workbook();
                    await ExcelOperateUtil.addWorkSheet(workbook,worksheet,worksheet.name);
                    await workbook.xlsx.writeFile(workBookList[i].fileDir+"\\"+worksheet.name+".xlsx");
                }
            }
        }
        if (isNeedZip) {
            //对 对应的目录进行压缩 生成zip文件
            // 创建一个新的 Zip 实例
            const zip = new AdmZip();

            // 递归遍历目录及其子目录中的文件和子目录
            function traverseDirectory(dirPath, relativePath = '') {
                const files = fs.readdirSync(dirPath);

                files.forEach(file => {
                    const filePath = path.join(dirPath, file);
                    const stats = fs.statSync(filePath);

                    if (stats.isDirectory()) {
                        const fileRelativeNext = path.join(relativePath, file);
                        zip.addFile(fileRelativeNext + '/', Buffer.alloc(0), '', 493); // 添加空文件夹
                        traverseDirectory(filePath,fileRelativeNext);
                    } else {
                        zip.addLocalFile(filePath, relativePath);
                    }
                });
            }
            traverseDirectory(fileDir,params.headLine);
            // 将 zip 文件保存到指定路径
            zip.writeZip(filePath);

            function deleteDirectory(dirPath) {
                if (fs.existsSync(dirPath)) {
                    fs.readdirSync(dirPath).forEach(file => {
                        const filePath = path.join(dirPath, file);

                        if (fs.lstatSync(filePath).isDirectory()) {
                            deleteDirectory(filePath); // 递归删除子目录
                        } else {
                            fs.unlinkSync(filePath); // 删除文件
                        }
                    });

                    fs.rmdirSync(dirPath); // 删除空目录
                    console.log('目录删除成功');
                } else {
                    console.log('目录不存在');
                }
            }
            deleteDirectory(fileDir);
        }
    }

    async getIndexIn_worksheets(order,workbook) {
        let index = 0;
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (workbook._worksheets[i]!=null) {
                index++;
                if (index == order) {
                    return i;
                }
            }
        }
    }

    async getIndexOfSameId(idParam,workbook) {
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (workbook._worksheets[i]!=null && workbook._worksheets[i].id == idParam) {
                return i;
            }
        }
    }

    async parseParams(params,project,single,unit,lanMuName,fileDir,args,workBookList,constructIs2022,taxCalculationMethodPath) {
        if (args == null) {
            args = {};
        }
        for (let i = 0; i < params.childrenList.length; i++) {
            let param = params.childrenList[i];
            //如果为总工程层级
            if (param.projectLevel != null && param.projectLevel == "project") {
                args["constructId"] =params.id;
                if (param.selected) {
                    let construct = PricingFileFindUtils.getProjectObjById(params.id);
                    await this.getWorkSheetWithData(construct,project,param.projectLevel,param.headLine,lanMuName,args);
                }else {
                    project.removeWorksheet(param.headLine);
                }
            }
            if (param.projectLevel != null && param.projectLevel == "single") {
                args["singleId"] =params.id;
                if (param.selected) {
                    let singleProject = PricingFileFindUtils.getSingleProject(args["constructId"],args["singleId"]);
                    await this.getWorkSheetWithData(singleProject,single,param.projectLevel,param.headLine,lanMuName,args);
                }else {
                    single.removeWorksheet(param.headLine);
                }
            }
            if (param.projectLevel != null && param.projectLevel == "unit") {
                //分情况  如果预算项目为单位工程 args要重新赋值 因为返回前端的是constructId
                if (params.biddingType == 2) {  //如果预算工程为单位工程
                    args["constructId"] =params.id;
                    args["singleId"] =params.id;
                    args["unitId"] =params.id;
                }else {
                    args["unitId"] =params.id;
                }
                //如果是其他栏目的  走到这里 args['singleId'] 为空  因为前面的递归遍历并没有给其赋值
                if (lanMuName == "其他") {
                    let unitArray = PricingFileFindUtils.getUnitList(args['constructId']);
                    let unitObject = unitArray.filter(item => item.sequenceNbr==params.id);
                    args['singleId'] = unitObject[0].spId;
                }
                if (param.selected) {
                    if (param.sheetType === ExportSheetTypeConstant.SHEET_TYPE_HAND_RCJ) {
                        await this.getWorkSheetWithDataHandRcj(unit, param.projectLevel, param.headLine, lanMuName, args);
                    } else {
                        let unitProject = PricingFileFindUtils.getUnit(args["constructId"],args["singleId"],args["unitId"]);
                        await this.getWorkSheetWithData(unitProject,unit, param.projectLevel, param.headLine, lanMuName, args);
                    }
                } else {
                    unit.removeWorksheet(param.headLine);
                }
            }
            //1、去对应的栏目下拿到对应的sheet
            //2、对每一层级的sheet进行组装   生成文件 并压缩
            //3、返回文件流  删掉原文件
        }
        //针对不同的workbook 生成该一层级的excel文件
        let filename = fileDir+"\\"+params.headLine+".xlsx";

        if (params.childrenList != null && params.childrenList.length>0 && ObjectUtils.isNotEmpty(params.childrenList[0].projectLevel) && params.childrenList[0].projectLevel == "project") {
            await this.resetOrderNo(params,project);
            if (project.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push({sheet:project,fileDir:fileDir,filename:filename});
            }
        }
        if (params.childrenList != null && params.childrenList.length>0 && ObjectUtils.isNotEmpty(params.childrenList[0].projectLevel) && params.childrenList[0].projectLevel == "single") {
            await this.resetOrderNo(params,single);
            if (single.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push({sheet:single,fileDir:fileDir,filename:filename});
            }
        }
        if (params.childrenList != null && params.childrenList.length>0 && ObjectUtils.isNotEmpty(params.childrenList[0].projectLevel) && params.childrenList[0].projectLevel == "unit") {
            //判断单位工程的计税方式,如果没有增值税报表就进行删除
            let argsObject = {};
            argsObject['levelType'] = 3;
            if (params.biddingType == 2) {  //如果预算工程为单位工程
                argsObject['constructId'] = params.id;
            }else {
                //如果是其他栏目的  走到这里 args['singleId'] 为空  因为前面的递归遍历并没有给其赋值
                let unitArray = PricingFileFindUtils.getUnitList(args['constructId']);
                let unitObject = unitArray.filter(item => item.sequenceNbr==params.id);
                args['singleId'] = unitObject[0].spId;
                argsObject['constructId'] = args['constructId'];
                argsObject['singleId'] = args['singleId'];
                argsObject['unitId'] = params.id;
            }
            let taxCalculation = await this.service.baseFeeFileService.taxCalculation(argsObject);

            //简易计税
            if (taxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
                unit.removeWorksheet("材料、机械、设备增值税计算表");
                unit.removeWorksheet("材料、机械、设备增值税计算表（实体）");
                unit.removeWorksheet("材料、机械、设备增值税计算表（措施）");
                unit.removeWorksheet("增值税进项税额计算汇总表");
            }
            //22定额标准下  不管单位excel里有没有  统一移除规费明细表和增值税sheet
            let is22Unit = PricingFileFindUtils.is22UnitById(argsObject['constructId'],argsObject['singleId'],argsObject['unitId']);
            if (is22Unit) {
                unit.removeWorksheet("材料、机械、设备增值税计算表");
                unit.removeWorksheet("材料、机械、设备增值税计算表（实体）");
                unit.removeWorksheet("材料、机械、设备增值税计算表（措施）");
                unit.removeWorksheet("增值税进项税额计算汇总表");
                unit.removeWorksheet("规费明细表");
            }
            //******临时删除这两张表  目前暂不输出*************//
            unit.removeWorksheet("材料、机械、设备增值税计算表（实体）");
            unit.removeWorksheet("材料、机械、设备增值税计算表（措施）");
            //*******************//
            unit.removeWorksheet("人材机手动新建分类模版");
            await this.resetOrderNo(params,unit);
            if (unit.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push({sheet:unit,fileDir:fileDir,filename:filename});
            }
        }
        let filter = params.childrenList.filter(itemParam => itemParam.childrenList!=null);//含有子节点的节点
        if (filter != null) {
            let directory;
            for (let i = 0; i < filter.length; i++) {
                //同时对single  和 unit对象进行初始化
                single = await this.initWorkBook(lanMuName,"single",constructIs2022,taxCalculationMethodPath);
                unit = await this.initWorkBook(lanMuName,"unit",constructIs2022,taxCalculationMethodPath);
                directory = fileDir +"\\"+filter[i].headLine;
                await this.parseParams(filter[i],project,single,unit,lanMuName,directory,args,workBookList,constructIs2022,taxCalculationMethodPath);
            }
        }
    }

    // 创建目录
    async createDirectory(directoryPath) {
        if (!fs.existsSync(directoryPath)) {
            fs.mkdirSync(directoryPath, { recursive: true });
            console.log('目录已创建');
        } else {
            console.log('目录已存在');
        }
    }

    async resetOrderNo(params,workbook) {
        let selectedSheet = params.childrenList.filter(item => item.selected).map(item => item.headLine);
        await this.removeRedundantWorkSheets(selectedSheet,workbook);
        let orderNo = 0;
        for (let i = 0; i < params.childrenList.length; i++) {
            let element = params.childrenList[i];
            if (element.selected) {
                orderNo++;
                let filterWorkSheet = workbook._worksheets.filter(item => item.name==element.headLine);
                if (ObjectUtils.isNotEmpty(filterWorkSheet)) {
                    filterWorkSheet[0].orderNo = orderNo;
                }
            }
        }
    }

    async removeRedundantWorkSheets(selectedSheet,workbook) {
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (ObjectUtils.isNotEmpty(workbook._worksheets[i]) && !selectedSheet.includes(workbook._worksheets[i].name)) {
                await workbook.removeWorksheet(workbook._worksheets[i].name);
                await this.removeRedundantWorkSheets(selectedSheet,workbook);
            }
        }

    }


    async initWorkBook(lanMuName,projectLevel,constructIs2022,taxCalculationMethodPath) {
        let loadDir = "";
        let loadPath = "";
        if (constructIs2022) {
            if (lanMuName == "招标项目报表"||lanMuName == "测评响应招标项目报表") {
                loadDir = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\22\\zhaoBiao";
            }
            if (lanMuName == "投标项目报表"||lanMuName == "测评响应投标项目报表") {
                loadDir = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\22\\touBiao";
            }
            if (lanMuName == "工程量清单报表"||lanMuName == "测评响应工程量清单报表") {
                loadDir = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\22\\unit";
            }
        }else {
            if (lanMuName == "招标项目报表"||lanMuName == "测评响应招标项目报表") {
                loadDir = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\12\\zhaoBiao";
            }
            if (lanMuName == "投标项目报表"||lanMuName == "测评响应投标项目报表") {
                loadDir = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\12\\touBiao";
            }
            if (lanMuName == "工程量清单报表"||lanMuName == "测评响应工程量清单报表") {
                loadDir = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\12\\unit";
            }
        }


        if (projectLevel == "single") {
            loadPath = loadDir+"\\单项工程层级.xlsx";
        } else if (projectLevel == "unit") {
            loadPath = loadDir+"\\单位工程层级.xlsx";
        }else if (projectLevel == "project") {
            loadPath = loadDir+"\\工程项目层级.xlsx";
        }
        if (lanMuName == "其他") {
            if (constructIs2022) {
                loadPath = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\22\\other\\招标通模板.xlsx";
            }else {
                loadPath = this.getProjectRootPath()+"\\excelTemplate\\ys\\"+taxCalculationMethodPath+"\\12\\other\\招标通模板.xlsx";
            }
        }
        //加载workbook
        let workbook = await ExcelUtil.readToWorkBook(loadPath);
        return workbook;
    }

    async getWorkSheetWithData(levelObject,workbook,projectType,sheetName,lanMuName,args) {
        let branchCondition = false;
        let sheetObject = {};
        if (ObjectUtils.isNotEmpty(levelObject.reportViewObject)) {
            sheetObject = await this.traverseGetHeadLineObject(levelObject.reportViewObject[lanMuName],sheetName);
            if (ObjectUtils.isNotEmpty(sheetObject.excelDataTemplate)) { //如果有对应的数据模板
                branchCondition = true;
            }
        }
        //如果该sheet页有对应的数据模板  则加载数据模板
        if (branchCondition) {
            workbook.removeWorksheet(sheetName);//原有模板删除
            let worksheet = workbook.addWorksheet(sheetName);
            let containsTaxCalculationBak = args['containsTaxCalculation'];
            try {
                //对于当前的这张表 快速设计的配置优先于导出配置
                if (ObjectUtils.isNotEmpty(sheetObject.containsTaxCalculation) && sheetObject.containsTaxCalculation) {
                    args['containsTaxCalculation'] = sheetObject.containsTaxCalculation;
                }
                args["numberZero"] = sheetObject.numberZero;
                args["emptyField"] = sheetObject.emptyField;
                if (lanMuName.includes("测评")) {
                    await this.dealWithJsonData(sheetObject.excelDataTemplate);
                }
                await this.service.exportDesignService.accordingTemplateToSheet(sheetObject.excelDataTemplate,args,worksheet,projectType,sheetObject.isLandScape);
            } catch (e) {
                console.log("根据数据模板加载数据异常");
            }
            args['containsTaxCalculation'] = containsTaxCalculationBak;
            args["numberZero"] = null;
            args["emptyField"] = null;
            return ;
        }

        let constructId = args['constructId'];
        let singleId = args['singleId'];
        let unitId = args['unitId'];
        let worksheet = workbook.getWorksheet(sheetName);
        args["workbook"] = workbook;
        try {
            //在这里单独处理导出时 表1-5单位工程费汇总表 及表1-5 单项工程费汇总表 需保持一页的问题
            if (sheetName.includes("单位工程费汇总表")) {
                let dataUnit = await this.getUnitSummaryData(constructId,singleId,unitId);
                if (dataUnit != null) {
                    await ZhaoBiaoUtil.writeDataToSheet8(dataUnit,worksheet);
                }
                await this.dealWithForSinglePageWhenExport(worksheet,workbook);

                let unitProject = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
                let single = PricingFileFindUtils.getSingleProject(constructId, singleId);
                let sheetProjectName = "";
                if (single != null) {
                    sheetProjectName = single.projectName+unitProject.upName;
                }else {
                    sheetProjectName = unitProject.upName;
                }
                ZhaoBiaoUtil.fillSheetProjectName(worksheet,sheetProjectName,"项目名称：");
                ZhaoBiaoUtil.fillSheetProjectName(worksheet,sheetProjectName,"工程名称：");

            }else if (sheetName.includes("表1-5 单项工程费汇总表")) {
                let summaryData = await this.getSingleSummaryData(constructId,singleId);
                await ZhaoBiaoUtil.writeDataToSheet5(summaryData,worksheet);
                await this.dealWithForSinglePageWhenExport(worksheet,workbook);

                let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);
                ZhaoBiaoUtil.fillSheetProjectName(worksheet,singleProject.projectName,"工程名称：");
            } else {
                await this.switchWorkSheet(projectType, lanMuName, worksheet, args);
            }

        } catch (e) {
            console.log("报表填充数据异常");
        }
        return worksheet;
    }


    async getWorkSheetWithDataHandRcj(workbook, projectType, sheetName, lanMuName, args) {
        let constructId = args['constructId'];
        let singleId = args['singleId'];
        let unitId = args['unitId'];
        let worksheet = workbook.getWorksheet("人材机手动新建分类模版");
        let addWorksheet = workbook.addWorksheet(sheetName);
        worksheet.eachRow({includeEmpty: true}, (row, rowNumber) => {
            const newRow = addWorksheet.addRow(row.values);
            newRow.height = row.height;
            row.eachCell({includeEmpty: true}, (cell, colNumber) => {
                const newCell = newRow.getCell(colNumber);
                Object.assign(newCell, {
                    style: cell.style,
                    value: cell.value
                    // formula: cell.formula,
                    // result: cell.result
                });
                newCell._column = cell._column;
            });
        });
        addWorksheet._columns = worksheet._columns;
        addWorksheet.name = sheetName;

        addWorksheet.unMergeCells([1,1,1,9]);
        addWorksheet.mergeCells([1,1,1,9]);
        addWorksheet.unMergeCells([2,1,2,6]);
        addWorksheet.mergeCells([2,1,2,6]);
        addWorksheet.unMergeCells([2,7,2,9]);
        addWorksheet.mergeCells([2,7,2,9]);


        args["workbook"] = workbook;
        try {
            let rcjCreateClassList = await this.service.rcjProcess.getUseRcjClassificationTableListToExport(constructId, singleId, unitId);
            if (ObjectUtils.isNotEmpty(rcjCreateClassList)) {
                let find = rcjCreateClassList.find(o => o.name === sheetName);
                if (ObjectUtils.isNotEmpty(find)) {
                    await ZhaoBiaoUtil.writeDataToUnitSheetHandRcj(find.rcjList, addWorksheet);
                    let headArgsFei2 = {};
                    headArgsFei2['headStartNum'] = 1;
                    headArgsFei2['headEndNum'] = 3;
                    headArgsFei2['titlePage'] = false;
                    await ExcelUtil.dealWithPage(addWorksheet, workbook, headArgsFei2);
                }
            }

            let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            let single = PricingFileFindUtils.getSingleProject(constructId, singleId);
            let sheetProjectName = "";
            if (single != null) {
                sheetProjectName = single.projectName + unitProject.upName;
            } else {
                sheetProjectName = unitProject.upName;
            }
            ZhaoBiaoUtil.fillSheetProjectName(addWorksheet, sheetProjectName, "工程名称：");
            let cellTitle = ExcelUtil.findValueCell(addWorksheet, "主要材料、设备明细表");
            if (ObjectUtils.isNotEmpty(cellTitle)) {
                let row = addWorksheet.getRow(cellTitle.cell._row._number);
                row._cells[1].value = sheetName + "明细表";
            }
            // ZhaoBiaoUtil.fillSheetProjectName(addWorksheet, sheetName + "明细表", "主要材料、设备明细表");
        } catch (e) {
            console.log("报表填充数据异常");
        }
        return addWorksheet;
    }

    async dealWithForSinglePageWhenExport(workSheet,workbook,headArgs) {
        let  headStartNum = 0;
        let headEndNum = 0;
        if (headArgs != null) {
            headStartNum = headArgs['headStartNum'];
            headEndNum = headArgs['headEndNum'];
            if (headArgs['titlePage']==null) {
                headArgs['titlePage'] = false;//默认为 数据页
            }
        }else {
            headArgs = {};
            headStartNum = 1;
            headEndNum = 4;
            headArgs['headStartNum'] = headStartNum;
            headArgs['headEndNum'] = headEndNum;
            headArgs['titlePage'] = false;//默认为 数据页
        }

        //1、复制表头
        //2、进行 行高自适应的处理 确定行高后  进行分页
        //10号字体
        // 在该行下方插入一个分页符
        //A4 行高 721.5   宽度
        // let marginLeft = ;//左边距
        //得到每一个cell的宽度比例 并计入map
        await ExcelUtil.getRatioWidthSheet(workSheet);


        let mergeMap = new Map(Object.entries(workSheet._merges));
        let fontSize = 13;
        //行高自适应  如果所需行高大于1行  则置为两行
        for(let i = headEndNum+1; i<=workSheet._rows.length; i++){
            let minHeight = 0;
            let fitRight = true;//这里预设为false 就会保留初始模板的空白行高度 为true针对空白行统统高度为0

            for (let j = 0; j < workSheet.getRow(i)._cells.length; j++) {
                let cell = workSheet.getRow(i)._cells[j];
                let celltextValue = cell.model.value;
                if (!celltextValue) {
                    continue;
                }
                fitRight = true;
                if (typeof celltextValue === 'number') {
                    celltextValue = String(celltextValue);
                }
                let contents;
                try {
                    contents = celltextValue.split("\n"); //内容中可能包含换行符，这里以"\n"字符串为换行标识
                } catch (e) {
                    console.log("");
                }
                let mergeName = ExcelUtil.getMergeName(workSheet._merges, cell);
                let mergeLength = 0;//得到该cell的宽度大小
                if (mergeName != null) {
                    let value = mergeMap.get(mergeName).model;
                    for (let m = value.left; m <= value.right; m++) {
                        mergeLength +=workSheet.getRow(i)._cells[m-1]._column.width;
                    }
                } else {
                    mergeLength =cell._column.width;
                }
                // let rowWordNum = Math.trunc(mergeLengthRatio * ExcelEnum.A4Width / ((fontSize / 72) * 10)) //每一列能够存放的字数
                //这若为0  会造成递归死循环
                let rowWordNum = Math.trunc(mergeLength / ((fontSize / 72) * 10)) //每一列能够存放的字数
                let rowSpace = 2;//行间距
                let rowNumTotal = 0;
                for (let j = 0; j < contents.length; j++) {
                    let rowText = contents[j];
                    if (!rowText && rowText.length == 0) {
                        continue;
                    }
                    // "垂直运输费 ±0.00以下 四层以内"  类似这种问题 在excel中会展示三行 实际计算是两行  18/9 此时满除就多加一行
                    let rowNum = Math.ceil(rowText.length / rowWordNum);
                    //优化处理  如果单行字数超过五  考虑到单元格的两侧边界距离  实际每行能存放的字数进行减二
                    if (rowNum >= 2 && rowNum * rowWordNum == rowText.length) {
                        rowNum++;
                    }
                    rowNumTotal += rowNum;
                }
                if (rowNumTotal > 2) {
                    rowNumTotal = 2;  //最大置为两行
                }
                let newMinHeight = ((fontSize) + rowSpace) * rowNumTotal+8;   //计算出该Cell列 的最小适应高度  加4是上下的总共边距

                if (minHeight < newMinHeight) {
                    minHeight = newMinHeight; //得到该行的最大行高
                }
            }
            if (fitRight) {
                workSheet.getRow(i).height = minHeight;
            }
        }
        //分页处理
        // await workbook.xlsx.writeFile("D:\\csClient\\测试\\单位工程层级.xlsx");
        if (!headArgs['titlePage']) {  //如果不是扉页
            let totalPage = await ExcelUtil.pageSplit(workSheet,1,headArgs,0);
            /*****************************************/
            //对页码显示进行处理
            let cellList = ExcelUtil.findContainValueCell(workSheet,"第 1 页  共 1 页");
            if (cellList.length == 0) {
                cellList = ExcelUtil.findContainValueCell(workSheet,"第 1 页 共 1 页");//横版是如此格式
            }
            const grouped = cellList.reduce((result, obj) => {
                const key = obj.cell._row._number;
                if (!result[key]) {
                    result[key] = [];
                }
                result[key].push(obj.cell);
                return result;
            }, {});
            let mergeMap = new Map(Object.entries(grouped));
            let count = 0;
            for (let [key, value] of mergeMap) {
                count++;
                let str = "第 "+(count)+" 页 共 "+totalPage+" 页";
                for (let i = 0; i < value.length; i++) {
                    let elementCell = value[i];
                    elementCell.value = str;
                }
            }
            /*****************对空白行的处理********************************/
            //要求空白行只能是在末尾页  而不是在页中  否则逻辑出错
            // let blankRowList = await this.getBlankRow(workSheet);
            await ExcelUtil.dealWithBlankRow(workSheet,headArgs);
        }
    }

    async switchWorkSheet(projectType,lanMuName,worksheet,args) {
        let {constructId,unitId,singleId,workbook} = args;
        let constructIs2022= PricingFileFindUtils.getConstructDeStandard(args.constructId)==ConstantUtil.DE_STANDARD_22;
        let unit = {};
        if (constructId != null && singleId != null && unitId != null) {
            unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        }
        if (lanMuName == "招标项目报表"||lanMuName == "测评响应招标项目报表") {
            if (projectType == "project") {
                switch (worksheet.name) {
                    //工程项目层级
                    case "封面2 招标控制价（标底）":
                        let param1 ={};
                        //是招标还是投标 1招标 2投标
                        param1.ifZbOrTb = 1;

                        //工程项目ID
                        param1.constructId = constructId;
                        let constructProjectJBXX = await this.getconstructProjectJBXX(param1);
                        await ZhaoBiaoUtil.writeDataToCover1(constructProjectJBXX,worksheet);
                        let headArgs = {};
                        headArgs['headStartNum'] = 1;
                        headArgs['headEndNum'] = 9;
                        headArgs['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs);
                        break;
                    case "封面2 招标控制价(不含造价咨询人)":
                        let param2 ={};
                        //是招标还是投标 1招标 2投标
                        param2.ifZbOrTb = 1;

                        //工程项目ID
                        param2.constructId = constructId;
                        let constructProjectJBXX2 = await this.getconstructProjectJBXX(param2);
                        await ZhaoBiaoUtil.writeDataToCover1(constructProjectJBXX2,worksheet);
                        let headArgsContain = {};
                        headArgsContain['headStartNum'] = 1;
                        headArgsContain['headEndNum'] = 9;
                        headArgsContain['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsContain);
                        break;
                    case "扉页2 招标控制价（标底）":
                        let param3 ={};
                        //是招标还是投标 1招标 2投标
                        param3.ifZbOrTb = 1;

                        //工程项目ID
                        param3.constructId = constructId;
                        let constructProjectZBKZJBD =await this.getconstructProjectZBKZJBD(param3,1);
                        await ZhaoBiaoUtil.writeDataToCover2(constructProjectZBKZJBD,worksheet);
                        let headArgsBottom = {};
                        headArgsBottom['headStartNum'] = 1;
                        headArgsBottom['headEndNum'] = 12;
                        headArgsBottom['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsBottom);
                        break;
                    case "扉页2 招标控制价（不含甲供设备及其税金）":
                        // 当 expression 的值与 value3 匹配时执行的代码

                        let param4 ={};
                        //是招标还是投标 1招标 2投标
                        param4.ifZbOrTb = 1;

                        //工程项目ID
                        param4.constructId = constructId;
                        let constructProjectZBKZJBD1 =await this.getconstructProjectZBKZJBD(param4,2);
                        await ZhaoBiaoUtil.writeDataToCover2(constructProjectZBKZJBD1,worksheet);
                        let headArgsBottom2 = {};
                        headArgsBottom2['headStartNum'] = 1;
                        headArgsBottom2['headEndNum'] = 12;
                        headArgsBottom2['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsBottom2);
                        break;
                    case "表1-1 工程量清单编制说明":
                        // 当 expression 的值与 value3 匹配时执行的代码
                        let param5 ={};
                        //是招标还是投标 1招标 2投标
                        param5.ifZbOrTb = 1;

                        //工程项目ID
                        param5.constructId = constructId;
                        let organization =await this.getOrganization(param5);
                        if (organization[1].remark != null) {
                            let remark = await ExcelUtil.removeTags(organization[1].remark);
                            await ZhaoBiaoUtil.writeDataToCover3(remark,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-2 工程量清单报价说明":

                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        //TODO 宏成说没有 该章说明的数据位置 直接输出空报表
                        break;
                    case "表1-3 工程项目总价表":
                        let param7 ={};
                        //是招标还是投标 1招标 2投标
                        param7.ifZbOrTb = 2;
                        //工程项目ID
                        param7.constructId = constructId;
                        let gcxmzjb =await this.getGcxmzjb(param7);
                        await ZhaoBiaoUtil.writeDataToSheet1(gcxmzjb,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-3 工程项目总价表（沧州）":
                        let param77 ={};
                        //是招标还是投标 1招标 2投标
                        param77.ifZbOrTb = 2;
                        //工程项目ID
                        param77.constructId = constructId;
                        let gcxmzjb77 =await this.getGcxmzjb(param77);

                        let gdawf77 = {};
                        gdawf77.sortNo = "";
                        gdawf77.name = "安全生产、文明施工费（含税）";
                        gdawf77.level = "parent";
                        gdawf77.gf = null;
                        let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
                        gdawf77.awf = ObjectUtil.isEmpty(projectObj.securityFee) ? 0 : projectObj.securityFee;
                        gdawf77.total = gdawf77.awf;
                        gcxmzjb77.push(gdawf77);

                        await ZhaoBiaoUtil.writeDataToSheet1(gcxmzjb77,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-4 单项工程费汇总表":
                        let param14 ={};
                        param14['levelType'] = 1;
                        param14['constructId'] = constructId;
                        let analysisData14 = await this.service.unitProjectService.getCostAnalysisData(param14);
                        let dataList = {"analysisZJ":[],"amount":0,"gfeeTotal":0,"awenFeeTotal":0,
                            "fbfxhj":0,"csxhj":0,"qtxmhj":0,"sj":0,"average":0,"unitcost":0,"sbfsj":0}
                        await this.getSingleSummaryDataForConstruct(analysisData14,dataList);
                        await ZhaoBiaoUtil.writeDataToSheet2(dataList,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "单项工程造价分析表":
                        let param ={};
                        param['levelType'] = 1;
                        param['constructId'] = constructId;
                        let analysisData = await this.service.unitProjectService.getCostAnalysisData(param);
                        let dataAnalysis = {"analysisZJ":[],"amount":0,"gfeeTotal":0,"awenFeeTotal":0,
                            "fbfxhj":0,"csxhj":0,"qtxmhj":0,"sj":0,"average":0,"unitcost":0,"sbfsj":0}
                        await this.getSingleSummaryDataForConstruct(analysisData,dataAnalysis);
                        await ZhaoBiaoUtil.writeDataToSheet3(dataAnalysis,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "安全生产、文明施工费汇总表":
                        let paramAwen ={};
                        paramAwen['levelType'] = 1;
                        paramAwen['constructId'] = constructId;
                        let analysisConstruct = await this.service.unitProjectService.getCostAnalysisData(paramAwen);
                        let data = new Array();
                        await this.getAwenFeeDetailData(analysisConstruct,data,null,constructId);
                        await ZhaoBiaoUtil.writeDataToSheet4(data,worksheet,analysisConstruct.costAnalysisConstructVOList);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                }
            }
            if (projectType == "single") {
                switch (worksheet.name) {
                    //单项工程层级
                    case "表1-5 单项工程费汇总表":
                        let summaryData = await this.getSingleSummaryData(constructId,singleId);
                        await ZhaoBiaoUtil.writeDataToSheet5(summaryData,worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                }
            }
            if (projectType == "unit") {
                switch (worksheet.name) {
                    //单位工程层级
                    case "封面2 招标控制价（标底）":

                        let zbkzj = await this.getUnitProjectzbkzj(constructId,singleId,unitId);
                        if (zbkzj != null) {
                            await ZhaoBiaoUtil.writeUnitDataToCover1(zbkzj,worksheet);
                        }
                        let headArgsBot = {};
                        headArgsBot['headStartNum'] = 1;
                        headArgsBot['headEndNum'] = 9;
                        headArgsBot['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsBot);
                        break;
                    case "封面2 招标控制价(不含造价咨询人)":

                        let zbkzj1 = await this.getUnitProjectzbkzj(constructId,singleId,unitId);
                        if (zbkzj1 != null) {
                            await ZhaoBiaoUtil.writeUnitDataToCover1(zbkzj1,worksheet);
                        }
                        let headArgsContain = {};
                        headArgsContain['headStartNum'] = 1;
                        headArgsContain['headEndNum'] = 9;
                        headArgsContain['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsContain);
                        break;
                    case "扉页2 招标控制价（标底）":

                        let zbkzjBd = await this.getUnitProjectZBKZJBD(constructId,singleId,unitId,1);
                        if (zbkzjBd != null) {
                            await ZhaoBiaoUtil.writeUnitDataToCover2(zbkzjBd,worksheet);
                        }
                        let headArgsBottom = {};
                        headArgsBottom['headStartNum'] = 1;
                        headArgsBottom['headEndNum'] = 12;
                        headArgsBottom['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsBottom);
                        break;
                    case "扉页2 招标控制价（不含甲供设备及其税金）":

                        let zbkzjBdBh = await this.getUnitProjectZBKZJBD(constructId,singleId,unitId,2);
                        if (zbkzjBdBh != null) {
                            await ZhaoBiaoUtil.writeUnitDataToCover2(zbkzjBdBh,worksheet);
                        }
                        let headArgsBottom2 = {};
                        headArgsBottom2['headStartNum'] = 1;
                        headArgsBottom2['headEndNum'] = 12;
                        headArgsBottom2['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsBottom2);
                        break;
                    case "表1-1 工程量清单编制说明":

                        let organizationInstructions = PricingFileFindUtils.getOrganizationInstructions(3,constructId,singleId,unitId);
                        if (organizationInstructions.context != null) {
                            let remark = await ExcelUtil.removeTags(organizationInstructions.context);
                            await ZhaoBiaoUtil.writeDataToCover3(remark,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-2 工程量清单报价说明":
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-3 工程项目总价表":
                        let gcxmzjb =await this.getUnitGcxmzjb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToSheet1(gcxmzjb.list,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-5 单位工程费汇总表":
                        let dataUnit = await this.getUnitSummaryData(constructId,singleId,unitId);
                        if (dataUnit != null) {
                            await ZhaoBiaoUtil.writeDataToSheet8(dataUnit,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");
                        break;
                    case "表1-5 单位工程费汇总表--(唐山地区)":
                        let dataUnitTangShan = await this.getUnitSummaryData(constructId,singleId,unitId);
                        if (dataUnitTangShan != null) {
                            await ZhaoBiaoUtil.writeDataToSheet8(dataUnitTangShan,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        // await worksheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");
                        break;
                    case "表1-6 分部分项工程量清单与计价表":
                        let fbFxAll = unit.itemBillProjects;
                        //过滤临时删除项
                        let fbFx = await this.filterFbfxTempDelete(fbFxAll);
                        await ZhaoBiaoUtil.writeDataToUnitSheet9(fbFx.filter(item => item.kind === BranchProjectLevelConstant.qd|| item.kind === BranchProjectLevelConstant.fb||item.kind === BranchProjectLevelConstant.zfb),worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-7 单价措施项目工程量清单与计价表":
                        let qdByDjcsAll = await PricingFileFindUtils.getQdFbList(constructId,singleId,unitId);
                        //过滤临时删除项
                        let qdByDjcs = await this.filterFbfxTempDelete(qdByDjcsAll);
                        await ZhaoBiaoUtil.writeDataToUnitSheet10(qdByDjcs,worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-8 总价措施项目清单与计价表":
                        let data = [];
                        let csxmTotalAll = unit.measureProjectTables;
                        //过滤临时删除项
                        let csxmTotal = await this.filterFbfxTempDelete(csxmTotalAll);
                        let awen = csxmTotal.filter(csxm => csxm.kind === BranchProjectLevelConstant.qd && csxm.name == "安全生产、文明施工费");
                        let qdByZjcs = PricingFileFindUtils.getQdByZjcs(unit.constructId,unit.spId,unitId);
                        data.push(awen,qdByZjcs);
                        //费用定额的话需要重新计算计算基数
                        await ZhaoBiaoUtil.writeDataToUnitSheet11(data,worksheet);
                        let headArgs = {};
                        headArgs['headStartNum'] = 1;
                        headArgs['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs);
                        // let sheetStylePromise1 =await  ExcelUtil.findCellStyleList(worksheet);
                        break;
                    case "表1-9 其他项目清单与计价表":
                        let otherItems = unit.otherProjects;
                        await ZhaoBiaoUtil.writeDataToUnitSheet12(otherItems,worksheet);
                        let headArgs9 = {};
                        headArgs9['headStartNum'] = 1;
                        headArgs9['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs9);
                        break;
                    case "表1-10 暂列金额明细表":
                        let zlJE = unit.otherProjectProvisionals;
                        if (zlJE != null) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet13(zlJE,worksheet);
                        }
                        let headArgs10 = {};
                        headArgs10['headStartNum'] = 1;
                        headArgs10['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs10);
                        break;
                    case "表1-11 暂估价表":
                        let zanGu = unit.otherProjectZygcZgjs;//专业工程暂估价
                        let array = new Array();
                        if (!ObjectUtils.isEmpty(zanGu)) {
                            zanGu.forEach(i => {
                                let otherProjectZygcZgj = new OtherProjectZygcZgj();
                                ConvertUtil.setDstBySrc(i,otherProjectZygcZgj);
                                array.push(otherProjectZygcZgj);
                            })
                        }

                        if (array != null && array.length>0) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet14(array,worksheet);
                        }
                        let headArgs11 = {};
                        headArgs11['headStartNum'] = 1;
                        headArgs11['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs11);
                        break;
                    case "表1-12 总承包服务费计价表":
                        let serviceCosts = unit.otherProjectServiceCosts;
                        if (serviceCosts!=null){
                            await ZhaoBiaoUtil.writeDataToUnitSheet15(serviceCosts,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-13 计日工表":
                        let dayWorks = unit.otherProjectDayWorks;
                        await ZhaoBiaoUtil.writeDataToUnitSheet16(dayWorks,worksheet);
                        let headArgs13 = {};
                        headArgs13['headStartNum'] = 1;
                        headArgs13['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs13);
                        break;
                    case "表1-14 招标人供应材料、设备明细表":

                        let zbrgyclsbmxb = await this.getZbrgyclsbmxb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToUnitSheet17(zbrgyclsbmxb.rcjlist,worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-15 主要材料、设备明细表":
                         let zyclsb =  await  this.getZyclsb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToUnitSheet18(zyclsb.rcjlist,worksheet);
                        let headArgs15 = {};
                        headArgs15['headStartNum'] = 1;
                        headArgs15['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs15);
                        break;
                    case "表1-15 主要材料明细表":

                        let zyclmx = await this.getZyclmxb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToUnitSheet18(zyclmx.rcjlist,worksheet);
                        let headArgs151 = {};
                        headArgs151['headStartNum'] = 1;
                        headArgs151['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs151);
                        break;
                    case "表1-15 设备明细表":
                        let sbmx = await this.getSbmxb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToUnitSheet18(sbmx.rcjlist,worksheet);
                        let headArgs152 = {};
                        headArgs152['headStartNum'] = 1;
                        headArgs152['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs152);
                        break;
                    case "表1-16 分部分项工程量清单综合单价分析表":
                        let fbFx1All = unit.itemBillProjects;
                        //过滤临时删除项
                        let fbFx1 = await this.filterFbfxTempDelete(fbFx1All);
                        if (fbFx1 != null) {
                            let filter = fbFx1.filter(item => item.kind === BranchProjectLevelConstant.qd ||
                                item.kind === BranchProjectLevelConstant.de
                            );
                            filter.forEach(function(element) {
                                if (element.kind === BranchProjectLevelConstant.de) {
                                    let constructProjectRcjs = unit.constructProjectRcjs;
                                    if (ObjectUtils.isNotEmpty(constructProjectRcjs)) {
                                        let deRcjs = constructProjectRcjs.filter(item => item.deId == element.sequenceNbr && item.type=="人工费");
                                        if (deRcjs.length > 0 && ObjectUtils.isNotEmpty(deRcjs[0].marketPrice)) {
                                            element.rfeePrice = deRcjs[0].marketPrice.toFixed(2);
                                        }
                                    }
                                    if (ObjectUtils.isEmpty(element.rfeePrice)) {  //若没有人工单价  展示为0
                                        element.rfeePrice = 0;
                                    }
                                }
                            });
                            await ZhaoBiaoUtil.writeDataToUnitSheet116(filter,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-17 单价措施项目工程量清单综合单价分析表":
                        let csxmcsAll = unit.measureProjectTables;
                        //过滤临时删除项
                        let csxmcs = await this.filterFbfxTempDelete(csxmcsAll);
                        if (csxmcs != null) {
                            let filter = csxmcs.filter(item => (item.constructionMeasureType===ConstructionMeasureTypeConstant.DJCS));
                            if (filter.length > 0) {
                                let djcsQdAndDe = [];
                                for (let i = 0; i < filter.length; i++) {
                                    let djcsData = this.getCSXMQdAndDe(csxmcs,filter[i],"").datas.filter(item => (!ObjectUtils.isEmpty(item.fxCode) && (item.kind === BranchProjectLevelConstant.qd||item.kind === BranchProjectLevelConstant.de) && item.constructionMeasureType!=ConstructionMeasureTypeConstant.DJCS));
                                    djcsQdAndDe.push(...djcsData);
                                }
                                djcsQdAndDe.forEach(function(element) {
                                    if (element.kind === BranchProjectLevelConstant.de) {
                                        let constructProjectRcjs = unit.constructProjectRcjs;
                                        if (ObjectUtils.isNotEmpty(constructProjectRcjs)) {
                                            let deRcjs = constructProjectRcjs.filter(item => item.deId == element.sequenceNbr && item.type=="人工费");
                                            if (deRcjs.length > 0 && ObjectUtils.isNotEmpty(deRcjs[0].marketPrice)) {
                                                element.rfeePrice = deRcjs[0].marketPrice.toFixed(2);
                                            }
                                        }
                                        if (ObjectUtils.isEmpty(element.rfeePrice)) {  //若没有人工单价  展示为0
                                            element.rfeePrice = 0;
                                        }
                                    }
                                });
                                await ZhaoBiaoUtil.writeDataToUnitSheet117(djcsQdAndDe,worksheet);
                            }
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-18 总价措施项目费分析表":
                        let zjCsAll = unit.measureProjectTables;
                        //过滤临时删除项
                        let zjCs = await this.filterFbfxTempDelete(zjCsAll);
                        if (zjCs != null) {
                            let filterOtherZJ = zjCs.filter(item => (item.constructionMeasureType===ConstructionMeasureTypeConstant.ZJCS));
                            let awenZj = zjCs.filter(item => (item.constructionMeasureType===ConstructionMeasureTypeConstant.AWF));
                            //拿到其他总价措施数据  费用定额 isCostDe !=0
                            let zjcsQdAndDe = [];
                            for (let i = 0; i < filterOtherZJ.length; i++) {
                                let zjcsData = this.getCSXMQdAndDe(zjCs,filterOtherZJ[i],"").datas.filter(item => (item.constructionMeasureType!=ConstructionMeasureTypeConstant.ZJCS));
                                zjcsQdAndDe.push(...zjcsData);
                            }
                            //拿到安文费清单数据
                            let awenFee = this.getCSXMQdAndDe(zjCs,awenZj[0],"").datas.filter(item => (item.constructionMeasureType!=ConstructionMeasureTypeConstant.ZJCS));
                            let dataZJ = awenFee.filter(item => (item.constructionMeasureType!=ConstructionMeasureTypeConstant.AWF && item.kind==BranchProjectLevelConstant.qd));
                            if (ObjectUtils.isNotEmpty(dataZJ)) {
                                const awfQdItem = dataZJ[0];
                                zjcsQdAndDe = zjcsQdAndDe.filter(de => de.parentId != awfQdItem.sequenceNbr);
                            }
                            for (const obj of zjcsQdAndDe) {
                                dataZJ.push(obj);
                            }
                            let constructProjectRcjs = unit.constructProjectRcjs;
                            await ZhaoBiaoUtil.writeDataToUnitSheet118(dataZJ,worksheet,constructProjectRcjs);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "材料、机械、设备增值税计算表":
                        // this.service.baseFeeFileService.taxCalculation(arg);
                        // async taxCalculation(arg) {
                        // /**
                        //  * 计税方式  '1' 一般计税；'2'：简易计税
                        //  */
                        // static TAX_MODE_1 = 1;
                        // static TAX_MODE_2 = 2;
                        let clJxSbZzsjsb = await this.getClJxSbZzsjsb(constructId,singleId,unitId);


                        if (clJxSbZzsjsb != null) {
                            let total = await this.calculateTotal(clJxSbZzsjsb);
                            clJxSbZzsjsb['totalObject'] = total;
                            await ClJxSbZzsUtil.writeDataToClJxSbZzsjsb(clJxSbZzsjsb,worksheet);
                        }
                        let headArgsTax = {};
                        headArgsTax['headStartNum'] = 1;
                        headArgsTax['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTax);
                        break;
                    case "材料、机械、设备增值税计算表（实体）":
                        let clJxSbZzsjsbSt = await this.getClJxSbZzsjsbSt(constructId,singleId,unitId);
                        if (clJxSbZzsjsbSt != null) {
                            let total = await this.calculateTotal(clJxSbZzsjsbSt);
                            clJxSbZzsjsbSt['totalObject'] = total;
                            await ClJxSbZzsUtil.writeDataToClJxSbZzsjsb(clJxSbZzsjsbSt,worksheet);
                        }
                        let headArgsTax1 = {};
                        headArgsTax1['headStartNum'] = 1;
                        headArgsTax1['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTax1);
                        break;
                    case "材料、机械、设备增值税计算表（措施）":
                        let clJxSbZzsjsbCs = await this.getClJxSbZzsjsbCs(constructId,singleId,unitId);
                        if (clJxSbZzsjsbCs != null) {
                            let total = await this.calculateTotal(clJxSbZzsjsbCs);
                            clJxSbZzsjsbCs['totalObject'] = total;
                            await ClJxSbZzsUtil.writeDataToClJxSbZzsjsb(clJxSbZzsjsbCs,worksheet);
                        }
                        let headArgsTax2 = {};
                        headArgsTax2['headStartNum'] = 1;
                        headArgsTax2['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTax2);
                        break;
                    case "增值税进项税额计算汇总表":
                        let zzsJxs = await this.getZzsJxs(constructId,singleId,unitId);
                        if (zzsJxs != null) {
                            await ClJxSbZzsUtil.writeDataToZzsJxs(zzsJxs,worksheet);
                        }
                        let headArgsSum = {};
                        headArgsSum['headStartNum'] = 1;
                        headArgsSum['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsSum);
                        break;
                    default:
                }

            }
        }
        if (lanMuName == "投标项目报表"||lanMuName == "测评响应投标项目报表") {
            if (projectType == "project") {
                switch (worksheet.name) {
                    //工程项目层级
                    case "封面3 投标总价":
                        let param1 ={};
                        //是招标还是投标 1招标 2投标
                        param1.ifZbOrTb = 2;

                        //工程项目ID
                        param1.constructId = constructId;
                        let constructProjectJBXX = await this.getconstructProjectJBXX(param1);
                        await TouBiaoUtil.writeDataToCover1(constructProjectJBXX,worksheet);
                        let headArgsTb = {};
                        headArgsTb['headStartNum'] = 1;
                        headArgsTb['headEndNum'] = 6;
                        headArgsTb['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTb);
                        break;
                    case "扉页3 投标总价":
                        let param3 ={};
                        //是招标还是投标 1招标 2投标
                        param3.ifZbOrTb = 2;

                        //工程项目ID
                        param3.constructId = constructId;
                        let constructProjectZBKZJBD =await this.getconstructProjectZBKZJBD(param3,1);
                        await TouBiaoUtil.writeDataToCover2(constructProjectZBKZJBD,worksheet);
                        let headArgsFei = {};
                        headArgsFei['headStartNum'] = 1;
                        headArgsFei['headEndNum'] = 13;
                        headArgsFei['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsFei);
                        break;
                    case "扉页3 投标总价(不含甲供设备及其税金)":
                        let param4 ={};
                        //是招标还是投标 1招标 2投标
                        param4.ifZbOrTb = 2;

                        //工程项目ID
                        param4.constructId = constructId;
                        let constructProject =await this.getconstructProjectZBKZJBD(param4,2);
                        await TouBiaoUtil.writeDataToCover2(constructProject,worksheet);
                        let headArgsFei2 = {};
                        headArgsFei2['headStartNum'] = 1;
                        headArgsFei2['headEndNum'] = 13;
                        headArgsFei2['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsFei2);
                        break;
                    case "表1-1 工程量清单编制说明":
                        let param5 ={};
                        //是招标还是投标 1招标 2投标
                        param5.ifZbOrTb = 2;

                        //工程项目ID
                        param5.constructId = constructId;
                        let organization =await this.getOrganization(param5);
                        if (organization[1].remark != null) {
                            let remark = await ExcelUtil.removeTags(organization[1].remark);
                            await ZhaoBiaoUtil.writeDataToCover3(remark,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-2 工程量清单报价说明":

                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-3 工程项目总价表":
                        let param7 ={};
                        //是招标还是投标 1招标 2投标
                        param7.ifZbOrTb = 2;

                        //工程项目ID
                        param7.constructId = constructId;
                        let gcxmzjb =await this.getGcxmzjb(param7);
                        await ZhaoBiaoUtil.writeDataToSheet1(gcxmzjb,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-3 工程项目总价表（沧州）":
                        let param77 ={};
                        //是招标还是投标 1招标 2投标
                        param77.ifZbOrTb = 2;
                        //工程项目ID
                        param77.constructId = constructId;
                        let gcxmzjb77 =await this.getGcxmzjb(param77);

                        let gdawf77 = {};
                        gdawf77.sortNo = "";
                        gdawf77.name = "安全生产、文明施工费（含税）";
                        gdawf77.level = "parent";
                        gdawf77.gf = null;
                        let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
                        gdawf77.awf = ObjectUtil.isEmpty(projectObj.securityFee) ? 0 : projectObj.securityFee;
                        gdawf77.total = gdawf77.awf;
                        gcxmzjb77.push(gdawf77);

                        await ZhaoBiaoUtil.writeDataToSheet1(gcxmzjb77,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-4 单项工程费汇总表":
                        let param14 ={};
                        param14['levelType'] = 1;
                        param14['constructId'] = constructId;
                        let analysisData14 = await this.service.unitProjectService.getCostAnalysisData(param14);
                        let dataList = {"analysisZJ":[],"amount":0,"gfeeTotal":0,"awenFeeTotal":0,
                            "fbfxhj":0,"csxhj":0,"qtxmhj":0,"sj":0,"average":0,"unitcost":0,"sbfsj":0}
                        await this.getSingleSummaryDataForConstruct(analysisData14,dataList);
                        await ZhaoBiaoUtil.writeDataToSheet2(dataList,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "单项工程造价分析表":
                        let param ={};
                        param['levelType'] = 1;
                        param['constructId'] = constructId;
                        let analysisData = await this.service.unitProjectService.getCostAnalysisData(param);
                        let dataAnalysis = {"analysisZJ":[],"amount":0,"gfeeTotal":0,"awenFeeTotal":0,
                            "fbfxhj":0,"csxhj":0,"qtxmhj":0,"sj":0,"average":0,"unitcost":0,"sbfsj":0}
                        await this.getSingleSummaryDataForConstruct(analysisData,dataAnalysis);
                        await ZhaoBiaoUtil.writeDataToSheet3(dataAnalysis,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                }
            }
            if (projectType == "single") {
                switch (worksheet.name) {
                    //单项工程层级
                    case "表1-5 单项工程费汇总表":
                        let summaryData = await this.getSingleSummaryData(constructId,singleId);
                        await ZhaoBiaoUtil.writeDataToSheet5(summaryData,worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                }
            }
            if (projectType == "unit") {
                switch (worksheet.name) {
                    //单位工程层级
                    case "填表须知":
                        let headArgsXz = {};
                        headArgsXz['headStartNum'] = 1;
                        headArgsXz['headEndNum'] = 2;
                        headArgsXz['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsXz);
                        break;
                    case "封面3 投标总价":

                        let tbzj = await this.getTbTbzj(constructId,singleId,unitId);
                        if (tbzj != null) {
                            await TouBiaoUtil.writeUnitDataToCover1(tbzj,worksheet);
                        }
                        let headArgsTb = {};
                        headArgsTb['headStartNum'] = 1;
                        headArgsTb['headEndNum'] = 6;
                        headArgsTb['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTb);
                        break;
                    case "扉页3 投标总价":

                        let tbUnitTbzj = await this.getTbUnitTbzj(constructId,singleId,unitId,1);
                        if (tbUnitTbzj != null) {
                            await TouBiaoUtil.writeUnitDataToCover2(tbUnitTbzj,worksheet);
                        }
                        let headArgsFei = {};
                        headArgsFei['headStartNum'] = 1;
                        headArgsFei['headEndNum'] = 13;
                        headArgsFei['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsFei);
                        break;
                    case "扉页3 投标总价(不含甲供设备及其税金)":

                        let tbUnitTbzjBh = await this.getTbUnitTbzj(constructId,singleId,unitId,2);
                        if (tbUnitTbzjBh != null) {
                            await TouBiaoUtil.writeUnitDataToCover2(tbUnitTbzjBh,worksheet);
                        }
                        let headArgsFei2 = {};
                        headArgsFei2['headStartNum'] = 1;
                        headArgsFei2['headEndNum'] = 13;
                        headArgsFei2['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsFei2);
                        break;
                    case "表1-1 工程量清单编制说明":
                        let organizationInstructions = PricingFileFindUtils.getOrganizationInstructions(3,constructId,singleId,unitId);
                        if (organizationInstructions.context != null) {
                            let remark = await ExcelUtil.removeTags(organizationInstructions.context);
                            await ZhaoBiaoUtil.writeDataToCover3(remark,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-2 工程量清单报价说明":
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-3 工程项目总价表":
                        let gcxmzjb =await this.getUnitGcxmzjb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToSheet1(gcxmzjb.list,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-5 单位工程费汇总表":
                        let dataSum = await this.getUnitSummaryData(constructId,singleId,unitId);
                        if (dataSum != null) {
                            await ZhaoBiaoUtil.writeDataToSheet8(dataSum,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-5 单位工程费汇总表--(唐山地区)":
                        let dataUnitTangShan = await this.getUnitSummaryData(constructId,singleId,unitId);
                        if (dataUnitTangShan != null) {
                            await ZhaoBiaoUtil.writeDataToSheet8(dataUnitTangShan,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-6 分部分项工程量清单与计价表":
                        let fbFxAll = unit.itemBillProjects;
                        //过滤临时删除项
                        let fbFx = await this.filterFbfxTempDelete(fbFxAll);
                        await ZhaoBiaoUtil.writeDataToUnitSheet9(fbFx.filter(item => item.kind === BranchProjectLevelConstant.qd|| item.kind === BranchProjectLevelConstant.fb||item.kind === BranchProjectLevelConstant.zfb),worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-7 单价措施项目工程量清单与计价表":
                        let qdByDjcsAll = await PricingFileFindUtils.getQdFbList(constructId,singleId,unitId);
                        //过滤临时删除项
                        let qdByDjcs = await this.filterFbfxTempDelete(qdByDjcsAll);
                        await ZhaoBiaoUtil.writeDataToUnitSheet10(qdByDjcs,worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-8 总价措施项目清单与计价表":
                        let data = [];
                        let csxmTotalAll = unit.measureProjectTables;
                        //过滤临时删除项
                        let csxmTotal = await this.filterFbfxTempDelete(csxmTotalAll);
                        let awen = csxmTotal.filter(csxm => csxm.kind === BranchProjectLevelConstant.qd && csxm.name == "安全生产、文明施工费");
                        let qdByZjcs = PricingFileFindUtils.getQdByZjcs(unit.constructId,unit.spId,unitId);
                        data.push(awen,qdByZjcs);
                        //费用定额的话需要重新计算计算基数
                        await ZhaoBiaoUtil.writeDataToUnitSheet11(data,worksheet);
                        let headArgs = {};
                        headArgs['headStartNum'] = 1;
                        headArgs['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs);
                        break;
                    case "表1-9 其他项目清单与计价表":
                        let otherItems = unit.otherProjects;
                        await ZhaoBiaoUtil.writeDataToUnitSheet12(otherItems,worksheet);
                        let headArgs9 = {};
                        headArgs9['headStartNum'] = 1;
                        headArgs9['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs9);
                        break;
                    case "表1-10 暂列金额明细表":
                        let zlJE = unit.otherProjectProvisionals;
                        if (zlJE != null) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet13(zlJE,worksheet);
                        }
                        let headArgs10 = {};
                        headArgs10['headStartNum'] = 1;
                        headArgs10['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs10);
                        break;
                    case "表1-11 暂估价表":
                        let zanGu = unit.otherProjectZygcZgjs;//专业工程暂估价
                        let array = new Array();
                        if (!ObjectUtils.isEmpty(zanGu)) {
                            zanGu.forEach(i => {
                                let otherProjectZygcZgj = new OtherProjectZygcZgj();
                                ConvertUtil.setDstBySrc(i,otherProjectZygcZgj);
                                array.push(otherProjectZygcZgj);
                            })
                        }

                        if (array != null && array.length>0) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet14(array,worksheet);
                        }
                        let headArgs11 = {};
                        headArgs11['headStartNum'] = 1;
                        headArgs11['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs11);
                        break;
                    case "表1-12 总承包服务费计价表":
                        let serviceCosts = unit.otherProjectServiceCosts;
                        if (serviceCosts != null) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet15(serviceCosts,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-13 计日工表":
                        let dayWorks = unit.otherProjectDayWorks;
                        if (dayWorks != null) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet16(dayWorks,worksheet);
                        }
                        let headArgs13 = {};
                        headArgs13['headStartNum'] = 1;
                        headArgs13['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs13);
                        break;
                    case "表1-14 招标人供应材料、设备明细表":

                        let zbrgyclsbmxb = await this.getZbrgyclsbmxb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToUnitSheet17(zbrgyclsbmxb.rcjlist,worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-15 主要材料、设备明细表":
                        let zyclsb =  await  this.getZyclsb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToUnitSheet18(zyclsb.rcjlist,worksheet);
                        let headArgs15 = {};
                        headArgs15['headStartNum'] = 1;
                        headArgs15['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs15);
                        break;
                    case "表1-15 主要材料明细表":

                        let zyclmx = await this.getZyclmxb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToUnitSheet18(zyclmx.rcjlist,worksheet);
                        let headArgs151 = {};
                        headArgs151['headStartNum'] = 1;
                        headArgs151['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs151);
                        break;
                    case "表1-15 设备明细表":
                        let sbmx = await this.getSbmxb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToUnitSheet18(sbmx.rcjlist,worksheet);
                        let headArgs152 = {};
                        headArgs152['headStartNum'] = 1;
                        headArgs152['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs152);
                        break;
                    case "表1-16 分部分项工程量清单综合单价分析表":
                        let fbFx1All = unit.itemBillProjects;
                        //过滤临时删除项
                        let fbFx1 = await this.filterFbfxTempDelete(fbFx1All);
                        if (fbFx1 != null) {
                            let filter = fbFx1.filter(item => item.kind === BranchProjectLevelConstant.qd ||
                                item.kind === BranchProjectLevelConstant.de
                            );
                            filter.forEach(function(element) {
                                if (element.kind === BranchProjectLevelConstant.de) {
                                    let constructProjectRcjs = unit.constructProjectRcjs;
                                    if (ObjectUtils.isNotEmpty(constructProjectRcjs)) {
                                        let deRcjs = constructProjectRcjs.filter(item => item.deId == element.sequenceNbr && item.type=="人工费");
                                        if (deRcjs.length > 0 && ObjectUtils.isNotEmpty(deRcjs[0].marketPrice)) {
                                            element.rfeePrice = deRcjs[0].marketPrice.toFixed(2);
                                        }
                                    }
                                    if (ObjectUtils.isEmpty(element.rfeePrice)) {  //若没有人工单价  展示为0
                                        element.rfeePrice = 0;
                                    }
                                }
                            });
                            await ZhaoBiaoUtil.writeDataToUnitSheet116(filter,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-17 单价措施项目工程量清单综合单价分析表":
                        let csxmcsAll = unit.measureProjectTables;
                        //过滤临时删除项
                        let csxmcs = await this.filterFbfxTempDelete(csxmcsAll);
                        if (csxmcs != null) {
                            let filter = csxmcs.filter(item => (item.constructionMeasureType===ConstructionMeasureTypeConstant.DJCS));
                            if (filter.length > 0) {
                                let djcsQdAndDe = [];
                                for (let i = 0; i < filter.length; i++) {
                                    let djcsData = this.getCSXMQdAndDe(csxmcs,filter[i],"").datas.filter(item => (!ObjectUtils.isEmpty(item.fxCode) && (item.kind === BranchProjectLevelConstant.qd ||item.kind === BranchProjectLevelConstant.de)&& item.constructionMeasureType!=ConstructionMeasureTypeConstant.DJCS));
                                    djcsQdAndDe.push(...djcsData);
                                }
                                djcsQdAndDe.forEach(function(element) {
                                    if (element.kind === BranchProjectLevelConstant.de) {
                                        let constructProjectRcjs = unit.constructProjectRcjs;
                                        if (ObjectUtils.isNotEmpty(constructProjectRcjs)) {
                                            let deRcjs = constructProjectRcjs.filter(item => item.deId == element.sequenceNbr && item.type=="人工费");
                                            if (deRcjs.length > 0 && ObjectUtils.isNotEmpty(deRcjs[0].marketPrice)) {
                                                element.rfeePrice = deRcjs[0].marketPrice.toFixed(2);
                                            }
                                        }
                                        if (ObjectUtils.isEmpty(element.rfeePrice)) {  //若没有人工单价  展示为0
                                            element.rfeePrice = 0;
                                        }
                                    }
                                });
                                await ZhaoBiaoUtil.writeDataToUnitSheet117(djcsQdAndDe,worksheet);
                            }
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-18 总价措施项目费分析表":
                        let zjCsAll = unit.measureProjectTables;
                        //过滤临时删除项
                        let zjCs = await this.filterFbfxTempDelete(zjCsAll);
                        if (zjCs != null) {
                            let filterOtherZJ = zjCs.filter(item => (item.constructionMeasureType===ConstructionMeasureTypeConstant.ZJCS));
                            let awenZj = zjCs.filter(item => (item.constructionMeasureType===ConstructionMeasureTypeConstant.AWF));
                            //拿到其他总价措施数据
                            let zjcsQdAndDe = [];
                            for (let i = 0; i < filterOtherZJ.length; i++) {
                                let zjcsData = this.getCSXMQdAndDe(zjCs,filterOtherZJ[i],"").datas.filter(item => (item.constructionMeasureType!=ConstructionMeasureTypeConstant.ZJCS));
                                zjcsQdAndDe.push(...zjcsData);
                            }
                            //拿到安文费清单数据
                            let awenFee = this.getCSXMQdAndDe(zjCs,awenZj[0],"").datas.filter(item => (item.constructionMeasureType!=ConstructionMeasureTypeConstant.ZJCS));
                            let dataZJ = awenFee.filter(item => (item.constructionMeasureType!=ConstructionMeasureTypeConstant.AWF && item.kind==BranchProjectLevelConstant.qd));
                            if (ObjectUtils.isNotEmpty(dataZJ)) {
                                const awfQdItem = dataZJ[0];
                                zjcsQdAndDe = zjcsQdAndDe.filter(de => de.parentId != awfQdItem.sequenceNbr);
                            }
                            for (const obj of zjcsQdAndDe) {
                                dataZJ.push(obj);
                            }
                            let constructProjectRcjs = unit.constructProjectRcjs;
                            await ZhaoBiaoUtil.writeDataToUnitSheet118(dataZJ,worksheet,constructProjectRcjs);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "材料、机械、设备增值税计算表":
                        let clJxSbZzsjsb = await this.getClJxSbZzsjsb(constructId,singleId,unitId);
                        if (clJxSbZzsjsb != null) {
                            let total = await this.calculateTotal(clJxSbZzsjsb);
                            clJxSbZzsjsb['totalObject'] = total;
                            await ClJxSbZzsUtil.writeDataToClJxSbZzsjsb(clJxSbZzsjsb,worksheet);
                        }
                        let headArgsTax = {};
                        headArgsTax['headStartNum'] = 1;
                        headArgsTax['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTax);
                        break;
                    case "材料、机械、设备增值税计算表（实体）":
                        let clJxSbZzsjsbSt = await this.getClJxSbZzsjsbSt(constructId,singleId,unitId);
                        if (clJxSbZzsjsbSt != null) {
                            let total = await this.calculateTotal(clJxSbZzsjsbSt);
                            clJxSbZzsjsbSt['totalObject'] = total;
                            await ClJxSbZzsUtil.writeDataToClJxSbZzsjsb(clJxSbZzsjsbSt,worksheet);
                        }
                        let headArgsTax1 = {};
                        headArgsTax1['headStartNum'] = 1;
                        headArgsTax1['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTax1);
                        break;
                    case "材料、机械、设备增值税计算表（措施）":
                        let clJxSbZzsjsbCs = await this.getClJxSbZzsjsbCs(constructId,singleId,unitId);
                        if (clJxSbZzsjsbCs != null) {
                            let total = await this.calculateTotal(clJxSbZzsjsbCs);
                            clJxSbZzsjsbCs['totalObject'] = total;
                            await ClJxSbZzsUtil.writeDataToClJxSbZzsjsb(clJxSbZzsjsbCs,worksheet);
                        }
                        let headArgsTax2 = {};
                        headArgsTax2['headStartNum'] = 1;
                        headArgsTax2['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTax2);
                        break;
                    case "增值税进项税额计算汇总表":
                        let zzsJxs = await this.getZzsJxs(constructId,singleId,unitId);
                        if (zzsJxs != null) {
                            await ClJxSbZzsUtil.writeDataToZzsJxs(zzsJxs,worksheet);
                        }
                        let headArgsSum = {};
                        headArgsSum['headStartNum'] = 1;
                        headArgsSum['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsSum);
                        break;
                    default:
                }
            }
        }
        if (lanMuName == "工程量清单报表"||lanMuName == "测评响应工程量清单报表") {
            if (projectType == "project") {
                switch (worksheet.name) {
                    //工程项目层级
                    case "封面1 工程量清单":
                        let param1 ={};
                        //是招标还是投标 1招标 2投标
                        param1.ifZbOrTb = 1;
                        //工程项目ID
                        param1.constructId = constructId;
                        let constructProjectJBXX = await this.getconstructProjectJBXX(param1);
                        await ProjectQdUtil.writeDataToCover1(constructProjectJBXX,worksheet);
                        let headArgsTb = {};
                        headArgsTb['headStartNum'] = 1;
                        headArgsTb['headEndNum'] = 8;
                        headArgsTb['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTb);
                        break;
                    case "扉页1 工程量清单":
                        let param3 ={};
                        //是招标还是投标 1招标 2投标
                        param3.ifZbOrTb = 1;

                        //工程项目ID
                        param3.constructId = constructId;
                        let constructProjectZBKZJBD =await this.getconstructProjectZBKZJBD(param3,1);
                        await ProjectQdUtil.writeDataToCover2(constructProjectZBKZJBD,worksheet);
                        let headArgsQd = {};
                        headArgsQd['headStartNum'] = 1;
                        headArgsQd['headEndNum'] = 9;
                        headArgsQd['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsQd);
                        break;
                    case "表1-1 工程量清单编制说明":

                        let param5 ={};
                        //是招标还是投标 1招标 2投标
                        param5.ifZbOrTb = 1;

                        //工程项目ID
                        param5.constructId = constructId;
                        let organization =await this.getOrganization(param5);
                        if (organization[1].remark != null) {
                            let remark = await ExcelUtil.removeTags(organization[1].remark);
                            await ZhaoBiaoUtil.writeDataToCover3(remark,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-3 工程项目总价表":
                        let param7 ={};
                        //是招标还是投标 1招标 2投标
                        param7.ifZbOrTb = 1;

                        //工程项目ID
                        param7.constructId = constructId;
                        let gcxmzjb =await this.getGcxmzjb(param7);
                        await ProjectQdUtil.writeDataToSheet1(gcxmzjb,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-3 工程项目总价表（沧州）":
                        let param77 ={};
                        //是招标还是投标 1招标 2投标
                        param77.ifZbOrTb = 2;
                        //工程项目ID
                        param77.constructId = constructId;
                        let gcxmzjb77 =await this.getGcxmzjb(param77);

                        let gdawf77 = {};
                        gdawf77.sortNo = "";
                        gdawf77.name = "安全生产、文明施工费（含税）";
                        gdawf77.level = "parent";
                        gdawf77.gf = null;
                        let projectObj = PricingFileFindUtils.getProjectObjById(constructId);
                        gdawf77.awf = ObjectUtil.isEmpty(projectObj.securityFee) ? 0 : projectObj.securityFee;
                        gdawf77.total = gdawf77.awf;
                        gcxmzjb77.push(gdawf77);

                        await ProjectQdUtil.writeDataToSheet1(gcxmzjb77,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-4 单项工程费汇总表":
                        let param14 ={};
                        param14['levelType'] = 1;
                        param14['constructId'] = constructId;
                        let analysisData14 = await this.service.unitProjectService.getCostAnalysisData(param14);
                        let dataList = {"analysisZJ":[],"amount":0,"gfeeTotal":0,"awenFeeTotal":0,
                        "fbfxhj":0,"csxhj":0,"qtxmhj":0,"sj":0,"average":0,"unitcost":0,"sbfsj":0};
                        await this.getSingleSummaryDataForConstruct(analysisData14,dataList);
                        await ZhaoBiaoUtil.writeDataToSheet2(dataList,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                }
            }
            if (projectType == "single") {
                switch (worksheet.name) {
                    //单项工程层级
                    case "表1-5 单项工程费汇总表":
                        let summaryData = await this.getSingleSummaryData(constructId,singleId);
                        await ProjectQdUtil.writeDataToSheet5(summaryData,worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                }
            }
            if (projectType == "unit") {
                switch (worksheet.name) {
                    //单位工程层级
                    case "填表须知":
                        let headArgsXz = {};
                        headArgsXz['headStartNum'] = 1;
                        headArgsXz['headEndNum'] = 2;
                        headArgsXz['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsXz);
                        break;
                    case "封面1 工程量清单":
                        let zbkzj = await this.getUnitProjectzbkzj(constructId,singleId,unitId);
                        if (zbkzj != null) {
                            await ProjectQdUtil.writeDataToCover1(zbkzj,worksheet);
                        }
                        let headArgsQd = {};
                        headArgsQd['headStartNum'] = 1;
                        headArgsQd['headEndNum'] = 8;
                        headArgsQd['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsQd);
                        break;
                    case "扉页1 工程量清单":
                        let zbkzjBd = await this.getUnitProjectZBKZJBD(constructId,singleId,unitId,1);
                        if (zbkzjBd != null) {
                            await ProjectQdUtil.writeDataToCover2(zbkzjBd,worksheet);
                        }
                        let headArgsQd2 = {};
                        headArgsQd2['headStartNum'] = 1;
                        headArgsQd2['headEndNum'] = 9;
                        headArgsQd2['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsQd2);
                        break;
                    case "表1-1 工程量清单编制说明":

                        let organizationInstructions = PricingFileFindUtils.getOrganizationInstructions(3,constructId,singleId,unitId);
                        if (organizationInstructions.context != null) {
                            let remark = await ExcelUtil.removeTags(organizationInstructions.context);
                            await ZhaoBiaoUtil.writeDataToCover3(remark,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-3 工程项目总价表":
                        let gcxmzjb =await this.getUnitGcxmzjb(constructId,singleId,unitId);
                        await ProjectQdUtil.writeDataToSheet1(gcxmzjb.list,worksheet,constructIs2022);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-5 单位工程费汇总表":
                        let dataSum = await this.getUnitSummaryData(constructId,singleId,unitId);
                        if (dataSum != null) {
                            await ProjectQdUtil.writeDataToSheet8(dataSum,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-5 单位工程费汇总表--(唐山地区)":
                        let dataUnitTangShan = await this.getUnitSummaryData(constructId,singleId,unitId);
                        if (dataUnitTangShan != null) {
                            await ProjectQdUtil.writeDataToSheet8(dataUnitTangShan,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-6 分部分项工程量清单与计价表":
                        let fbFxAll = unit.itemBillProjects;
                        //过滤临时删除项
                        let fbFx = await this.filterFbfxTempDelete(fbFxAll);
                        let qd = fbFx.filter(item => item.kind === BranchProjectLevelConstant.qd|| item.kind === BranchProjectLevelConstant.fb||item.kind === BranchProjectLevelConstant.zfb);
                        await ProjectQdUtil.writeDataToUnitSheet9(qd,worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-7 单价措施项目工程量清单与计价表":
                        let qdByDjcsAll = await PricingFileFindUtils.getQdFbList(constructId,singleId,unitId);
                        //过滤临时删除项
                        let qdByDjcs = await this.filterFbfxTempDelete(qdByDjcsAll);
                        await ProjectQdUtil.writeDataToUnitSheet10(qdByDjcs,worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-8 总价措施项目清单与计价表":
                        let data = [];
                        let csxmTotalAll = unit.measureProjectTables;
                        //过滤临时删除项
                        let csxmTotal = await this.filterFbfxTempDelete(csxmTotalAll);
                        let awen = csxmTotal.filter(csxm => csxm.kind === BranchProjectLevelConstant.qd && csxm.name == "安全生产、文明施工费");
                        let qdByZjcs = PricingFileFindUtils.getQdByZjcs(unit.constructId,unit.spId,unitId);
                        data.push(awen,qdByZjcs);
                        //费用定额的话需要重新计算计算基数
                        await ProjectQdUtil.writeDataToUnitSheet11(data,worksheet);
                        let headArgs = {};
                        headArgs['headStartNum'] = 1;
                        headArgs['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs);
                        break;
                    case "表1-9 其他项目清单与计价表":
                        let otherItems = unit.otherProjects;
                        await ZhaoBiaoUtil.writeDataToUnitSheet12(otherItems,worksheet);
                        let headArgs9 = {};
                        headArgs9['headStartNum'] = 1;
                        headArgs9['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs9);
                        break;
                    case "表1-10 暂列金额明细表":
                        let zlJE = unit.otherProjectProvisionals;
                        if (zlJE != null) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet13(zlJE,worksheet);
                        }
                        let headArgs10 = {};
                        headArgs10['headStartNum'] = 1;
                        headArgs10['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs10);
                        break;
                    case "表1-11 暂估价表":
                        let zanGu = unit.otherProjectZygcZgjs;//专业工程暂估价
                        let array = new Array();
                        if (!ObjectUtils.isEmpty(zanGu)) {
                            zanGu.forEach(i => {
                                let otherProjectZygcZgj = new OtherProjectZygcZgj();
                                ConvertUtil.setDstBySrc(i,otherProjectZygcZgj);
                                array.push(otherProjectZygcZgj);
                            })
                        }

                        if (array != null && array.length>0) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet14(array,worksheet);
                        }
                        let headArgs11 = {};
                        headArgs11['headStartNum'] = 1;
                        headArgs11['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs11);
                        break;
                    case "表1-12 总承包服务费计价表":
                        let serviceCosts = unit.otherProjectServiceCosts;
                        if (serviceCosts != null) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet15(serviceCosts,worksheet);
                        }
                        let headArgs12 = {};
                        headArgs12['headStartNum'] = 1;
                        headArgs12['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs12);
                        break;
                    case "表1-13 计日工表":
                        let dayWorks = unit.otherProjectDayWorks;
                        if (dayWorks != null) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet16(dayWorks,worksheet);
                        }
                        let headArgs13 = {};
                        headArgs13['headStartNum'] = 1;
                        headArgs13['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs13);
                        break;
                    case "表1-14 招标人供应材料、设备明细表":

                        let zbrgyclsbmxb = await this.getZbrgyclsbmxb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToUnitSheet17(zbrgyclsbmxb.rcjlist,worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-15 主要材料、设备明细表":
                        // let zyclsb =  await  this.getZyclsb(constructId,singleId,unitId);
                        // await ZhaoBiaoUtil.writeDataToUnitSheet18(zyclsb.rcjlist,worksheet);
                        let headArgs15 = {};
                        headArgs15['headStartNum'] = 1;
                        headArgs15['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs15);
                        break;
                    case "表1-15 主要材料明细表":

                        // let zyclmx = await this.getZyclmxb(constructId,singleId,unitId);
                        // await ZhaoBiaoUtil.writeDataToUnitSheet18(zyclmx.rcjlist,worksheet);
                        let headArgs151 = {};
                        headArgs151['headStartNum'] = 1;
                        headArgs151['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs151);
                        break;
                    case "表1-15 设备明细表":
                        // let sbmx = await this.getSbmxb(constructId,singleId,unitId);
                        // await ZhaoBiaoUtil.writeDataToUnitSheet18(sbmx.rcjlist,worksheet);
                        let headArgs152 = {};
                        headArgs152['headStartNum'] = 1;
                        headArgs152['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs152);
                        break;
                    case "材料、机械、设备增值税计算表":
                        // let clJxSbZzsjsb = await this.getClJxSbZzsjsb(constructId,singleId,unitId);
                        // if (clJxSbZzsjsb != null) {
                        //     await ClJxSbZzsUtil.writeDataToClJxSbZzsjsb(clJxSbZzsjsb,worksheet);
                        // }
                        let headArgsTax = {};
                        headArgsTax['headStartNum'] = 1;
                        headArgsTax['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTax);
                        break;
                    case "材料、机械、设备增值税计算表（实体）":
                        // let clJxSbZzsjsbSt = await this.getClJxSbZzsjsbSt(constructId,singleId,unitId);
                        // if (clJxSbZzsjsbSt != null) {
                        //     await ClJxSbZzsUtil.writeDataToClJxSbZzsjsb(clJxSbZzsjsbSt,worksheet);
                        // }
                        let headArgsTax1 = {};
                        headArgsTax1['headStartNum'] = 1;
                        headArgsTax1['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTax1);
                        break;
                    case "材料、机械、设备增值税计算表（措施）":
                        // let clJxSbZzsjsbCs = await this.getClJxSbZzsjsbCs(constructId,singleId,unitId);
                        // if (clJxSbZzsjsbCs != null) {
                        //     await ClJxSbZzsUtil.writeDataToClJxSbZzsjsb(clJxSbZzsjsbCs,worksheet);
                        // }
                        let headArgsTax2 = {};
                        headArgsTax2['headStartNum'] = 1;
                        headArgsTax2['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTax2);
                        break;
                    case "增值税进项税额计算汇总表":
                        // let zzsJxs = await this.getZzsJxs(constructId,singleId,unitId);
                        // if (zzsJxs != null) {
                        //     await ClJxSbZzsUtil.writeDataToZzsJxs(zzsJxs,worksheet);
                        // }
                        let headArgsSum = {};
                        headArgsSum['headStartNum'] = 1;
                        headArgsSum['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsSum);
                        break;
                    default:
                }
            }
        }
        if (lanMuName == "其他") {
            if (projectType == "unit") {  //这里只针对招标通
                switch (worksheet.name) {
                    case "填表须知":
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "封面1 工程量清单":
                        let zbkzj = await this.getUnitProjectzbkzj(constructId,singleId,unitId);
                        if (zbkzj != null) {
                            await ProjectQdUtil.writeDataToCover1(zbkzj,worksheet);
                        }
                        let headArgsQd = {};
                        headArgsQd['headStartNum'] = 1;
                        headArgsQd['headEndNum'] = 8;
                        headArgsQd['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsQd);
                        break;
                    case "扉页1 工程量清单":
                        let zbkzjBd = await this.getUnitProjectZBKZJBD(constructId,singleId,unitId,1);
                        if (zbkzjBd != null) {
                            await ProjectQdUtil.writeDataToCover2(zbkzjBd,worksheet);
                        }
                        let headArgsQd2 = {};
                        headArgsQd2['headStartNum'] = 1;
                        headArgsQd2['headEndNum'] = 9;
                        headArgsQd2['titlePage'] = true;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsQd2);
                        break;
                    case "表1-1 工程量清单编制说明":
                        let organizationInstructions = PricingFileFindUtils.getOrganizationInstructions(3,constructId,singleId,unitId);
                        if (organizationInstructions.context != null) {
                            let remark = await ExcelUtil.removeTags(organizationInstructions.context);
                            await ZhaoBiaoUtil.writeDataToCover3(remark,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-5 单位工程费汇总表":
                        let dataSum = await this.getUnitSummaryData(constructId,singleId,unitId);
                        if (dataSum != null) {
                            await ZhaoBiaoUtil.writeDataToSheet8(dataSum,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;

                    case "表1-6 分部分项工程量清单与计价表":
                        let fbFxAll = unit.itemBillProjects;
                        //过滤临时删除项
                        let fbFx = await this.filterFbfxTempDelete(fbFxAll);
                        await ZhaoBiaoUtil.writeDataToUnitSheet9(fbFx.filter(item => item.kind === BranchProjectLevelConstant.qd|| item.kind === BranchProjectLevelConstant.fb||item.kind === BranchProjectLevelConstant.zfb),worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-7 单价措施项目工程量清单与计价表":
                        let qdByDjcsAll = await PricingFileFindUtils.getQdFbList(constructId,singleId,unitId);
                        //过滤临时删除项
                        let qdByDjcs = await this.filterFbfxTempDelete(qdByDjcsAll);
                        await ZhaoBiaoUtil.writeDataToUnitSheet10(qdByDjcs,worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-8 总价措施项目清单与计价表":
                        let data = [];
                        let csxmTotalAll = unit.measureProjectTables;
                        //过滤临时删除项
                        let csxmTotal = await this.filterFbfxTempDelete(csxmTotalAll);
                        let awen = csxmTotal.filter(csxm => csxm.kind === BranchProjectLevelConstant.qd && csxm.name == "安全生产、文明施工费");
                        let qdByZjcs = PricingFileFindUtils.getQdByZjcs(unit.constructId,unit.spId,unitId);
                        data.push(awen,qdByZjcs);
                        //费用定额的话需要重新计算计算基数
                        await ZhaoBiaoUtil.writeDataToUnitSheet11(data,worksheet);
                        let headArgs = {};
                        headArgs['headStartNum'] = 1;
                        headArgs['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs);
                        // let sheetStylePromise1 =await  ExcelUtil.findCellStyleList(worksheet);
                        break;
                    case "表1-9 其他项目清单与计价表":
                        let otherItems = unit.otherProjects;
                        await ZhaoBiaoUtil.writeDataToUnitSheet12(otherItems,worksheet);
                        let headArgs9 = {};
                        headArgs9['headStartNum'] = 1;
                        headArgs9['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs9);
                        break;
                    case "表1-10 暂列金额明细表":
                        let zlJE = unit.otherProjectProvisionals;
                        if (zlJE != null) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet13(zlJE,worksheet);
                        }
                        let headArgs10 = {};
                        headArgs10['headStartNum'] = 1;
                        headArgs10['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs10);
                        break;
                    case "表1-11 暂估价表":
                        let zanGu = unit.otherProjectZygcZgjs;//专业工程暂估价
                        let array = new Array();
                        if (!ObjectUtils.isEmpty(zanGu)) {
                            zanGu.forEach(i => {
                                let otherProjectZygcZgj = new OtherProjectZygcZgj();
                                ConvertUtil.setDstBySrc(i,otherProjectZygcZgj);
                                array.push(otherProjectZygcZgj);
                            })
                        }

                        if (array != null && array.length>0) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet14(array,worksheet);
                        }
                        let headArgs11 = {};
                        headArgs11['headStartNum'] = 1;
                        headArgs11['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs11);
                        break;
                    case "表1-12 总承包服务费计价表":
                        let serviceCosts = unit.otherProjectServiceCosts;
                        if (serviceCosts != null) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet15(serviceCosts,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-13 计日工表":
                        let dayWorks = unit.otherProjectDayWorks;
                        if (dayWorks != null) {
                            await ZhaoBiaoUtil.writeDataToUnitSheet16(dayWorks,worksheet);
                            let headArgs13 = {};
                            headArgs13['headStartNum'] = 1;
                            headArgs13['headEndNum'] = 3;
                            await ExcelUtil.dealWithPage(worksheet,workbook,headArgs13);
                        }
                        break;
                    case "表1-14 招标人供应材料、设备明细表":

                        let zbrgyclsbmxb = await this.getZbrgyclsbmxb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToUnitSheet17(zbrgyclsbmxb.rcjlist,worksheet);
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-15 主要材料、设备明细表":
                        let zyclsb =  await  this.getZyclsb(constructId,singleId,unitId);
                        await ZhaoBiaoUtil.writeDataToUnitSheet18(zyclsb.rcjlist,worksheet);
                        let headArgs15 = {};
                        headArgs15['headStartNum'] = 1;
                        headArgs15['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgs15);
                        break;
                    case "表1-16 分部分项工程量清单综合单价分析表":
                        let fbFx1All = unit.itemBillProjects;
                        //过滤临时删除项
                        let fbFx1 = await this.filterFbfxTempDelete(fbFx1All);
                        if (fbFx1 != null) {
                            let filter = fbFx1.filter(item => item.kind === BranchProjectLevelConstant.qd ||
                                item.kind === BranchProjectLevelConstant.de
                            );
                            filter.forEach(function(element) {
                                if (element.kind === BranchProjectLevelConstant.de) {
                                    let constructProjectRcjs = unit.constructProjectRcjs;
                                    if (ObjectUtils.isNotEmpty(constructProjectRcjs)) {
                                        let deRcjs = constructProjectRcjs.filter(item => item.deId == element.sequenceNbr && item.type=="人工费");
                                        if (deRcjs.length > 0 && ObjectUtils.isNotEmpty(deRcjs[0].marketPrice)) {
                                            element.rfeePrice = deRcjs[0].marketPrice.toFixed(2);
                                        }
                                    }
                                    if (ObjectUtils.isEmpty(element.rfeePrice)) {  //若没有人工单价  展示为0
                                        element.rfeePrice = 0;
                                    }
                                }
                            });
                            await ZhaoBiaoUtil.writeDataToUnitSheet116(filter,worksheet);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-17 单价措施项目工程量清单综合单价分析表":
                        let csxmcsAll = unit.measureProjectTables;
                        //过滤临时删除项
                        let csxmcs = await this.filterFbfxTempDelete(csxmcsAll);
                        if (csxmcs != null) {
                            let filter = csxmcs.filter(item => (item.constructionMeasureType===ConstructionMeasureTypeConstant.DJCS));
                            if (filter.length > 0) {
                                let djcsQdAndDe = [];
                                for (let i = 0; i < filter.length; i++) {
                                    let djcsData = this.getCSXMQdAndDe(csxmcs,filter[i],"").datas.filter(item => (!ObjectUtils.isEmpty(item.fxCode) && (item.kind === BranchProjectLevelConstant.qd||item.kind === BranchProjectLevelConstant.de) && item.constructionMeasureType!=ConstructionMeasureTypeConstant.DJCS));
                                    djcsQdAndDe.push(...djcsData);
                                }
                                djcsQdAndDe.forEach(function(element) {
                                    if (element.kind === BranchProjectLevelConstant.de) {
                                        let constructProjectRcjs = unit.constructProjectRcjs;
                                        if (ObjectUtils.isNotEmpty(constructProjectRcjs)) {
                                            let deRcjs = constructProjectRcjs.filter(item => item.deId == element.sequenceNbr && item.type=="人工费");
                                            if (deRcjs.length > 0 && ObjectUtils.isNotEmpty(deRcjs[0].marketPrice)) {
                                                element.rfeePrice = deRcjs[0].marketPrice.toFixed(2);
                                            }
                                        }
                                        if (ObjectUtils.isEmpty(element.rfeePrice)) {  //若没有人工单价  展示为0
                                            element.rfeePrice = 0;
                                        }
                                    }
                                });
                                await ZhaoBiaoUtil.writeDataToUnitSheet117(djcsQdAndDe,worksheet);
                            }
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "表1-18 总价措施项目费分析表":
                        let zjCsAll = unit.measureProjectTables;
                        //过滤临时删除项
                        let zjCs = await this.filterFbfxTempDelete(zjCsAll);
                        if (zjCs != null) {
                            let filterOtherZJ = zjCs.filter(item => (item.constructionMeasureType===ConstructionMeasureTypeConstant.ZJCS));
                            let awenZj = zjCs.filter(item => (item.constructionMeasureType===ConstructionMeasureTypeConstant.AWF));
                            //拿到其他总价措施数据
                            let zjcsQdAndDe = [];
                            for (let i = 0; i < filterOtherZJ.length; i++) {
                                let zjcsData = this.getCSXMQdAndDe(zjCs,filterOtherZJ[i],"").datas.filter(item => (item.constructionMeasureType!=ConstructionMeasureTypeConstant.ZJCS));
                                zjcsQdAndDe.push(...zjcsData);
                            }
                            //拿到安文费清单数据
                            let awenFee = this.getCSXMQdAndDe(zjCs,awenZj[0],"").datas.filter(item => (item.constructionMeasureType!=ConstructionMeasureTypeConstant.ZJCS));
                            let dataZJ = awenFee.filter(item => (item.constructionMeasureType!=ConstructionMeasureTypeConstant.AWF && item.kind==BranchProjectLevelConstant.qd));
                            if (ObjectUtils.isNotEmpty(dataZJ)) {
                                const awfQdItem = dataZJ[0];
                                zjcsQdAndDe = zjcsQdAndDe.filter(de => de.parentId != awfQdItem.sequenceNbr);
                            }
                            for (const obj of zjcsQdAndDe) {
                                dataZJ.push(obj);
                            }
                            let constructProjectRcjs = unit.constructProjectRcjs;
                            await ZhaoBiaoUtil.writeDataToUnitSheet118(dataZJ,worksheet,constructProjectRcjs);
                        }
                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "材料、机械、设备增值税计算表":
                        let clJxSbZzsjsb = await this.getClJxSbZzsjsb(constructId,singleId,unitId);
                        if (clJxSbZzsjsb != null) {
                            let total = await this.calculateTotal(clJxSbZzsjsb);
                            clJxSbZzsjsb['totalObject'] = total;
                            await ClJxSbZzsUtil.writeDataToClJxSbZzsjsb(clJxSbZzsjsb,worksheet);
                        }
                        let headArgsTax = {};
                        headArgsTax['headStartNum'] = 1;
                        headArgsTax['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsTax);
                        break;
                    case "增值税进项税额计算汇总表":
                        let zzsJxs = await this.getZzsJxs(constructId,singleId,unitId);
                        if (zzsJxs != null) {
                            await ClJxSbZzsUtil.writeDataToZzsJxs(zzsJxs,worksheet);
                        }
                        let headArgsSum = {};
                        headArgsSum['headStartNum'] = 1;
                        headArgsSum['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsSum);
                        break;
                    case "签证及索赔计价表":

                        await ExcelUtil.dealWithPage(worksheet,workbook);
                        break;
                    case "工程量偏差计算表":
                        let headArgsOffset = {};
                        headArgsOffset['headStartNum'] = 1;
                        headArgsOffset['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsOffset);
                        break;
                    case "规费明细表":

                        let gfees = unit.gfees;
                        if (gfees!=null && gfees.length > 0) {

                            let array = new Array();
                            gfees.forEach(i => {
                                let gfee = new Gfee();
                                ConvertUtil.setDstBySrc(i,gfee);
                                array.push(gfee);
                                gfee['qfjs'] = "人工预算价+机械预算价";
                            });
                            await ClJxSbZzsUtil.writeDataToGfeeDetails(array,worksheet);
                        }
                        let headArgsGf = {};
                        headArgsGf['headStartNum'] = 1;
                        headArgsGf['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsGf);
                        break;
                    case "安全文明施工费明细表"://和 安全生产、文明施工费汇总表  还不一样

                        let awf = await this.getAqwmsgfmxb(constructId,singleId,unitId);
                        if (awf.list != null && awf.list.length > 0) {
                            await ClJxSbZzsUtil.writeDataToAwenFeeDetails(awf.list,worksheet);
                        }
                        let headArgsAwf = {};
                        headArgsAwf['headStartNum'] = 1;
                        headArgsAwf['headEndNum'] = 3;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsAwf);
                        break;
                    case "水电费明细表":
                        let waterElectricCostData = await this.getOtherWater(constructId,singleId,unitId);
                        if(!waterElectricCostData.customWaterElectricFlag && ObjectUtils.isNotEmpty(waterElectricCostData.waterElectricData)){
                            await ClJxSbZzsUtil.writeDataToWaterDetails(waterElectricCostData,worksheet);
                        }
                        let headArgsSdf = {};
                        headArgsSdf['headStartNum'] = 1;
                        headArgsSdf['headEndNum'] = 4;
                        headArgsSdf['titlePage'] = false;
                        await ExcelUtil.dealWithPage(worksheet,workbook,headArgsSdf);
                        break;
                    case "水电费明细表（独立设置）":
                        let waterElectricCostDataDuli = await this.getOtherWater(constructId,singleId,unitId);
                        if(waterElectricCostDataDuli.customWaterElectricFlag){
                            await ClJxSbZzsUtil.writeDataToWaterDuliDetails(waterElectricCostDataDuli,worksheet);
                        }
                        // let headArgsSdfdlsz = {};
                        // headArgsSdfdlsz['headStartNum'] = 1;
                        // headArgsSdfdlsz['headEndNum'] = 4;
                        // headArgsSdfdlsz['titlePage'] = false;
                        // await ExcelUtil.dealWithPage(worksheet,workbook,headArgsSdfdlsz);
                        break;
                    default:
                }
            }
        }

        //填充工程项目名称
        if (projectType == "project" && !(worksheet.name.includes("封面")||worksheet.name.includes("扉页"))) {
            //填充 项目名称
            let project = PricingFileFindUtils.getProjectObjById(constructId);
            ZhaoBiaoUtil.fillSheetProjectName(worksheet,project.constructName,"工程名称：");
            //填充 工程项目总价表的工程名称
            ZhaoBiaoUtil.fillSheetProjectName(worksheet,project.constructName,"项目名称：");
        }
        if (projectType == "single") {
            let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);
            ZhaoBiaoUtil.fillSheetProjectName(worksheet,singleProject.projectName,"工程名称：");
        }
        if (projectType == "unit" && !(worksheet.name.includes("封面")||worksheet.name.includes("扉页"))) {
            let unitProject = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
            let single = PricingFileFindUtils.getSingleProject(constructId, singleId);
            let sheetProjectName = "";
            if (single != null) {
                sheetProjectName = single.projectName+unitProject.upName;
            }else {
                sheetProjectName = unitProject.upName;
            }
            ZhaoBiaoUtil.fillSheetProjectName(worksheet,sheetProjectName,"项目名称：");
            ZhaoBiaoUtil.fillSheetProjectName(worksheet,sheetProjectName,"工程名称：");
        }

    }

    /**
     * 过滤临时删除项
     * @param fbFxList
     * @returns {Promise<*[]>}
     */
    async filterFbfxTempDelete(fbFxList) {
        let allNodes;
        if (Array.isArray(fbFxList)) {
            allNodes = fbFxList;
        } else {
            allNodes = fbFxList.flattenTree(fbFxList.root);
        }
        let fbFxNoDel = [];
        if (ObjectUtils.isNotEmpty(allNodes)) {
            fbFxNoDel = allNodes.filter(item => ObjectUtils.isEmpty(item.tempDeleteFlag) || item.tempDeleteFlag === false);   //202404分部分项临时删除项过滤
        }
        if (Array.isArray(fbFxList) && ObjectUtils.isNotEmpty(fbFxNoDel)) {
            fbFxNoDel.sort((a, b) => {
                if (ObjectUtils.isNotEmpty(a.dispNo) && ObjectUtils.isNotEmpty(b.dispNo)) {
                    return a.dispNo - b.dispNo;
                } else {
                    return 0;
                }
            });
        }
        return fbFxNoDel;
    }


    async getLanMuWorkBook(loadPath,args,lanMuName,projectType,headLineList) {
        let {constructId,unitId,singleId} = args;
        let unit;
        // /**********用于自测**************/
        // if (projectType == "unit") {
        //     let unitList = PricingFileFindUtils.getUnitList(args.constructId);
        //     singleId = unitList[0].spId;
        //     unitId = unitList[0].sequenceNbr;
        //     args['singleId'] = singleId;
        //     args['unitId'] = unitId;
        //     unit = unitList[0];
        // }
        // if (projectType == "single") {
        //     let unitList = PricingFileFindUtils.getUnitList(args.constructId);
        //     singleId = unitList[0].spId;
        //     unit = unitList[0];
        // }
        /************************/
        //生成对应栏目层级下的workbook对象 后遍历sheet表  填充数据 拿到格式 返回map数据
        let workbook = await ExcelUtil.readToWorkBook(loadPath);
        args["workbook"] = workbook;
        for (let i = 0; i < workbook.worksheets.length; i++) {
            let worksheet = workbook.worksheets[i];
            try {
                await this.switchWorkSheet(projectType,lanMuName,worksheet,args);
            } catch (e) {
                console.log("");
            }
        }
        //保存workbook
        // let strLoc = lanMuName+"单位工程层级.xlsx";
        // await workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\"+strLoc);
        //对workbook的sheet表格式进行输出
        for (let i = 0; i < workbook.worksheets.length; i++) {
            let sheet = workbook.worksheets[i];
            let result = await ExcelUtil.findCellStyleList(sheet);
            // headLineList
            if (result != null) {
                let object = this.traverseHeadLineList(headLineList,sheet.name);
                if (object!=null){
                    object['sheetStyle'] = result;
                }
            }
        }
        console.log("");
    }

    traverseHeadLineList(headLineList,strCondition) {
        for (let i = 0; i < headLineList.length; i++) {
            let element = headLineList[i];
            if (element.headLine == strCondition) {
                return element;
            }
            if (element.hasOwnProperty("children")) {
                let result = this.traverseHeadLineList(element.children,strCondition);
                if (result!=null) return result;
            }
        }
        return null;
    }

    //拿到list中 没有children的元素
    async traverseGetHeadLineAndLeaf(headLineList,list = null) {
        if (list == null) {
            list = [];
        }
        for (let i = 0; i < headLineList.length; i++) {
            let element = headLineList[i];

            if (element.projectLevel != null && element.children==null) { //为sheet页
                if (ObjectUtils.isEmpty(element['selected'])) {
                    element['selected'] = false;
                }
                list.push(element);
            }
            if (element.childrenList!=null) {
                await this.traverseGetHeadLineAndLeaf(element.childrenList,list);
            }

            if (element.children!=null) {
                await this.traverseGetHeadLineAndLeaf(element.children,list);
            }
        }
        return list;
    }

    /**
     * 封面3 投标总价
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async getTbTbzj(constructId, singleId, unitId){



        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);


        //获取工程项目对象
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

        //获取基本信息
        let constructProjectJBXX = projectObjById.constructProjectJBXX;

        let array = new Array();
        if (constructProjectJBXX != null) {
            constructProjectJBXX.forEach(i=>{
                let project = {};
                switch (i.name){
                    case "投标人(承包人)":
                        project.name = "投标人";
                        project.remark = i.remark;
                        array.push(project) ;
                        break;

                    case "编制时间":
                        if (i.groupCode === 3){
                            project.name = i.name;
                            project.remark = i.remark;
                            array.push(project) ;
                        }

                        break;
                }
            });
        }
        let project1 = {};
        project1.name = "工程名称";
        if (single != null) {
            project1.remark = `${single.projectName} ${unit.upName}`;
        }else {
            project1.remark = `${unit.upName}`;
        }

        array.push(project1);

        return array;

    }

    /**
     * 安全文明施工费明细表
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async getAqwmsgfmxb(constructId, singleId, unitId){

        let mxb = {};

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);

        //单位工程名称
        if (single != null) {
            mxb.name = `${single.projectName} ${unit.upName}`;
        }else {
            mxb.name = `${unit.upName}`;
        }

        let args = {};
        args.constructId = constructId;
        args.singleId = singleId;
        args.unitId = unitId;

        let safeFee = await this.service.safeFeeService.getSafeFee(args);

        let array = new Array();

        if (!ObjectUtils.isEmpty(safeFee)){
            let i = 0;
            for (let safeFeeElement of safeFee) {
                let dx = {};
                //序号
                dx.sort = ++i;
                //取费专业名称
                dx.costMajorName =safeFeeElement.costMajorName;
                //取费基数
                dx.qfjs = "分部分项工程费+措施费+其他项目合计+规费";
                //取费金额
                dx.qfje = safeFeeElement.costFeeBase;
                //基本费率
                dx.basicRate = safeFeeElement.basicRate;
                //增加费率
                dx.addRate = safeFeeElement.addRate;
                //安全文明施工费金额
                dx.feeAmount = safeFeeElement.feeAmount;

                array.push(dx);

            }
        }

        mxb.list = array;

        return mxb;
    }


    /**
     * 水电费明细表
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async getOtherWater(constructId, singleId, unitId){
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);

        let mxb = {};
        //单位工程名称
        if (single != null) {
            mxb.name = `${single.projectName} ${unit.upName}`;
        }else {
            mxb.name = `${unit.upName}`;
        }

        let args = {};
        args.constructId = constructId;
        args.singleId = singleId;
        args.unitId = unitId;

        let waterElectricCostData = await this.service.waterElectricCostMatchService.getWaterElectricCostData(args);


        return waterElectricCostData;
    }

    /**
     * 规费明细表
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async getGfmxb(constructId, singleId, unitId){

        let mxb = {};

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);

        //单位工程名称
        if (single != null) {
            mxb.name = `${single.projectName} ${unit.upName}`;
        }else {
            mxb.name = `${unit.upName}`;
        }

        let args = {};
        args.constructId = constructId;
        args.singleId = singleId;
        args.unitId = unitId;

        unit.gfees = gfees;
        let safeFee = await this.service.safeFeeService.getSafeFee(args);

        let array = new Array();

        if (!ObjectUtils.isEmpty(safeFee)){
            let i = 0;
            for (let safeFeeElement of safeFee) {
                let dx = {};
                //序号
                dx.sort = ++i;
                //取费专业名称
                dx.costMajorName =safeFeeElement.costMajorName;
                //取费基数
                dx.qfjs = "人工预算价+机械预算价";
                //取费金额
                dx.qfje = safeFeeElement.costFeeBase;
                //基本费率
                dx.basicRate = safeFeeElement.basicRate;
                //增加费率
                dx.addRate = safeFeeElement.addRate;
                //安全文明施工费金额
                dx.feeAmount = safeFeeElement.feeAmount;

                array.push(dx);

            }
        }

        mxb.list = array;

        return mxb;
    }

    /**
     * 表1-14 招标人供应材料、设备明细表
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async getZbrgyclsbmxb(constructId, singleId, unitId){
        let mxb = {};

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);

        //单位工程名称
        if (single != null) {
            mxb.name = `${single.projectName} ${unit.upName}`;
        }else {
            mxb.name = `${unit.upName}`;
        }


        //单位人材机汇总
        let unitConstructProjectRcjQuery = this.service.rcjProcess.unitConstructProjectRcjQuery(constructId,singleId, unitId,9);
        if (ObjectUtils.isEmpty(unitConstructProjectRcjQuery)){
            mxb.rcjlist = null;
            return mxb;
        }
        unitConstructProjectRcjQuery = unitConstructProjectRcjQuery.filter(i=>!(i.markSum ===1 && (i.levelMark ===RcjLevelMarkConstant.SINK_PB||  i.levelMark ===RcjLevelMarkConstant.SINK_JX )));
        let array = new Array();
        let i = 0;
        for (let rcj of unitConstructProjectRcjQuery) {
            let  dx = {};
            //序号
            dx.sort = ++i;
            //名称
            dx.materialName = rcj.materialName;
            //规格型号
            dx.specification = rcj.specification;
            //单位
            dx.unit = rcj.unit;
            //数量
            dx.totalNumber = rcj.totalNumber;
            //单价
            dx.marketPrice = rcj.marketPrice;
            //合价
            dx.total = rcj.total;
            //质量等级
            dx.qualityGrade = rcj.qualityGrade;
            //供应时间
            dx.priceDate = rcj.priceDate;
            //送达地点
            dx.deliveryLocation = rcj.deliveryLocation;
            //备注
            dx.description = rcj.description;

            array.push(dx);
        }

        mxb.rcjlist = array;
        return mxb;
    }

    /**
     * 表1-15 主要材料、设备明细表
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async getZyclsb(constructId, singleId, unitId){

        let mxb = {};

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);

        //单位工程名称
        if (single != null) {
            mxb.name = `${single.projectName} ${unit.upName}`;
        }else {
            mxb.name = `${unit.upName}`;
        }


        //单位人材机汇总
        //材料
        let cailiao = await this.service.rcjProcess.unitConstructProjectRcjQuery(constructId,singleId, unitId,2);

        let clmx =  await this.getZyclsbCl(cailiao,1);

        //设备
        let shebei = await this.service.rcjProcess.unitConstructProjectRcjQuery(constructId,singleId, unitId,4);
        let sbmx =  await this.getZyclsbCl(shebei,2);


        let rcjlist = clmx.concat(sbmx);

        mxb.rcjlist = rcjlist;

        return  mxb;
    }

    /**
     * 表1-15 主要材料 设备 获取明细
     * @param rcjArray
     * @param type 1 材料明细 2设备明细
     * @returns {Promise<void>}
     */
    async getZyclsbCl(rcjArray,type){

        let array = new Array();
        let mingcheng  = {};

        if (type ===1){
            mingcheng.sort = 1;
            mingcheng.materialName = "材料";

        }else {
            mingcheng.sort = 2;
            mingcheng.materialName = "设备";
        }
        //规格型号
        mingcheng.specification = "/";
        //单位
        mingcheng.unit = "/";
        //数量
        mingcheng.totalNumber = "/";
        //单价
        mingcheng.marketPrice = "/";
        //合价
        mingcheng.total = "/";
        mingcheng.type = type;
        array.push(mingcheng);
        let totalJe = 0;
        if (!ObjectUtils.isEmpty(rcjArray)) {
            let rcjQuery = rcjArray.filter(i => !(i.markSum === 1 && (i.levelMark === RcjLevelMarkConstant.SINK_PB   || i.levelMark === RcjLevelMarkConstant.SINK_JX)));

            if (!ObjectUtils.isEmpty(rcjQuery)) {

                if (rcjQuery.length > 50) {
                    rcjQuery = rcjQuery.slice(0, 50);
                }

                let i = 0;

                for (let rcj of rcjQuery) {
                    let dx = {};
                    //序号
                    if (type === 1) {
                        dx.sort = 1 + "." + (++i);
                    } else {
                        dx.sort = 2 + "." + (++i);
                    }

                    // 编码
                    dx.materialCode = rcj.materialCode;
                    //名称
                    dx.materialName = rcj.materialName;
                    //规格型号
                    dx.specification = rcj.specification;
                    //单位
                    dx.unit = rcj.unit;
                    //数量
                    dx.totalNumber = rcj.totalNumber;
                    //单价
                    dx.marketPrice = rcj.marketPrice;
                    //合价
                    dx.total = rcj.total;
                    totalJe = NumberUtil.add(totalJe, dx.total);
                    //备注
                    dx.description = rcj.description;
                    dx.type = type;
                    array.push(dx);
                }
            }
        }
        let xiaoji  = {};

        //名称
        xiaoji.materialName = "小计";

        //规格型号
        xiaoji.specification = "/";
        //单位
        xiaoji.unit = "/";
        //数量
        xiaoji.totalNumber = "/";
        //单价
        xiaoji.marketPrice = "/";
        //合价
        xiaoji.total = totalJe;
        xiaoji.type = type;
        array.push(xiaoji);

        return array;
    }

    /**
     * 获取 表1-15 主要材料明细表
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async getZyclmxb(constructId, singleId, unitId){

        let mxb = {};

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);

        //单位工程名称
        if (single != null) {
            mxb.name = `${single.projectName} ${unit.upName}`;
        }else {
            mxb.name = `${unit.upName}`;
        }

        //单位人材机汇总
        //主要材料
        let zycl = await this.service.rcjProcess.unitConstructProjectRcjQuery(constructId,singleId, unitId,7);

        let array = new Array();

        if (!ObjectUtils.isEmpty(zycl)) {
            let rcjQuery = zycl.filter(i => !(i.markSum === 1 && (i.levelMark === RcjLevelMarkConstant.SINK_PB  || i.levelMark === RcjLevelMarkConstant.SINK_JX)));
            if (ObjectUtils.isEmpty(rcjQuery)) {
                mxb.rcjlist = null;
                return mxb;
            }


            let i = 0;
            for (let rcj of rcjQuery) {

                let dx = {};
                //序号
                dx.sort = "1."+(++i);
                // 编码
                dx.materialCode = rcj.materialCode;
                //名称
                dx.materialName = rcj.materialName;
                //规格型号
                dx.specification = rcj.specification;
                //单位
                dx.unit = rcj.unit;
                //数量
                dx.totalNumber = rcj.totalNumber;
                //单价
                dx.marketPrice = rcj.marketPrice;
                //合价
                dx.total = rcj.total;
                //备注
                dx.description = rcj.description;
                array.push(dx);
            }
        }
        let firstDx = {};
        firstDx.sort = "1";
        firstDx.materialName = "材料";
        //规格型号
        firstDx.specification = "/";
        //单位
        firstDx.unit = "/";
        //数量
        firstDx.totalNumber = "/";
        //单价
        firstDx.marketPrice = "/";
        //合价
        firstDx.total = "/";
        array.unshift(firstDx);
        mxb.rcjlist = array;
        return mxb;

    }

    /**
     * 表1-15 设备明细表
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async getSbmxb(constructId, singleId, unitId){

        let mxb = {};

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);

        if (single != null) {
            //单位工程名称
            mxb.name = `${single.projectName} ${unit.upName}`;
        }else {
            mxb.name = `${unit.upName}`;
        }

        //单位人材机汇总
        //主要设备
        let zySb = await this.service.rcjProcess.unitConstructProjectRcjQuery(constructId,singleId, unitId,4);
        if (zySb == null) {
            mxb.rcjlist = null;
            return mxb;
        }
        let rcjQuery = zySb.filter(i => !(i.markSum === 1 && (i.levelMark === RcjLevelMarkConstant.SINK_PB || i.levelMark === RcjLevelMarkConstant.SINK_JX )));
        if (ObjectUtils.isEmpty(rcjQuery)){
            mxb.rcjlist = null;
            return mxb;
        }

        let array = new Array();
        let i = 0;
        for (let rcj of rcjQuery) {

            let dx = {};
            //序号
            dx.sort = "2."+(++i);
            // 编码
            dx.materialCode = rcj.materialCode;
            //名称
            dx.materialName = rcj.materialName;
            //规格型号
            dx.specification = rcj.specification;
            //单位
            dx.unit = rcj.unit;
            //数量
            dx.totalNumber = rcj.totalNumber;
            //单价
            dx.marketPrice = rcj.marketPrice;
            //合价
            dx.total = rcj.total;
            //备注
            dx.description = rcj.description;
            array.push(dx);
        }
        let firstDx = {};
        firstDx.sort = "2";
        firstDx.materialName = "设备";
        //规格型号
        firstDx.specification = "/";
        //单位
        firstDx.unit = "/";
        //数量
        firstDx.totalNumber = "/";
        //单价
        firstDx.marketPrice = "/";
        //合价
        firstDx.total = "/";
        array.unshift(firstDx);
        mxb.rcjlist = array;
        return mxb;
    }

    /**
     * 材料、机械、设备增值税计算表
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<{}>}
     */
    async getClJxSbZzsjsb(constructId, singleId, unitId){
        let jsb = {};
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);

        //单位工程名称
        if (single != null) {
            jsb.name = `${single.projectName} ${unit.upName}`;
        }else {
            jsb.name = `${unit.upName}`;
        }

        //单位人材机汇总
        let unitConstructProjectRcjQuery = this.service.rcjProcess.unitConstructProjectRcjQuery(constructId,singleId, unitId,null);
        if (ObjectUtils.isEmpty(unitConstructProjectRcjQuery)){
            return  null;
        }

        //过滤 解析的父类
        let ts = unitConstructProjectRcjQuery.filter(i=>!(i.markSum ===1 && (i.levelMark ===RcjLevelMarkConstant.SINK_PB  ||  i.levelMark ===RcjLevelMarkConstant.SINK_JX)));

        let array = new Array();
        for (let t of ts) {
            let dx = {};
            dx.materialCode = t.materialCode;
            dx.materialName = ObjectUtils.isEmpty(t.specification)?t.materialName : t.materialName + " " + t.specification;
            dx.unit = t.unit;
            //数量
            dx.totalNumber = t.totalNumber;
            //除税系数
            dx.taxRemoval = t.taxRemoval;
            //含税价格
            dx.marketPrice = t.marketPrice;
            //含税合计
            dx.total = t.total;
            //除税价格
            dx.csPrice = null;
            //除税价格合计
            dx.csTotal = null;
            //进项税额合计
            dx.jxTotal = t.jxTotal;
            //销项税额合计
            dx.xxTotal = null;


            dx.kind=t.kind;
            array.push(dx);
        }
        //材料部分
        let clList = array.filter(i=>i.kind !==1 && i.kind !==3 && i.kind !==4 && i.kind !==5);
        await this.getListTotal(clList);

        //机械部分
        let jxList = array.filter(i=>i.kind ===3);
        await this.getListTotal(jxList);

        //设备部分
        let sbList = array.filter(i=>i.kind ===4);
        await this.getListTotal(sbList);

        jsb.clList = clList;
        jsb.jxList = jxList;
        jsb.sbList = sbList;

        return jsb;

    }

    /**
     * 材料、机械、设备增值税计算表（实体）
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<{}>}
     */
    async getClJxSbZzsjsbSt(constructId, singleId, unitId){

        let jsb = {};
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);

        //单位工程名称
        if (single != null) {
            jsb.name = `${single.projectName} ${unit.upName}`;
        }else {
            jsb.name = `${unit.upName}`;
        }

        let ts =await this.getUnitRcjList(constructId, singleId, unitId,1);

        if (ObjectUtils.isEmpty(ts)){
            return null;
        }

        let array = new Array();
        for (let t of ts) {
            let dx = {};
            dx.materialCode = t.materialCode;
            dx.materialName = ObjectUtils.isEmpty(t.specification)?t.materialName : t.materialName + " " + t.specification;
            dx.unit = t.unit;
            //数量
            dx.totalNumber = t.totalNumber;
            //除税系数
            dx.taxRemoval = t.taxRemoval;
            //含税价格
            dx.marketPrice = t.marketPrice;
            //含税合计
            dx.total = t.total;
            //除税价格
            dx.csPrice = null;
            //除税价格合计
            dx.csTotal = null;
            //进项税额合计
            dx.jxTotal = t.jxTotal;
            //销项税额合计
            dx.xxTotal = null;

            dx.kind=t.kind;
            array.push(dx);
        }
        //材料部分
        let clList = array.filter(i=>i.kind !==1 && i.kind !==3 && i.kind !==4 && i.kind !==5);
        await this.getListTotal(clList);

        //机械部分
        let jxList = array.filter(i=>i.kind ===3);
        await this.getListTotal(jxList);

        //设备部分
        let sbList = array.filter(i=>i.kind ===4);
        await this.getListTotal(sbList);

        jsb.clList = clList;
        jsb.jxList = jxList;
        jsb.sbList = sbList;

        return jsb;

    }


    /**
     * 材料、机械、设备增值税计算表（措施）
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<{}>}
     */
    async getClJxSbZzsjsbCs(constructId, singleId, unitId){

        let jsb = {};
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);

        //单位工程名称
        if (single != null) {
            jsb.name = `${single.projectName} ${unit.upName}`;
        }else {
            jsb.name = `${unit.upName}`;
        }

        let ts =await this.getUnitRcjList(constructId, singleId, unitId,2);

        if (ObjectUtils.isEmpty(ts)){
            return null;
        }

        let array = new Array();
        for (let t of ts) {
            let dx = {};
            dx.materialCode = t.materialCode;
            dx.materialName = ObjectUtils.isEmpty(t.specification)?t.materialName : t.materialName + " " + t.specification;
            dx.unit = t.unit;
            //数量
            dx.totalNumber = t.totalNumber;
            //除税系数
            dx.taxRemoval = t.taxRemoval;
            //含税价格
            dx.marketPrice = t.marketPrice;
            //含税合计
            dx.total = t.total;
            //除税价格
            dx.csPrice = null;
            //除税价格合计
            dx.csTotal = null;
            //进项税额合计
            dx.jxTotal = t.jxTotal;
            //销项税额合计
            dx.xxTotal = null;

            dx.kind=t.kind;

            array.push(dx);
        }
        //材料部分
        let clList = array.filter(i=>i.kind !==1 && i.kind !==3 && i.kind !==4 && i.kind !==5);
        await this.getListTotal(clList);

        //机械部分
        let jxList = array.filter(i=>i.kind ===3);
        await this.getListTotal(jxList);

        //设备部分
        let sbList = array.filter(i=>i.kind ===4);
        await this.getListTotal(sbList);

        jsb.clList = clList;
        jsb.jxList = jxList;
        jsb.sbList = sbList;

        return jsb;

    }

    /**
     * 增值税进项税额计算汇总表
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<void>}
     */
    async getZzsJxs(constructId, singleId, unitId){
        let sjb = {};
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);

        //单位工程名称
        if (single != null) {
            sjb.name = `${single.projectName} ${unit.upName}`;
        }else {
            sjb.name = `${unit.upName}`;
        }

        let unitCostCodePrices = unit.unitCostCodePrices;
        let array = new Array();
        for (let unitCostCodePrice of unitCostCodePrices) {
            let sort = null;
            switch (unitCostCodePrice.code) {
                case "CLFJXSE":
                    sort=1;
                    break;
                case "JXFJXSE":
                    sort=2;
                    break;
                case "SBFJXSE":
                    sort=3;
                    break;
                case "AQWMSGFJXSE":
                    sort=4;
                    break;
                case "FLJXSE":
                    sort=5;
                    break;
                case "GLFJXSE":
                    sort=6;
                    break;
                case "ZLJEJXSE":
                    sort=7;
                    break;
                case "ZYGCZGJJXSE":
                    sort=8;
                    break;
                case "JRGJXSE":
                    sort=9;
                    break;
            }

            if (!ObjectUtils.isEmpty(sort)){
                let dx = {};
                dx.sortNo = sort;
                dx.name = unitCostCodePrice.name;
                dx.total = unitCostCodePrice.price;
                array.push(dx);
            }
        }

        sjb.list = array;

        return sjb;
    }

    /**
     * 计算list合计
     * @param args 人材机 lis
     * @returns {Promise<void>}
     */
    async getListTotal(args){
        if (!ObjectUtils.isEmpty(args)){
            let  total = 0;
            let  csTotal = 0;
            let  jxTotal = 0;
            let  xxTotal = 0;


            for (let arg of args) {
                total = NumberUtil.add(total,arg.total);
                csTotal = NumberUtil.add(csTotal,arg.csTotal);
                jxTotal = NumberUtil.add(jxTotal,arg.jxTotal);
                xxTotal = NumberUtil.add(xxTotal,arg.xxTotal);
            }
            let dx = {};
            dx.materialName = "小计";
            dx.total = total;
            dx.csTotal = csTotal;
            dx.jxTotal = jxTotal;
            dx.xxTotal = xxTotal;
            args.push(dx);
        }

    }

    /**
     *
     * @param constructId
     * @param singleId
     * @param unitId
     * @param type 1代表获取 分部分项, 2获取措施项目   null代表获取所有
     * @returns {Promise<void>}
     */
    async getUnitRcjList(constructId, singleId, unitId, type){
        let constructRcjArray = new Array();
        //let rcjMingxi = new Array();

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let constructProjectRcjsAll = unit.constructProjectRcjs;
        let rcjDetailList = unit.rcjDetailList;
        if (constructProjectRcjsAll==null) return null;
        //过滤临时删除项
        let constructProjectRcjs = await this.filterFbfxTempDelete(constructProjectRcjsAll);
        let deListAll;
        if (type  === 1){
            deListAll = unit.itemBillProjects;
        }else if (type === 2){
            deListAll = unit.measureProjectTables;
        }
        //过滤临时删除项
        let deList = await this.filterFbfxTempDelete(deListAll);

        for (let itemBillProject of deList) {
            let constructProjectRcjs1 = constructProjectRcjs.filter(i=>i.deId === itemBillProject.sequenceNbr);
            if (!ObjectUtils.isEmpty(constructProjectRcjs1)) {
                for (let constructProjectRcj of constructProjectRcjs1) {
                    if (constructProjectRcj.markSum ===1 && (constructProjectRcj.levelMark ===RcjLevelMarkConstant.SINK_PB ||constructProjectRcj.levelMark ===RcjLevelMarkConstant.SINK_JX)){
                        let rcjDetails = rcjDetailList.filter(i=>i.rcjId === constructProjectRcj.sequenceNbr);
                        if (!ObjectUtils.isEmpty(rcjDetails)){
                            for (let rcjDetail of rcjDetails) {
                                let constructProjectRcj = new ConstructProjectRcj();
                                ConvertUtil.setDstBySrc(rcjDetail,constructProjectRcj);
                                constructRcjArray.push(constructProjectRcj);
                            }
                        }
                    }else {
                        let constructProjectRcj1 = new ConstructProjectRcj();
                        ConvertUtil.setDstBySrc(constructProjectRcj, constructProjectRcj1)
                        constructRcjArray.push(constructProjectRcj1)
                    }
                }
            }
        }


        //拼接相同材料
        if (!ObjectUtils.isEmpty(constructRcjArray)) {
            for (let arrayElement of constructRcjArray) {
                arrayElement.tempcol = arrayElement.materialCode.concat(arrayElement.materialName)
                    .concat(arrayElement.specification).concat(arrayElement.unit).concat(arrayElement.dePrice)
                    .concat(arrayElement.markSum);
            }
        } else {
            return null;
        }

        //分组
        const grouped = constructRcjArray.reduce((accumulator, currentValue) => {
            // 将分组作为对象的 key，相同分组的项放入同一个数组
            (accumulator[currentValue.tempcol] = accumulator[currentValue.tempcol] || []).push(currentValue);
            return accumulator;
        }, {});

        //循环分组之后的人材机
        let array1 = new Array();
        for (let group in grouped) {
            if (grouped.hasOwnProperty(group)) {
                let groupedElement = grouped[group][0];
                let number = 0;
                grouped[group].forEach(item => {
                    number = NumberUtil.add(number, item.totalNumber)
                });
                groupedElement.totalNumber = NumberUtil.numberScale(number,4);

                groupedElement.rcjDetailsDTOs = null;
                groupedElement.type = this.service.baseRcjService.getRcjTypeEnumDescByCode(groupedElement.kind);
                groupedElement.total = NumberUtil.multiplyToString(number, groupedElement.marketPrice,2);
                groupedElement.priceDifferenc = NumberUtil.subtract(groupedElement.marketPrice,groupedElement.dePrice);
                groupedElement.priceDifferencSum = NumberUtil.multiplyToString(groupedElement.priceDifferenc,groupedElement.totalNumber,2);
                let decimal = NumberUtil.multiplyToString(groupedElement.taxRemoval,0.01);
                groupedElement.jxTotal = NumberUtil.multiplyToString(groupedElement.total,decimal,2);
                array1.push(groupedElement);
            }
        }

        //机械费 组合
        let ts = array1.filter(i=>i.kind ===3);
        //非机械费 非父级材料
        let ts3 = array1.filter(i=>i.kind !==3);
        ts3.sort((a,b) =>{
            if (a.kind === b.kind){
                return a.materialCode.localeCompare(b.materialCode)
            }
            return a.kind - b.kind;
        });

        ts.sort((a,b) => a.kind - b.kind);
        ts3 = ts3.concat(ts);

        return ts3;

    }


    //计算材料、机械、设备增值税计算表的合计
    async calculateTotal(data) {
        let total = {};
        total['total'] = 0;
        total['csTotal'] = 0;
        total['jxTotal'] = 0;
        total['xxTotal'] = 0;
        let collect = [];
        if (data.hasOwnProperty("clList")) {
            let filter = data.clList.filter(item=> item.materialName=="小计");
            if (filter[0] != null) {
                collect.push(filter[0]);
            }
        }
        if (data.hasOwnProperty("jxList")) {
            let filter = data.jxList.filter(item=> item.materialName=="小计");
            if (filter[0] != null) {
                collect.push(filter[0]);
            }
        }
        if (data.hasOwnProperty("sbList")) {
            let filter = data.sbList.filter(item=> item.materialName=="小计");
            if (filter[0] != null) {
                collect.push(filter[0]);
            }
        }
        for (let i = 0; i < collect.length; i++) {
            let element = collect[i];
            total.total += element.total;
            total.csTotal += element.csTotal;
            total.jxTotal += element.jxTotal;
            total.xxTotal += element.xxTotal;
        }
        return total;
    }

    async getOrganization(param){

        //获取工程项目对象
        let projectObjById = PricingFileFindUtils.getProjectObjById(param.constructId);
        //获取基本信息
        let constructProjectJBXX = projectObjById.constructProjectJBXX;
        if (constructProjectJBXX == null) {
            return ;
        }
        let t = constructProjectJBXX.find(i=>i.name === "工程名称");
        let array = new Array();
        let project = {};
        project.name = t.name;
        project.remark = t.remark;
        array.push(project) ;


        let organizationInstructions = PricingFileFindUtils.getOrganizationInstructions(1,param.constructId,null,null);
        let project1 = {};
        project1.name = "工程量清单编制说明";
        project1.remark = organizationInstructions.context;
        array.push(project1) ;

        return array;

    }


    async getUnitGcxmzjb(constructId,singleId,unitId){
        let args = {};
        args.constructId =constructId;
        args.singleId = singleId;
        args.unitId = unitId;
        args.levelType = ProjectLevelConstant.unit;


        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let single = PricingFileFindUtils.getSingleProject(constructId, singleId);

        //单位工程名称
        let unitName = "";
        if (single != null) {
            unitName = `${single.projectName} ${unit.upName}`;
        }else {
            unitName = `${unit.upName}`;
        }

        let je =await this.service.unitProjectService.getCostAnalysisData(args);


        let gf = 0;
        let awf = 0;
        let sbfSj = 0;
        let total = 0;
        let totalBhsbf = 0;
        if (!ObjectUtils.isEmpty(je)){
            if (!ObjectUtils.isEmpty(je.costAnalysisUnitVOList)){
                 if (!ObjectUtils.isEmpty(je.costAnalysisUnitVOList.find(i => i.name === "规费"))) {
                     gf = je.costAnalysisUnitVOList.find(i=>i.name === "规费").context;
                 }
                 awf = je.costAnalysisUnitVOList.find(i=>i.name === "安全生产、文明施工费").context;
                 sbfSj = je.costAnalysisUnitVOList.find(i=>i.name === "设备费及其税金(不含甲供)").context;
                 total = je.costAnalysisUnitVOList.find(i=>i.name === "工程总造价含设备及其税金(含甲供 小写)").context;
                 totalBhsbf = je.costAnalysisUnitVOList.find(i=>i.name === "工程总造价(不含设备费及其税金 小写)").context;
            }
        }

        let result ={};
        result.name = unitName;

        let array = new Array();
        let dx1 ={};
        dx1.sortNo = "1";
        dx1.name = unitName + " 合计" ;
        dx1.total = total;
        dx1.gf = gf;
        dx1.awf = awf;
        dx1.level = "parent";
        array.push(dx1);
        let dx2 ={};
        dx2.sortNo = "1.1";
        dx2.name = unitName + " 工程费" ;
        dx2.total = totalBhsbf;
        dx2.gf = gf;
        dx2.awf = awf;

        array.push(dx2);

        let dx3 ={};
        dx3.sortNo = "1.2";
        dx3.name = unitName + " 设备费及其税金" ;
        dx3.total = sbfSj;
        dx3.gf = "/";
        dx3.awf = "/";
        array.push(dx3);
        result.list = array;

        return result;

    }
    //判断单项是否是涵盖单位的最后一层 如果是将单项加入数据集合
    async getGcxmzjbData(costAnalysis,data,sortNoObject) {
        if (ObjectUtils.isNotEmpty(costAnalysis.costAnalysisConstructVOList)) {
            for (let i = 0; i < costAnalysis.costAnalysisConstructVOList.length; i++) {
                await this.getGcxmzjbData(costAnalysis.costAnalysisConstructVOList[i],data,sortNoObject);
            }
        }else {  //元素为单项
            let result = false;
            //单项如果有子集 并且子集为单位
            if (ObjectUtils.isNotEmpty(costAnalysis.childrenList)&& costAnalysis.childrenList.length>0) {
                if (costAnalysis.childrenList[0].levelType == ProjectLevelConstant.unit) {
                    result = true;
                }
            }
            if (result) {
                //加入数据集
                await this.packageSingleCostAnalysis(costAnalysis,sortNoObject,data);
            }else {  //继续遍历其下的子单项
                if (ObjectUtils.isNotEmpty(costAnalysis.childrenList)) {

                    for (let i = 0; i < costAnalysis.childrenList.length; i++) {
                        await this.getGcxmzjbData(costAnalysis.childrenList[i],data,sortNoObject);
                    }
                }
            }

        }


    }

    async getGcxmzjb(param){
        let args = {};
        args.constructId = param.constructId;
        args.levelType = ProjectLevelConstant.construct;
        let je =await this.service.unitProjectService.getCostAnalysisData(args);

        let resultList = [];
        let sortNoObject = {"sortNo":1};
        await this.getGcxmzjbData(je,resultList,sortNoObject);

        return resultList;
    }

    //封装 造价分析的单项对象用于工程项目总价表
    async packageSingleCostAnalysis(costAnalysisSingleVO,sortNoObject,array) {
        let dx = {};
        dx.sortNo = sortNoObject.sortNo;
        sortNoObject.sortNo = sortNoObject.sortNo+1;
        dx.name = costAnalysisSingleVO.projectName+ " 合计";

        dx.gf = costAnalysisSingleVO.gfee;
        dx.awf = costAnalysisSingleVO.safeFee;
        dx.level = "parent";

        let dx1 = {};
        dx1.sortNo = dx.sortNo + ".1";
        dx1.name = costAnalysisSingleVO.projectName+ " 工程费";
        dx1.total = costAnalysisSingleVO.gczj;
        dx1.gf = costAnalysisSingleVO.gfee;
        dx1.awf = costAnalysisSingleVO.safeFee;


        let dx2 = {};
        dx2.sortNo = dx.sortNo + ".2";
        dx2.name = costAnalysisSingleVO.projectName+ " 设备费及其税金";
        dx2.total = costAnalysisSingleVO.sbfsj;
        dx2.gf = "/";
        dx2.awf = "/";

        dx.total = dx1.total + dx2.total;
        array.push(dx);
        array.push(dx1);
        array.push(dx2);
        return array;
    }

    /**
     * 获取 封面2 招标控制价（标底）
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<any[]>}
     */
    async getUnitProjectzbkzj(constructId,singleId, unitId){
        //获取工程项目对象
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);


        //单项工程对象
        let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);

        //单位工程对象
        let unitProject = PricingFileFindUtils.getUnit(constructId,singleId, unitId);


        //获取基本信息
        let constructProjectJBXX = projectObjById.constructProjectJBXX;
        let array = new Array();
        if (constructProjectJBXX != null) {
            constructProjectJBXX.forEach(i=>{
                let project = {};
                switch (i.name){
                    case "招标人(发包人)":
                        project.name = "招标人";
                        project.remark = i.remark;
                        array.push(project) ;
                        break;
                    case "工程造价咨询人":
                        project.name = "造价咨询人";
                        project.remark = i.remark;
                        array.push(project) ;
                        break;
                    case "编制时间":

                        if (i.groupCode === 2){
                            project.name = i.name;
                            project.remark = i.remark;
                            array.push(project) ;
                        }

                        break;
                }
            });
        }

        let project = {};
        project.name = "工程名称";
        if (singleProject != null) {
            project.remark = singleProject.projectName +"" + unitProject.upName;
        }else {
            project.remark = unitProject.upName;
        }
        array.push(project);
        return array;

    }


    async getconstructProjectJBXX(param){
        //获取工程项目对象
        let projectObjById = PricingFileFindUtils.getProjectObjById(param.constructId);
        //获取基本信息
        let constructProjectJBXX = projectObjById.constructProjectJBXX;

        let array = new Array();

        constructProjectJBXX.forEach(i=>{
            let project = {};
            switch (i.name){

                case "工程名称":
                    project.name = i.name;
                    project.remark = i.remark;
                    array.push(project) ;
                    break;
                case "招标人(发包人)":
                    project.name = "招标人";
                    project.remark = i.remark;
                    array.push(project) ;
                    break;
                case "工程造价咨询人":
                    project.name = "造价咨询人";
                    project.remark = i.remark;
                    array.push(project) ;
                    break;
                case "编制时间":
                    if (param.ifZbOrTb ===1 ){
                        if (i.groupCode === 2){
                            project.name = i.name;
                            project.remark = i.remark;
                            array.push(project) ;
                        }
                    }else {
                        if (i.groupCode === 3){
                            project.name = i.name;
                            project.remark = i.remark;
                            array.push(project) ;
                        }
                    }

                    break;
            }
        })

        return array;

    }

    /**
     * 扉页3 投标总价   扉页3 投标总价(不含甲供设备及其税金)
     * @param constructId
     * @param singleId
     * @param unitId
     * @param type 1  包含 设备费及税金  2 不包含设备费及税金
     * @returns {Promise<void>}
     */
    async getTbUnitTbzj(constructId,singleId, unitId,type){
        //获取工程项目对象
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        //获取基本信息
        let constructProjectJBXX = projectObjById.constructProjectJBXX;

        //单项工程对象
        let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);

        //单位工程对象
        let unitProject = PricingFileFindUtils.getUnit(constructId,singleId, unitId);

        let array = new Array();
        if (constructProjectJBXX != null) {
            constructProjectJBXX.forEach(i=>{
                let project = {};
                switch (i.name){

                    case "招标人(发包人)":
                        project.name = "招标人";
                        project.remark = i.remark;
                        array.push(project) ;
                        break;
                    case "投标人(承包人)":
                        project.name = "投标人";
                        project.remark = i.remark;
                        array.push(project) ;
                        break;
                    case "投标人(承包人)法人或其授权人":
                        project.name = "法定代表人或委托代理人";
                        project.remark = i.remark;
                        array.push(project) ;
                        break;
                    case "投标单位造价工程师":
                        project.name = "造价工程师";
                        project.remark = i.remark;
                        array.push(project) ;
                        break;
                    case "编制时间":
                        if (i.groupCode === 3){
                            project.name = i.name;
                            project.remark = i.remark;
                            array.push(project) ;
                        }
                        break;
                }
            });
        }

        //计算 招标控制价
        let args = {};
        args.constructId = constructId;
        args.levelType = ProjectLevelConstant.unit;
        args.singleId = singleId;
        args.unitId = unitId;

        let je =await this.service.unitProjectService.getCostAnalysisData(args);
        let zbkzjXx = 0;
        let zbkzjDx = 0;

        if (!ObjectUtils.isEmpty(je)){
            if (!ObjectUtils.isEmpty(je.costAnalysisUnitVOList)){
                if (type === 1){
                    let find = je.costAnalysisUnitVOList.find(i=>i.name === "工程总造价含设备及其税金(不含甲供 小写)");
                    if (!ObjectUtils.isEmpty(find)){
                        zbkzjXx = find.context;
                    }

                    let find1 = je.costAnalysisUnitVOList.find(i=>i.name === "工程总造价含设备及其税金(不含甲供 大写)");
                    if (!ObjectUtils.isEmpty(find1)){
                        zbkzjDx = find1.context;
                    }
                }else {
                    let find = je.costAnalysisUnitVOList.find(i=>i.name === "工程总造价(不含设备费及其税金 小写)");
                    if (!ObjectUtils.isEmpty(find)){
                        zbkzjXx = find.context;
                    }

                    let find1 = je.costAnalysisUnitVOList.find(i=>i.name === "工程总造价(不含设备费及其税金 大写)");
                    if (!ObjectUtils.isEmpty(find1)){
                        zbkzjDx = find1.context;
                    }

                }

            }

        }


        let zbkzjxx = {};
        zbkzjxx.name = "招标控制价小写"
        zbkzjxx.remark =zbkzjXx ;

        array.push(zbkzjxx);

        let zbkzjdx = {};
        zbkzjdx.name = "招标控制价大写"
        zbkzjdx.remark =zbkzjDx ;

        array.push(zbkzjdx);

        let project = {};
        project.name = "工程名称";
        if (singleProject != null) {
            project.remark = `${singleProject.projectName}${unitProject.upName}`;
        }else {
            project.remark = `${unitProject.upName}`;
        }
        array.push(project);

        return array;

    }


    /**
     *
     * @param param
     * @param type 1  包含 设备费及税金  2 不包含设备费及税金
     * @returns {Promise<any[]>}
     */
    async getUnitProjectZBKZJBD(constructId,singleId, unitId,type){
        //获取工程项目对象
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        //获取基本信息
        let constructProjectJBXX = projectObjById.constructProjectJBXX;

        //单项工程对象
        let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);

        //单位工程对象
        let unitProject = PricingFileFindUtils.getUnit(constructId,singleId, unitId);

        let array = new Array();
        if (constructProjectJBXX != null) {
            constructProjectJBXX.forEach(i=>{
                let project = {};
                switch (i.name){

                    case "招标人(发包人)":
                        project.name = "招标人";
                        project.remark = i.remark;
                        array.push(project) ;
                        break;
                    case "招标人(发包人)法人或其授权人":
                        project.name = "法定代表人或委托代理人(第三行)";
                        project.remark = i.remark;
                        array.push(project) ;
                        break;
                    case "工程造价咨询人法人或其授权人":
                        project.name = "法定代表人或委托代理人(第五行)";
                        project.remark = i.remark;
                        array.push(project) ;
                        break;
                    case "工程造价咨询人":
                        project.name = i.name;
                        project.remark = i.remark;
                        array.push(project) ;
                        break;
                    case "编制时间":
                        if (i.groupCode === 2){
                            project.name = i.name;
                            project.remark = i.remark;
                            array.push(project) ;
                        }

                        break;
                    case "编制人":

                        if (i.groupCode === 2){
                            project.name = i.name;
                            project.remark = i.remark;
                            array.push(project) ;
                        }


                        break;
                    case "核对人(复核人)":

                        if (i.groupCode === 2){
                            project.name = "复核人";
                            project.remark = i.remark;
                            array.push(project) ;

                        }

                        break;
                }
            });
        }
        //计算 招标控制价
        let args = {};
        args.constructId = constructId;
        args.levelType = ProjectLevelConstant.unit;
        args.singleId = singleId;
        args.unitId = unitId;

        let je =await this.service.unitProjectService.getCostAnalysisData(args);
        let zbkzjXx = 0;
        let zbkzjDx = 0;

        if (!ObjectUtils.isEmpty(je)){
            if (!ObjectUtils.isEmpty(je.costAnalysisUnitVOList)){
                if (type === 1){
                    let find = je.costAnalysisUnitVOList.find(i=>i.name === "工程总造价含设备及其税金(不含甲供 小写)");
                    if (!ObjectUtils.isEmpty(find)){
                        zbkzjXx = find.context;
                    }

                    let find1 = je.costAnalysisUnitVOList.find(i=>i.name === "工程总造价含设备及其税金(不含甲供 大写)");
                    if (!ObjectUtils.isEmpty(find1)){
                        zbkzjDx = find1.context;
                    }
                }else {
                    let find = je.costAnalysisUnitVOList.find(i=>i.name === "工程总造价(不含设备费及其税金 小写)");
                    if (!ObjectUtils.isEmpty(find)){
                        zbkzjXx = find.context;
                    }

                    let find1 = je.costAnalysisUnitVOList.find(i=>i.name === "工程总造价(不含设备费及其税金 大写)");
                    if (!ObjectUtils.isEmpty(find1)){
                        zbkzjDx = find1.context;
                    }

                }

            }


        }


        let zbkzjxx = {};
        zbkzjxx.name = "招标控制价小写"
        zbkzjxx.remark =zbkzjXx ;

        array.push(zbkzjxx);

        let zbkzjdx = {};
        zbkzjdx.name = "招标控制价大写"
        zbkzjdx.remark =zbkzjDx ;

        array.push(zbkzjdx);

        let project = {};
        project.name = "工程名称";
        if (singleProject != null) {
            project.remark = singleProject.projectName +"" + unitProject.upName;
        }else {
            project.remark = unitProject.upName;
        }
        array.push(project);

        return array;

    }



    /**
     *
     * @param param
     * @param type 1  包含 设备费及税金  2 不包含设备费及税金
     * @returns {Promise<any[]>}
     */
    async getconstructProjectZBKZJBD(param,type){
        //获取工程项目对象
        let projectObjById = PricingFileFindUtils.getProjectObjById(param.constructId);
        //获取基本信息
        let constructProjectJBXX = projectObjById.constructProjectJBXX;

        let array = new Array();

        constructProjectJBXX.forEach(i=>{
            let project = {};
            switch (i.name){

                case "工程名称":
                    project.name = i.name;
                    project.remark = i.remark;
                    array.push(project) ;
                    break;
                case "招标人(发包人)":
                    project.name = "招标人";
                    project.remark = i.remark;
                    array.push(project) ;
                    break;
                case "投标人(承包人)":
                    project.name = "投标人";
                    project.remark = i.remark;
                    array.push(project) ;
                    break;
                case "投标单位造价工程师":
                    project.name = "造价工程师或造价员";
                    project.remark = i.remark;
                    array.push(project) ;
                    break;
                case "招标人(发包人)法人或其授权人":
                    project.name = "法定代表人或委托代理人(第三行)";//用于招标报表
                    project.remark = i.remark;
                    array.push(project) ;
                    break;
                case "投标人(承包人)法人或其授权人":
                    project.name = "投标人(承包人)法人或其授权人";
                    project.remark = i.remark;
                    array.push(project) ;
                    break;
                case "工程造价咨询人法人或其授权人":
                    project.name = "法定代表人或委托代理人(第五行)";//用于招标报表
                    project.remark = i.remark;
                    array.push(project) ;
                    break;
                case "工程造价咨询人":
                    project.name = i.name;
                    project.remark = i.remark;
                    array.push(project) ;
                    break;
                case "编制时间":
                    if (param.ifZbOrTb ===1 ){
                        if (i.groupCode === 2){
                            project.name = i.name;
                            project.remark = i.remark;
                            array.push(project) ;
                        }
                    }else {
                        if (i.groupCode === 3){
                            project.name = i.name;
                            project.remark = i.remark;
                            array.push(project) ;
                        }
                    }

                    break;
                case "编制人":
                    if (param.ifZbOrTb ===1 ){
                        if (i.groupCode === 2){
                            project.name = i.name;
                            project.remark = i.remark;
                            array.push(project) ;
                        }
                    }else {
                        if (i.groupCode === 3){
                            project.name = i.name;
                            project.remark = i.remark;
                            array.push(project) ;
                        }
                    }

                    break;
                case "核对人(复核人)":
                    if (param.ifZbOrTb ===1 ){
                        if (i.groupCode === 2){
                            project.name = "复核人";
                            project.remark = i.remark;
                            array.push(project) ;
                        }
                    }

                    break;
            }
        })
        let args = {};
        args.constructId = param.constructId;
        args.levelType = ProjectLevelConstant.construct;
        let je =await this.service.unitProjectService.getCostAnalysisData(args);

        let zbkzjJe = 0;

        if (!ObjectUtils.isEmpty(je.costAnalysisConstructVOList)){
           let costList =  je.costAnalysisConstructVOList;

            costList.forEach(i=>{
                zbkzjJe = NumberUtil.addParams(i.gczj,zbkzjJe,ObjectUtils.isNotEmpty(projectObjById.securityFee)?projectObjById.securityFee:0);
                if (type === 1){
                    zbkzjJe = NumberUtil.add(i.sbfsj,zbkzjJe);
                }

            })
        }
        let zbkzjxx = {};
        zbkzjxx.name = "招标控制价小写"
        zbkzjxx.remark = zbkzjJe;

        array.push(zbkzjxx);

        let zbkzjdx = {};
        zbkzjdx.name = "招标控制价大写"
        zbkzjdx.remark = NumberUtil.numToCny(zbkzjJe);

        array.push(zbkzjdx);

        return array;

    }

    //获取表1-5单项工程费汇总表的数据
    async getSingleSummaryData(constructId,singleId) {
        let dataList = [];
        let args = {};
        args.constructId = constructId;
        args.levelType = ProjectLevelConstant.single;
        args.singleId = singleId;

        let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);
        if (ObjectUtils.isNotEmpty(singleProject.unitProjects) && singleProject.unitProjects.length > 0) {
            return await this.getSingleSummaryUnitData(constructId,singleId);
        }

        let zj =await this.service.unitProjectService.getCostAnalysisData(args);

        let orderNum = 0;
        if (ObjectUtils.isNotEmpty(zj.costAnalysisSingleVOList.childrenList)) {
            for (let i = 0; i < zj.costAnalysisSingleVOList.childrenList.length; i++) {
                let analysisSingleVO = zj.costAnalysisSingleVOList.childrenList[i];
                orderNum++;
                let unitObject = {};
                unitObject['dispNo'] = orderNum;
                unitObject['name'] = analysisSingleVO.projectName+"  合计";
                unitObject['instructions'] = "/";//计算基数
                unitObject['rate'] = "/";
                unitObject['price'] = analysisSingleVO.gczj;
                unitObject['rg'] = NumberUtil.numberScale(analysisSingleVO.fbfxrgf+analysisSingleVO.djcsxrgf+analysisSingleVO.zjcsxrgf,2);
                unitObject['cl'] = NumberUtil.numberScale(analysisSingleVO.fbfxclf+analysisSingleVO.djcsxclf+analysisSingleVO.zjcsxclf,2);
                unitObject['jx'] = NumberUtil.numberScale(analysisSingleVO.fbfxjxf+analysisSingleVO.djcsxjxf+analysisSingleVO.zjcsxjxf,2);
                let arrayObject = new Array();
                arrayObject.push(unitObject);
                dataList.push(arrayObject);
            }
        }
        return dataList;
    }

    async getSingleSummaryUnitData(constructId,singleId) {
        let unitSummaryList = [];
        let unitList = PricingFileFindUtils.getUnitList(constructId);
        unitList = unitList.filter(unit => unit.spId == singleId);
        let orderNum = 0;
        for (let i = 0; i < unitList.length; i++) {
            let summaryUnit = [];
            let summary = PricingFileFindUtils.getUnitCostSummary(constructId,unitList[i].spId,unitList[i].sequenceNbr);
            for (let j = 0; j < summary.length; j++) {
                let newObject = Object.assign({}, summary[j]);
                summaryUnit.push(newObject);
            }
            let args = {
                "constructId":constructId,
                "singleId":singleId,
                "unitId":unitList[i].sequenceNbr,
                "levelType":3
            }
            let unitCostCodePrice = PricingFileFindUtils.getUnitCostCodePrice(args.constructId,args.singleId, args.unitId);
            let fbfx = summaryUnit.filter(item => item.type=="分部分项工程量清单合计")[0];
            let csxm = summaryUnit.filter(item => item.type=="措施项目清单合计")[0];
            let djcs = summaryUnit.filter(item => item.type=="单价措施项目费")[0];
            let zjcs = summaryUnit.filter(item => item.type=="其他总价措施项目费")[0];


            fbfx.rg = unitCostCodePrice.filter(item => item.name=="分部分项人工费")[0].price;
            fbfx.cl = unitCostCodePrice.filter(item => item.name=="分部分项材料费")[0].price;
            fbfx.jx = unitCostCodePrice.filter(item => item.name=="分部分项机械费")[0].price;


            csxm.rg = NumberUtil.numberScale(unitCostCodePrice.filter(item => item.name=="单价措施人工费")[0].price+unitCostCodePrice.filter(item => item.name=="其他总价措施人工费")[0].price,2);
            csxm.cl = NumberUtil.numberScale(unitCostCodePrice.filter(item => item.name=="单价措施材料费")[0].price+unitCostCodePrice.filter(item => item.name=="其他总价措施材料费")[0].price,2);
            csxm.jx = NumberUtil.numberScale(unitCostCodePrice.filter(item => item.name=="单价措施机械费")[0].price+unitCostCodePrice.filter(item => item.name=="其他总价措施机械费")[0].price,2);

            // djcs.rg = unitCostCodePrice.filter(item => item.name=="单价措施人工费")[0].price;
            // djcs.cl = unitCostCodePrice.filter(item => item.name=="单价措施材料费")[0].price;
            // djcs.jx = unitCostCodePrice.filter(item => item.name=="单价措施机械费")[0].price;
            //
            // zjcs.rg = unitCostCodePrice.filter(item => item.name=="其他总价措施人工费")[0].price;
            // zjcs.cl = unitCostCodePrice.filter(item => item.name=="其他总价措施材料费")[0].price;
            // zjcs.jx = unitCostCodePrice.filter(item => item.name=="其他总价措施机械费")[0].price;

            orderNum++;
            let unitObject = {};
            unitObject['dispNo'] = orderNum;
            unitObject['name'] = unitList[i].upName;
            unitObject['instructions'] = "/";
            unitObject['rate'] = "/";
            unitObject['price'] = unitList[i].gczj;
            unitObject['rg'] = NumberUtil.numberScale(unitList[i].fbfxrgf+unitList[i].csxrgf+unitList[i].qtxmrgf,2);
            unitObject['cl'] = NumberUtil.numberScale(unitList[i].fbfxclf+unitList[i].csxclf+unitList[i].qtxmclf,2);
            unitObject['jx'] = NumberUtil.numberScale(unitList[i].fbfxjxf+unitList[i].csxjxf+unitList[i].qtxmjxf,2);

            for (let j = 0; j < summaryUnit.length; j++) {
                let summaryElement = summaryUnit[j];
                summaryElement.dispNo = orderNum+"."+summaryElement.dispNo;
            }
            summaryUnit.unshift(unitObject);
            unitSummaryList.push(summaryUnit);
        }
        return unitSummaryList;
    }

    //获取表1-5 单位工程费汇总表的数据
    async getUnitSummaryData(constructId,singleId,unitId) {

        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        let summary = PricingFileFindUtils.getUnitCostSummary(constructId,unit.spId,unit.sequenceNbr);
        let data = [];
        for (let j = 0; j < summary.length; j++) {
            let newObject = Object.assign({}, summary[j]);
            data.push(newObject);
        }
        for (let i = 0; i < data.length; i++) {
            let element = data[i];
            if (element.name=="分部分项工程量清单计价合计") {
                element['rg'] = unit.fbfxrgf;
                element['cl'] = unit.fbfxclf;
                element['jx'] = unit.fbfxjxf;
            }
            if (element.name=="措施项目清单计价合计") {
                element['rg'] = unit.csxrgf;
                element['cl'] = unit.csxclf;
                element['jx'] = unit.csxjxf;
            }
            if (element.name=="单价措施项目工程量清单计价合计") {
                element['rg'] = unit.djcsxrgf;
                element['cl'] = unit.djcsxclf;
                element['jx'] = unit.djcsxjxf;
            }
            if (element.name=="其他总价措施项目清单计价合计") {
                element['rg'] = unit.zjcsxrgf;
                element['cl'] = unit.zjcsxclf;
                element['jx'] = unit.zjcsxjxf;
            }
        }
        return data;
    }

    //得到工程项目层级安文费表的数据
    async getAwenFeeDetailData(analysisData,data,parent,constructId) {
        if (ObjectUtils.isNotEmpty(analysisData.costAnalysisConstructVOList)) {
            for (let i = 0; i < analysisData.costAnalysisConstructVOList.length; i++) {
                await this.getAwenFeeDetailData(analysisData.costAnalysisConstructVOList[i],data,null,constructId);
            }
        }else if (analysisData.levelType == ProjectLevelConstant.unit) {

            let element = {};
            element['name'] = analysisData.projectName;
            element['dispNo'] = analysisData.dispNo;
            element['awen'] = analysisData.safeFee;
            data.push(element);

            let param ={};
            param['singleId'] = parent.sequenceNbr;
            param['unitId'] = analysisData.sequenceNbr;
            param['constructId'] = constructId;
            let safeFee = await this.service.safeFeeService.getSafeFee(param);
            for (let k = 0; k < safeFee.length; k++) {
                let element = {};
                element['name'] = safeFee[k].costMajorName;
                element['dispNo'] = "";
                element['awen'] = safeFee[k].feeAmount;
                data.push(element);
            }
        }else {
            let element = {};
            element['name'] = analysisData.projectName;
            element['dispNo'] = analysisData.dispNo;
            element['awen'] = analysisData.safeFee;
            data.push(element);
        }

        //单项如果有子集
        if (ObjectUtils.isNotEmpty(analysisData.childrenList)) {
            for (let i = 0; i < analysisData.childrenList.length; i++) {
                await this.getAwenFeeDetailData(analysisData.childrenList[i],data,analysisData,constructId);
            }
        }
    }

    //得到工程项目层级 表1-4单项工程费汇总表的数据
    async getSingleSummaryDataForConstruct(analysisData,data) {
        if (ObjectUtils.isNotEmpty(analysisData.costAnalysisConstructVOList)) {
            for (let i = 0; i < analysisData.costAnalysisConstructVOList.length; i++) {
                data.amount += analysisData.costAnalysisConstructVOList[i].gczj;
                data.gfeeTotal += analysisData.costAnalysisConstructVOList[i].gfee;
                data.awenFeeTotal += analysisData.costAnalysisConstructVOList[i].safeFee;
                data.fbfxhj += analysisData.costAnalysisConstructVOList[i].fbfxhj;
                data.csxhj += analysisData.costAnalysisConstructVOList[i].csxhj;
                data.qtxmhj += analysisData.costAnalysisConstructVOList[i].qtxmhj;
                data.sj += analysisData.costAnalysisConstructVOList[i].sj;
                data.average += Number.parseFloat(analysisData.costAnalysisConstructVOList[i].average);//工程规模
                data.unitcost += analysisData.costAnalysisConstructVOList[i].unitcost;//造价指标  单方造价
                data.sbfsj += analysisData.costAnalysisConstructVOList[i].sbfsj;//设备费及其税金
                await this.getSingleSummaryDataForConstruct(analysisData.costAnalysisConstructVOList[i],data);
            }
        }else {
            data.analysisZJ.push(analysisData);//单项和单位数据均放入
        }
        //单项如果有子集
        if (ObjectUtils.isNotEmpty(analysisData.childrenList)) {
            for (let i = 0; i < analysisData.childrenList.length; i++) {
                await this.getSingleSummaryDataForConstruct(analysisData.childrenList[i],data);
            }
        }
    }

    //获取表1-7数据用于生成excel
    async getSheetData(constructId,singleId,unitId) {
        let fbFx = PricingFileFindUtils.getFbFx(constructId,singleId,unitId);
        // let excelPath = this.getProjectRootPath()+"\\build\\extraResources\\excelTemplate\\unit\\单位工程层级.xlsx";
        let excelPath = "C:\\Users\\<USER>\\Desktop\\拆行问题处理\\路灯工程字数少.xlsx";
        let sheetName = "表1-6 分部分项工程量清单与计价表";

        // let excelPath = "C:\\Users\\<USER>\\Desktop\\工程项目清单\\二期5#6#住宅楼工程投标报价\\二期5#住宅楼\\电气\\电气.xlsx";
        // let sheetName = "表1-6 分部分项工程量清单与计价表";
        // let worksheetPromise = await ExcelUtil.read(excelPath, sheetName);

        // read from a file
        let workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(excelPath)

        // 获取第一个工作表
        const workSheet = workbook.getWorksheet(sheetName);
        try {
            await ExcelUtil.dealWithPage(workSheet,workbook);
        } catch (e) {
            console.log("");
        }
        // //merges 重置
        await workSheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");
        // console.log("");

    }

     getProjectRootPath() {
        // let relativePath = __filename;
        // let index = relativePath.indexOf("pricing-cs");
        // let prefix = relativePath.substring(0,index);
         return UtilsPs.getExtraResourcesDir();
        // return prefix+"pricing-cs";
    }

    async showSheetStyleSample () {

        let excelPath = this.getProjectRootPath()+"\\excelTemplate\\unit\\sample.xlsx";
        let sheetName = "表1-6 分部分项工程量清单与计价表";
        let worksheet = await ExcelUtil.read(excelPath, sheetName);
        let result = await ExcelUtil.findCellStyleList(worksheet);
        return ResponseData.success(result);
    }

    async exportConfiguration(args) {
        let {constructId,exportPageEyeBrowPageFoot,containsTaxCalculation,batchExport} = args;
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtils.isEmpty(projectObjById.exportConfig)) {  //给默认值
            projectObjById.exportConfig = new ExportConfig(null,null,"excelWithLevel");
        }
        if (ObjectUtils.isNotEmpty(exportPageEyeBrowPageFoot) && ObjectUtils.isNotEmpty(projectObjById.exportConfig)) {
            projectObjById.exportConfig.exportPageEyeBrowPageFoot = exportPageEyeBrowPageFoot;
        }
        if (ObjectUtils.isNotEmpty(containsTaxCalculation) && ObjectUtils.isNotEmpty(projectObjById.exportConfig)) {
            projectObjById.exportConfig.containsTaxCalculation = containsTaxCalculation;
        }
        if (ObjectUtils.isNotEmpty(batchExport) && ObjectUtils.isNotEmpty(projectObjById.exportConfig)) {
            projectObjById.exportConfig.batchExport = batchExport;
        }
        return;
    }

    async applySelectedTemplateToTarget(args) {
        let {constructId,singleId,unitId,lanMuName,itemLevel,jsonData,headLine,originHeadLine} = args;
        let object = null;//该对象
        if (itemLevel == "project") {
            object = PricingFileFindUtils.getProjectObjById(constructId);
        }
        if (itemLevel == "single") {
            object = PricingFileFindUtils.getSingleProject(constructId,singleId);
        }
        if (itemLevel == "unit") {
            object = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        }
        let selected = object.reportViewObject[lanMuName].filter(item => item.headLine== headLine);
        if (ObjectUtils.isNotEmpty(selected)) {
            return ResponseData.fail("选择报表重复");
        }
        let filter = object.reportViewObject[lanMuName].filter(item => item.headLine== originHeadLine);
        if (ObjectUtils.isNotEmpty(filter)) {
            filter[0].excelDataTemplate = jsonData;
            filter[0].headLine = headLine;
            filter[0].isManual = true;
        }

        return ResponseData.success(true);
    }

    //获取措施项目对应的清单和定额
    getCSXMQdAndDe(allData, pointLine, unInfluce) {
        if (pointLine.kind === BranchProjectLevelConstant.top) {
            return {
                "begin": 0,
                "end" : allData.length,
                "datas": allData
            };
        }
        if (pointLine.kind === BranchProjectLevelConstant.fb ||
            pointLine.kind === BranchProjectLevelConstant.zfb ||
            pointLine.kind === BranchProjectLevelConstant.qd) {
            let begin = -1;
            let end = allData.length;
            let parents = this.findParents(allData, pointLine);
            for(let index = 0; index < allData.length; ++index) {
                if (allData[index].sequenceNbr === pointLine.sequenceNbr) {
                    begin = index;
                    continue;
                }
                if (begin>-1) {
                    // 1.下一行级别高于当前行，2.parentId在当前行的 [parentIds] 中 则认为结束
                    if (Number.parseInt(pointLine.kind)>Number.parseInt(allData[index].kind) ||
                        parents[allData[index].parentId]) {
                        end = index;
                        break;
                    }
                }
            }
            return {
                "begin": begin,
                "end" : end,
                "datas": allData.slice(begin, end)
            }; // [bengin, end) bengin为被选中的行
        }
        return {
            "begin":0, "end":0, "datas": this.findLine(allData, pointLine)
        };
    }

    findParents(allData, line) {
        let parents = {};
        parents[line.parentId] = true;
        let lineIndex;
        let buffer = line;
        for (let index = 0; index<allData.length; ++index) {
            if (line.sequenceNbr === allData[index].sequenceNbr) {
                lineIndex = index;
                break;
            }
        }
        while (lineIndex>0) {
            if (allData[lineIndex].sequenceNbr === buffer.parentId) {
                buffer = allData[lineIndex];
                parents[buffer.parentId] = true;
            }
            --lineIndex;
        }

        return parents;
    }

    findLine(allData, pointLine) {
        let res = [];
        for (let i = 0; i<allData.length;++i) {
            if (pointLine.sequenceNbr === allData[i].sequenceNbr) {
                res.push(allData[i]);
            }
        }
        return res;
    }

    async saveSelected(args) {

        let {constructObj,itemLevel,headLineList,lanMuName,params} = args;
        if (ObjectUtils.isNotEmpty(params)) {  //导出窗口的勾选
            await this.traverseParamsExport(params,lanMuName);
        }else {
            let {constructId,singleId,unitId} = constructObj;
            let object = null;
            if (itemLevel == "project") {
                object  = PricingFileFindUtils.getProjectObjById(constructId);
            }else if (itemLevel == "single") {
                object  = PricingFileFindUtils.getSingleProject(constructId,singleId);
            }else if (itemLevel == "unit") {
                object  = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
            }
            for (let i = 0; i < object.reportViewObject[lanMuName].length; i++) {
                if (ObjectUtils.isNotEmpty(headLineList) && headLineList.includes(object.reportViewObject[lanMuName][i].headLine)) {
                    object.reportViewObject[lanMuName][i].selected = true;
                }else {
                    object.reportViewObject[lanMuName][i].selected = false;
                }
            }
        }
        return true;
    }

    async traverseParamsExport(params,lanMuName,args ={}) {
        if (ObjectUtils.isNotEmpty(params.childrenList)) {
            for (let i = 0; i < params.childrenList.length; i++) {
                let paramElement = params.childrenList[i];
                //如果为总工程层级
                if (paramElement.projectLevel != null && paramElement.projectLevel == "project") {
                    args["constructId"] =params.id;
                    let object  = PricingFileFindUtils.getProjectObjById(params.id);
                    let reportElement = object.reportViewObject[lanMuName].filter(item => item.headLine==paramElement.headLine)[0];
                    if (paramElement.selected) {
                        reportElement.selected = true;
                    }else {
                        reportElement.selected = false;
                    }
                }
                if (paramElement.projectLevel != null && paramElement.projectLevel == "single") {
                    args["singleId"] =params.id;
                    let object  = PricingFileFindUtils.getSingleProject(args.constructId,args.singleId);
                    let reportElement = object.reportViewObject[lanMuName].filter(item => item.headLine==paramElement.headLine)[0];
                    if (paramElement.selected) {
                        reportElement.selected = true;
                    }else {
                        reportElement.selected = false;
                    }
                }
                if (paramElement.projectLevel != null && paramElement.projectLevel == "unit") {
                    args["unitId"] =params.id;
                    let object  = PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
                    let reportElement = object.reportViewObject[lanMuName].filter(item => item.headLine==paramElement.headLine)[0];
                    if (paramElement.selected) {
                        reportElement.selected = true;
                    }else {
                        reportElement.selected = false;
                    }
                }
            }
        }
        let filter = params.childrenList.filter(itemParam => itemParam.childrenList!=null);//含有子节点的节点
        if (filter != null) {
            for (let i = 0; i < filter.length; i++) {
                await this.traverseParamsExport(filter[i],lanMuName,args);
            }
        }
    }

}


ExportQueryService.toString = () => '[class ExportQueryService]';
module.exports = ExportQueryService;
