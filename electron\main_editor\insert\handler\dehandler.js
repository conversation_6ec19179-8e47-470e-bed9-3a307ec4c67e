const { TreeNode } = require('../../tree');
const { Snowflake } = require('../../../utils/Snowflake');
const { get2022BY2012Cslb } = require('../../../model/Map2022And2012');
const ConstantUtil = require('../../../enum/ConstantUtil');
const EE = require('../../../../core/ee');
const _ = require('lodash');
const { NumberUtil } = require('../../../utils/NumberUtil');
const { CupmuteContext } = require('../../../unit_price_composition/compute/ComputingCore');
const { keysDeMap, deBaseFn, deRules } = require('../../rules/de');
const { keysSupplementDeMap, baseSupplementFn } = require('../../rules/supplemetDe');
const { PricingFileFindUtils } = require('../../../utils/PricingFileFindUtils');
const InsertRcjStrategy = require('../../../rcj_handle/insert/insertRcjStrategy');

const { isSameUnit, getUnitNum } = require('../../util');
const { de } = require('../../../enum/BranchProjectLevelConstant');
const { ObjectUtils } = require('../../../utils/ObjectUtils');
const DePropertyTypeConstant = require('../../../enum/DePropertyTypeConstant');
const PumpingAddFeeExpressionConstant = require('../../../enum/PumpingAddFeeExpressionConstant');
const {getDeUnitFormatEnum} = require("../../rules/format");

/**
 * 补充定额
 */
class SupplementDeHandler extends CupmuteContext {
  constructor(ctx, pageInfo) {
    super();
    this.ctx = ctx;
    this.allData = ctx.allData;
    this.pageInfo = pageInfo;
  }

  async prepare() {
    let {pointLine, libraryCode} = this.pageInfo;
    this.pointLine = this.allData.getNodeById(pointLine.sequenceNbr);
    this.pointLine.isSupplement = 1;
    this.unitFeeFileDTO = await this._getDefaultFeeFile(libraryCode);
    this.analyzeBaseFn(baseSupplementFn);
    let appendType = ['补'];//补充定额需要展示为【补】
    this.pointLine.appendType = appendType;
  }

  getValue({ type, kind, cloumn }) {
    let value = '';
    switch (type) {
      case 'FEE_FILE': {
        //取费文件
        if (typeof cloumn == 'function') {
          value = cloumn(this.unitFeeFileDTO);
        } else {
          value = this.unitFeeFileDTO[cloumn] || '';
        }
        break;
      }
      case 'from': {
        //取费文件
        if (typeof cloumn == 'function') {
          value = cloumn(this.pageInfo);
        } else {
          value = this.pageInfo[cloumn] || '';
        }
        break;
      }
      default: {
        if (typeof cloumn == 'function') {
          value = cloumn(this);
        }
      }
    }
    return value;
  }

  async replace() {
    await this.prepare();
    await this._fillDELineData();
    await this._fillRcjData();
    return this.pointLine;
  }

  async _fillDELineData() {
    this.pointLine.isSupplement = 1;
    this.pointLine.isStandard = 0;
    this.pointLine.standardId = null;
    this.pointLine.isCostDe = 0;
    for (const key of keysSupplementDeMap) {
      this.pointLine[key] = this.parseParams(key);
    }
    //处理工程量和工程量表达式
    await this.handlerQuantity();
    let appendType = ['补'];//补充定额需要展示为【补】
    this.pointLine.appendType = appendType;

  }

  async handlerQuantity() {
    let { service } = EE.app;
    let { constructId, singleId, unitId } = this.ctx;
    let { quantityExpression } = this.pageInfo;
    let unitNum = getDeUnitFormatEnum(this.pointLine.unit).value;
    if (quantityExpression && quantityExpression !== '') {
      this.pointLine.quantityExpression = quantityExpression;
    } else {
      let qdLine = this.pointLine.parent;
      if (isSameUnit(qdLine.unit, this.pointLine.unit)) {
        this.pointLine.quantityExpression = qdLine.quantityExpression;
      } else {
        this.pointLine.quantityExpression = '0';
        this.pointLine.quantity = 0;
      }
    }
    this.pointLine.quantityExpressionNbr = service.baseBranchProjectOptionService.getQuantityExpressionNbr(constructId, singleId, unitId, this.pointLine); // 重新计算
    this.pointLine.quantity = NumberUtil.numberScale(NumberUtil.divide(this.pointLine.quantityExpressionNbr, getUnitNum(this.pointLine)),unitNum);
  }

  async _getDefaultFeeFile(libraryCode) {
    let { service, betterSqlite3DataSource } = EE.app;
    let { is2022: unitIs2022, constructId, singleId, unitId, unit } = this.ctx;
    const is22Unit = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
    // 根据 主定额库 和 工程专业 获取取费文件code
    let querySql = 'select default_qf_code as feecode\n' +
      (is22Unit ? 'from base_speciality_de_fee_relation_2022\n' : 'from base_speciality_de_fee_relation\n') +
        'where library_code = ?';
    let sqlRes = betterSqlite3DataSource.prepare(querySql).all(libraryCode);
    let feeCode = sqlRes[0].feecode;
    // 根据 feeCode 查询取费文件
    let feeFile = await service.baseFeeFileService.updateFBFXFeeFile(constructId, singleId, unitId, feeCode);

    return feeFile;
  }

  async _fillRcjData() {
    let { constructId, singleId, unitId } = this.ctx;
    let insertRcjStrategy = new InsertRcjStrategy({
      constructId,
      singleId,
      unitId,
      projectObj: PricingFileFindUtils.getProjectObjById(constructId)
    });
    await insertRcjStrategy.execute({ de: this.pointLine,args: this.pageInfo});

  }
}

/**
 * 标准定额
 */
class DeHandler extends CupmuteContext {
  constructor(ctx, upDateInfo) {
    super();
    this.ctx = ctx;
    this.allData = ctx.allData;
    this.pointLine = null;
    this.upDateInfo = upDateInfo;
    this.pointConfig={
      "rcjDetailAmount": 2,   //人材机明细区：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
      "rcjSummaryAmount": 2,   //人材机汇总：消耗量、合计数量，数量类：小数点后4位、第5位小数四舍五入
      "qDDeAmount": 2,  // 工程量，数量类：小数点后3位，第4位四舍五入
      "costPrice": 2, // 金额、合计，金额类：小数点后2位，第3位四舍五入
      "rate": 2,  // 费率、指数、比率(%)：小数点后2位，第3位四舍五入

    }
  }

  async standardPrepare() {
    let { service } = EE.app;
    let { pointLine, indexId, is2022, unit, rcjFlag } = this.upDateInfo;
    let { is2022: unitIs2022, constructId, singleId, unitId } = this.ctx;
    this.baseQdOrDe = await service.baseQdDeProcess.selectBaseQdOrDe(indexId, de, is2022);
    let cslbCode = this.baseQdOrDe.cslbCode;
    //12和22d的映射
    // if (unitIs2022 && !is2022) {
    //   cslbCode = get2022BY2012Cslb(cslbCode);
    // }
    //获取措施类别
    this.baseFeeFileRelationDTO = await service.baseFeeFileRelationService.queryCslbByQfCode(cslbCode, unitIs2022);
    //获取取费文件
    this.unitFeeFileDTO = await service.baseFeeFileService.handleUnitAddFeeFile(constructId, singleId, unitId, this.baseQdOrDe.sequenceNbr, is2022);
  }

  async awfDataPrepare() {
    // 安文费的数据要单独处理   因为安文费没有施工组织措施类别、人材机、单价构成
    let { service } = EE.app;
    let { indexId } = this.upDateInfo;
    let { is2022: unitIs2022, unit } = this.ctx;

    this.baseQdOrDe = await service.baseDeAwfRelationService.getBaseDeAwfRelation(indexId, unitIs2022);
    const baseFeeFile = await service.baseFeeFileService.getBaseFeeFile(this.baseQdOrDe.qfCode, unitIs2022);
    const awfFeeFile = unit.feeFiles.filter(item => item.feeFileCode == baseFeeFile.qfCode);
    this.unitFeeFileDTO = awfFeeFile[0];

  }

  async zjcsDataPrepare() {
    // 总价措施费用定额没有施工组织措施类别
    let { service } = EE.app;
    let { pointLine, indexId, is2022, unit, rcjFlag } = this.upDateInfo;
    let { is2022: unitIs2022, constructId, singleId, unitId } = this.ctx;
    this.baseQdOrDe = await service.baseQdDeProcess.selectBaseQdOrDe(indexId, de, is2022);
    let cslbCode = this.baseQdOrDe.cslbCode;
    //12和22d的映射
    if (unitIs2022 && !is2022) {
      cslbCode = get2022BY2012Cslb(cslbCode);
    }
    //获取措施类别
    this.baseFeeFileRelationDTO = await service.baseFeeFileRelationService.queryCslbByQfCode(cslbCode, unitIs2022);
    //获取取费文件
    this.unitFeeFileDTO = await service.baseFeeFileService.handleUnitAddFeeFile(constructId, singleId, unitId, this.baseQdOrDe.sequenceNbr, is2022);

  }

  /**
   * 数据准备 规则 定额 取费文件 取费文件关系
   * @returns {Promise<void>}
   */
  async prepare() {
    let { pointLine } = this.upDateInfo;
    this.pointLine = await this.allData.getNodeById(pointLine.sequenceNbr);
    if (this.pointLine.isCostDe == DePropertyTypeConstant.AWF_DE) {
      await this.awfDataPrepare();
    } else if (this.pointLine.isCostDe == DePropertyTypeConstant.ZJCS_DE) {
      await this.zjcsDataPrepare();
    } else {
      await this.standardPrepare();
    }
    await this.analyzeBaseFn(deBaseFn);
    await this.analyzeCoreRules(deRules);
    let { service } = EE.app;
    this.pointConfig=service.globalConfigurationService.getDecimalPointConfig()
  }

  //用于基础数据的获取
  getValue({ type, kind, cloumn }) {
    //获取方式留给子类实现
    let value = '';
    switch (type) {
      case 'DE': {
        //基础定额
        if (typeof cloumn == 'function') {
          value = cloumn(this.baseQdOrDe);
        } else {
          value = this.baseQdOrDe[cloumn];
        }
        break;
      }
      case 'BASE_FEE_FILE_RELATION': {
        //取费文件关系
        if (typeof cloumn == 'function') {
          value = cloumn(this);
        } else {
          value = this.baseFeeFileRelationDTO[cloumn] || '';
        }
        break;
      }
      case 'FEE_FILE': {
        //取费文件
        if (typeof cloumn == 'function') {
          value = cloumn(this);
        } else {
          value = this.unitFeeFileDTO[cloumn] || '';
        }

        break;
      }
      default: {
        if (typeof cloumn == 'function') {
          value = cloumn(this);
        }
      }
    }
    return value;
  }

  //从索引添加定额
  async replace() {
    let { skip } = this.upDateInfo;
    await this.prepare();
    //代表是标准库里来的数据 不包含 安文费
    await this._fillDELineData();

    if (!skip.rcj) await this._fillRcjData();

    //处理新增定额的工程量表达式
    return this.pointLine;
  }
  convertValue(value) {
    return NumberUtil.numberScale(value, this.pointConfig.costPrice);
  }
  /**
   * 填充空行数据
   * @returns {Promise<void>}
   * @private
   */
  async _fillDELineData() {
    let { overwriteColumn, skip } = this.upDateInfo;
    this.pointLine.isSupplement = 0;
    for (const key of keysDeMap) {
      //如果配置了值覆盖
      if (overwriteColumn) {
        this.pointLine[key] = this.parseParams(key);
      } else {
        //值为空的情况下 走默认
        if (!this.pointLine[key]) {
          this.pointLine[key] = this.parseParams(key);
        }
      }

    }
    //处理工程量和工程量表达式
    if (!skip.quantity) await this.handlerQuantity();

  }


  //处理工程量 总价措施  超高  地上垂运  地下垂运  安装费
  async handlerQuantity() {
    let { is2022, constructId, singleId, unitId } = this.ctx;
    let { service } = EE.app;
    let qdLine = this.pointLine.parent;
    let unitQd = qdLine.unit;
    let unitDe = this.pointLine.unit;
    let { option } = this.upDateInfo;
    let unitNum = getDeUnitFormatEnum(unitDe).value;
    let costDe = [DePropertyTypeConstant.AZ_DE, DePropertyTypeConstant.AWF_DE, DePropertyTypeConstant.ZJCS_DE];
    if (!is2022) {
      costDe.push(DePropertyTypeConstant.CG_DE);
    }
    if (costDe.includes(this.pointLine.isCostDe)) {
      // 添加的费用定额  工程量默认为1
      this.pointLine.quantityExpression = 1;
      this.pointLine.quantityExpressionNbr = 1;
      this.pointLine.quantity = NumberUtil.numberScale((this.pointLine.quantityExpressionNbr / getUnitNum(this.pointLine)),unitNum);
    } else {
      //如果是替换逻辑 则继承之前的
      if ('replace' == option) {
        if (!ObjectUtils.isEmpty(getUnitNum(this.pointLine)) && getUnitNum(this.pointLine) != 0) {
          this.pointLine.quantity = NumberUtil.numberScale((this.pointLine.quantityExpressionNbr / getUnitNum(this.pointLine)),unitNum);
        }
        if (this.pointLine.quantityExpression == 'HSGCL') {
          this.pointLine.quantityExpression = this.pointLine.quantityExpressionNbr;
        }
      } else {
        //新增逻辑
        if (PumpingAddFeeExpressionConstant.BSHNTL_LIST.includes(this.pointLine.quantityExpression)) {
          // 泵送费的工程量使用添加的值  工程量表达式使用规定值  不做改动
          this.pointLine.quantity = NumberUtil.numberScale((this.pointLine.quantityExpressionNbr / getUnitNum(this.pointLine)),unitNum);
        } else {
          if (isSameUnit(unitQd, unitDe) || ObjectUtils.isEmpty(unitQd)) {
            this.pointLine.quantityExpression = 'QDL';
            this.pointLine.quantityExpressionNbr = qdLine.quantityExpressionNbr;
            this.pointLine.quantity = NumberUtil.numberScale((this.pointLine.quantityExpressionNbr / getUnitNum(this.pointLine)),unitNum);

          } else {
            this.pointLine.quantityExpression = 'QDL*0';
            this.pointLine.quantityExpressionNbr = 0;
            this.pointLine.quantity = 0;
          }
        }

      }
    }
    //处理工程量表达式
    if (this.pointLine.tempDeleteFlag) {
      this.pointLine.quantityExpressionTemp = this.pointLine.quantityExpression;
      this.pointLine.quantityExpression = null;
    }
  }


  /**
   * 处理定额相关的人材机
   * @returns {Promise<void>}
   * @private
   */
  async _fillRcjData() {
    let { service } = EE.app;
    let { constructId, singleId, unitId } = this.ctx;
    let insertRcjStrategy = new InsertRcjStrategy({
      constructId,
      singleId,
      unitId,
      projectObj: PricingFileFindUtils.getProjectObjById(constructId)
    });
    await insertRcjStrategy.execute({ de: this.pointLine });
  }
}


module.exports = {
  DeHandler, SupplementDeHandler
};
