<template>
  <!-- <div class="head">
    <a-button type="text" @click="insertHandle(null)">插入</a-button>
    <a-button type="text" @click="deleteHandle(null)">删除</a-button>
  </div> -->
  <div class="table-content">
    <vxe-table
      align="center"
      height="98%"
      style="width: 600px;"
      ref="featureTable"
      :loading="loading"
      :column-config="{ resizable: true }"
      :row-config="{
        isHover: true,
        isCurrent: true,
        keyField: cacheAll.rowKeyFiled,
      }"
      :data="featureData"
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
      :cell-class-name="
        ({ column, row, $columnIndex }) => {
          const selectName = selectedClassName({ $columnIndex, row, column });
          if (column.field === 'name' && row.type === 'title') {
            return 'title-bold ' + selectName;
          }
          if (column.field === 'name' && ['name7', 'name8'].includes(row.key)) {
            return 'color-red ' + selectName;
          }
          return selectName;
        }
      "
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
        showIcon: false,
        showStatus: false,
      }"
      @cell-click="
        cellData => {
          useCellClickEvent(cellData);
        }
      "
      id="sequenceNbr"
      :tree-config="{ childrenField: 'children', line: false, expandAll: true }"
      class="table-edit-common"
      keep-source
    >
      <vxe-column
        field="dispNo"
        width="50"
        align='left'
        title="序号"
        tree-node
      ></vxe-column>
      <vxe-column
        field="name"
        title="名称"
        align='left'
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            v-if="row.addFlag"
            placeholder="请输入名称"
            v-model="row.name"
            type="text"
            name="name"
            @blur="inputChange(row, $event, 'name')"
          ></vxe-input>
          <span v-else>{{ row.name }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="context"
        title="内容"
        align='left'
        :edit-render="{}"
      >
        <template #edit="{ row }">
          <!-- <a-input
              v-model:value="row.context"
              placeholder="请输入备注"
              type="text"
              name="context"
              @blur="inputChange(row, $event)"
          ></a-input> -->
          <vxeTableEditSelect
            :filedValue="row.context"
            :list="row.jsonStr"
            :isNotLimit="true"
            @update:filedValue="
              newValue => {
                saveCustomInput(newValue, row, 'context', $rowIndex);
              }
            "
          ></vxeTableEditSelect>
        </template>
      </vxe-column>
      <vxe-column
        field="remark"
        title="备注"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            v-model="row.remark"
            placeholder="请输入备注"
            type="text"
            name="remark"
            @blur="inputChange(row, $event, 'remark')"
          ></vxe-input>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script setup>
import {
  ref,
  onUpdated,
  watch,
  onMounted,
  toRaw,
  onActivated,
  getCurrentInstance,
} from 'vue';
import { message, Modal } from 'ant-design-vue';
import xeUtils from 'xe-utils';
import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@/api/csProject';
import { useCellClick } from '@/hooks/useCellClick';
import { insetBus } from '@/hooks/insetBus';
import { comBasicInfoFun } from './comBasiciInfo';

const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();

let featureTable = ref();
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;

let featureData = ref([]);
const activeKey = ref(1);
const projectStore = projectDetailStore();
const cacheAll = {
  currentRecord: null,
  treeChildrenKey: 'childrenList',
  rowKeyFiled: 'sequenceNbr',
  lockStatus: 0,
  newRecord: function () {
    return {
      sequenceNbr: Date.now(),
      name: '',
      remark: null,
      addFlag: 1,
      lockFlag: 0,
      jsonStr: '',
    };
  },
  copyRow: function (row) {
    let newRow = {};
    Object.assign(newRow, row);
    newRow.sequenceNbr = Date.now();
    newRow.childrenList = null;
    newRow.addFlag = 1;
    newRow.lockFlag = 0;

    return newRow;
  },
};
const { getPageData, delOperateFun, saveAndUpdateOperate } = comBasicInfoFun({
  activeKey,
  pageType: 'gctz',
});
function modalTip(content) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: '',
      content: content,
      onOk: () => {
        resolve(true);
      },
      onCancel() {
        resolve(false);
      },
    });
  });
}

const insertInData = (list, selectNode, newNode) => {
  const key = cacheAll.rowKeyFiled;
  const index = list.findIndex(item => item[key] === selectNode[key]) + 1;
  list.splice(index, 0, newNode);
};

const resetCurrentRow = () => {
  cacheAll.currentRecord = cacheAll.currentRecord || featureData.value[0];
  featureTable.value?.setCurrentRow(cacheAll.currentRecord);
};

async function insertHandle(row, newRow) {
  const selectRecord = row || featureTable.value?.getCurrentRecord();

  if (!selectRecord) {
    await modalTip('请选中要插入的位置');
    return;
  }

  let allData = featureData.value;
  let newRecord = newRow || cacheAll.newRecord();
  insertInData(allData, selectRecord, newRecord);
  saveOrUpdateFeature({ data: xeUtils.clone(allData, true) });
  cacheAll.currentRecord = newRecord;
  // featureTable.value.setEditRow(cacheAll.currentRecord)
  resetCurrentRow();
}

async function deleteHandle(row) {
  const selectRecord = row || featureTable.value?.getCurrentRecord();
  if (!selectRecord.addFlag) {
    //await modalTip("默认信息不能删除")
    message.warning('默认信息不能删除');
    return;
  }

  const status = await modalTip('确定要删除选中行？');
  if (!status) {
    return;
  }

  deleteRowForFeature(selectRecord[cacheAll.rowKeyFiled]);
}

const inputChange = (row, e, attr) => {
  let value = xeUtils.trim(e.value);
  if (value.length > 50) {
    value = value.slice(0, 50);
    row[attr] = value;
    message.warn('输入过长，请输入50个字符范围内');
  }
  row[attr] = value;

  saveOrUpdateFeature({ data: xeUtils.clone(featureData.value, true) }, 0);
};

const saveCustomInput = (newValue, row, name, index) => {
  console.log('newValue,row,name', newValue, row, name);
  const reg = /[^\d.]/g;
  const list = [null, undefined, ''];
  if (
    (list.includes(row[name]) && list.includes(newValue)) ||
    row[name] === newValue
  )
    return;
  if (
    newValue !== '' &&
    (row.name === '建筑面积(m、㎡)' ||
      row.name === '其中地下室基础面积' ||
      row.name === '设备管道夹层面积') &&
    reg.test(newValue)
  ) {
    message.error('请输入大于0的数值');
    featureTable.value.revertData(row, 'context');
  } else {
    row[name] = newValue;
    saveOrUpdateFeature({ data: featureData.value }, 0);
  }
  // row[name] = newValue;
  // saveOrUpdateFeature({ data: featureData.value }, 0);
};

const menuConfig = ref({
  body: {
    options: [
      [
        { code: 'insertRow', name: '插入行', disabled: false },
        { code: 'copyRow', name: '复制行', disabled: false },
        { code: 'deleteRow', name: '删除行', disabled: false },
      ],
    ],
  },
  visibleMethod({ row, type, options }) {
    const $table = featureTable.value;
    if ($table) {
      if (type === 'body') {
        options.forEach(list => {
          list.forEach(item => {
            if (item.code === 'deleteRow') {
              if (row && !row.addFlag) {
                item.disabled = true;
              } else {
                item.disabled = false;
              }
            }
          });
        });
      }
    }
    return true;
  },
});

const contextMenuClickEvent = ({ menu, row, column }) => {
  const $table = featureTable.value;
  if ($table) {
    switch (menu.code) {
      case 'insertRow':
        insertHandle(row);
        break;
      case 'copyRow':
        let newRow = cacheAll.copyRow(row);
        insertHandle(row, newRow);
        break;
      case 'deleteRow':
        deleteHandle(row);
        break;
    }
  }
};

const getEngineeringFeature = async () => {
  let res = await getPageData();
  if (res.status === 200) {
    featureData.value = res.result;
    resetCurrentRow();
  }
};

const deleteRowForFeature = async sequenceNbr => {
  let res = await delOperateFun(sequenceNbr);
  if (res.status === 200) {
    cacheAll.currentRecord = null;
    getEngineeringFeature();
    message.success('删除成功');
  }
};

// type 1 重新复制，0 不用重新复制
const saveOrUpdateFeature = async (param, type = 1) => {
  let res = await saveAndUpdateOperate(param.data);
  if (res.status === 200) message.success('操作成功');
  if (type) {
    if (res.status === 200) {
      // message.success('操作成功');
      getEngineeringFeature();
    }
  }
};

onUpdated(() => {
  console.log('*******************');
  console.log(featureData);
  resetCurrentRow();
});

watch(
  () => projectStore.asideMenuCurrentInfo,
  () => {
    console.log('工程特征');
    if (
      projectStore.asideMenuCurrentInfo?.key === '13' &&
      activeKey.value === 1
    ) {
      getEngineeringFeature();
    }
  }
);

onMounted(() => {
  getEngineeringFeature();
});
onActivated(() => {
  insetBus(bus, projectStore.componentId, 'engineerFeature', async data => {
    if (data.name === 'insert') insertHandle(null);
    if (data.name === 'delete') deleteHandle(null);
  });
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 10px;
}
.table-content {
  height: calc(100% - 40px);
}
.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }
  .color-red {
    color: #de3f3f;
  }
  &.vxe-table--render-default .vxe-tree-cell {
    padding-left: 0;
  }
}
</style>
