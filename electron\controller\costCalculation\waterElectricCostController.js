const { Controller } = require('../../../core');
const { ResponseData } = require('../../utils/ResponseData');
const { ObjectUtil } = require('../../../common/ObjectUtil');


class WaterElectricCostController extends Controller {


  /**
   * 构造函数
   * @param ctx
   */
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 获取水电费列表数据
   */
  async getWaterElectricCostData(args) {
    return ResponseData.success(await this.service.waterElectricCostMatchService.getWaterElectricCostData(args));
  }

  /**
   * 水电费页面临时更新水电费数据
   */
  async updateWaterElectricCostData(args) {
    if (ObjectUtil.isEmpty(args.waterElectricCostData)) {
      return ResponseData.fail('参数错误');
    }
    return ResponseData.success(await this.service.waterElectricCostMatchService.updateWaterElectricCostData(args));
  }

  /**
   * 保存水电费数据
   */
  async saveWaterElectricCostData(args) {
    return ResponseData.success(await this.service.waterElectricCostMatchService.saveWaterElectricCostData(args));
  }

}

WaterElectricCostController.toString = () => '[class WaterElectricCostController]';
module.exports = WaterElectricCostController;