'use strict';

const {Service} = require("../../../core");
const {ResponseData} = require("../../utils/ResponseData");
const {PricingFileFindUtils} = require("../../utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../utils/ObjectUtils");
const {BaseCarryingPriceInformation} = require("../../model/BaseCarryingPriceInformation");
const {ArrayUtil} = require("../../utils/ArrayUtil");
const {ConstructProjectRcj} = require("../../model/ConstructProjectRcj");
const {ConvertUtil} = require("../../utils/ConvertUtils");
const {RcjDetails} = require("../../model/RcjDetails");
const TaxCalculationMethodEnum = require("../../enum/TaxCalculationMethodEnum");
const {NumberUtil} = require("../../utils/NumberUtil");
const {HttpUtils} = require("../../utils/HttpUtils");
const LoadPriceTypeEnum = require("../../enum/LoadPriceTypeEnum");
const BsRemoteUrl = require("../../enum/BsRemoteUrl");
const RcjLevelMarkConstant = require("../../enum/RcjLevelMarkConstant");
const BranchProjectDisplayConstant = require("../../enum/BranchProjectDisplayConstant");
const {LoadPriceComparison} = require("../../model/loadPrice/LoadPriceComparison");
const {arrayToTree,treeToArray}= require("../../main_editor/tree.js");
const rcjTypeTree = require("../../jsonData/rcjTypeTree.json");
const ConstantUtil = require("../../enum/ConstantUtil");
const {UnitRcjCacheUtil} = require("../../rcj_handle/cache/UnitRcjCacheUtil");
const { ConstructRcjAvgRule } = require('../../model/ConstructRcjAvgRule');
const { ObjectUtil } = require('../../../common/ObjectUtil');

/**
 * 示例服务
 * @class
 */
class LoadPriceSetService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    //去重
     removeDuplicates(arr) {
        const seen = new Set();
        return arr.filter(item => {
            const key = JSON.stringify([item.cityName, item.fileDate, item.dataType]); // 将需要对比的多个属性组合成一个唯一的 key
            if (seen.has(key)) {
                return false;
            } else {
                seen.add(key);
                return true;
            }
        });
    }



    /**
     * 获取载价设置初始化数据
     * @param arg
     * @return {Promise<ResponseData>}
     */
    async queryLoadPriceAreaDate(arg) {
        let {constructId, singleId, unitId} = arg;
        let rgfId = null;
        //工程项目
        if (ObjectUtils.isEmpty(unitId)) {
            let project = PricingFileFindUtils.getProjectObjById(constructId);
            rgfId = project.rgfId;
        } else {
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            rgfId = unit.rgfId;
        }
        //查询人材机政策文件
        let promise = {};

        if (!ObjectUtils.isEmpty(rgfId)) {
            promise = await this.service.basePolicyDocumentService.queryBySequenceNbr(rgfId);//cl
        }else {
            promise.cityName = "石家庄市";
        }
        let allLoadPrice = await this.queryAllLoadPriceData();
        //冀外地区去重
        allLoadPrice = this.removeDuplicates(allLoadPrice);

        //根据价格类别分组
        let priceType = ArrayUtil.group(allLoadPrice, "dataTypeName");
        let keysArray = Array.from(priceType.keys());
        let result = {};
        for (const key of keysArray) {
            let areaMap = {};
            let priceMap = priceType.get(key);
            //根据地区分组
            let areaType = ArrayUtil.group(priceMap, "cityName");
            let areaKeysArray = Array.from(areaType.keys());
            if (key !== '推荐价'){
                //根据政策文件的地区展示默认
                areaMap[promise.cityName] = areaType.get(promise.cityName).sort((a, b) => {
                    const dateA = new Date(a.fileDate.replace('年', '-').replace('月', ''));
                    const dateB = new Date(b.fileDate.replace('年', '-').replace('月', ''));
                    return dateB - dateA;
                }).map(k => k.fileDate);
            }
            for (const areaKey of areaKeysArray) {
                // if (areaKey === '冀外地区' && ['推荐价','信息价'].includes(key)){
                //     continue;
                // }
                if (areaKey === promise.cityName) {
                    continue;
                }
                let v = areaType.get(areaKey);
                //需要根据日期进行倒序排列
                v.sort((a, b) => {
                    const dateA = new Date(a.fileDate.replace('年', '-').replace('月', ''));
                    const dateB = new Date(b.fileDate.replace('年', '-').replace('月', ''));
                    return dateB - dateA;
                });
                areaMap[areaKey] = v.map(k => k.fileDate);
            }
            result[key] = areaMap;
        }
        return result;
    }

    /**
     * 查询所有的载价信息表
     * @param arg
     * @return {Promise<void>}
     */
    async queryAllLoadPriceData() {
        let result = await this.app.appDataSource.getRepository(BaseCarryingPriceInformation).find();
        return result;
    }



    /**
     * 根据条件查询所有的载价信息表
     * @param arg
     * @return {Promise<void>}
     */
    async queryLoadPriceDataByCityName(cityName) {
        let loadPrice = await this.app.appDataSource.getRepository(BaseCarryingPriceInformation).find({
            where: {
                cityName: cityName
            }
        });
        //根据价格类别分组
        let priceType = ArrayUtil.group(loadPrice, "dataTypeName");
        let keysArray = Array.from(priceType.keys());
        let result = {};
        for (const key of keysArray) {
            let priceList = priceType.get(key);
            //需要根据日期进行倒序排列
            priceList.sort((a, b) => {
                const dateA = new Date(a.fileDate.replace('年', '-').replace('月', ''));
                const dateB = new Date(b.fileDate.replace('年', '-').replace('月', ''));
                return dateB - dateA;
            });
            let {fileDate} = priceList[0];
            if (key === '推荐价'){
                result.recommend = fileDate;
            }
            if (key === '信息价'){
                result.information = fileDate;
            }
            if (key === '市场价'){
                result.market = fileDate;
            }
        }
        return result;
    }



    /**
     * 获取远程的人材机数据
     */
    async getRemoteRcjData(args) {
        //type 1 = 工程项目  2 单项工程 3单位工程
        //batchAllFlag,是否批量调整所有价格
        //loadPriortyList 载价优先级设置 1信息价 2市场价  3推荐价  ，如果选择为空传0
        //laodPriceConditionList 载价条件  默认还是 1信息价 2市场价  3推荐价
        let {constructId, singleId, unitId, type, batchAllFlag, loadPriortyList, laodPriceConditionList,deStandardReleaseYear} = args;
        //计税方式
        let simpleMethod = PricingFileFindUtils.getConstructProjectTaxCalculationMethod(constructId);
        let is22 =null;
        if (type == 1 || 2){
            is22 = deStandardReleaseYear=="22"?true:false;
        }else {
            is22 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        }

        if (is22 == false){
            simpleMethod = true;
        }

        //type 1 = 工程项目  2 单项工程 3单位工程
        let rcjData = await this.loadPriceRcjData(type, 0, constructId, singleId, unitId, batchAllFlag);
        //人材机list
        let rcjs = [];
        if (!ObjectUtils.isEmpty(rcjData)) {
            for (const rcj of rcjData) {
                let {standardId, materialName} = rcj;
                rcjs.push({id: standardId, name: materialName});
            }
        }
        //信息价
        let informationPriceList = laodPriceConditionList[0];
        let informationPriceListNew = [];
        for (const item of informationPriceList) {
            for (const key in item) {
                informationPriceListNew.push({areaName: key, yearMonths: item[key]})
            }
        }
        //市场价
        let marketPriceList = laodPriceConditionList[1];
        let marketPriceListNew = [];
        for (const item of marketPriceList) {
            for (const key in item) {
                marketPriceListNew.push({areaName: key, yearMonths: item[key]})
            }
        }

        //推荐价
        let recommendPriceList = laodPriceConditionList[2];
        let recommendPriceListNew = [];
        for (const item of recommendPriceList) {
            for (const key in item) {
                recommendPriceListNew.push({areaName: key, yearMonths: item[key]})
            }
        }
        let params = {
            simpleMethod:simpleMethod,
            rcjs: rcjs,
            informationPriceList: informationPriceListNew,
            marketPriceList: marketPriceListNew,
            recommendPriceList: recommendPriceListNew
        };
        //调用远程接口
        let promise = await HttpUtils.POST(BsRemoteUrl.loadPriceAcquire, params);
        let result = JSON.parse(promise.result);
        return result;
    }


    /**
     * 批量载价前的人材机数据查询
     * @param type
     * @param kind
     * @param constructId
     * @param singleId
     * @param unitId
     * @param isCheck 是否勾选批量调整所有价格
     */
    async loadPriceRcjData(type, kind, constructId, singleId, unitId, isCheck) {

        let rcjData = this.queryConstructRcjByDeIdNew(type, kind, constructId, singleId, unitId);

        if (ObjectUtils.isEmpty(rcjData)) {
            return null;
        }
        //除单位为元、单位为%，已锁定市场价、已勾选是否汇总/二次解析的父级材料以外的
        rcjData = rcjData.filter(k => k.unit !== '元' && k.unit !== '%').filter(k => k.ifLockStandardPrice !== 1)
            .filter(k=>k.supplementDeRcjFlag !== 1)
            .filter(k => (k.markSum == 1 && (k.levelMark != 1 && k.levelMark != 2)) ||(k.markSum == 0 && (k.levelMark == 1 || k.levelMark == 2)))

        if (!isCheck){
            //没勾的时候过滤  加上价格来源为自行询价条件
            rcjData = rcjData.filter(k => k.sourcePrice !== "自行询价");
        }

        return rcjData;
    }


    /**
     * 查询人材机汇总 载价查询专用版
     * @param type 数据类型 1 工程项目层级  2 单项工程 3单位工程
     * @param kind 人材机类型 0所有人才机  1：人工；2：材料；3：机械；4：设备；5：主材； 6 预拌混凝土 7 主要材料.设备
     * @param constructId 工程项目id
     * @param singleId 单项工程id
     * @param unitId 单位工程id
     */
    queryConstructRcjByDeIdNew(type, kind, constructId, singleId, unitId) {
        let list
        if (type == 3) {
            //单位工程
            list = this.unitConstructProjectRcjQuery(constructId, singleId, unitId, kind);
        } else if (type == 1) {
            //工程项目
            list = this.constructProjectRcjQuery(constructId, kind);
        } else if (type == 2){
            //单项工程
            if (ObjectUtils.isEmpty(singleId)){
                return null;
            }
            list = this.constructProjectRcjQuery(constructId, kind,singleId);
        }

        list  = list.filter(i=>i.isSupplement != 1)

        return list;
    }

    //kind 人材机类型 0所有人才机  1：人工；2：材料；3：机械；4：设备；5：主材； 6 预拌混凝土 7 主要材料.设备
    unitConstructProjectRcjQuery(constructId, singleId, unitId, kind) {
        let constructRcjArray = new Array();
        let rcjMingxi = new Array();
        //获取单位工程
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        if (ObjectUtils.isEmpty(unit)) {
            return null;
        }


        if (!ObjectUtils.isEmpty(unit.constructProjectRcjs)) {
            for (let constructProjectRcj of unit.constructProjectRcjs) {

                if (constructProjectRcj.supplementDeRcjFlag == 1) {
                    constructProjectRcj.edit = 1;
                }

                this.rcjAddEdit(constructProjectRcj, unit);
                let constructProjectRcj1 = new ConstructProjectRcj();
                ConvertUtil.setDstBySrc(constructProjectRcj, constructProjectRcj1)
                if (constructProjectRcj1.ifDonorMaterial == undefined ||constructProjectRcj1.ifDonorMaterial == null ) {
                    constructProjectRcj1.ifDonorMaterial = 0;
                }
                constructRcjArray.push(constructProjectRcj1)
            }

        }
        if (!ObjectUtils.isEmpty(unit.rcjDetailList)) {
            for (let rcjDetail of unit.rcjDetailList) {

                if (rcjDetail.supplementDeRcjFlag == 1) {
                    rcjDetail.edit = 1;
                }
                let rcjDetails = new RcjDetails();
                ConvertUtil.setDstBySrc(rcjDetail, rcjDetails)
                if (rcjDetails.ifDonorMaterial == undefined ||rcjDetails.ifDonorMaterial == null ) {
                    rcjDetails.ifDonorMaterial = 0;
                }
                rcjMingxi.push(rcjDetails);
            }
        }


        let ts1 = constructRcjArray.filter(i => i.markSum === 1);
        if (!ObjectUtils.isEmpty(ts1)) {
            for (let t of ts1) {
                let ts2 = rcjMingxi.filter(i => i.rcjId === t.sequenceNbr);

                if (!ObjectUtils.isEmpty(ts2)) {
                    for (let t1 of ts2) {
                        let constructProjectRcj = new ConstructProjectRcj();
                        ConvertUtil.setDstBySrc(t1, constructProjectRcj);
                        constructRcjArray.push(constructProjectRcj);
                    }
                }
            }
        }


        //拼接相同材料
        if (!ObjectUtils.isEmpty(constructRcjArray)) {
            for (let arrayElement of constructRcjArray) {
                arrayElement.tempcol = arrayElement.materialCode.concat(!ObjectUtils.isEmpty(arrayElement.materialName)?arrayElement.materialName:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.specification)?arrayElement.specification:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.unit)?arrayElement.unit:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.dePrice)?arrayElement.dePrice:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.markSum)?arrayElement.markSum:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.ifDonorMaterial)?arrayElement.ifDonorMaterial:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.ifProvisionalEstimate)?arrayElement.ifProvisionalEstimate:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.marketPrice)?arrayElement.marketPrice:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.ifLockStandardPrice)?arrayElement.ifLockStandardPrice:"");
            }
        } else {
            return null;
        }

        //分组
        const grouped = constructRcjArray.reduce((accumulator, currentValue) => {
            // 将分组作为对象的 key，相同分组的项放入同一个数组
            (accumulator[currentValue.tempcol] = accumulator[currentValue.tempcol] || []).push(currentValue);
            return accumulator;
        }, {});

        //工程项目
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

        //计税对象
        let projectTaxCalculation = projectObjById.projectTaxCalculation;

        //计税方式
        let taxCalculationMethod = projectTaxCalculation.taxCalculationMethod;
        let simple = false;
        //简易计税
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            simple = true;
        }


        //循环分组之后的人材机
        let array1 = new Array();
        for (let group in grouped) {
            if (grouped.hasOwnProperty(group)) {
                let groupedElement = grouped[group][0];
                let number = 0;
                grouped[group].forEach(item => {
                    number = NumberUtil.add(number, item.totalNumber)
                });
                groupedElement.totalNumber = NumberUtil.numberScale(number, 4);


                groupedElement.rcjDetailsDTOs = null;
                groupedElement.type = this.service.baseRcjService.getRcjTypeEnumDescByCode(groupedElement.kind);
                groupedElement.total = NumberUtil.multiplyToString(number, groupedElement.marketPrice, 2);
                groupedElement.priceDifferenc = NumberUtil.subtract(groupedElement.marketPrice, groupedElement.dePrice);
                groupedElement.priceDifferencSum = NumberUtil.multiplyToString(groupedElement.priceDifferenc, groupedElement.totalNumber, 2);

                //计税判断
                if (!simple) {
                    //一般
                    let decimal = NumberUtil.multiplyToString(groupedElement.taxRemoval, 0.01);
                    groupedElement.jxTotal = NumberUtil.multiplyToString(groupedElement.total, decimal, 2);
                } else {

                    //简易
                    groupedElement.jxTotal = 0;

                }

                array1.push(groupedElement);
            }
        }

        //处理 查询 主要材料 设备表逻辑
        if (kind === 7) {
            let ts7 = array1.filter(i => i.kind == 2 || i.kind == 5 || i.kind == 6 || i.kind == 7 || i.kind == 8 || i.kind == 9 || i.kind == 10);
            let array = new Array();
            if (!ObjectUtils.isEmpty(ts7)) {
                ts7.sort((a, b) => b.totalNumber - a.totalNumber);
                if (ts7.length <= 50) {
                    array = ts7;
                } else {
                    array = ts7.slice(0, 50);
                }
            }

            let ts8 = array1.filter(i => i.kind == 4);

            if (!ObjectUtils.isEmpty(ts8)) {
                ts8.sort((a, b) => b.totalNumber - a.totalNumber);
                if (ts8.length <= 50) {
                    array = array.concat(ts8);
                } else {
                    array = array.concat(ts8.slice(0, 50));
                }
            }

            array1 = array;

        }

        //处理市场价 定额价不一致时 颜色问题
        //this.constructProjectRcjColour(array1);
        //机械费 组合
        let ts = array1.filter(i => i.kind === 3);
        //非机械费 非父级材料
        let ts3 = array1.filter(i => i.kind !== 3);
        ts3.sort((a, b) => {
            if (a.kind === b.kind) {
                return a.materialCode.localeCompare(b.materialCode)
            }
            return a.kind - b.kind;
        });

        ts.sort((a, b) => a.kind - b.kind);
        ts3 = ts3.concat(ts);

        let ts4 = ts3.filter(i => i.markSum === 1 && (i.levelMark === RcjLevelMarkConstant.SINK_PB  || i.levelMark === RcjLevelMarkConstant.SINK_JX ));
        let ts5 = ts3.filter(i => !(i.markSum === 1 && (i.levelMark === RcjLevelMarkConstant.SINK_PB  || i.levelMark === RcjLevelMarkConstant.SINK_JX )));
        let ts6 = ts5.concat(ts4);

        //根据类型筛选
        if (!ObjectUtils.isEmpty(kind)) {
            if (kind !== 0 && kind != 7) {
                if (kind === 1 || kind === 3 || kind === 4 || kind === 5 || kind === 6) {
                    return ts6.filter(i => i.kind == kind);
                } else if (kind === 2) {
                    return ts6.filter(i => i.kind == 2 || i.kind == 5 || i.kind == 6 || i.kind == 7 || i.kind == 8 || i.kind == 9 || i.kind == 10);
                } else if (kind === 8) {
                    return ts6.filter(i => i.ifProvisionalEstimate === 1);
                } else if (kind === 9) {
                    return ts6.filter(i => i.ifDonorMaterial === 1);
                } else if (kind === 10) {
                    return null;
                } else {
                    return null;
                }
            }
        }
        //let countRcjList = this.getCountRcjList(constructId, singleId, unitId);
        return ts6;
    }

    constructProjectRcjQuery(constructId, kind,singleId) {

        let constructRcjArray = new Array();
        let rcjMingxi = new Array();
        let unitList
        //查询工程项目汇总
        if (ObjectUtils.isEmpty(singleId)){
            unitList =  PricingFileFindUtils.getUnitList(constructId);
        }else {
            unitList = PricingFileFindUtils.getUnitList(constructId).filter(i=>i.spId == singleId);
        }

        for (let unitListKey of unitList) {
            if (!ObjectUtils.isEmpty(unitListKey.constructProjectRcjs)) {

                for (let constructProjectRcj of unitListKey.constructProjectRcjs) {

                    if (constructProjectRcj.supplementDeRcjFlag == 1) {
                        constructProjectRcj.edit = 1;
                    }

                    this.rcjAddEdit(constructProjectRcj, unitListKey);
                    let constructProjectRcj1 = new ConstructProjectRcj();

                    ConvertUtil.setDstBySrc(constructProjectRcj, constructProjectRcj1)
                    if (constructProjectRcj1.ifDonorMaterial == undefined ||constructProjectRcj1.ifDonorMaterial == null) {
                        constructProjectRcj1.ifDonorMaterial = 0;
                    }
                    this._materialDispose(constructProjectRcj1);
                    constructRcjArray.push(constructProjectRcj1)

                }
            }
            if (!ObjectUtils.isEmpty(unitListKey.rcjDetailList)) {

                for (let rcjDetail of unitListKey.rcjDetailList) {


                    if (rcjDetail.supplementDeRcjFlag == 1) {
                        rcjDetail.edit = 1;
                    }

                    let rcjDetails = new RcjDetails();
                    /*if (rcjDetail.ifDonorMaterial == 0) {
                        rcjDetail.ifDonorMaterial = null;
                    }*/

                    ConvertUtil.setDstBySrc(rcjDetail, rcjDetails)
                    if (rcjDetails.ifDonorMaterial == undefined || rcjDetails.ifDonorMaterial == null) {
                        rcjDetails.ifDonorMaterial = 0;
                    }
                    this._materialDispose(rcjDetails);
                    rcjMingxi.push(rcjDetails);

                }
            }
        }

        let ts1 = constructRcjArray.filter(i => i.markSum === 1);
        if (!ObjectUtils.isEmpty(ts1)) {
            for (let t of ts1) {
                let ts2 = rcjMingxi.filter(i => i.rcjId === t.sequenceNbr);

                if (!ObjectUtils.isEmpty(ts2)) {
                    for (let t1 of ts2) {
                        let constructProjectRcj = new ConstructProjectRcj();
                        ConvertUtil.setDstBySrc(t1, constructProjectRcj);
                        constructRcjArray.push(constructProjectRcj);
                    }
                }
            }
        }


        //拼接相同材料
        if (!ObjectUtils.isEmpty(constructRcjArray)) {
            for (let arrayElement of constructRcjArray) {

                arrayElement.tempcol = arrayElement.materialCode.concat(!ObjectUtils.isEmpty(arrayElement.materialName)?arrayElement.materialName:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.specification)?arrayElement.specification:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.unit)?arrayElement.unit:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.dePrice)?arrayElement.dePrice:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.markSum)?arrayElement.markSum:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.ifDonorMaterial)?arrayElement.ifDonorMaterial:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.ifProvisionalEstimate)?arrayElement.ifProvisionalEstimate:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.marketPrice)?arrayElement.marketPrice:"")
                    .concat(!ObjectUtils.isEmpty(arrayElement.ifLockStandardPrice)?arrayElement.ifLockStandardPrice:"");

            }
        } else {
            return null;
        }

        //分组
        const grouped = constructRcjArray.reduce((accumulator, currentValue) => {
            // 将分组作为对象的 key，相同分组的项放入同一个数组
            (accumulator[currentValue.tempcol] = accumulator[currentValue.tempcol] || []).push(currentValue);
            return accumulator;
        }, {});

        //工程项目
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

        //计税对象
        let projectTaxCalculation = projectObjById.projectTaxCalculation;

        //计税方式
        let taxCalculationMethod = projectTaxCalculation.taxCalculationMethod;

        let simple = false;
        //简易计税
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            simple = true;
        }

        //循环分组之后的人材机
        let array1 = new Array();
        for (let group in grouped) {
            if (grouped.hasOwnProperty(group)) {
                let groupedElement = grouped[group][0];
                let number = 0;
                grouped[group].forEach(item => {
                    number = NumberUtil.add(number, item.totalNumber)
                });
                groupedElement.totalNumber = NumberUtil.numberScale(number, 4);

                groupedElement.rcjDetailsDTOs = null;
                groupedElement.type = this.service.baseRcjService.getRcjTypeEnumDescByCode(groupedElement.kind);
                groupedElement.total = NumberUtil.multiplyToString(number, groupedElement.marketPrice, 2);
                groupedElement.priceDifferenc = NumberUtil.subtract(groupedElement.marketPrice, groupedElement.dePrice);
                groupedElement.priceDifferencSum = NumberUtil.multiplyToString(groupedElement.priceDifferenc, groupedElement.totalNumber, 2);

                //计税判断
                if (!simple) {
                    //一般
                    let decimal = NumberUtil.multiplyToString(groupedElement.taxRemoval, 0.01);
                    groupedElement.jxTotal = NumberUtil.multiplyToString(groupedElement.total, decimal, 2);
                } else {
                    //简易
                    groupedElement.jxTotal = 0;

                }

                array1.push(groupedElement);
            }
        }

        //处理市场价 定额价不一致时 颜色问题
        //this.constructProjectRcjColour(array1);
        //机械费 组合
        let ts = array1.filter(i => i.kind === 3);
        //非机械费 非父级材料
        let ts3 = array1.filter(i => i.kind !== 3);
        ts3.sort((a, b) => {
            if (a.kind === b.kind) {
                return a.materialCode.localeCompare(b.materialCode)
            }
            return a.kind - b.kind;
        });

        ts.sort((a, b) => a.kind - b.kind);
        ts3 = ts3.concat(ts);

        let ts4 = ts3.filter(i => i.markSum === 1 && (i.levelMark === RcjLevelMarkConstant.SINK_PB || i.levelMark === RcjLevelMarkConstant.SINK_JX ));
        let ts5 = ts3.filter(i => !(i.markSum === 1 && (i.levelMark === RcjLevelMarkConstant.SINK_PB || i.levelMark === RcjLevelMarkConstant.SINK_JX )));
        let ts6 = ts5.concat(ts4);

        //根据类型筛选
        if (!ObjectUtils.isEmpty(kind)) {
            if (kind !== 0) {
                if (kind === 1 || kind === 3 || kind === 4 || kind === 5 || kind === 6) {
                    return ts6.filter(i => i.kind == kind);
                } else if (kind === 2) {
                    return ts6.filter(i => i.kind == 2 || i.kind == 5 || i.kind == 6 || i.kind == 7 || i.kind == 8 || i.kind == 9 || i.kind == 10);
                } else if (kind === 7) {
                    let ts7 = ts6.filter(i => i.kind == 2 || i.kind == 5 || i.kind == 6 || i.kind == 7 || i.kind == 8 || i.kind == 9 || i.kind == 10);
                    ts7.sort((a, b) => b.totalNumber - a.totalNumber);
                    if (ts7.length <= 100) {
                        return ts7;
                    } else {
                        return ts7.slice(0, 100);
                    }
                } else if (kind === 8) {
                    return ts6.filter(i => i.ifProvisionalEstimate === 1);
                } else if (kind === 9) {
                    return ts6.filter(i => i.ifDonorMaterial === 1);
                } else if (kind === 10) {
                    return null;
                } else {
                    return null;
                }
            }
        }
        //let countRcjList = this.getCountRcjList(constructId, singleId, unitId);
        return ts6;

    }

    /**
     * 处理编码
     * @param constructProjectRcj 人材机对象
     */
    _materialDispose(constructProjectRcj) {

        if (constructProjectRcj.hasOwnProperty("materialCode")) {
            if (constructProjectRcj.materialCode.includes("#")) {
                constructProjectRcj.materialCode = constructProjectRcj.materialCode.substring(0, constructProjectRcj.materialCode.lastIndexOf("#"));
            }
        }
    }


    /**
     * 标记人材机是否是 费用定额下
     * @param rcj 人材机父级对象
     * @param unit 单位工程对象
     */
    rcjAddEdit(rcj, unit) {
        let deId = rcj.deId;

        let find = {};
        let itemBillProjects = unit.itemBillProjects;
        find = itemBillProjects.find(i => i.sequenceNbr === deId);
        //费用定额判断
        if (ObjectUtils.isEmpty(find)) {
            let measureProjectTables = unit.measureProjectTables;
            find = measureProjectTables.find(i => i.sequenceNbr === deId);
        }

        if (!ObjectUtils.isEmpty(find)) {

            if (find.isCostDe !== 0 && find.isCostDe !== 4 && find.isStandard === 1) {
                rcj.edit = 1;

                if (rcj.levelMark === RcjLevelMarkConstant.SINK_PB  || rcj.levelMark === RcjLevelMarkConstant.SINK_JX) {
                    let rcjDetailList = unit.rcjDetailList;
                    if (!ObjectUtils.isEmpty(rcjDetailList)) {
                        for (let rcjDetailListElement of rcjDetailList) {

                            rcjDetailListElement.edit = 1;
                        }
                    }
                }
            }
        }

    }

    /**
     * 点击批量载价 返回载价编辑弹窗数据
     * @returns {Promise<void>}
     */
    async loadingPrice(args){
        //type 1 = 工程项目  2 单项工程 3单位工程
        //batchAllFlag,是否批量调整所有价格
        //loadPriortyList 载价优先级设置 1信息价 2市场价  3推荐价  ，如果选择为空传0
        //laodPriceConditionList 载价条件  默认还是 1信息价 2市场价  3推荐价
        let {constructId, singleId,unitId,type,batchAllFlag,loadPriortyList,laodPriceConditionList} = args;

        //远程获取的人材机数据
        let remoteRcjData = await this.getRemoteRcjData(args);
        let rcjData = await this.loadPriceRcjData(type, 0, constructId, singleId, unitId, batchAllFlag);

        for (const item of rcjData) {
            let filter = remoteRcjData.filter(itemRemote => itemRemote.id== item.standardId)[0];
            item.marketPriceBeforeLoading = item.marketPrice;
            item.marketSourcePriceBeforeLoading = item.sourcePrice;

            item.informationPrice = filter.informationPrice;
            item.recommendPrice = filter.recommendPrice;
            item.marketPrice = filter.marketPrice;
            item.informationSourcePrice = filter.informationSourcePrice;
            item.marketSourcePrice = filter.marketSourcePrice;
            item.recommendSourcePrice = filter.recommendSourcePrice;

            //原始的精准数据备份  用于优先级调整后
            item.marketPriceOrigin = filter.marketPrice;
            item.marketSourcePriceOrigin = filter.marketSourcePrice;//市场价价格来源
            item.recommendPriceOrigin = filter.recommendPrice;
            item.recommendSourcePriceOrigin = filter.recommendSourcePrice;
            item.informationPriceOrigin = filter.informationPrice;
            item.informationSourcePriceOrigin = filter.informationSourcePrice;

            item.informationPriceList = filter.informationPriceList;
            item.marketPriceList = filter.marketPriceList;
            item.recommendPriceList = filter.recommendPriceList;

            //挂待载价格
            await this.updateLoadPriceByLevel(loadPriortyList,item,0);

            //以下处理用于 有精准匹配 且匹配数据只有一条时 前端的放大镜不进行展示
            if (item.highlight) {
                if (item.informationPriceList!=null && item.informationPriceList.length == 1) {
                    item.informationPriceList = null;
                }
                if (item.marketPriceList !=null && item.marketPriceList.length == 1) {
                    item.marketPriceList = null;
                }
                if (item.recommendPriceList !=null && item.recommendPriceList.length == 1) {
                    item.recommendPriceList = null;
                }
            }
            item.isExecuteLoadPrice = true;
        }

        if (type == 3) {  //单位工程
            let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
            unit.unitRcjsLoading = rcjData;
        }else if (type == 1) {
            let project = PricingFileFindUtils.getProjectObjById(constructId);
            project.projectRcjsLoading = rcjData;
        }else if (type == 2){
            let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);
            singleProject.singleRcjsLoading = rcjData;
        }

        return rcjData.map(item => item.sequenceNbr);
        //查询人材机数据
        //调用接口得到精准、模糊数据
        //根据条件  更新人材机的待载价格、价格来源、信息价、市场价、推荐价、是否勾选全部置1
        //统计载入前后价格

    }


    /**
     * 根据载价优先级的设置 更改待载价格
     * @returns {Promise<void>}
     */
    //1信息价 2市场价  3推荐价
    async updateLoadPriceByLevel(loadPriortyList,rcjData,index) {

        if (loadPriortyList[index] == 0 && index !=2) {  //前两个选项都为空 走这
            return await this.updateLoadPriceByLevel(loadPriortyList,rcjData,index+1);
        };
        if ((loadPriortyList[index] == 0 && index == 2)||index==3) { //如果走到这里  取原来人材机的市场价
            if (rcjData.informationPrice!=null) {
                if (loadPriortyList[0] == 1 || (loadPriortyList[0] == 0 && loadPriortyList[1] == 1) || (loadPriortyList[0] == 0 && loadPriortyList[1] == 0 && loadPriortyList[2] == 1)) {
                    rcjData.loadPrice = rcjData.informationPrice;
                    rcjData.sourcePrice = rcjData.informationSourcePrice;
                    rcjData['highlight'] = true;
                    return ;
                }
            }
            if (rcjData.recommendPrice!=null) {
                if (loadPriortyList[0] == 3 || (loadPriortyList[0] == 0 && loadPriortyList[1] == 3) || (loadPriortyList[0] == 0 && loadPriortyList[1] == 0 && loadPriortyList[2] == 3)) {
                    rcjData.loadPrice = rcjData.recommendPrice;
                    rcjData.sourcePrice = rcjData.recommendSourcePrice;
                    rcjData['highlight'] = true;
                    return ;
                }
            }
            if (rcjData.marketPrice!=null) {
                if (loadPriortyList[0] == 2 || (loadPriortyList[0] == 0 && loadPriortyList[1] == 2) || (loadPriortyList[0] == 0 && loadPriortyList[1] == 0 && loadPriortyList[2] == 2)) {
                    rcjData.loadPrice = rcjData.marketPrice;
                    rcjData.sourcePrice = rcjData.marketSourcePrice;
                    rcjData['highlight'] = true;
                    return ;
                }
            }
            rcjData.loadPrice = rcjData.marketPriceBeforeLoading;
            rcjData.sourcePrice = rcjData.marketSourcePriceBeforeLoading;
            rcjData['highlight'] = false;
            return;
        }
        if (loadPriortyList[index] == 1) { //如果为信息价
            if (rcjData.informationPriceOrigin != null) {
                rcjData.loadPrice = rcjData.informationPriceOrigin;
                rcjData.sourcePrice = rcjData.informationSourcePriceOrigin;

                //--STRAT---数据初始化--------------------------
                // rcjData.informationPrice = rcjData.informationPriceOrigin;
                // rcjData.recommendPrice = rcjData.recommendPriceOrigin;
                // rcjData.marketPrice = rcjData.marketPriceOrigin;
                //
                // rcjData.informationSourcePrice = rcjData.informationSourcePriceOrigin;
                // rcjData.marketSourcePrice = rcjData.marketSourcePriceOrigin;
                // rcjData.recommendSourcePrice = rcjData.recommendSourcePriceOrigin;
                //--END---数据初始化--------------------------
                rcjData['highlight'] = true;
                return ;
            }else {
                return await this.updateLoadPriceByLevel(loadPriortyList,rcjData,index+1);
            }
        }
        if (loadPriortyList[index] == 2) {  //如果为市场价
            if (rcjData.marketPriceOrigin != null) {
                rcjData.loadPrice = rcjData.marketPriceOrigin;
                rcjData.sourcePrice = rcjData.marketSourcePriceOrigin;

                //--STRAT---数据初始化--------------------------
                // rcjData.informationPrice = rcjData.informationPriceOrigin;
                // rcjData.recommendPrice = rcjData.recommendPriceOrigin;
                // rcjData.marketPrice = rcjData.marketPriceOrigin;
                //
                // rcjData.informationSourcePrice = rcjData.informationSourcePriceOrigin;
                // rcjData.marketSourcePrice = rcjData.marketSourcePriceOrigin;
                // rcjData.recommendSourcePrice = rcjData.recommendSourcePriceOrigin;
                //--END---数据初始化--------------------------
                rcjData['highlight'] = true;
                return ;
            }else {
                return await this.updateLoadPriceByLevel(loadPriortyList,rcjData,index+1);
            }
        }
        if (loadPriortyList[index] == 3) {  //如果为推荐价
            if (rcjData.recommendPriceOrigin != null) {
                rcjData.loadPrice = rcjData.recommendPriceOrigin;
                rcjData.sourcePrice = rcjData.recommendSourcePriceOrigin;

                //--STRAT---数据初始化--------------------------
                // rcjData.informationPrice = rcjData.informationPriceOrigin;
                // rcjData.recommendPrice = rcjData.recommendPriceOrigin;
                // rcjData.marketPrice = rcjData.marketPriceOrigin;
                //
                // rcjData.informationSourcePrice = rcjData.informationSourcePriceOrigin;
                // rcjData.marketSourcePrice = rcjData.marketSourcePriceOrigin;
                // rcjData.recommendSourcePrice = rcjData.recommendSourcePriceOrigin;
                //--END---数据初始化--------------------------
                rcjData['highlight'] = true;
                return ;
            }else {
                return await this.updateLoadPriceByLevel(loadPriortyList,rcjData,index+1);
            }
        }
    }

    /**
     * 双击价格选择弹窗的数据行 更新待载价格
     * @param rcjId
     * @param popUpDataId
     * @param updatePrice
     * @returns {Promise<void>}
     */
    async updateLoadPrice(args){
        let {constructId, singleId,unitId,type,rcjId,popUpDataId,updatePrice,sourcePrice,loadPrice,vagueSourcePrice} = args;
        let rcjDatas = await this.getCurrentLoadingRcjs(constructId,singleId,unitId,type);
        let data = rcjDatas.filter(item => item.sequenceNbr==rcjId);

        if (popUpDataId == null && sourcePrice != null && updatePrice != null) { //表示双击价格
            data[0].loadPrice = updatePrice;
            data[0].sourcePrice = sourcePrice;
            data[0].highlight = true;
            return ;
        }

        if (popUpDataId == null && !ObjectUtils.isEmpty(loadPrice) ){
            data[0].loadPrice = loadPrice;
            data[0].sourcePrice = "自行询价";
            data[0].highlight = true;
            return ;
        }
        let recommendList = [];
        let informationList = [];
        let marketList = [];
        if (data[0].recommendPriceList != null) {
            recommendList = data[0].recommendPriceList.map(item => item.sequenceNbr);
        }
        if (data[0].informationPriceList!=null) {
            informationList = data[0].informationPriceList.map(item => item.sequenceNbr);
        }
        if (data[0].marketPriceList != null) {
            marketList = data[0].marketPriceList.map(item => item.sequenceNbr);
        }
        if (popUpDataId != null && updatePrice==null) {
            //根据id判断修改的是市场价还是信息价等  修改待载价格、信息价等及价格来源
            if (recommendList.includes(popUpDataId)) {
                let filter = data[0].recommendPriceList.filter(item => item.sequenceNbr==popUpDataId);
                data[0].loadPrice = filter[0].marketPrice;
                data[0].recommendPrice = filter[0].marketPrice;
                data[0].recommendSourcePrice = filter[0].sourcePrice;
                data[0].sourcePrice = filter[0].sourcePrice;
            }
            if (informationList.includes(popUpDataId)) {
                let filter = data[0].informationPriceList.filter(item => item.sequenceNbr==popUpDataId);
                data[0].loadPrice = filter[0].marketPrice;
                data[0].informationPrice = filter[0].marketPrice;
                data[0].informationSourcePrice = filter[0].sourcePrice;
                data[0].sourcePrice = filter[0].sourcePrice;
            }
            if (marketList.includes(popUpDataId)) {
                let filter = data[0].marketPriceList.filter(item => item.sequenceNbr==popUpDataId);
                data[0].loadPrice = filter[0].marketPrice;
                data[0].marketPrice = filter[0].marketPrice;
                data[0].marketSourcePrice = filter[0].sourcePrice;
                data[0].sourcePrice = filter[0].sourcePrice;
            }
            data[0].highlight = true;
        }else if (popUpDataId != null && updatePrice!=null) {
            //根据id判断修改的是市场价还是信息价等  修改待载价格、信息价等及价格来源
            if (recommendList.includes(popUpDataId)) {
                let filter = data[0].recommendPriceList.filter(item => item.sequenceNbr==popUpDataId && vagueSourcePrice ==item.sourcePrice);
                data[0].loadPrice = updatePrice;
                data[0].recommendPrice = updatePrice;
                data[0].recommendSourcePrice = filter[0].sourcePrice;
                data[0].sourcePrice = filter[0].sourcePrice;
            }
            if (informationList.includes(popUpDataId)) {
                let filter = data[0].informationPriceList.filter(item => item.sequenceNbr==popUpDataId && vagueSourcePrice ==item.sourcePrice);
                data[0].loadPrice = updatePrice;
                data[0].informationPrice = updatePrice;
                data[0].informationSourcePrice = filter[0].sourcePrice;
                data[0].sourcePrice = filter[0].sourcePrice;
            }
            if (marketList.includes(popUpDataId)) {
                let filter = data[0].marketPriceList.filter(item => item.sequenceNbr==popUpDataId && vagueSourcePrice ==item.sourcePrice);
                data[0].loadPrice = updatePrice;
                data[0].marketPrice = updatePrice;
                data[0].marketSourcePrice = filter[0].sourcePrice;
                data[0].sourcePrice = filter[0].sourcePrice;
            }
            data[0].highlight = true;
        }
    }

    /**
     * 取消勾选及类型 更新载入前后的人材机总价 及执行载价的条数
     * @param args
     * @returns {Promise<void>}
     */
    async cancelCheckOrType(args){
        let {constructId, singleId,unitId,rcjId,rcjType,type} = args;

        let rcjDatas = await this.getCurrentLoadingRcjs(constructId,singleId,unitId,type);
        //对对应的人材机数据的标识进行更改
        if (rcjId != null) {
            let filter = rcjDatas.filter(item => item.sequenceNbr == rcjId);
            filter.isExecuteLoadPrice = false;
        }
        if (rcjType != null) {
            let rcjList = rcjDatas.filter(item => item.kind == rcjType);
            for (let i = 0; i < rcjList.length; i++) {
                rcjList[i].isExecuteLoadPrice = false;
            }
        }

    }

    /**
     * 返回载价编辑弹窗数据
     * @param args
     * @returns {Promise<void>}
     */
    async loadPriceEditPage(args) {
        //rcjIdList 为勾选的人材机id   kindList 为勾选的类型
        let {constructId, singleId,unitId,type,pageNum,pageSize,kindList,rcjIdList,loadPriortyList} = args;
        let rcjDatas = await this.getCurrentLoadingRcjs(constructId,singleId,unitId,type);

        //按照优先级进行挂载
        if (loadPriortyList != null) {
            for (let i = 0; i < rcjDatas.length; i++) {
                await this.updateLoadPriceByLevel(loadPriortyList,rcjDatas[i],0);
            }
        }
        let rcjDataWant = rcjDatas.filter(item => kindList.includes(item.kind));
        for (let i = 0; i < rcjDataWant.length; i++) {
            rcjDataWant[i].isExecuteLoadPrice = true;
        }
        //同时对过滤掉的标识置为false
        let rcjDataNotWant = rcjDatas.filter(item => !kindList.includes(item.kind));
        for (let i = 0; i < rcjDataNotWant.length; i++) {
            rcjDataNotWant[i].isExecuteLoadPrice = false;
        }
        //对筛选出来的数据未执行勾选的 标识置为false
        for (let i = 0; i < rcjDataWant.length; i++) {
            if (!rcjIdList.includes(rcjDataWant[i].sequenceNbr)) {
                rcjDataWant[i].isExecuteLoadPrice = false;
            }
        }

        let start = (pageNum-1)*pageSize;
        let end = pageNum*pageSize;
        let total = rcjDataWant.length;
        let dataWant = rcjDataWant.slice(start,end);

        //同时返回统计信息
        let rcjDataOrigin = this.queryConstructRcjByDeIdNew(type, 0, constructId, singleId, unitId);

        //不要二次解析的父级材料
        rcjDataOrigin = rcjDataOrigin.filter(k => !(k.markSum == 1 && (k.levelMark == 1 || k.levelMark == 2)));
        let result = await this.getLoadPriceVariety(rcjDatas,rcjDataOrigin);
        result['total'] = total;
        result['data'] = dataWant;
        return result;
    }


    /**
     * 批量应用接口更新市场价、价格来源
     * @param args
     * @returns {Promise<void>}
     */
    async applyLoadingPriceInRcjDetails(args) {
        let {constructId, singleId,unitId,type} = args;

        let rcjDatas = (await this.getCurrentLoadingRcjs(constructId, singleId, unitId, type)).filter(item => item.isExecuteLoadPrice);
        //拿到所有人材机  进行数据更新
        if (type == 3) {
            for (let i = 0; i < rcjDatas.length; i++) {
                let unitProject = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
                await this.applyLoadingForUnit(unitProject,rcjDatas[i]);
            }
            //费用定额自动记取计算
            await this.service.autoCostMathService.autoCostMath({
                constructId:constructId,
                singleId:singleId,
                unitId:unitId});
            //计算费用代码和更新费用汇总
            this.service.unitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
            });
        }
        if (type == 1) { //工程项目层级
            let unitList = PricingFileFindUtils.getUnitList(constructId);
            for (let unit of unitList) {
                for (let i = 0; i < rcjDatas.length; i++) {
                    await this.applyLoadingForConstruct(unit,rcjDatas[i]);
                }
                //费用定额自动记取计算
                await this.service.autoCostMathService.autoCostMath({
                    constructId:constructId,
                    singleId:unit.spId,
                    unitId:unit.sequenceNbr});
                //计算费用代码和更新费用汇总
                await this.service.unitCostCodePriceService.countCostCodePrice({
                    constructId: constructId,
                    singleId: unit.spId,
                    unitId: unit.sequenceNbr,
                });
                //清除单位的本次载价标识
                if (!ObjectUtils.isEmpty(unit.constructProjectRcjs)) {
                    for (let i = 0; i < unit.constructProjectRcjs.length; i++) {
                        unit.constructProjectRcjs[i].currentLoadingFinished =null;
                    }
                }
                if (!ObjectUtils.isEmpty(unit.rcjDetailList)) {
                    for (let i = 0; i < unit.rcjDetailList.length; i++) {
                        unit.rcjDetailList[i].currentLoadingFinished = null;
                    }
                }

            }
        };
        if (type == 2){
            let unitList = PricingFileFindUtils.getUnitList(constructId).filter(i=>i.spId == singleId);
            for (let unit of unitList) {
                for (let i = 0; i < rcjDatas.length; i++) {
                    await this.applyLoadingForConstruct(unit,rcjDatas[i]);
                }
                //费用定额自动记取计算
                await this.service.autoCostMathService.autoCostMath({
                    constructId:constructId,
                    singleId:unit.spId,
                    unitId:unit.sequenceNbr});
                //计算费用代码和更新费用汇总
                await this.service.unitCostCodePriceService.countCostCodePrice({
                    constructId: constructId,
                    singleId: unit.spId,
                    unitId: unit.sequenceNbr,
                });
                //清除单位的本次载价标识
                if (!ObjectUtils.isEmpty(unit.constructProjectRcjs)) {
                    for (let i = 0; i < unit.constructProjectRcjs.length; i++) {
                        unit.constructProjectRcjs[i].currentLoadingFinished =null;
                    }
                }
                if (!ObjectUtils.isEmpty(unit.rcjDetailList)) {
                    for (let i = 0; i < unit.rcjDetailList.length; i++) {
                        unit.rcjDetailList[i].currentLoadingFinished = null;
                    }
                }

            }
        }

    }


    async applyLoadingForUnit(unitProject, rcjData) {

        let constructProjectTaxCalculationMethod = PricingFileFindUtils.getConstructProjectTaxCalculationMethod(unitProject.constructId);

        //工程项目层级的相同材料划分粒度更细
        let rcjs = unitProject.constructProjectRcjs.filter(item => {
            return item.materialCode === rcjData.materialCode
                && item.materialName === rcjData.materialName
                && item.specification === rcjData.specification
                && item.unit === rcjData.unit
                && item.dePrice === rcjData.dePrice
                && item.markSum === rcjData.markSum
                //&& item.marketPrice == rcjData.marketPriceBeforeLoading;
        });
        //二级材料的子级材料
        let rcjDetails = [];
        if (!ObjectUtils.isEmpty(unitProject.rcjDetailList)) {
            rcjDetails = unitProject.rcjDetailList.filter(item => {
                return item.materialCode === rcjData.materialCode
                    && item.materialName === rcjData.materialName
                    && item.specification === rcjData.specification
                    && item.unit === rcjData.unit
                    && item.dePrice === rcjData.dePrice
                    && item.markSum === rcjData.markSum
                    //&& item.marketPrice == rcjData.marketPriceBeforeLoading
            });
        }

        // 22定额 为 是
        let unitIs2022= PricingFileFindUtils.is22Unit(unitProject);

        if (rcjs.length > 0) {
            //修改市场价、价格来源等
            for (let j = 0; j < rcjs.length; j++) {

                if (unitIs2022){
                    if (constructProjectTaxCalculationMethod){
                        rcjs[j].marketPriceBeforeLoading = rcjs[j].priceMarketTax;
                        //简易 含税
                        rcjs[j].priceMarketTax = rcjData.loadPrice;
                        rcjs[j].priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(
                            100,NumberUtil.divide(rcjs[j].priceMarketTax,NumberUtil.add(100,rcjs[j].taxRate))));
                        rcjs[j].marketPrice = rcjs[j].priceMarketTax;

                    }else {
                        //一般 不含税
                        rcjs[j].marketPriceBeforeLoading = rcjs[j].priceMarket;
                        rcjs[j].priceMarket = rcjData.loadPrice;
                        rcjs[j].priceMarketTax = NumberUtil.costPriceAmountFormat(
                            NumberUtil.multiply(0.01,NumberUtil.multiply(
                                rcjs[j].priceMarket,NumberUtil.add(100,rcjs[j].taxRate))));
                        rcjs[j].marketPrice = rcjs[j].priceMarket;
                    }

                    rcjs[j].total = NumberUtil.multiplyToString(rcjs[j].marketPrice,
                        rcjs[j].totalNumber, 2);
                }else {
                    rcjs[j].marketPriceBeforeLoading = rcjs[j].marketPrice;
                    rcjs[j].marketPrice = rcjData.loadPrice;
                    rcjs[j].total = NumberUtil.costPriceAmountFormat(rcjs[j].marketPrice * rcjs[j].totalNumber);
                }

                rcjs[j].sourcePrice = rcjData.sourcePrice;
                rcjs[j].isExecuteLoadPrice = rcjData.isExecuteLoadPrice;

                if (rcjs[j].highlight!=null && rcjs[j].highlight && !rcjData.highlight) { //如果原来人材机数据高亮过
                                                                                         // 说明进行过匹配 已匹配的数据保留原来的高亮状态
                }else {
                    rcjs[j].highlight = rcjData.highlight;
                }
                //更新缓存数据
                UnitRcjCacheUtil.update(unitProject, rcjs[j],ObjectUtils.cloneDeep(rcjs[j]));
            }
            let constructProjectRcj = new ConstructProjectRcj();
            constructProjectRcj.type = rcjData.type;
            constructProjectRcj.materialName = rcjData.materialName;
            constructProjectRcj.specification = rcjData.specification;
            constructProjectRcj.unit = rcjData.unit;
            constructProjectRcj.dePrice = rcjData.dePrice;
            await this.service.unitPriceService.caculateDeByRcj(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr,constructProjectRcj);

        }
        if (rcjDetails.length > 0) { //如果是二次解析材料  rcjDatas[i] 为人材机配比明细
            for (let j = 0; j < rcjDetails.length; j++) {
                rcjDetails[j].marketPriceBeforeLoading = rcjDetails[j].marketPrice;

                rcjDetails[j].sourcePrice = rcjData.sourcePrice;


                if (unitIs2022){
                    rcjDetails[j].priceMarketTax = rcjData.loadPrice;
                    rcjDetails[j].priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(
                        100,NumberUtil.divide(rcjDetails[j].priceMarketTax,NumberUtil.add(100,rcjDetails[j].taxRate))));
                }else {
                    rcjDetails[j].marketPrice = rcjData.loadPrice;
                    rcjDetails[j].total = NumberUtil.multiplyToString(rcjDetails[j].marketPrice, rcjDetails[j].totalNumber,2);
                }

                rcjDetails[j].isExecuteLoadPrice = rcjData.isExecuteLoadPrice;

                if (rcjDetails[j].highlight!=null && rcjDetails[j].highlight && !rcjData.highlight) { //如果原来人材机数据高亮过  说明进行过匹配
                }else {
                    rcjDetails[j].highlight = rcjData.highlight;
                }
                let parent = unitProject.constructProjectRcjs.find(i=>i.sequenceNbr === rcjDetails[j].rcjId);
                await this.service.rcjProcess.parentMaterialPrice(unitProject,parent);
                let constructProjectRcj = new ConstructProjectRcj();
                constructProjectRcj.type = parent.type;
                constructProjectRcj.materialName = parent.materialName;
                constructProjectRcj.specification = parent.specification;
                constructProjectRcj.unit = parent.unit;
                constructProjectRcj.dePrice = parent.dePrice;
                await this.service.unitPriceService.caculateDeByRcj(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr,constructProjectRcj);
                //更新缓存数据
                UnitRcjCacheUtil.update(unitProject, rcjDetails[j],ObjectUtils.cloneDeep(rcjDetails[j]));
            }
        }
    }


    async applyLoadingForConstruct(unitProject, rcjData) {
        if (!ObjectUtils.isEmpty(rcjData)){
            let find = unitProject.constructProjectRcjs.find(i=>i.sequenceNbr == rcjData.sequenceNbr);
            if (ObjectUtils.isEmpty(find)){
                find = unitProject.rcjDetailList.find(i=>i.sequenceNbr == rcjData.sequenceNbr);
            }
            if (!ObjectUtils.isEmpty(find)){
                rcjData.materialCode = find.materialCode;
            }
        }

        //对于工程项目级别的载价  存在多找多的情况 即一对多改完的价格可能是另一个 一对多要找的价格  此时数据就发生错乱  添加本次载价的唯一标识
        let concatString = rcjData.materialCode.concat(!ObjectUtils.isEmpty(rcjData.materialName)?rcjData.materialName:"")
            .concat(!ObjectUtils.isEmpty(rcjData.specification)?rcjData.specification:"")
            .concat(!ObjectUtils.isEmpty(rcjData.unit)?rcjData.unit:"")
            .concat(!ObjectUtils.isEmpty(rcjData.dePrice)?rcjData.dePrice:"")
            .concat(!ObjectUtils.isEmpty(rcjData.markSum)?rcjData.markSum:"")
            .concat(!ObjectUtils.isEmpty(rcjData.ifDonorMaterial)?rcjData.ifDonorMaterial:"")
            .concat(!ObjectUtils.isEmpty(rcjData.ifProvisionalEstimate)?rcjData.ifProvisionalEstimate:"")
            .concat(!ObjectUtils.isEmpty(rcjData.marketPriceBeforeLoading)?rcjData.marketPriceBeforeLoading:"")
            .concat(!ObjectUtils.isEmpty(rcjData.ifLockStandardPrice)?rcjData.ifLockStandardPrice:"");
        //工程项目层级的相同材料划分粒度更细
        let rcjs = [];
        if (!ObjectUtils.isEmpty(unitProject.constructProjectRcjs)) {
            rcjs = unitProject.constructProjectRcjs.filter(item => {

                let itemConcat = item.materialCode.concat(!ObjectUtils.isEmpty(item.materialName)?item.materialName:"")
                    .concat(!ObjectUtils.isEmpty(item.specification)?item.specification:"")
                    .concat(!ObjectUtils.isEmpty(item.unit)?item.unit:"")
                    .concat(!ObjectUtils.isEmpty(item.dePrice)?item.dePrice:"")
                    .concat(!ObjectUtils.isEmpty(item.markSum)?item.markSum:"")
                    .concat(!ObjectUtils.isEmpty(item.ifDonorMaterial)?item.ifDonorMaterial:"")
                    .concat(!ObjectUtils.isEmpty(item.ifProvisionalEstimate)?item.ifProvisionalEstimate:"")
                    .concat(!ObjectUtils.isEmpty(item.marketPrice)?item.marketPrice:"")
                    .concat(!ObjectUtils.isEmpty(item.ifLockStandardPrice)?item.ifLockStandardPrice:"");

                return itemConcat == concatString && (!item.currentLoadingFinished||item.currentLoadingFinished==null);
            });
        }

        //二级材料的子级材料
        let rcjDetails = [];
        if (!ObjectUtils.isEmpty(unitProject.rcjDetailList)) {
            rcjDetails = unitProject.rcjDetailList.filter(item => {
                let itemConcat = item.materialCode.concat(!ObjectUtils.isEmpty(item.materialName)?item.materialName:"")
                    .concat(!ObjectUtils.isEmpty(item.specification)?item.specification:"")
                    .concat(!ObjectUtils.isEmpty(item.unit)?item.unit:"")
                    .concat(!ObjectUtils.isEmpty(item.dePrice)?item.dePrice:"")
                    .concat(!ObjectUtils.isEmpty(item.markSum)?item.markSum:"")
                    .concat(!ObjectUtils.isEmpty(item.ifDonorMaterial)?item.ifDonorMaterial:"")
                    .concat(!ObjectUtils.isEmpty(item.ifProvisionalEstimate)?item.ifProvisionalEstimate:"")
                    .concat(!ObjectUtils.isEmpty(item.marketPrice)?item.marketPrice:"")
                    .concat(!ObjectUtils.isEmpty(item.ifLockStandardPrice)?item.ifLockStandardPrice:"");

                return itemConcat == concatString && (!item.currentLoadingFinished||item.currentLoadingFinished==null);
            });
        }

        let unitIs2022 = PricingFileFindUtils.is22Unit(unitProject);
        let constructProjectTaxCalculationMethod = PricingFileFindUtils.getConstructProjectTaxCalculationMethod(unitProject.constructId);


        if (rcjs.length > 0) {
            //修改市场价、价格来源等
            for (let j = 0; j < rcjs.length; j++) {
                rcjs[j].marketPriceBeforeLoading = rcjs[j].marketPrice;
                rcjs[j].marketPrice = rcjData.loadPrice;
                rcjs[j].sourcePrice = rcjData.sourcePrice;
                rcjs[j].total = NumberUtil.costPriceAmountFormat(rcjs[j].marketPrice * rcjs[j].totalNumber);
                rcjs[j].isExecuteLoadPrice = rcjData.isExecuteLoadPrice;

                if (unitIs2022){
                    if (constructProjectTaxCalculationMethod){
                        rcjs[j].marketPriceBeforeLoading = rcjs[j].priceMarketTax;
                        //简易 含税
                        rcjs[j].priceMarketTax = rcjData.loadPrice;
                        rcjs[j].priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(
                            100,NumberUtil.divide(rcjs[j].priceMarketTax,NumberUtil.add(100,rcjs[j].taxRate))));
                        rcjs[j].marketPrice = rcjs[j].priceMarketTax;

                    }else {
                        //一般 不含税
                        rcjs[j].marketPriceBeforeLoading = rcjs[j].priceMarket;
                        rcjs[j].priceMarket = rcjData.loadPrice;
                        rcjs[j].priceMarketTax = NumberUtil.costPriceAmountFormat(
                            NumberUtil.multiply(0.01,NumberUtil.multiply(
                                rcjs[j].priceMarket,NumberUtil.add(100,rcjs[j].taxRate))));
                        rcjs[j].marketPrice = rcjs[j].priceMarket;
                    }

                    rcjs[j].total = NumberUtil.multiplyToString(rcjs[j].marketPrice,
                        rcjs[j].totalNumber, 2);
                }


                if (rcjs[j].highlight!=null && rcjs[j].highlight && !rcjData.highlight) { //如果原来人材机数据高亮过
                    // 说明进行过匹配 已匹配的数据保留原来的高亮状态
                }else {
                    rcjs[j].highlight = rcjData.highlight;
                }
                rcjs[j].currentLoadingFinished = true;
                //更新缓存数据
                UnitRcjCacheUtil.update(unitProject, rcjs[j],ObjectUtils.cloneDeep(rcjs[j]));
            }
            let constructProjectRcj = new ConstructProjectRcj();
            constructProjectRcj.type = rcjData.type;
            constructProjectRcj.materialName = rcjData.materialName;
            constructProjectRcj.specification = rcjData.specification;
            constructProjectRcj.unit = rcjData.unit;
            constructProjectRcj.dePrice = rcjData.dePrice;
            await this.service.unitPriceService.caculateDeByRcj(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr,constructProjectRcj);
        }
        if (rcjDetails.length > 0) { //如果是二次解析材料  rcjDatas[i] 为人材机配比明细
            for (let j = 0; j < rcjDetails.length; j++) {
                rcjDetails[j].marketPriceBeforeLoading = rcjDetails[j].marketPrice;
                rcjDetails[j].marketPrice = rcjData.loadPrice;
                rcjDetails[j].sourcePrice = rcjData.sourcePrice;
                rcjDetails[j].total = NumberUtil.multiplyToString(rcjDetails[j].marketPrice, rcjDetails[j].totalNumber,2);
                rcjDetails[j].isExecuteLoadPrice = rcjData.isExecuteLoadPrice;

                if (unitIs2022) {
                    if (constructProjectTaxCalculationMethod) {
                        rcjDetails[j].marketPriceBeforeLoading = rcjDetails[j].priceMarketTax;
                        //简易 含税
                        rcjDetails[j].priceMarketTax = rcjData.loadPrice;
                        rcjDetails[j].priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(
                            100, NumberUtil.divide(rcjDetails[j].priceMarketTax, NumberUtil.add(100, rcjDetails[j].taxRate))));
                        rcjDetails[j].marketPrice = rcjDetails[j].priceMarketTax;

                    } else {
                        //一般 不含税
                        rcjDetails[j].marketPriceBeforeLoading = rcjDetails[j].priceMarket;
                        rcjDetails[j].priceMarket = rcjData.loadPrice;
                        rcjDetails[j].priceMarketTax = NumberUtil.costPriceAmountFormat(
                            NumberUtil.multiply(0.01, NumberUtil.multiply(
                                rcjDetails[j].priceMarket, NumberUtil.add(100, rcjDetails[j].taxRate))));
                        rcjDetails[j].marketPrice = rcjDetails[j].priceMarket;
                    }
                }
                if (rcjDetails[j].highlight!=null && rcjDetails[j].highlight && !rcjData.highlight) { //如果原来人材机数据高亮过  说明进行过匹配
                }else {
                    rcjDetails[j].highlight = rcjData.highlight;
                }
                rcjDetails[j].currentLoadingFinished = true;
                let parent = unitProject.constructProjectRcjs.find(i=>i.sequenceNbr === rcjDetails[j].rcjId);
                await this.service.rcjProcess.parentMaterialPrice(unitProject,parent);
                let constructProjectRcj = new ConstructProjectRcj();
                constructProjectRcj.type = parent.type;
                constructProjectRcj.materialName = parent.materialName;
                constructProjectRcj.specification = parent.specification;
                constructProjectRcj.unit = parent.unit;
                constructProjectRcj.dePrice = parent.dePrice;
                await this.service.unitPriceService.caculateDeByRcj(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr,constructProjectRcj);
                //更新缓存数据
                UnitRcjCacheUtil.update(unitProject, rcjDetails[j],ObjectUtils.cloneDeep(rcjDetails[j]));
            }
        }
    }

    /**
     * 获取执行载价前后的人材机总价变化及载价条数的变化
     * @param rcjListPage 载价编辑页面的人材机数据
     * @param rcjDataTotal
     */
    async getLoadPriceVariety(rcjListPage,rcjDataTotal) {
        let totalBefore = 0;
        let totalAfter = 0;
        let loadNumber = 0;//载价条数统计
        for (let i = 0; i < rcjDataTotal.length; i++) {
            totalBefore +=parseFloat(rcjDataTotal[i].total);
            //拿到载价页面的数据
            let filter = rcjListPage.filter(item => item.sequenceNbr==rcjDataTotal[i].sequenceNbr);
            if (filter[0] !=null) {
                if (filter[0].isExecuteLoadPrice) {
                    totalAfter += parseFloat((filter[0].loadPrice * filter[0].totalNumber).toFixed(2));
                    loadNumber+=1;
                }else {
                    totalAfter += parseFloat(rcjDataTotal[i].total);
                }
            }else {
                totalAfter += parseFloat(rcjDataTotal[i].total);
            }
        }
        return {
            "loadNumber":loadNumber,
            "totalAfter":totalAfter.toFixed(2),//载价后的人材机总价
            "totalBefore":totalBefore.toFixed(2)
        };
    }

    async getCurrentLoadingRcjs(constructId,singleId,upId,type) {
        if (type == 3) {
            let unit = PricingFileFindUtils.getUnit(constructId,singleId,upId);
            return unit.unitRcjsLoading;
        }else if (type == 1) {
            let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
            return projectObjById.projectRcjsLoading;
        }else if (type == 2){
            let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);
            return singleProject.singleRcjsLoading;
        }
        return null;
    }
    /**
     * 查询载价状态
     * @return {Promise<void>}
     */
    async loadPriceStatus(args) {
        //type 1 = 工程项目  2 单项工程 3单位工程
        let {constructId, singleId, unitId, type} = args;
        let result = [];
        let flag = false;
        if (type == 1) {
            //查询工程项目汇总
            let unitList = PricingFileFindUtils.getUnitList(constructId);
            for (let unitListKey of unitList) {
                if (!ObjectUtils.isEmpty(unitListKey.constructProjectRcjs)) {
                    flag = true;
                }
            }
        } else if (type == 3) {
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            if (!ObjectUtils.isEmpty(unit.constructProjectRcjs)){
                flag = true;
            }
        } else if (type == 2){
            let unitList = PricingFileFindUtils.getUnitList(constructId).filter(i=>i.spId == singleId);
            for (let unitListKey of unitList) {
                if (!ObjectUtils.isEmpty(unitListKey.constructProjectRcjs)) {
                    flag = true;
                }
            }
        }
        if (flag){
            result.push(LoadPriceTypeEnum.LoadType4.code);
        }else {
            result.push(LoadPriceTypeEnum.LoadType5.code);
        }
        return result;
    }

    /**
     * 查询载价报告明细列表
     * @param args
     * @returns {Promise<void>}
     */
    async loadPriceList(args){
        //type 1 = 工程项目  2 单项工程 3单位工程
        let {constructId, singleId,unitId,type} = args;
        let queryConstructRcjByDeIdNew1 = this.queryConstructRcjByDeIdNew(type,null,constructId, singleId,unitId);
        if (ObjectUtils.isEmpty(queryConstructRcjByDeIdNew1)){
            return null;
        }

        let ts = queryConstructRcjByDeIdNew1.filter(i=>!ObjectUtils.isEmpty(i.highlight) && i.highlight );

        return ts;

    }

    /**
     * 查询载价报告 -扇形图
     * @param args
     * @returns {Promise<void>}
     */
    async queryLoadPriceReportRcj(args){
        //type 1 = 工程项目  2 单位工程
        let {constructId, singleId,unitId,type} = args;
        let rcjList = this.queryConstructRcjByDeIdNew(type,null,constructId, singleId,unitId);

        if (ObjectUtils.isEmpty(rcjList)){
            return null;
        }

        let result = rcjList.reduce((acc, item) => {
            const key = item.type;
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(item);
            return acc;
        }, {});

        let array = new Array();
        for (const type of Object.keys(result)) {
            let loadPriceComparison = new LoadPriceComparison();
            loadPriceComparison.type = type;
            let total = 0;
            for (const item of result[type]) {
                total = NumberUtil.add(total, parseFloat(item.total));
            }
            loadPriceComparison.total = total;
            array.push(loadPriceComparison);
        }

        return array;

    }

    /**
     * 查询载价报告 -柱状图
     * @param args
     * @returns {Promise<void>}
     */
    async queryLoadPriceReportTarget(args){
        //type 1 = 工程项目  2 单项工程 3单位工程
        let {constructId, singleId,unitId,type} = args;
        let rcjList = this.queryConstructRcjByDeIdNew(type,null,constructId, singleId,unitId);

        if (ObjectUtils.isEmpty(rcjList)){
            return null;
        }

        let result = rcjList.reduce((acc, item) => {
            const key = item.type;
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(item);
            return acc;
        }, {});

        let array = new Array();
        for (const type of Object.keys(result)) {
            let loadPriceComparison = new LoadPriceComparison();
            loadPriceComparison.type = type;
            let beforePrice = 0;
            let afterPrice = 0;
            for (const item of result[type]) {
                if (!ObjectUtils.isEmpty(item.highlight) && item.highlight==true){
                    //进行过载价
                    afterPrice = NumberUtil.add(afterPrice ,NumberUtil.costPriceAmountFormat(NumberUtil.multiply(item.marketPrice,parseFloat(item.totalNumber))));
                    beforePrice = NumberUtil.add( beforePrice ,NumberUtil.costPriceAmountFormat(NumberUtil.multiply(item.marketPriceBeforeLoading,parseFloat(item.totalNumber))));

                }else {
                    //没有进行过载价
                    afterPrice = NumberUtil.add(afterPrice ,NumberUtil.costPriceAmountFormat(NumberUtil.multiply(item.marketPrice,parseFloat(item.totalNumber))));
                    beforePrice =NumberUtil.add(beforePrice ,NumberUtil.costPriceAmountFormat(NumberUtil.multiply(item.marketPrice,parseFloat(item.totalNumber))));
                }

            }
            loadPriceComparison.beforePrice = beforePrice;
            loadPriceComparison.afterPrice = afterPrice;
            array.push(loadPriceComparison);
        }

        return array;

    }

    /**
     * 智能询价
     * @param args
     * @returns {Promise<void>}
     */
    async smartLoadPrice(args){
        //loadPriortyList 载价优先级设置 1信息价 2市场价  3推荐价  ，如果选择为空传0
        //laodPriceConditionList 载价条件  默认还是 1信息价 2市场价  3推荐价
        let {constructId, singleId, unitId,standardId,materialName} = args;
        if (ObjectUtils.isEmpty(standardId) || ObjectUtils.isEmpty(materialName)){
            return null;
        }

        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        let ssCityName = projectObjById.ssCityName;


        if (ObjectUtils.isEmpty(ssCityName)){
            ssCityName = "石家庄市";
        }
        //根据 城市名称 获取 最新 期刊日期
        let promise1 = await this.queryLoadPriceDataByCityName(ssCityName);

        //获取推荐价最新期刊
        let promise2 = await this.queryLoadPriceDataByCityName("推荐价数据");


        //人材机list
        let rcjs = [];
        rcjs.push(
            {id: standardId,
             name: materialName}
        );



        //信息价

        let informationPriceListNew = [];

        informationPriceListNew.push({areaName: ssCityName, yearMonths: promise1.information});


        //市场价

        let marketPriceListNew = [];

        marketPriceListNew.push({areaName: ssCityName, yearMonths: promise1.market });


        //推荐价
        let recommendPriceListNew = [];

        recommendPriceListNew.push({areaName: "推荐价数据", yearMonths: promise2.recommend });

        if (!ObjectUtils.isEmpty(projectObjById.loadPriceCache)){
            let loadPriceCache = projectObjById.loadPriceCache;

            let type1 = loadPriceCache["type1"];

            if (!ObjectUtils.isEmpty(type1)){
                informationPriceListNew = [];

                informationPriceListNew.push({areaName: type1.areaName, yearMonths: type1.yearMonths})
            }
            let type2 = loadPriceCache["type2"];
            if (!ObjectUtils.isEmpty(type2)){
                marketPriceListNew = [];
                marketPriceListNew.push({areaName: type2.areaName, yearMonths: type2.yearMonths})
            }
            let type3 = loadPriceCache["type3"];
            if (!ObjectUtils.isEmpty(type3)){
                recommendPriceListNew = [];
                recommendPriceListNew.push({areaName: type3.areaName, yearMonths: type3.yearMonths });
            }

        }


        let params = {
            rcjs: rcjs,
            informationPriceList: informationPriceListNew,
            marketPriceList: marketPriceListNew,
            recommendPriceList: recommendPriceListNew
        };
        //调用远程接口
        let promise = await HttpUtils.POST(BsRemoteUrl.loadPriceAcquire, params);
        let result = JSON.parse(promise.result);
        return result;

    }

    /**
     * 智能询价后应用
     * @param args
     * @returns {Promise<void>}
     */
    async smartLoadPriceUse(args){
        let {constructId, singleId, unitId,sequenceNbr,loadPrice} = args;
        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        // 22定额
        let unitIs2022= PricingFileFindUtils.is22Unit(unitProject);

        //计税方式
        let taxMethod = PricingFileFindUtils.getConstructProjectTaxCalculationMethod(constructId);

        let constructProjectRcjs = unitProject.constructProjectRcjs;

        let rcjDetailList = unitProject.rcjDetailList;
        let rcjData = null;
        rcjData = constructProjectRcjs.find(i=>i.sequenceNbr ==sequenceNbr);
        if (ObjectUtils.isEmpty(rcjData)){
            rcjData = rcjDetailList.find(i=>i.sequenceNbr == sequenceNbr);
        }

        let rcjs = constructProjectRcjs.filter(item => {
            return item.materialCode === rcjData.materialCode
                && item.materialName === rcjData.materialName
                && item.specification === rcjData.specification
                && item.unit === rcjData.unit
                && item.dePrice === rcjData.dePrice
                && item.markSum === rcjData.markSum
        });
        //二级材料的子级材料
        let rcjDetails = rcjDetailList.filter(item => {
            return item.materialCode === rcjData.materialCode
                && item.materialName === rcjData.materialName
                && item.specification === rcjData.specification
                && item.unit === rcjData.unit
                && item.dePrice === rcjData.dePrice
                && item.markSum === rcjData.markSum
        });



        if (rcjs.length > 0) {
            //修改市场价、价格来源等
            for (let j = 0; j < rcjs.length; j++) {
                rcjs[j].marketPriceBeforeLoading = rcjs[j].marketPrice;
                rcjs[j].marketPrice = loadPrice.marketPrice;
                rcjs[j].sourcePrice = loadPrice.sourcePrice;

                if (unitIs2022){
                    //简易
                    if (taxMethod){
                        //简易 含税
                        rcjs[j].priceMarketTax = loadPrice.marketPrice;
                        rcjs[j].priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(
                            100,NumberUtil.divide(rcjs[j].priceMarketTax,NumberUtil.add(100,rcjs[j].taxRate))));
                        rcjs[j].marketPrice = rcjs[j].priceMarketTax;

                    }else {
                    //一般
                        rcjs[j].priceMarket = loadPrice.marketPrice;
                        rcjs[j].priceMarketTax = NumberUtil.costPriceAmountFormat(
                            NumberUtil.multiply(0.01,NumberUtil.multiply(
                                rcjs[j].priceMarket,NumberUtil.add(100,rcjs[j].taxRate))));
                        rcjs[j].marketPrice = rcjs[j].priceMarket;

                    }
                }
                rcjs[j].total = NumberUtil.costPriceAmountFormat(rcjs[j].marketPrice * rcjs[j].totalNumber);

                rcjs[j].isExecuteLoadPrice = true;
                rcjs[j].highlight =true;
                //更新缓存数据
                UnitRcjCacheUtil.update(unitProject, rcjs[j],ObjectUtils.cloneDeep(rcjs[j]));
            }
            let constructProjectRcj = new ConstructProjectRcj();
            constructProjectRcj.type = rcjData.type;
            constructProjectRcj.materialName = rcjData.materialName;
            constructProjectRcj.specification = rcjData.specification;
            constructProjectRcj.unit = rcjData.unit;
            constructProjectRcj.dePrice = rcjData.dePrice;
            this.service.unitPriceService.caculateDeByRcj(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr,constructProjectRcj);


        }
        if (rcjDetails.length > 0) { //如果是二次解析材料  rcjDatas[i] 为人材机配比明细
            for (let j = 0; j < rcjDetails.length; j++) {
                rcjDetails[j].marketPriceBeforeLoading = rcjDetails[j].marketPrice;
                rcjDetails[j].marketPrice = loadPrice.marketPrice;
                rcjDetails[j].sourcePrice = loadPrice.sourcePrice;
                if (unitIs2022){
                    //简易
                    if (taxMethod){
                        //简易 含税
                        rcjDetails[j].priceMarketTax = loadPrice.marketPrice;
                        rcjDetails[j].priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(
                            100,NumberUtil.divide(rcjDetails[j].priceMarketTax,NumberUtil.add(100,rcjDetails[j].taxRate))));
                        rcjDetails[j].marketPrice = rcjDetails[j].priceMarketTax;

                    }else {
                        //一般
                        rcjDetails[j].priceMarket = loadPrice.marketPrice;
                        rcjDetails[j].priceMarketTax = NumberUtil.costPriceAmountFormat(
                            NumberUtil.multiply(0.01,NumberUtil.multiply(
                                rcjDetails[j].priceMarket,NumberUtil.add(100,rcjDetails[j].taxRate))));
                        rcjDetails[j].marketPrice = rcjDetails[j].priceMarket;

                    }
                }

                rcjDetails[j].total = NumberUtil.multiplyToString(rcjDetails[j].marketPrice, rcjDetails[j].totalNumber,2);
                rcjDetails[j].isExecuteLoadPrice = true;
                rcjDetails[j].highlight =true;
                let parent = unitProject.constructProjectRcjs.find(i=>i.sequenceNbr === rcjDetails[j].rcjId);
                await this.service.rcjProcess.parentMaterialPrice(unitProject,parent);
                let constructProjectRcj = new ConstructProjectRcj();
                constructProjectRcj.type = parent.type;
                constructProjectRcj.materialName = parent.materialName;
                constructProjectRcj.specification = parent.specification;
                constructProjectRcj.unit = parent.unit;
                constructProjectRcj.dePrice = parent.dePrice;
                this.service.unitPriceService.caculateDeByRcj(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr,constructProjectRcj);
                //更新缓存数据
                UnitRcjCacheUtil.update(unitProject, rcjDetails[j],ObjectUtils.cloneDeep(rcjDetails[j]));
            }
        }

        return true;
    }

    /**
     * 清除载价
     * @param args
     * @returns {Promise<void>}
     */
    async clearLoadPriceUse(args){
        //type 1 = 工程项目  2 单项工程 3单位工程
        let {constructId, singleId, unitId,type,rcj} = args;
        if (type ==1){
            await this.clearLoadPriceUseConstruct(args);
        }else if (type == 3){
            await this.clearLoadPriceUseUnit(constructId,singleId,unitId,rcj);
        }else if (type == 2){
            await this.clearLoadPriceUseSingle(args);
        }


        return true;
    }

    /**
     * 单位工程汇总 清除载价
     * @param constructId
     * @param singleId
     * @param unitId
     * @param rcjData 人材机对象
     * @returns {Promise<void>}
     */
    async clearLoadPriceUseUnit(constructId,singleId,unitId,rcjData){

        //type 1 = 工程项目  2 单项工程 3单位工程

        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        //人材机一级
        let constructProjectRcjs = unitProject.constructProjectRcjs;

        //人材机二级
        let rcjDetailList = unitProject.rcjDetailList;

        let unitIs22 = PricingFileFindUtils.is22UnitById(constructId,singleId,unitId);
        let taxMethod = PricingFileFindUtils.getConstructProjectTaxCalculationMethod(constructId);

        //查询人材机政策文件
        let promise= null;
        if (!ObjectUtils.isEmpty(unitProject.rgfId)) {
            promise = await this.service.basePolicyDocumentService.queryBySequenceNbr(unitProject.rgfId);//cl
        }


        let rcjs = constructProjectRcjs.filter(item => {
            return item.materialCode === rcjData.materialCode
                && item.materialName === rcjData.materialName
                && item.specification === rcjData.specification
                && item.unit === rcjData.unit
                && item.dePrice === rcjData.dePrice
                && item.markSum === rcjData.markSum
                && item.marketPrice === rcjData.marketPrice
        });
        //二级材料的子级材料
        let rcjDetails = rcjDetailList.filter(item => {
            return item.materialCode === rcjData.materialCode
                && item.materialName === rcjData.materialName
                && item.specification === rcjData.specification
                && item.unit === rcjData.unit
                && item.dePrice === rcjData.dePrice
                && item.markSum === rcjData.markSum
                && item.marketPrice === rcjData.marketPrice
        });

        if (rcjs.length > 0) {
            //修改市场价、价格来源等
            for (let j = 0; j < rcjs.length; j++) {
                rcjs[j].marketPriceBeforeLoading = null;

                if ((rcjs[j].materialCode == "10000001" ||//
                    rcjs[j].materialCode == "10000002" ||
                    rcjs[j].materialCode == "10000003" ||
                    rcjs[j].materialCode == "JXPB-005" ||
                    rcjs[j].materialCode == "R00001") &&
                    !ObjectUtils.isEmpty(promise)){
                    await this.useRgf(rcjs[j],promise);

                }else {
                    rcjs[j].marketPrice = rcjs[j].dePrice;
                    rcjs[j].sourcePrice = null;
                }

                rcjs[j].total = NumberUtil.costPriceAmountFormat(rcjs[j].marketPrice * rcjs[j].totalNumber);
                rcjs[j].highlight = false;

                if (unitIs22){
                    if (taxMethod){
                        //简易
                        rcjs[j].priceMarketTax = rcjs[j].marketPrice;
                        rcjs[j].priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(
                            100,NumberUtil.divide(rcjs[j].priceMarketTax,NumberUtil.add(100,rcjs[j].taxRate))));
                        rcjs[j].marketPrice = rcjs[j].priceMarketTax;

                    }else {
                        //一般
                        rcjs[j].priceMarket = rcjs[j].marketPrice;
                        rcjs[j].priceMarketTax = NumberUtil.costPriceAmountFormat(
                            NumberUtil.multiply(0.01,NumberUtil.multiply(
                                rcjs[j].priceMarket,NumberUtil.add(100,rcjs[j].taxRate))));
                        rcjs[j].marketPrice = rcjs[j].priceMarket;
                    }
                }

                //更新缓存数据
                UnitRcjCacheUtil.update(unitProject, rcjs[j],ObjectUtils.cloneDeep(rcjs[j]));

            }
            let constructProjectRcj = new ConstructProjectRcj();
            constructProjectRcj.type = rcjData.type;
            constructProjectRcj.materialName = rcjData.materialName;
            constructProjectRcj.specification = rcjData.specification;
            constructProjectRcj.unit = rcjData.unit;
            constructProjectRcj.dePrice = rcjData.dePrice;
            this.service.unitPriceService.caculateDeByRcj(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr,constructProjectRcj);

        }
        if (rcjDetails.length > 0) { //如果是二次解析材料  rcjDatas[i] 为人材机配比明细
            for (let j = 0; j < rcjDetails.length; j++) {
                rcjDetails[j].marketPriceBeforeLoading = null;

                if ((rcjDetails[j].materialCode == "10000001" ||//
                    rcjDetails[j].materialCode == "10000002" ||
                    rcjDetails[j].materialCode == "10000003" ||
                    rcjDetails[j].materialCode == "JXPB-005" ||
                    rcjDetails[j].materialCode == "R00001") &&
                    !ObjectUtils.isEmpty(promise)){
                    await this.useRgf(rcjDetails[j],promise);
                }else {
                    rcjDetails[j].marketPrice = rcjDetails[j].dePrice;
                    rcjDetails[j].sourcePrice =null;
                }
                if (unitIs22){
                    if (taxMethod){
                        //简易
                        rcjDetails[j].priceMarketTax = rcjDetails[j].marketPrice;
                        rcjDetails[j].priceMarket = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(
                            100,NumberUtil.divide(rcjDetails[j].priceMarketTax,NumberUtil.add(100,rcjDetails[j].taxRate))));
                        rcjDetails[j].marketPrice = rcjDetails[j].priceMarketTax;

                    }else {
                        //一般
                        rcjDetails[j].priceMarket = rcjDetails[j].marketPrice;
                        rcjDetails[j].priceMarketTax = NumberUtil.costPriceAmountFormat(
                            NumberUtil.multiply(0.01,NumberUtil.multiply(
                                rcjDetails[j].priceMarket,NumberUtil.add(100,rcjDetails[j].taxRate))));
                        rcjDetails[j].marketPrice = rcjDetails[j].priceMarket;
                    }
                }
                rcjDetails[j].total = NumberUtil.multiplyToString(rcjDetails[j].marketPrice, rcjDetails[j].totalNumber,2);
                rcjDetails[j].highlight = false;


                let parent = unitProject.constructProjectRcjs.find(i=>i.sequenceNbr === rcjDetails[j].rcjId);
                await this.service.rcjProcess.parentMaterialPrice(unitProject,parent);
                let constructProjectRcj = new ConstructProjectRcj();
                constructProjectRcj.type = parent.type;
                constructProjectRcj.materialName = parent.materialName;
                constructProjectRcj.specification = parent.specification;
                constructProjectRcj.unit = parent.unit;
                constructProjectRcj.dePrice = parent.dePrice;
                this.service.unitPriceService.caculateDeByRcj(unitProject.constructId, unitProject.spId, unitProject.sequenceNbr,constructProjectRcj);
                //更新缓存数据
                UnitRcjCacheUtil.update(unitProject, rcjDetails[j],ObjectUtils.cloneDeep(rcjDetails[j]));
            }
        }

        //费用定额自动记取计算
        await this.service.autoCostMathService.autoCostMath({
            constructId:constructId,
            singleId:singleId,
            unitId:unitId});
        //计算费用代码和更新费用汇总
        this.service.unitCostCodePriceService.countCostCodePrice({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
        });
    }


    /**
     * 工程项目汇总 清除载价
     * @param args
     * @returns {Promise<void>}
     */
    async clearLoadPriceUseConstruct(args){

        //type 1 = 工程项目  2 单项工程 3单位工程
        let {constructId,rcj} = args;

        let unitList = PricingFileFindUtils.getUnitList(constructId);
        for (let unit of unitList) {
            await this.clearLoadPriceUseUnit(constructId,unit.spId,unit.sequenceNbr,rcj);
        }
    }

    /**
     * 单项工程 清除载价
     * @param args
     * @returns {Promise<void>}
     */
    async clearLoadPriceUseSingle(args){

        //type 1 = 工程项目  2 单项工程 3单位工程
        let {constructId,rcj,singleId} = args;

        let unitList = PricingFileFindUtils.getUnitList(constructId).filter(i=>i.spId == singleId);
        if (ObjectUtils.isEmpty(unitList)){
            return ;
        }
        for (let unit of unitList) {
            await this.clearLoadPriceUseUnit(constructId,unit.spId,unit.sequenceNbr,rcj);
        }
    }


    /**
     * 鼠标右键查询人材机关联定额
     * @param args
     * @returns {Promise<void>}
     */
    async getRcjDe(args){
        let list = [];
        let {constructId, singleId, unitId,rcj} = args;
        //获取单位工程
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);

        let constructProjectRcjs = unit.constructProjectRcjs;

        let rcjDetailList = unit.rcjDetailList;

        //查询对应人材机
        let constructProjectRcj = {};
           constructProjectRcj = constructProjectRcjs.find(i=>i.sequenceNbr ==rcj.sequenceNbr);
        if (ObjectUtils.isEmpty(constructProjectRcj) && !ObjectUtils.isEmpty(rcjDetailList)){
            constructProjectRcj = rcjDetailList.find(i=>i.sequenceNbr ==rcj.sequenceNbr);
        }
        if (!ObjectUtils.isEmpty(constructProjectRcj)){
            rcj = constructProjectRcj;
        }



        //分部分项
        let itemBillProjects = treeToArray(unit.itemBillProjects)

        //措施项目
        let measureProjectTables = treeToArray(unit.measureProjectTables)

        //一级材料
        let rcjs ;
        //二级材料的子级材料
        let rcjDetails =null;
        if(ObjectUtils.isEmpty(constructProjectRcj)){
            //工程项目
            rcjs = constructProjectRcjs.filter(item => {
                return  this.handleMaterialCode( item.materialCode) == rcj.materialCode
                    && item.materialName == rcj.materialName
                    && item.specification == rcj.specification
                    && item.unit == rcj.unit
                    && item.dePrice == rcj.dePrice
                    && item.markSum == rcj.markSum
                    && item.marketPrice == rcj.marketPrice
            });

            if (!ObjectUtils.isEmpty(rcjDetailList)) {
                rcjDetails = rcjDetailList.filter(item => {
                    return this.handleMaterialCode(  item.materialCode) == rcj.materialCode
                        && item.materialName == rcj.materialName
                        && item.specification == rcj.specification
                        && item.unit == rcj.unit
                        && item.dePrice == rcj.dePrice
                        && item.markSum == rcj.markSum
                        && item.marketPrice == rcj.marketPrice
                });
            }


        }else {
            rcjs = constructProjectRcjs.filter(item => {
                return item.materialCode == rcj.materialCode
                    && item.materialName == rcj.materialName
                    && item.specification == rcj.specification
                    && item.unit == rcj.unit
                    && item.dePrice == rcj.dePrice
                    && item.markSum == rcj.markSum
                    && item.marketPrice == rcj.marketPrice
            });

            if (!ObjectUtils.isEmpty(rcjDetailList)) {
                rcjDetails = rcjDetailList.filter(item => {
                    return  item.materialCode == rcj.materialCode
                        && item.materialName == rcj.materialName
                        && item.specification == rcj.specification
                        && item.unit == rcj.unit
                        && item.dePrice == rcj.dePrice
                        && item.markSum == rcj.markSum
                        && item.marketPrice == rcj.marketPrice
                });
            }

        }



        if (ObjectUtils.isEmpty(rcjs) && ObjectUtils.isEmpty(rcjDetails)){
            return null;
        }
        let set = new Set();
        //添加包含2级材料 定额id
        if (!ObjectUtils.isEmpty(rcjDetails)){
            for (let rcjDetail of rcjDetails) {
                let t = constructProjectRcjs.find(i=>i.sequenceNbr == rcjDetail.rcjId);
                set.add(t.deId);
            }
        }
        //添加包含1级材料 定额id
        if (!ObjectUtils.isEmpty(rcjs)){
            for (let rcj1 of rcjs) {
                set.add(rcj1.deId);
            }
        }
        let a =[];
        //查询所有包含人材机定额
        if (!ObjectUtils.isEmpty(set)){
            let set1 = new Set();

            //递归获取上级所有id
            for (let setElement of set) {
                await this.recursionGetDeParentId(setElement,set1,itemBillProjects,measureProjectTables);
            }

            /*let fbFx = treeToArray(PricingFileFindUtils.getFbFx(constructId, singleId, unitId));
            let csxm = treeToArray(PricingFileFindUtils.getCSXM(constructId, singleId, unitId));*/

            let itemBillProjects1 = itemBillProjects.filter(i=>set1.has(i.sequenceNbr));
            let measureProjectTables1 = measureProjectTables.filter(i=>set1.has(i.sequenceNbr));


            if (!ObjectUtils.isEmpty(itemBillProjects1)){
                a.push(...itemBillProjects1);
            }

            if (!ObjectUtils.isEmpty(measureProjectTables1)){
                a.push(...measureProjectTables1);
            }
        }

        let label = false;
        let label1 = false;
        for (let itemBillProject of a) {
            if (itemBillProject.kind == "03"){
                if (!label){
                    let name =null;
                    let promise = await this.recursionGetQdParentName(itemBillProject.parentId,name,itemBillProjects,measureProjectTables);
                    let fb = {};
                    fb.sequenceNbr = itemBillProject.parentId;
                    fb.name = promise;
                    fb.bdName = promise;
                    fb.kind = "02";
                    list.push(fb);
                    label1 = true;
                    label =true;
                }
                let b = ConvertUtil.deepCopy(itemBillProject);
                if (!b.hasOwnProperty('bdCode')){
                    b.bdCode = b.fxCode;
                }
                list.push(b);
            }
            if (itemBillProject.kind == "04"){
                let b = ConvertUtil.deepCopy(itemBillProject);
                list.push(b);
            }

            if ((itemBillProject.kind == "01"|| itemBillProject.kind == "02") && label1 == true){
                label = false
            }
        }

        let qdTotal  = 0;
        let fbTotal  = 0;
        for (let i = list.length - 1; i >= 0; i--) {
            if (list[i].kind == "04"){
                let total =  await this.getDeRcjTotal(list[i].sequenceNbr,constructProjectRcjs,rcjDetailList,rcj,args.levelType);
                list[i].total = total;
                qdTotal = NumberUtil.add(qdTotal,total);
            }

            if (list[i].kind == "03"){
                let total =  await this.getDeRcjTotal(list[i].sequenceNbr,constructProjectRcjs,rcjDetailList,rcj,args.levelType);
                qdTotal = NumberUtil.add(qdTotal,total);
                list[i].total = qdTotal;
                fbTotal = NumberUtil.add(fbTotal,qdTotal);
                qdTotal = 0;
            }

            if (list[i].kind == "02"){
                list[i].total = fbTotal;
                fbTotal = 0;
            }
        }


        for (let listElement of list) {
            //处理编码不一致问题
            if (ObjectUtils.isEmpty(listElement.bdCode) && !ObjectUtils.isEmpty(listElement.fxCode)){
                listElement.bdCode = listElement.fxCode
            }
            //处理箭头问题
            if (listElement.displaySign ==BranchProjectDisplayConstant.close) {
                listElement.displaySign = BranchProjectDisplayConstant.open;
            }
        }

        if (itemBillProjects[0].displaySign == BranchProjectDisplayConstant.close){
            //itemBillProjects[0].displaySign = BranchProjectDisplayConstant.open;
            unit.itemBillProjects.root.displaySign =BranchProjectDisplayConstant.open ;
        }

        if (measureProjectTables[0].displaySign == BranchProjectDisplayConstant.close){
            unit.measureProjectTables.root.displaySign =BranchProjectDisplayConstant.open ;
        }


        return list;
    }

    /**
     * 获取定额下某一种材料的 数量汇总
     * @param id
     * @param constructProjectRcjs
     * @param rcjDetailList
     * @param rcj
     * @returns {Promise<void>}
     */
    async getDeRcjTotal(id,constructProjectRcjs,rcjDetailList,rcj,levelType){

        let totalNumber = 0;
        for (let constructProjectRcj of constructProjectRcjs) {
            if (constructProjectRcj.deId == id){

                if(levelType===1 ){
                    if (this.handleMaterialCode(constructProjectRcj.materialCode) === this.handleMaterialCode(rcj.materialCode)
                        && constructProjectRcj.materialName === rcj.materialName
                        && constructProjectRcj.specification === rcj.specification
                        && constructProjectRcj.unit === rcj.unit
                        && constructProjectRcj.dePrice === rcj.dePrice
                        && constructProjectRcj.markSum === rcj.markSum){
                        totalNumber = NumberUtil.add(totalNumber,constructProjectRcj.totalNumber)
                    }
                }else {
                    if (constructProjectRcj.materialCode === rcj.materialCode
                        && constructProjectRcj.materialName === rcj.materialName
                        && constructProjectRcj.specification === rcj.specification
                        && constructProjectRcj.unit === rcj.unit
                        && constructProjectRcj.dePrice === rcj.dePrice
                        && constructProjectRcj.markSum === rcj.markSum){
                        totalNumber = NumberUtil.add(totalNumber,constructProjectRcj.totalNumber)
                    }
                }

                let rcjDetaiFilter =[];
                if (!ObjectUtils.isEmpty(rcjDetailList)) {
                    rcjDetaiFilter= rcjDetailList.filter(i => i.rcjId == constructProjectRcj.sequenceNbr);
                }


                for (let rcjDetaiFilterElement of rcjDetaiFilter) {

                    if (rcjDetaiFilterElement.materialCode === rcj.materialCode
                        && rcjDetaiFilterElement.materialName === rcj.materialName
                        && rcjDetaiFilterElement.specification === rcj.specification
                        && rcjDetaiFilterElement.unit === rcj.unit
                        && rcjDetaiFilterElement.dePrice === rcj.dePrice
                        && rcjDetaiFilterElement.markSum === rcj.markSum){

                        totalNumber = NumberUtil.add(totalNumber,rcjDetaiFilterElement.totalNumber)

                    }
                }
            }
        }
        return totalNumber;
    }

    /**
     * 递归把父级名字拼出来
     * @param id
     * @param name
     * @param itemBillProjects
     * @param measureProjectTables
     * @returns {Promise<void>}
     */
    async recursionGetQdParentName(id,name,itemBillProjects,measureProjectTables){
        let find =null;
        if (!ObjectUtils.isEmpty(itemBillProjects)){
            find = itemBillProjects.find(i=>i.sequenceNbr == id);
        }
        if (ObjectUtils.isEmpty(find) && !ObjectUtils.isEmpty(measureProjectTables)){
            find = measureProjectTables.find(i=>i.sequenceNbr == id);
        }

        if (!ObjectUtils.isEmpty(find)){
            let bdName =null;
            if (!ObjectUtils.isEmpty(find.bdName)){
                bdName = find.bdName;
            }
            if (!ObjectUtils.isEmpty(find.name)){
                bdName = find.name;
            }

            if (bdName == null){
                bdName = " ";
            }

            if (ObjectUtils.isEmpty(name)){
                name = bdName;
            }else {
                if (!ObjectUtils.isEmpty(bdName)) {
                    name = bdName + "/" + name;
                }else {
                    name = " " + "/" + name;
                }
            }
            if (!ObjectUtils.isEmpty(find.parentId) && find.parentId !="0"){
                return await this.recursionGetQdParentName(find.parentId,name,itemBillProjects,measureProjectTables);
            }else {
                return name;
            }
        }

    }

    /**
     * 递归获取定额父id
     * @param id
     * @param set
     * @param itemBillProjects
     * @param measureProjectTables
     * @returns {Promise<void>}
     */
    async recursionGetDeParentId(id,set,itemBillProjects,measureProjectTables){
        let find =null;
        if (!ObjectUtils.isEmpty(itemBillProjects)){
            find = itemBillProjects.find(i=>i.sequenceNbr == id);
        }
        if (ObjectUtils.isEmpty(find) && !ObjectUtils.isEmpty(measureProjectTables)){
            find = measureProjectTables.find(i=>i.sequenceNbr == id);
        }


        if (!ObjectUtils.isEmpty(find)){
            set.add(find.sequenceNbr);
            if (!ObjectUtils.isEmpty(find.parentId) && find.parentId !="0"){
                await this.recursionGetDeParentId(find.parentId,set,itemBillProjects,measureProjectTables);
            }
        }

    }


    /**
     * 鼠标右键查询人材机关联定额树结构
     * @param args
     * @returns {Promise<void>}
     */
    async getConstructIdTree(args){
        let {constructId,rcj} = args;

        let unitList = PricingFileFindUtils.getUnitList(constructId);

        let t = new ConstructProjectRcj();
        //查询修改 对象
        for (let unitListKey of unitList) {
            let constructProjectRcjs = unitListKey.constructProjectRcjs;
            if (!ObjectUtils.isEmpty(constructProjectRcjs)) {
                let find = constructProjectRcjs.find(i => i.sequenceNbr === rcj.sequenceNbr);
                if (!ObjectUtils.isEmpty(find)) {
                    ConvertUtil.setDstBySrc(find, t);
                    break;
                }
            }

            let rcjDetailList = unitListKey.rcjDetailList;

            if (!ObjectUtils.isEmpty(rcjDetailList)) {
                let find1 = rcjDetailList.find(i => i.sequenceNbr === rcj.sequenceNbr);
                if (!ObjectUtils.isEmpty(find1)) {
                    ConvertUtil.setDstBySrc(find1, t);
                    break;
                }
            }
        }

        rcj =t;

        let set = new Set();
        set.add(constructId);
        for (let unit of unitList) {

            let constructProjectRcjs = unit.constructProjectRcjs;

            let rcjDetailList = unit.rcjDetailList;


            //一级材料
            let rcjs =[];
            let rcjDetails = [];
            if (!ObjectUtils.isEmpty(constructProjectRcjs)) {
                //工程项目
                rcjs = constructProjectRcjs.filter(item => {
                    return    item.materialCode === rcj.materialCode
                        && item.materialName === rcj.materialName
                        && item.specification === rcj.specification
                        && item.unit === rcj.unit
                        && item.dePrice === rcj.dePrice
                        && item.markSum === rcj.markSum
                        && item.marketPrice === rcj.marketPrice
                });



            }
            //二级材料的子级材料
            if (!ObjectUtils.isEmpty(rcjDetailList)) {
                rcjDetails = rcjDetailList.filter(item => {
                    return   item.materialCode === rcj.materialCode
                        && item.materialName === rcj.materialName
                        && item.specification === rcj.specification
                        && item.unit === rcj.unit
                        && item.dePrice === rcj.dePrice
                        && item.markSum === rcj.markSum
                        && item.marketPrice === rcj.marketPrice
                });
            }


            if (rcjs.length!=0 || rcjDetails.length!=0 ){
                set.add(unit.sequenceNbr);
                set.add(unit.spId);
            }
        }


        //获取项目结构树
        let projectObj = await PricingFileFindUtils.getProjectObjById(constructId);
        let generateLevelTreeNode = await this.service.constructProjectService.generateLevelTreeNode(projectObj);
        let filter = generateLevelTreeNode.filter(i=>set.has(i.id));

        return filter;
    }

    /**
     * 鼠标右键 查询定额是否存在 以及 是分部分项还是措施项目
     * @param args
     * @returns {Promise<void>}
     */
    async existDe(args){
        let {constructId, singleId, unitId,sequenceNbr} = args;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        let itemBillProjects = unit.itemBillProjects;
        let measureProjectTables = unit.measureProjectTables;

        let t = itemBillProjects.find(i=>i.sequenceNbr == sequenceNbr);
        if (!ObjectUtils.isEmpty(t)){

            this.service.baseBranchProjectOptionService.openLineAndParent(t,itemBillProjects);
            return {
                "exist":true,
                "type":"fbfx"
            }
        }
        let t1 = measureProjectTables.find(i=>i.sequenceNbr == sequenceNbr);
        if (!ObjectUtils.isEmpty(t1)){
            this.service.baseBranchProjectOptionService.openLineAndParent(t1,measureProjectTables);
            return {
                "exist":true,
                "type":"csxm"
            }
        }

        return {
            "exist":false
        };

    }

    /**
     * 人工费使用 取费文件
     * @param rcj
     * @param promise
     * @returns {Promise<void>}
     */
    async useRgf(rcj,promise) {

        if ((rcj.materialCode == "10000001" ||//
            rcj.materialCode == "10000002" ||
            rcj.materialCode == "10000003" ||
            rcj.materialCode == "JXPB-005" ||
            rcj.materialCode == "R00001") && !ObjectUtils.isEmpty(promise)) {

            switch (rcj.materialCode) {
                case "10000001"://
                    rcj.marketPrice = promise.zhygLevel1;
                    rcj.sourcePrice = promise.cityName + promise.pricesource;
                    break;
                case "10000002":
                    rcj.marketPrice = promise.zhygLevel2;
                    rcj.sourcePrice = promise.cityName + promise.pricesource;
                    break;
                case "10000003":
                    rcj.marketPrice = promise.zhygLevel3;
                    rcj.sourcePrice = promise.cityName + promise.pricesource;
                    break;
                case "JXPB-005":
                    rcj.marketPrice = promise.zhygLevel2;
                    rcj.sourcePrice = promise.cityName + promise.pricesource;
                    break;
                case "R00001":
                    rcj.marketPrice = promise.zhygLevel2;
                    rcj.sourcePrice = promise.cityName + promise.pricesource;
                    break;
            }
        }
    }

    /**
     * 将含有井号的编码去掉井号之后
     * @param materialCode
     * @returns {string}
     */
    handleMaterialCode(materialCode){
        if (materialCode.includes("#")){
            materialCode = materialCode.substring(0,materialCode.lastIndexOf("#"));
        }
        return materialCode
    }

    async getSfbPeriodical(){
        let promise;
        try {
            promise = await HttpUtils.GET(BsRemoteUrl.sfb_periodical);
            if (!ObjectUtils.isEmpty(promise)){
                if (promise.hasOwnProperty("result")){
                    promise = promise.result;
                }

            }
        } catch (e) {
            return null;
        }

        return promise
    }

    /**
     * 逐条载价 查询人材机
     * @param args
     * @returns {Promise<void>}
     */
    async getZtzjRcj(args){
        let {type, areaName, yearMonths} = args;

        let params = {
            materialName: args.materialName,
            classlevel01: args.classlevel01,
            classlevel02: args.classlevel02,
            classlevel03: args.classlevel03,
            classlevel04: args.classlevel04,
            areaName: args.areaName,
            yearMonths: args.yearMonths,
            type: args.type
        };

        if (!ObjectUtils.isEmpty(args.constructId)){
            let constructId = args.constructId;

            //单位工程
            let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

            if (!ObjectUtils.isEmpty(projectObjById)){

                if (ObjectUtils.isEmpty(projectObjById.loadPriceCache)){
                    projectObjById.loadPriceCache = {};
                }
                let typeKye = "type" + type;

                let typeDx ={
                    "type" :type,
                    "areaName" :areaName,
                    "yearMonths" :yearMonths
                }
                projectObjById.loadPriceCache[typeKye] = typeDx;
            }

        }

        let promise;
        try {
            //调用远程接口
            promise = await HttpUtils.POST(BsRemoteUrl.loadPriceGetZtzj, params);
            if (!ObjectUtils.isEmpty(promise)){
                if (promise.hasOwnProperty("result")){
                    promise = promise.result;
                }

            }
        } catch (e) {
            return null;
        }

        return promise
    }

    /**
     * 逐条载价 查询人材机(载价平均价)
     * @param args
     * @returns {Promise<void>}
     */
    async getZtzjRcjAvg(args){
        let allList = [];
        let resultList = [];
        //并集数组
        let resultUnion = [];
        //获取加权规则
        let ruleArr = PricingFileFindUtils.getProjectObjById(args.constructId).constructProjectRcjAvgRule;
        ruleArr = ruleArr.filter(item => item.areaName === args.areaName && item.tabType === args.tabType);
        let promise = [];
            try {
            //调用远程接口
            for (let rule of ruleArr) {
                let params = {
                    materialName: args.materialName,
                    classlevel01: args.classlevel01,
                    classlevel02: args.classlevel02,
                    classlevel03: args.classlevel03,
                    classlevel04: args.classlevel04,
                    areaName: rule.areaName,
                    yearMonths: rule.yearMonths,
                    type: args.type
                }
                promise = await HttpUtils.POST(BsRemoteUrl.loadPriceGetZtzj, params);
                if (!ObjectUtils.isEmpty(promise)){
                    if (promise.hasOwnProperty("result")){
                        promise = promise.result;
                    }
                }
                //每期的数据
                let resultList1 = JSON.parse(promise);
                for (let item of resultList1) {
                    item.up = rule.up;
                    item.down = rule.down;
                    item.areaName = rule.areaName;
                    item.yearMonths = rule.yearMonths;
                    item.marketPriceAvg = 0;
                    item.notIncludingTaxMarketPriceAvg = 0;

                    //唯一标识:
                    item.unique = item.materialName + item.specification + item.unit;
                }
                //去重
                resultList1 = ArrayUtil.distinctList(resultList1, 'unique');
                if(resultList1.length===0){
                    //空数组给一个标记, 下面减去分母时候要用
                    let emty = {
                        up: rule.up,
                        down: rule.down
                    };
                    resultList1.push(emty);
                }
                allList.push(resultList1);
                resultUnion = resultUnion.concat(resultList1);
            }

            //取所有数据去重后的并集
            resultUnion = ArrayUtil.distinctList(resultUnion, 'unique');
            //拿到所有数据 第一期的id
            for (let res of resultUnion) {
                let allMap = {};
                allMap.unique = res.unique;
                allMap.value = 0;
                allMap.marketPriceAvg = 0;
                allMap.notIncludingTaxMarketPriceAvg = 0;
                allMap.materialName = res.materialName;
                allMap.specification = res.specification;
                allMap.unit = res.unit;
                allMap.sourcePrice = res.area+'平均价';
                allMap.down = res.down;
                resultList.push(allMap);

            }


            let map = new Map();

            //组装一个map,并处理数据
            for (let result of resultList) {
                let a = 0;
                let b = 0;
                let count = 0;
                if (!map.has(result.unique)){
                    map.set(result.unique,0);
                }
                for (let r of allList) {
                    let temp =  r.find(i=> i.unique === result.unique)
                    if(temp){
                        if (map.has(temp.unique)){
                            let v = map.get(temp.unique);
                            map.set(temp.unique,v+temp.up);
                        }
                        a += result.marketPriceAvg + NumberUtil.multiply(temp.marketPrice, temp.up);
                        b += result.notIncludingTaxMarketPriceAvg + NumberUtil.multiply(temp.notIncludingTaxMarketPrice, temp.up);
                        //result.materialName = temp.materialName;
                        result[temp.yearMonths+'(含税)'] = temp.marketPrice;
                        result[temp.yearMonths+'(不含税)'] = temp.notIncludingTaxMarketPrice;
                        if (temp.marketPrice === 0){
                            result.value = result.value+temp.up
                        }else{
                            count++;
                        }
                    }else{
                        result.down = result.down-r[0].up;
                    }
                }
                result.count = count;
                result.marketPriceAvg = a;
                result.notIncludingTaxMarketPriceAvg = b;


            }
            for (let result of resultList) {
                result.marketPriceAvg = NumberUtil.costPriceAmountFormat(result.marketPriceAvg / result.down);
                result.notIncludingTaxMarketPriceAvg = NumberUtil.costPriceAmountFormat(result.notIncludingTaxMarketPriceAvg / result.down);
                //result.marketPriceAvg = NumberUtil.costPriceAmountFormat(result.marketPriceAvg / map.get(result.unique));
                //result.notIncludingTaxMarketPriceAvg = NumberUtil.costPriceAmountFormat(result.notIncludingTaxMarketPriceAvg / map.get(result.unique));
            }

        } catch (e) {
            return null;
        }

        return resultList;
    }


    /**
     * 获取逐条载价 人材机类型树
     * @param
     * @returns
     */
    async getRcjTypeTree(){
        return rcjTypeTree;
    }

    /**
     * 逐条载价 应用
     * @param args
     * @returns {Promise<void>}
     */
    async useZtzjRcj(args){
        let {constructId, singleId,unitId,type,rcj,searchRcj} = args;

        rcj.marketPriceBeforeLoading = rcj.marketPrice;
        rcj.marketSourcePriceBeforeLoading = rcj.sourcePrice;


        //let constructProjectTaxCalculationMethod = PricingFileFindUtils.getConstructProjectTaxCalculationMethod(constructId);
        rcj.loadPrice = searchRcj.loadPrice;
        /*if (constructProjectTaxCalculationMethod){
            //简易
            rcj.loadPrice = searchRcj.marketPrice;
        }else {
            //一般
            rcj.loadPrice = searchRcj.notIncludingTaxMarketPrice;
        }*/

        rcj.sourcePrice = searchRcj.sourcePrice;
        rcj.isExecuteLoadPrice = true;
        rcj.highlight = true;


        //拿到所有人材机  进行数据更新
        if (type == 3) {

            let unitProject = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
            await this.applyLoadingForUnit(unitProject,rcj);

            //费用定额自动记取计算
            await this.service.autoCostMathService.autoCostMath({
                constructId:constructId,
                singleId:singleId,
                unitId:unitId});
            //计算费用代码和更新费用汇总
            this.service.unitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
            });
        }
        if (type == 1 || type==2) { //工程项目层级
            let unitList ;
            if (type == 1){
                unitList  =PricingFileFindUtils.getUnitList(constructId);
            }else if (type == 2){
                unitList  =PricingFileFindUtils.getUnitList(constructId).filter(i=>i.spId = singleId);
            }
            for (let unit of unitList) {

                await this.applyLoadingForConstruct(unit,rcj);

                //费用定额自动记取计算
                await this.service.autoCostMathService.autoCostMath({
                    constructId:constructId,
                    singleId:unit.spId,
                    unitId:unit.sequenceNbr});
                //计算费用代码和更新费用汇总
                await this.service.unitCostCodePriceService.countCostCodePrice({
                    constructId: constructId,
                    singleId: unit.spId,
                    unitId: unit.sequenceNbr,
                });
                //清除单位的本次载价标识
                if (!ObjectUtils.isEmpty(unit.constructProjectRcjs)) {
                    for (let i = 0; i < unit.constructProjectRcjs.length; i++) {
                        unit.constructProjectRcjs[i].currentLoadingFinished =null;
                    }
                }
                if (!ObjectUtils.isEmpty(unit.rcjDetailList)) {
                    for (let i = 0; i < unit.rcjDetailList.length; i++) {
                        unit.rcjDetailList[i].currentLoadingFinished = null;
                    }
                }

            }
        }

    }



    /**
     * 获取信息价 地区列表 城市下面带着地区
     * @param args
     * @returns {Promise<void>}
     */
    async getDimRegion(args){



        let promise;
        try {
            //调用远程接口
            promise = await HttpUtils.GET(BsRemoteUrl.sfb_Dim_Region);
            if (!ObjectUtils.isEmpty(promise)){
                if (promise.hasOwnProperty("result")){
                    promise = promise.result;
                }

            }
        } catch (e) {
            return null;
        }

        return promise
    }

    async loadingAvgPrice(args){
        let {constructId, singleId,unitId,type,batchAllFlag,loadPriortyList,laodPriceConditionList} = args;

        //远程获取的人材机数据
        let remoteRcjData = await this.getRemoteRcjDataAvg(args);
        let rcjData = await this.loadPriceRcjData(type, 0, constructId, singleId, unitId, batchAllFlag);

        for (const item of rcjData) {
            let filter = remoteRcjData.filter(itemRemote => itemRemote.id== item.standardId)[0];
            item.marketPriceBeforeLoading = item.marketPrice;
            item.marketSourcePriceBeforeLoading = item.sourcePrice;

            item.informationPrice = filter.informationPrice;
            item.recommendPrice = filter.recommendPrice;
            item.marketPrice = filter.marketPrice;
            item.informationSourcePrice = filter.informationSourcePrice;
            item.marketSourcePrice = filter.marketSourcePrice;
            item.recommendSourcePrice = filter.recommendSourcePrice;

            //原始的精准数据备份  用于优先级调整后
            item.marketPriceOrigin = filter.marketPrice;
            item.marketSourcePriceOrigin = filter.marketSourcePrice;//市场价价格来源
            item.recommendPriceOrigin = filter.recommendPrice;
            item.recommendSourcePriceOrigin = filter.recommendSourcePrice;
            item.informationPriceOrigin = filter.informationPrice;
            item.informationSourcePriceOrigin = filter.informationSourcePrice;

            item.informationPriceList = filter.informationPriceList;
            item.marketPriceList = filter.marketPriceList;
            item.recommendPriceList = filter.recommendPriceList;

            //挂载价格接口中, 需要用null判断, 0不行  所以把为0的置为null
            if(item.informationPriceOrigin===0){
                item.informationPriceOrigin=null;
            }
            if(item.marketPriceOrigin===0){
                item.marketPriceOrigin=null;
            }
            if(item.recommendPriceOrigin===0){
                item.recommendPriceOrigin=null;
            }
            //挂待载价格
            await this. updateLoadPriceByLevel(loadPriortyList,item,0);

            //以下处理用于 有精准匹配 且匹配数据只有一条时 前端的放大镜不进行展示
            /*if (item.highlight) {
                if (item.informationPriceList!=null && item.informationPriceList.length == 1) {
                    item.informationPriceList = null;
                }
                if (item.marketPriceList !=null && item.marketPriceList.length == 1) {
                    item.marketPriceList = null;
                }
                if (item.recommendPriceList !=null && item.recommendPriceList.length == 1) {
                    item.recommendPriceList = null;
                }
            }*/
            item.isExecuteLoadPrice = true;
        }

        //拿到数据集合组装返回
        if (type == 3) {  //单位工程
            let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
            unit.unitRcjsLoading = rcjData;
        }else if (type == 1) {
            let project = PricingFileFindUtils.getProjectObjById(constructId);
            project.projectRcjsLoading = rcjData;
        }else if (type == 2){
            let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);
            singleProject.singleRcjsLoading = rcjData;
        }
        return rcjData.map(item => item.sequenceNbr);

    }

    /**
     * 获取远程的人材机数据
     */
    async getRemoteRcjDataAvg(args) {
        //type 1 = 工程项目  2 单位工程
        //batchAllFlag,是否批量调整所有价格
        //loadPriortyList 载价优先级设置 1信息价 2市场价  3推荐价  ，如果选择为空传0
        //laodPriceConditionList 载价条件  默认还是 1信息价 2市场价  3推荐价
        let {constructId, singleId, unitId, type, batchAllFlag, loadPriortyList, laodPriceConditionList,deStandardReleaseYear} = args;
        //计税方式
        let simpleMethod = PricingFileFindUtils.getConstructProjectTaxCalculationMethod(constructId);
        let is22 =null;
        if (type == 1 || 2){
            is22 = deStandardReleaseYear=="22"?true:false;
        }else {
            is22 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitId);
        }

        if (is22 == false){
            simpleMethod = true;
        }

        //type 1 = 工程项目  2 单项工程 3单位工程
        let rcjData = await this.loadPriceRcjData(type, 0, constructId, singleId, unitId, batchAllFlag);
        //人材机list
        let rcjs = [];
        if (!ObjectUtils.isEmpty(rcjData)) {
            for (const rcj of rcjData) {
                let {standardId, materialName} = rcj;
                rcjs.push({id: standardId, name: materialName});
            }
        }

        /*for (const informationPrice of informationPriceList[0].loadPriceMessageList) {
            informationPriceListNew.push({
                areaName: informationPrice.areaName,
                yearMonths: informationPrice.yearMonths,
                up: informationPrice.up,
                down: informationPrice.down
            });
        }
        if(informationPriceListNew.length==0){
            //用之前的逻辑, 只取第一条
            for (const key in informationPriceList[0]) {
                if(key==='loadPriceMessageList'){
                    break;
                }
                informationPriceListNew.push({areaName: key, yearMonths: informationPriceList[0][key]})
            }
        }*/

        //信息价1 多条件
        let informationPriceList = laodPriceConditionList[0];
        let informationPriceListNew = [];
        for (const informationPrice of informationPriceList) {
            let informationPriceDy = [];
            if (!ObjectUtils.isEmpty(informationPrice.loadPriceMessageList)){
                for (const informationPrice1 of informationPrice.loadPriceMessageList) {
                    informationPriceDy.push({
                        areaName: informationPrice1.areaName,
                        yearMonths: informationPrice1.yearMonths,
                        up: informationPrice1.up,
                        down: informationPrice1.down
                    });
                }

            }else {
                for (const key in informationPrice) {
                    if (key != 'loadPriceMessageList' ){
                        informationPriceDy.push({areaName: key, yearMonths: informationPrice[key]})
                    }
                }
            }
            informationPriceListNew.push(informationPriceDy);
        }

        /*//市场价2 取第一个条件
        let marketPriceList = laodPriceConditionList[1];
        let marketPriceListNew = [];
        for (const loadPriceMessage of marketPriceList[0].loadPriceMessageList) {
            marketPriceListNew.push({
                areaName: loadPriceMessage.areaName,
                yearMonths: loadPriceMessage.yearMonths,
                up: loadPriceMessage.up,
                down: loadPriceMessage.down
            });
        }
        if(marketPriceListNew.length==0){
            //用之前的逻辑, 只取第一条
            //市场价
            for (const key in marketPriceList[0]) {
                if(key==='loadPriceMessageList'){
                    break;
                }
                marketPriceListNew.push({areaName: key, yearMonths: marketPriceList[0][key]})
            }
        }*/

        //市场价2 多条件
        let marketPriceList = laodPriceConditionList[1];
        let marketPriceListNew = [];
        for (const marketPrice of marketPriceList) {
            let marketPriceDy = [];
            if (!ObjectUtils.isEmpty(marketPrice.loadPriceMessageList)){
                for (const marketPrice1 of marketPrice.loadPriceMessageList) {
                    marketPriceDy.push({
                        areaName: marketPrice1.areaName,
                        yearMonths: marketPrice1.yearMonths,
                        up: marketPrice1.up,
                        down: marketPrice1.down
                    });
                }

            }else {
                for (const key in marketPrice) {
                    if(key!='loadPriceMessageList'){
                        marketPriceDy.push({areaName: key, yearMonths: marketPrice[key]})
                    }
                }
            }
            marketPriceListNew.push(marketPriceDy);
        }

        //推荐价3
        let recommendPriceList = laodPriceConditionList[2];
        let recommendPriceListNew = [];
        for (const item of recommendPriceList) {
            for (const key in item) {
                recommendPriceListNew.push({areaName: key, yearMonths: item[key]})
            }
        }
        let params = {
            simpleMethod:simpleMethod,
            rcjs: rcjs,
            informationPriceAvgList: informationPriceListNew,
            marketPriceAvgList: marketPriceListNew,
            recommendPriceList: recommendPriceListNew
        };
        //调用远程接口
        let promise = await HttpUtils.POST(BsRemoteUrl.loadPriceAcquireAvg, params);
        let result = JSON.parse(promise.result);
        console.log(result);
        return result;
    }

    /**
     * 保存加权规则
     */
    async saveAvgRule(args) {
        let saveList = args.loadPriceMessageList;
        for (const item of saveList) {
            item.tabType = args.tabType;
        }
        //获取工程项目对象
        let projectObj = PricingFileFindUtils.getProjectObjById(args.constructId);
        let list = projectObj.constructProjectRcjAvgRule;
        if(list==null){
            return projectObj.constructProjectRcjAvgRule = saveList;
        }
        //删掉当前条件下旧的, 重新保存新规则
        list = list.filter(s=> s.areaName!==args.areaName || s.tabType!==args.tabType);
        for (const item of saveList) {
            list.push(item);
        }
        projectObj.constructProjectRcjAvgRule = list
        return;
    }

    /**
     * 获取加权规则
     */
    async getAvgRule(args) {
        //获取工程项目对象
        let projectObj = PricingFileFindUtils.getProjectObjById(args.constructId);
        let list = projectObj.constructProjectRcjAvgRule;
        if(list==null){
            return;
        }
        return list.filter(item => item.areaName === args.areaName && item.tabType===args.tabType);
    }

    /**
     * 获取项目 应用的载价地区 和期刊设置
     * @param args
     * @returns {Promise<void>}
     */
    async getLoadPriceCache(args){
        let {constructId} = args;
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

        return projectObjById.loadPriceCache;

    }

    /**
     * 删除分部分项或者措施项目中空数据: type:0分部分项 1措施项目
     * @param args
     * @returns {Promise<void>}
     */
    async deleteEmptyDate(args){
        let fbfx;
        let csxm;
        let unitList = [];

        //如果unitId不为空则删除所有工程项目, 获取所有单位
        if(ObjectUtils.isEmpty(args.unitId)){
            //let projectObj = PricingFileFindUtils.getProjectObjById(args.constructId);
            unitList = PricingFileFindUtils.getUnitList(args.constructId);
        }else{
            let unit = PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);
            unitList.push(unit);
        }

        if(args.type===0){
            for (const unit of unitList) {
                fbfx = unit.itemBillProjects.getAllNodes();
                await this.reDeleteChild(unit, fbfx[0], args.type, args.constructId, unit.spId, unit.sequenceNbr, true);
            }
        }

        if(args.type===1){
            for (const unit of unitList) {
                csxm = unit.measureProjectTables.getAllNodes();
                //递归删除child
                await this.reDeleteChild(unit, csxm[0], args.type, args.constructId, unit.spId, unit.sequenceNbr, true);
            }
        }
        return null;

    }

    /**
     * 递归删除空白行
     * @returns {Promise<void>}
     * @param unit
     * @param item
     * @param type
     * @param constructId
     * @param singleId
     * @param unitId
     * @param isBlock
     */
    async reDeleteChild(unit, item, type, constructId, singleId, unitId, isBlock){

        if(ObjectUtils.isNotEmpty(item.children)){
            for (let i = item.children.length - 1; i >= 0; i--) {
                    //for (const ite of item.children) {
                let ite = item.children[i];
                    //递归删除child
                    await this.reDeleteChild(unit, ite, type, constructId, singleId, unitId, true);
            }
            if(ObjectUtils.isEmpty(item.children)){
                await this.deleteBlank(unit, item, type, constructId, singleId, unitId, isBlock);
            }
        }else{
             await this.deleteBlank(unit, item, type, constructId, singleId, unitId, isBlock);
        }

    }

    async deleteBlank(unit, item, type, constructId, singleId, unitId, isBlock){
        if(type === 0){
            if (ObjectUtils.isEmpty(item.name) && ObjectUtils.isEmpty(item.fxCode)) {
                //获取一行数据
                let pointLine = unit.itemBillProjects.getNodeById(item.sequenceNbr);
                const result = await this.service.itemBillProjectOptionService.removeLine(constructId, singleId, unitId, pointLine, isBlock);
            }

        }else{
            if (ObjectUtils.isEmpty(item.name) && ObjectUtils.isEmpty(item.fxCode)) {
                //获取一行数据
                let pointLine = unit.measureProjectTables.getNodeById(item.sequenceNbr);
                const result = await this.service.stepItemCostService.removeLine(constructId, singleId, unitId, pointLine, isBlock);
            }

        }
    }

}

LoadPriceSetService.toString = () => '[class LoadPriceSetService]';
module.exports = LoadPriceSetService;
