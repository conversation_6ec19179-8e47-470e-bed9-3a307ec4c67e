const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const { Service} = require('../../core');
const {NumberUtil} = require('../utils/NumberUtil');
const {ObjectUtils} = require("../utils/ObjectUtils");
const {getUnitFormatEnum, getDeUnitFormatEnum} = require("../main_editor/rules/format");

class ProjectConfigProcess extends Service {
    constructor(ctx) {
        super(ctx);
    }

    async refreshConstruct(cProject) {
        let globalConfig = this.service.globalConfigurationService.getGlobalConfig();

        if(cProject.globalConfigXX){
            return;
        }
        cProject.globalConfigXX = globalConfig.project;

        let unitList = PricingFileFindUtils.getUnitListByConstructObj(cProject);

        for(let unit of unitList){
            // 1. 刷新取费表费率保留位数
            this._refreshFeeFiles(unit.feeFiles || [])
            // 2. 刷新人材机明细消耗量、数量、价、合价保留位数
            await this._refreshRcjDetail(unit)

            // 3. 刷新清单、定额工程量保留位数
            let itemBillProjects = unit.itemBillProjects.getAllNodes() || [];
            for(let item of itemBillProjects){
                if(item.quantity == null){
                    continue;
                }
                let quantity = null;
                if(item.kind == '03'){  // 清单
                    quantity = NumberUtil.numberScale(item.quantity,getUnitFormatEnum(item.unit).value);

                }else if(item.kind == '04'){ // 定额
                    quantity = NumberUtil.numberScale(item.quantity,getDeUnitFormatEnum(item.unit).value);
                }else{
                    continue;
                }

                if(quantity != null && quantity != item.quantity){
                    /*await this.service.itemBillProjectOptionService.updateByList(unit.constructId, unit.spId, unit.sequenceNbr, item.sequenceNbr, {
                        column:"quantityExpression", value:quantity+""
                    });*/

                    await this.service.itemBillProjectOptionService.updateByList(unit.constructId, unit.spId, unit.sequenceNbr, item.sequenceNbr, {
                        column:"quantity", value:quantity+""
                    });
                }
            }

            let measureProjectTables = unit.measureProjectTables.getAllNodes() || [];
            for(let measure of measureProjectTables){
                if(measure.quantity == null){
                    continue;
                }
                let quantity = null;
                if(measure.kind == '03'){  // 清单
                    quantity = NumberUtil.numberScale(measure.quantity,getUnitFormatEnum(measure.unit).value);

                }else if(measure.kind == '04'){ // 定额
                    quantity = NumberUtil.numberScale(measure.quantity,getDeUnitFormatEnum(measure.unit).value);
                }else{
                    continue;
                }

                if(quantity != null && quantity != measure.quantity){
                    /*await this.service.stepItemCostService.upDateOnList(unit.constructId, unit.spId, unit.sequenceNbr, measure.sequenceNbr, {
                        column:"quantityExpression", value:quantity+""
                    });*/

                    await this.service.stepItemCostService.upDateOnList(unit.constructId, unit.spId, unit.sequenceNbr, measure.sequenceNbr, {
                        column:"quantity", value:quantity+""
                    });
                }
            }

            let idParams = {
                constructId: unit.constructId,
                singleId: unit.spId,
                unitId: unit.sequenceNbr
            }
            // 4. 重新计算单位工程费用
            await this.service.autoCostMathService.autoCostMath(idParams);
            //重新计算费用汇总
            await this.service.unitCostCodePriceService.countCostCodePrice(idParams);
        }

    }

    async _refreshRcjDetail(unit){
        let {constructProjectRcjs,rcjDetailList} = unit;
        // let is2002 = PricingFileFindUtils.is22Unit(unit);

        if (ObjectUtils.isNotEmpty(rcjDetailList)){
            rcjDetailList.forEach( rcj => {
                rcj.resQty = NumberUtil.rcjDetailAmountFormat(rcj.resQty)
            });
        }

        if (ObjectUtils.isNotEmpty(constructProjectRcjs)){
            constructProjectRcjs.forEach( rcj => {
                rcj.resQty = NumberUtil.rcjDetailAmountFormat(rcj.resQty)
            });
        }

    }

    _refreshFeeFiles(feeFiles){
        for(let ff of feeFiles){
            ff.managementFee = NumberUtil.rateFormat(ff.managementFee);
            ff.managementFeeBackUp = NumberUtil.rateFormat(ff.managementFeeBackUp);

            ff.profit = NumberUtil.rateFormat(ff.profit);
            ff.profitBackUp = NumberUtil.rateFormat(ff.profitBackUp);

            ff.fees = NumberUtil.rateFormat(ff.fees);
            ff.feesBackUp = NumberUtil.rateFormat(ff.feesBackUp);

            ff.anwenRateBase = NumberUtil.awfRateFormat(ff.anwenRateBase);
            ff.anwenRateBaseBackUp = NumberUtil.awfRateFormat(ff.anwenRateBaseBackUp);
            ff.anwenRateBase = NumberUtil.awfRateFormat(ff.anwenRateBase);
        }
    }
}

ProjectConfigProcess.toString = () => '[class ProjectConfigProcess]';
module.exports = ProjectConfigProcess;