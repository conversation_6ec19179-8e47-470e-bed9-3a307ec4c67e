const { Service } = require('../../core');
const { PricingFileFindUtils } = require('../utils/PricingFileFindUtils');
const Log = require('../../core/log');

const BranchProjectLevelConstant = require('../enum/BranchProjectLevelConstant');
const BranchProjectDisplayConstant = require('../enum/BranchProjectDisplayConstant');
const FbZlEnum = require('../enum/FbZlEnum');
const BranchProjectOptionMenuConstant = require('../enum/BranchProjectOptionMenuConstant');
const { Snowflake } = require('../utils/Snowflake');
const { ObjectUtils } = require('../utils/ObjectUtils');
const UnitConversion = require('../enum/UnitConversion');
const ConstructionMeasureTypeConstant = require('../enum/ConstructionMeasureTypeConstant');
const { NumberUtil } = require('../utils/NumberUtil');
const { SqlUtils } = require('../utils/SqlUtils');
const { ConvertUtil } = require('../utils/ConvertUtils');
const { ParamUtils } = require('../../core/core/lib/utils/ParamUtils');
const _ = require('lodash');
const ConstantUtil = require('../enum/ConstantUtil');
const { BaseDe2022, BaseDe } = require('../model/BaseDe');
const { get2022BY2012Cslb } = require('../model/Map2022And2012');
const { UPCContext } = require('../unit_price_composition/core/UPCContext');
const RcjTypeEnum = require('../enum/RcjTypeEnum');
const DePropertyTypeConstant = require('../enum/DePropertyTypeConstant');
const TypConstant = require('../rcj_handle/TypConstant');
const UpdateStrategy = require('../main_editor/update/updateStrategy');
const {tree ,treeToArray,arrayToTree} = require('../main_editor/tree');
const { UnitApplicationContext } = require('../main_editor/context');
const PumpingAddFeeExpressionConstant = require('../enum/PumpingAddFeeExpressionConstant');
const OptionMenuHandler = require("../main_editor/optionMenuHandler");
const CostCodeTypeEnum_2022 = require('../enum/CostCodeTypeEnum_2022');
const PrecastRateEnum = require('../enum/PrecastRateEnum');
const ChapterEnum = require('../enum/ChapterEnum');
const { MainSetting } = require('../model/MainSetting');
const { getUnitFormatEnum,getDeUnitFormatEnum } = require('../main_editor/rules/format');
class BaseBranchProjectOptionService extends Service {

  constructor(ctx) {
    super(ctx);
  }


  ZFB_MAXLEVEL = 3;

  //1：人工费；2：材料费；3：机械费；
  // 4：设备费；
  // 5：主材费；
  // 6：商砼；7：砼；8：浆；9：商浆；10：配比
  RCJKIN = ['', '人工费', '材料费', '机械费', '设备费', '主材费', '商砼', '砼', '浆', '商浆', '配比'];

  LINE_LOCKED = 1;


  findLineOnlyById(lineId) {
    let constructId = ParamUtils.getPatram('commonParam').constructId;
    let singleId = ParamUtils.getPatram('commonParam').singleId;
    let unitId = ParamUtils.getPatram('commonParam').unitId;

    let fbfxs = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
    let csxms = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);

    let res = fbfxs.find(f => f.sequenceNbr == lineId);
    if (res) {
      return {
        'line': res,
        'belong': 'fbfx'
      };
    } else {
      res = csxms.find(f => f.sequenceNbr == lineId);
      return {
        'line': res,
        'belong': 'csxm'
      };
    }
  }

  lockLine(allData, lineId) {
    let line = allData.getNodeById(lineId); //allData.filter(a => a.sequenceNbr === lineId)[0];
    line.isLocked = this.LINE_LOCKED;
    this.handleDeAddQdStatus(allData,line,1)
  }

  unLockLine(allData, lineId) {
    let line = allData.filter(a => a.sequenceNbr === lineId)[0];
    delete line.isLocked;
    this.handleDeAddQdStatus(allData,line,2)
  }

  //处理添加清单状态  type=1 锁定  lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.removeLine
  handleDeAddQdStatus(allData,qd,type){
    if(type!=1){
      if(!qd.optionMenu.includes(BranchProjectOptionMenuConstant.addListItem)){
        qd.optionMenu.push(BranchProjectOptionMenuConstant.addListItem);
      }
    }else{
      qd.optionMenu = qd.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.addListItem);
    }
    let deList = allData.filter(a => a.parentId == qd.sequenceNbr);
    deList.forEach(d=>{
      if(type!=1){
        let optionMenu = d.optionMenu;
        if(!optionMenu.includes(BranchProjectOptionMenuConstant.addListItem)){
          d.optionMenu.push(BranchProjectOptionMenuConstant.addListItem);
        }
      }else {
        d.optionMenu = d.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.addListItem);
      }
    })


  }

  disPlayType = {
    '0': ' ',
    '01': '部',
    '02': '部',
    '03': '清',
    '04': '定'
  };


  /**
   * 新版本的上下移动    需要结合新增数据的index
   * @param operateAction
   * @param selectId
   * @param unit
   * @param type
   * @returns {Promise<void>}
   */
  async qdDeUpAndDown(operateAction, selectId, unit, type) {
    //获取分部分项/措施项目数据
    let allData = unit[type == 'fbfx' ? 'itemBillProjects' : 'measureProjectTables'];
    //获取当前选中行数据
    let pointLine = allData.getNodeById(selectId);
    let parent = pointLine.parent;
    //获取所有子节点数据
    let index = pointLine.index;
    let temp;
    if (operateAction == 'up') {
      index--;
    } else {
      index++;
    }
    let replace = parent.children[index];
    temp = replace.index;
    replace.index = pointLine.index;
    pointLine.index = temp;
    if (pointLine.kind == BranchProjectLevelConstant.fb || pointLine.kind == BranchProjectLevelConstant.zfb) {
      pointLine.isDownFb = true;
    }
    if (replace.kind == BranchProjectLevelConstant.fb || replace.kind == BranchProjectLevelConstant.zfb) {
      replace.isDownFb = true;
    }

    pointLine.isFirst = false;
    pointLine.isLast = false;
    replace.isFirst = false;
    replace.isLast = false;
    //更换移动标识
    if (pointLine.index == 0) {
      pointLine.isFirst = true;
      pointLine.isLast = false;
      if (pointLine.kind == BranchProjectLevelConstant.fb) {
        pointLine.isDownFb = false;
      }
      if (pointLine.kind == BranchProjectLevelConstant.zfb) {
        pointLine.isDownFb = false;
      }
    }
    if (pointLine.index == parent.children.length - 1) {
      pointLine.isFirst = false;
      pointLine.isLast = true;
    }
    if (replace.index == 0) {
      replace.isFirst = true;
      replace.isLast = false;
      if (replace.kind == BranchProjectLevelConstant.fb) {
        replace.isDownFb = false;
      }
      if (replace.kind == BranchProjectLevelConstant.zfb) {
        replace.isDownFb = false;
      }
    }
    if (replace.index == parent.children.length - 1) {
      replace.isFirst = false;
      replace.isLast = true;
    }
    //重新排序
    allData.sortChildren(parent);
    //序号重排
    allData.refreshAssignDispNos();

  }

  async qdDeUpAndDownOld(operateAction, selectId, unit, type) {

    function swapElements(arr, index1, index2) {
      if (index1 < 0 || index2 < 0) return;
      var temp = arr[index1];
      let dispNo1 = arr[index1].dispNo;
      let dispNo2 = arr[index2].dispNo;

      arr[index1] = arr[index2];
      arr[index2] = temp;

      arr[index1].dispNo = dispNo1;
      arr[index2].dispNo = dispNo2;
    }

    //拿到对应清单下的所有定额  对选定定额进行位置交换  序号也进行交换
    if (type == 'fbfx') {
      let itemBillProjects = unit.itemBillProjects;

      let selectElement = itemBillProjects.getNodeById(selectId);//itemBillProjects.filter(fb=>fb.sequenceNbr==selectId)[0];
      //找到要移动的定额索引
      let index = -1;
      for (let i = 0; i < unit.itemBillProjects.length; i++) {
        index++;
        if (unit.itemBillProjects[i].sequenceNbr == selectId) {
          break;
        }
      }

      let toIndex = index;
      let fromIndex = index;
      //清单定额数据
      if (selectElement.kind == BranchProjectLevelConstant.qd || selectElement.kind == BranchProjectLevelConstant.de) {
        if (operateAction == 'up') {
          toIndex = toIndex - 1;
        }
        if (operateAction == 'down') {
          fromIndex = fromIndex + 1;
        }
        swapElements(unit.itemBillProjects, toIndex, fromIndex);
      } else {

        //获取当前分部下的所有数据
        let data = this.getFbDataByFbId(1, 300000, selectId, itemBillProjects, this.disPlayType, false);
        //过滤掉所有数据
        let newArr = itemBillProjects.filter(item => !data.includes(item));
        //获取插入数据位置
        let addIndex = this.getTargetIndex(itemBillProjects, selectElement, operateAction, newArr);
        if (operateAction == 'down') {
          addIndex = addIndex + 1;
        }
        // 使用 splice() 方法在指定位置插入数据
        newArr.splice(addIndex, 0, ...data);
        //填充数据
        unit.itemBillProjects = newArr;

      }

    }
    if (type == 'csxm') {
      let measureProjectTables = unit.measureProjectTables;
      let selectElement = measureProjectTables.filter(fb => fb.sequenceNbr == selectId)[0];
      //找到要移动的定额索引
      let mindex = -1;
      for (let i = 0; i < measureProjectTables.length; i++) {
        mindex++;
        if (measureProjectTables[i].sequenceNbr == selectId) {
          break;
        }
      }

      let toIndex = mindex;
      let fromIndex = mindex;
      //清单定额数据
      if (selectElement.kind == BranchProjectLevelConstant.qd || selectElement.kind == BranchProjectLevelConstant.de) {
        if (operateAction == 'up') {
          toIndex = toIndex - 1;
        }
        if (operateAction == 'down') {
          fromIndex = fromIndex + 1;
        }
        swapElements(measureProjectTables, toIndex, fromIndex);
      } else {

        //获取当前措施分部下的所有数据
        let data = this.getCsxmDataByFbId(selectElement, measureProjectTables);
        //过滤掉所有数据
        let newArr = measureProjectTables.filter(item => !data.includes(item));
        //获取插入数据位置
        let addIndex = this.getCsxmTargetIndex(measureProjectTables, selectElement, operateAction);
        // 使用 splice() 方法在指定位置插入数据
        newArr.splice(addIndex, 0, ...data);
        //填充数据
        unit.measureProjectTables = newArr;

      }


      //找到要移动的定额索引
      let index = -1;
      for (let i = 0; i < unit.measureProjectTables.length; i++) {
        index++;
        if (unit.measureProjectTables[i].sequenceNbr == selectId) {
          break;
        }
      }
      if (operateAction == 'up') {
        swapElements(unit.measureProjectTables, index - 1, index);
      }
      if (operateAction == 'down') {
        swapElements(unit.measureProjectTables, index, index + 1);
      }
    }

    //重新设置下挂清单定额数据的dispNo
    this.againDispNoV1(unit);

  }


  getTargetIndex(itemBillProjects, selectElement, operateAction, newArr) {
    //如果是分部 获取要换的分部 下标
    let fb = itemBillProjects.filter(fb => fb.parentId == selectElement.parentId);
    let fbIndex = -1;
    for (let i = 0; i < fb.length; i++) {
      fbIndex = fbIndex + 1;
      if (fb[i].sequenceNbr == selectElement.sequenceNbr) {
        break;
      }
    }
    let fbElement;
    //获取指定指定分布再原数据中的索引
    if (operateAction == 'up') {
      fbElement = fb[fbIndex - 1];
    } else {
      //获取下一个分部的所有数据
      fbElement = fb[fbIndex + 1];
      let data = this.getFbDataByFbId(1, 300000, fbElement.sequenceNbr, newArr, this.disPlayType, false);
      fbElement = data[data.length - 1];
    }
    let findex = -1;
    for (let i = 0; i < newArr.length; i++) {
      findex++;
      if (newArr[i].sequenceNbr == fbElement.sequenceNbr) {
        break;
      }
    }
    return findex;
  }

  /**
   * 获取升级降级的目标索引位置
   * @param itemBillProjects
   * @param selectElement
   * @param operateAction
   * @returns {number}
   */
  getSjJjTargetIndex(itemBillProjects, selectElement, operateAction, newArr) {

    let fbElement;
    //获取指定指定分布再原数据中的索引
    if (operateAction == 'up') {
      fbElement = itemBillProjects.find(fb => fb.sequenceNbr == selectElement.parentId);
      let data = this.getFbDataByFbId(1, 300000, fbElement.sequenceNbr, newArr, this.disPlayType, true);
      fbElement = data[data.length - 1];
    } else {
      //如果是分部 获取要换的分部 下标
      let fb = itemBillProjects.filter(fb => fb.parentId == selectElement.parentId);
      // let fbIndex=-1;
      // for (let i = 0; i < fb.length; i++) {
      //     fbIndex=fbIndex+1;
      //     if (fb[i].sequenceNbr == selectElement.sequenceNbr) {
      //         break;
      //     }
      // }
      //获取下一个分部的所有数据
      fbElement = fb.find(i => i.index == selectElement.index - 1);
      let data = this.getFbDataByFbId(1, 300000, fbElement.sequenceNbr, newArr, this.disPlayType, true);
      fbElement = data[data.length - 1];

    }
    let findex = -1;
    for (let i = 0; i < newArr.length; i++) {
      findex++;
      if (newArr[i].sequenceNbr == fbElement.sequenceNbr) {
        break;
      }
    }
    return findex;
  }


  getCsxmTargetIndex(measureProjectTables, selectElement, operateAction) {
    //如果是分部 获取要换的分部 下标
    let fb = measureProjectTables.filter(fb => fb.parentId == selectElement.parentId);
    let fbIndex = -1;
    for (let i = 0; i < fb.length; i++) {
      fbIndex = fbIndex + 1;
      if (fb[i].sequenceNbr == selectElement.sequenceNbr) {
        break;
      }
    }
    let fbElement;
    //获取指定指定分布再原数据中的索引
    if (operateAction == 'up') {
      fbElement = fb[fbIndex - 1];
    } else {
      //获取下一个分部的所有数据
      fbElement = fb[fbIndex + 1];
      let data = this.getCsxmDataByFbId(fbElement, measureProjectTables);
      fbElement = data[data.length - 1];
    }
    let findex = -1;
    for (let i = 0; i < measureProjectTables.length; i++) {
      findex++;
      if (measureProjectTables[i].sequenceNbr == fbElement.sequenceNbr) {
        break;
      }
    }
    return findex;
  }


  /**
   * 分部数据升级降级
   * @param operateAction
   * @param selectId   选中行id
   * @param unit   单位
   * 升级是升到父级的下一行位置
   * @returns {Promise<void>}
   */
  async fbDataUpAndDownOld(operateAction, selectId, unit, type) {
    //所有分部数据
    let itemBillProjects = unit.itemBillProjects;
    //获取当前选中数据
    let selectElement = itemBillProjects.find(fb => fb.sequenceNbr == selectId);
    //获取当前分部下的所有数据
    let fbDataByFbId = this.getFbDataByFbId(1, 300000, selectId, itemBillProjects, this.disPlayType, true);
    //过滤掉所有数据
    let newArr = itemBillProjects.filter(item => !fbDataByFbId.includes(item));
    //获取分部操作后的parentId
    let parentId;
    if (operateAction == 'up') {
      let parent = itemBillProjects.find(fb => fb.sequenceNbr == selectElement.parentId);
      parentId = parent.parentId;
      if (parent.kind == '01') {
        fbDataByFbId[0].kind = parent.kind;
      }

    } else {
      let findex = -1;
      for (let i = 0; i < itemBillProjects.length; i++) {
        findex++;
        if (itemBillProjects[i].sequenceNbr == selectElement.sequenceNbr) {
          break;
        }
      }
      //降级是降至上一个同级别的最后一个下级最后
      parentId = itemBillProjects[findex - 1].sequenceNbr;
      fbDataByFbId[0].kind = '02';
    }
    //获取插入数据位置
    let addIndex = this.getSjJjTargetIndex(itemBillProjects, selectElement, operateAction, newArr);

    //修改新数据的parentId
    fbDataByFbId[0].parentId = parentId;
    // 使用 splice() 方法在指定位置插入数据
    newArr.splice(addIndex + 1, 0, ...fbDataByFbId);
    //填充数据
    unit.itemBillProjects = newArr;
    //重新设置下挂清单定额数据的dispNo
    this.againDispNoV1(newArr);
  }

  async fbDataUpAndDown(operateAction, selectId, unit, type, constructId, singleId, unitId) {
    let fbList=[];
    //所有分部数据
    let allData = unit.itemBillProjects;
    let nodeById = allData.getNodeById(selectId);
    let fbDataByFbId = allData.findAllSubsets(nodeById);
    //获取所有数据
    let itemBillProjects = allData.getAllNodes();
    //获取当前选中数据
    let selectElement = itemBillProjects.find(fb => fb.sequenceNbr == selectId);

    //获取当前分部下的所有数据
    // let fbDataByFbId = this.getFbDataByFbId(1, 300000, selectId, itemBillProjects, this.disPlayType, true);

    //过滤掉所有数据
    let newArr = itemBillProjects.filter(item => !fbDataByFbId.includes(item));
    //获取分部操作后的parentId
    let parentId;
    let parent;
    if (operateAction == 'up') {
      parent = itemBillProjects.find(fb => fb.sequenceNbr == selectElement.parentId);
      parentId = parent.parentId;
      fbList.push(parent);
      if (parent.kind == '01') {
        fbDataByFbId[0].kind = parent.kind;
      }
      if(ObjectUtils.isNotEmpty(parent.children)){
        if(parent.children.length==1){
          parent.displaySign = 0;
        }
      }

    } else {
      // let findex = -1;
      // for (let i = 0; i < itemBillProjects.length; i++) {
      //     findex++;
      //     if (itemBillProjects[i].sequenceNbr == selectElement.sequenceNbr) {
      //         break;
      //     }
      // }
      //降级是降至上一个同级别的最后一个下级最后
      parent = itemBillProjects.filter(fb => fb.sequenceNbr == selectElement.parentId)[0].children.find(f => f.index == selectElement.index - 1);
      parentId = parent.sequenceNbr;
      fbDataByFbId[0].kind = '02';
      fbList.push(parent);
    }
    //获取插入数据位置
    let addIndex = this.getSjJjTargetIndex(itemBillProjects, selectElement, operateAction, newArr);
    //修改新数据的parentId
    fbDataByFbId[0].parentId = parentId;
    // 使用 splice() 方法在指定位置插入数据
    newArr.splice(addIndex + 1, 0, ...fbDataByFbId);
    //填充数据
    // unit.itemBillProjects=newArr;
    let find = newArr.find(item => item.sequenceNbr == parentId);

    find.displaySign = 1;
    fbList.push(find);
    //重新设置下挂清单定额数据的dispNo
    this.againDispNoV1(newArr);
    //对数据进行排序处理
    newArr.sort((a, b) => a.index - b.index);
    //转为树
    unit.itemBillProjects =arrayToTree(newArr);
    //状态数据处理
    this.setItemDtaStatus(unit.itemBillProjects.getAllNodes());
    //计算单价构成数据
    //获取所有的分部数据
    // let fbList = itemBillProjects.filter(fb => fb.kind == BranchProjectLevelConstant.fb || fb.kind == BranchProjectLevelConstant.zfb);
    // for (const fb of fbList) {
    //   let unitApplicationContext = new UnitApplicationContext({ constructId, singleId, unitId, pageType: 'fbfx' });
    //   unitApplicationContext.item = fb;
    //   await unitApplicationContext.after();
    // }

    for(const fb of fbList){
      this.fbZhhj(fb);
    }

  }


  /**
   * 分部分项层级拖拽调整
   * @param fbTreeModel   调整后的层级机构
   */
  async fbDragMoveAdjust(constructId, singleId, unitId, sourceFbId,targetFbId,positionFlag) {
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //获取源分部数据
    let fbFx =PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
    let find = fbFx.find(fb=>fb.sequenceNbr==sourceFbId);
    let copySource=ObjectUtils.cloneDeep(find);
    let parent = fbFx.find(fb=>fb.sequenceNbr==find.parentId);
    let targetFb = fbFx.find(fb=>fb.sequenceNbr==targetFbId);
    //如果sourceFbId  和 targetFbId  是同级别，标识移动分部位置
    //positionFlag  1 同级别   0 子集
    if(find.parentId == targetFb.parentId && positionFlag==1){
         //修改index 重新整理
      let children = parent.children;
      for(const c of children){
         if(c.index>targetFb.index){
             c.index++;
         }
      }
      find.index=targetFb.index+1;
      //重新排序
      fbFx.sortChildren(parent);
    }else  if(parent.sequenceNbr==targetFb.sequenceNbr){
       find.index=0;
       let index=1;
       let children = parent.children.filter(fb=>fb.sequenceNbr != find.sequenceNbr);
       for(const c of children){
         c.index=index;
         index++;
       }
       //重新排序
       fbFx.sortChildren(parent);
    }else {
     //  //将分部移动到第一个
     // // if(parent.kind==BranchProjectLevelConstant.top && targetFb.kind == BranchProjectLevelConstant.top){
       if (targetFb.kind == BranchProjectLevelConstant.top) {
         find.kind = BranchProjectLevelConstant.fb;
       } else {
         find.kind = BranchProjectLevelConstant.zfb;
       }
       if(positionFlag==1){
         find.parentId = targetFbId.parentId;
         find.parent = targetFbId.parent;
       }else {
         find.parentId = targetFbId;
         find.parent = targetFb;
       }
       if (ObjectUtils.isEmpty(targetFb.children)) {
         targetFb.children = [];
       }
       targetFb.children.push(find);
       //删除原本父级下的数据
       if (parent.children.length > 1) {
         parent.children = parent.children.filter(c => c.sequenceNbr != find.sequenceNbr);
       } else {
         parent.children = [];
         //修改收起展开状态
         parent.displaySign = 0;
       }
       targetFb.displaySign = 1;
       let fbList = [];
       fbList.push(find);
       fbList.push(targetFb);
       fbList.push(parent);
       for (const fb of fbList) {
         OptionMenuHandler.setOptionMenu(fb);
       }
       //状态数据处理
       this.setItemDtaStatus(PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes());
       // await this.againFbHj(fbFx, 'fbfx', constructId, singleId, unitId);
       //计算分部综合合价
       fbList.push(copySource);
       for (const fb of fbList) {
         this.fbZhhj(fb);
       }
     }
    // }
    //序号重排
    PricingFileFindUtils.getFbFx(constructId, singleId, unitId).refreshAssignDispNos();
  }



  /**
   * 分部分项层级拖拽调整
   * @param fbTreeModel   调整后的层级机构
   */
  async fbDragMoveAdjustOld(constructId, singleId, unitId, fbTreeModel) {
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let list = [];
    this.getTreeChild(fbTreeModel, list);
    //重新组装分部分项数据
    let newItemList = [];
    let allData = unit.itemBillProjects;
    //获取所有数据
    let itemBillProjects = allData.getAllNodes();
    for (const item of list) {
      let sequenceNbr = item.sequenceNbr;
      let find = itemBillProjects.find(data => data.sequenceNbr == sequenceNbr);
      // find.displaySign=item.displaySign;
      newItemList.push(find);
      //获取下挂的所有清单数据
      let qdList = itemBillProjects.filter(data => data.parentId == sequenceNbr && data.kind == '03');
      if (ObjectUtils.isNotEmpty(qdList)) {
        for (const qd of qdList) {
          newItemList.push(qd);
          //获取定额数据
          let deList = itemBillProjects.filter(data => data.parentId == qd.sequenceNbr);
          if (ObjectUtils.isNotEmpty(deList)) {
            for (const de of deList) {
              newItemList.push(de);
            }
          }
        }
      }
      find.parentId = item.parentId;
      find.kind = item.kind;
      find.displaySign = 1;
    }
    //重置数据
    // unit.itemBillProjects=newItemList;
    //重新设置下挂清单定额数据的dispNo
    this.againDispNoV1(newItemList);
    //状态数据处理
    this.setItemDtaStatus(newItemList);
    //对数据进行排序处理
    // newItemList.sort((a, b) => a.index - b.index);
    //转为树
    unit.itemBillProjects = arrayToTree(newItemList);
    await this.againFbHj(unit.itemBillProjects, 'fbfx', constructId, singleId, unitId);
  }



  //从新计算所有的分部分项合价
  async againFbHj(allData, type, constructId, singleId, unitId) {
    let fbList = allData.filter(fb => fb.kind == BranchProjectLevelConstant.fb || fb.kind == BranchProjectLevelConstant.zfb);
    let unitApplicationContext = new UnitApplicationContext({ constructId, singleId, unitId, pageType: type });
    for (const fb of fbList) {
      unitApplicationContext.item = fb;
      await unitApplicationContext.after();
    }
  }

  //统计分布综合合价
  fbZhhj(fb){
    //获取分布父级
    let parent = fb.parent;
    if(ObjectUtils.isNotEmpty(parent) && parent.kind!=BranchProjectLevelConstant.top){
        //获取子集分部
      parent.total=0;
      let children = parent.children;
      for(const c of children){
        parent.total=NumberUtil.numberScale2(parent.total+c.total);
      }
      this.fbZhhj(parent);
    }
  }




  getTreeChild(fbTreeModel, list) {
    let model = {};
    model.sequenceNbr = fbTreeModel.sequenceNbr;
    model.parentId = fbTreeModel.parentId;
    model.kind = fbTreeModel.kind;
    list.push(model);
    //获取子集数据
    let childTreeModel = fbTreeModel.childTreeModel;
    if (childTreeModel.length > 0) {
      for (const model of childTreeModel) {
        this.getTreeChild(model, list);
      }
    }
  }


  againDispNoV1(itemBillProjectDTOS) {
    try {
      // 给 dispNo 重新排序
      // let itemBillProjectDTOS = unit.itemBillProjects;
      let parentId = null;
      //获取单位工程行数据
      const collect = itemBillProjectDTOS.filter(item => {
        return item.kind == BranchProjectLevelConstant.top;
      });
      if (collect.length > 0) {
        parentId = collect[0].sequenceNbr;
        this.setDispNoV1(parentId, 1, 1, itemBillProjectDTOS);
      }

    } catch (err) {
      throw new Error('An error occurred during reordering dispNo:', err);
    }
  }


  setDispNoV1(sequenceNbr, qdDispNo, deDispNo, itemBillProjectDTOList) {
    // 查询当前单位工程数据
    const itemBillProjectEntities = itemBillProjectDTOList.filter(item => item.parentId === sequenceNbr);

    if (itemBillProjectEntities.length > 0) {
      deDispNo = 1;
      for (const item of itemBillProjectEntities) {
        // 清单
        if (item.kind === BranchProjectLevelConstant.qd) {
          // 设置
          item.dispNo = String(qdDispNo);
          qdDispNo++;
        }
        // 定额
        if (item.kind === BranchProjectLevelConstant.de) {
          item.dispNo = (qdDispNo - 1) + '.' + deDispNo;
          deDispNo += 1;
        }
        qdDispNo = this.setDispNoV1(item.sequenceNbr, qdDispNo, deDispNo, itemBillProjectDTOList);
      }
    }
    return qdDispNo;
  }


  /**
   * 复制行
   * @param constructId
   * @param singleId
   * @param unitId
   * @param sequenceNbrs 复制行id
   * @param menuType 操作类型  分部分项 . 措施项目 ..
   */
  copyLine(sequenceNbrs, menuType) {
    PricingFileFindUtils.setProjectBuffer(null);
    //TODO 根据 menuType 区分调用   PricingFileFindUtils.getFbFx   PricingFileFindUtils.getCsxm
    //TODO 查询出来的数据如果有子项 那就连子项一起复制
    let constructId = ParamUtils.getPatram('commonParam').constructId;
    let singleId = ParamUtils.getPatram('commonParam').singleId;
    let unitId = ParamUtils.getPatram('commonParam').unitId;
    let unitProject = _.cloneDeep(PricingFileFindUtils.getUnit(constructId, singleId, unitId));
    let coverData = {
      itemBillProjects: [],//分部分项
      measureProjectTables: [],//措施项目
      otherProjects: [],//其他项目
      unitCostSummarys: []
    };
    let filter = (list) => {
      let arr = [];
      sequenceNbrs.forEach(sequenceNbr => {
        let item = list.getNodeById(sequenceNbr);
        arr.push({ ...item, parent: null, children: null, prev: null, next: null });
      });
      return arr;
    };
    if (menuType == 'fbfx') {//拿到要复制到额分部分项
      let itemBillProjects = filter(unitProject.itemBillProjects);
      let itemBillProjectsKey = _.map(itemBillProjects, (item) => {
        return item.sequenceNbr;
      });
      for (let i = 0; i < itemBillProjects.length; i++) {
        let item = itemBillProjects[i];
        //如果是 HSGCL 额外处理
        if (item.quantityExpression == 'HSGCL' && !_.includes(itemBillProjectsKey, item.relationDeId)) {
          item.quantityExpression = '0';
          item.quantityExpressionNbr = 0;
          item.quantity =  0;
        }
      }
      coverData.itemBillProjects = itemBillProjects;

    } else {//拿到要复制的措施项目
      coverData.measureProjectTables = filter(unitProject.measureProjectTables);
    }
    PricingFileFindUtils.setProjectBuffer(Object.assign({}, unitProject, coverData));

  }


  /**
   * 复制主材设备数据
   * @param sequenceNbrs 复制行id
   * @param menuType 操作类型  分部分项 . 措施项目 ..
   */
  copyLineZcsb(sequenceNbrs) {
    PricingFileFindUtils.setProjectBuffer(null);
    let constructId = ParamUtils.getPatram('commonParam').constructId;
    let singleId = ParamUtils.getPatram('commonParam').singleId;
    let unitId = ParamUtils.getPatram('commonParam').unitId;
    //获取主材设备数据
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let constructProjectRcjs = unit.constructProjectRcjs.filter(rcj => sequenceNbrs.includes(rcj.sequenceNbr));
    PricingFileFindUtils.setProjectBuffer(constructProjectRcjs);

  }


  /**
   * 增加一行数据
   * 补充： 迭代1时，数据大多为插入在当前数据结束位置，少部分数据插入在父级数据下一行
   *       迭代2时，所有数据插入位置修正为本数据开始前一行
   * @param pointLine    前端鼠标点击的行
   * @param newLine      前端新增的行信息
   * @param doAfterInsert
   * @param allData    全量数据
   */
  insertLine(pointLine, newLine, doAfterInsert, allData, rootLineId, bzhs) {

    // **正常逻辑为 高级可插入低级别，同级别可插入同级别** 特殊情况处理：子分部插入分部
    let org;
    if (pointLine.kind === BranchProjectLevelConstant.zfb && newLine.kind === BranchProjectLevelConstant.fb) {
      org = pointLine;
      pointLine = this._findLineById(allData, pointLine.parentId);
      newLine.kind = BranchProjectLevelConstant.zfb;
    }
    if (pointLine.displaySign === BranchProjectDisplayConstant.close) {
      this.openLine(pointLine, allData);
    }
    // 获取拆分的index， index为插入行的下标
    let splitIndex = this._getSplitIndex(allData, pointLine, newLine, org, bzhs);               // int型
    // 根据index 将数组拆分为 [top, below]
    let top = allData.slice(0, splitIndex);                                         // 数组 top   [0, splitIndex)
    let below = allData.slice(splitIndex, allData.length);                          // 数组 below [splitIndex, 0]
    // 为newLine填充 id, parentId, num, display, displaySign
    this._fillNewLine(allData, splitIndex, newLine, pointLine);
    newLine.isSupplement = 0;
    // 处理展开收起的箭头
    this._dealDisplaySignWhenInsert(top, pointLine, newLine, below);
    // 重构下半部分数据 包括parentId, num
    this._rebuildBelowDatas(newLine, below);
    // 合并数据并重新写入json
    let newDatas = this._rewriteJsonFile(top, newLine, below);
    // 回调函数 插入完成后的回调函数
    if (doAfterInsert) {
      doAfterInsert(newDatas, newLine, splitIndex);
    }

    // 计算显示的index
    let displayIndex = this._getInsertPage(allData, pointLine, splitIndex, rootLineId);

    if (newLine.kind === BranchProjectLevelConstant.de) {
      //处理费用定额重新计算问题
      let constructId = ParamUtils.getPatram('commonParam').constructId;
      let singleId = ParamUtils.getPatram('commonParam').singleId;
      let unitId = ParamUtils.getPatram('commonParam').unitId;
    }
    if (newLine.kind === BranchProjectLevelConstant.de && !newLine.isCostDe) {
      newLine.isCostDe = this.service.constructCostMathService.costDeByDe(newLine);
      if (!newLine.isCostDe) {
        newLine.isCostDe = 0;
      }
    }

    return {
      'allDatas': newDatas,
      'newData': newLine,
      'insertIndex': splitIndex,
      'displayIndex': displayIndex
    };
  }


  updateQdFeature(constructId, singleId, unitId, pointLine, updateStr) {
    pointLine.projectAttr = updateStr;
    this.service.listFeatureService.cleanFeature(constructId, singleId, unitId, pointLine.sequenceNbr);
  }

  async replaceFronIndexPage(allData, constructId, singleId, unitWorkId, selectId, replaceId, type, conversionCoefficient, kind, unit, caculateParam, feeDe, type1, is2022) {
    // is2022 = is2022 || PricingFileFindUtils.is22De(constructId);
    // 1.获取行数据
    let replaceLine = this._findLineById(allData, replaceId);
    // 2.删除行数据相关的关系（关联的明细，单价构成）.78
    this.service.itemBillProjectProcess.delLineRelevantData(replaceLine, allData, constructId, singleId, unitWorkId, true);


    //定额工程量 表达式
    /*let qdLine = this._findLineById(allData, replaceLine.parentId);
        let unitQd = qdLine.unit;
        let unitDe = replaceLine.unit;
        // 单位一致 继承清单
        if (this._isSameUnit(unitQd, unitDe)) {
            replaceLine.quantityExpression = qdLine.quantityExpression;
            replaceLine.quantityExpressionNbr = qdLine.quantityExpressionNbr;
            replaceLine.quantity = (replaceLine.quantityExpressionNbr / this.getUnitNum(replaceLine)).toFixed(6);
        } else {
            // 不一致，给 0
            replaceLine.quantityExpression = "0";
            replaceLine.quantityExpressionNbr = 0;
            replaceLine.quantity = 0;
        }*/
    let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId);
    if (unitProject.conversionInfoList && unitProject.conversionInfoList.length > 0) {
      unitProject.conversionInfoList = unitProject.conversionInfoList.filter(f => f.deId != replaceLine.sequenceNbr);
    }
    // 3.调用替换接口（编辑区（定额清单， 人材机），明细区）
    let updateInfo = { 'indexId': selectId, 'unit': unit, 'is2022': is2022, 'type': type };
    if (feeDe) {
      for (let attr in feeDe) {
        if (attr === 'indexId' || attr === 'unit') continue;
        updateInfo[attr] = feeDe[attr];
      }
    }
    await this.service.baseBranchProjectOptionService.updateFromIndexPage(allData,
      constructId, singleId, unitWorkId, replaceLine,
      updateInfo,
      null, caculateParam, type1);
    // 4.如果是定额 借换定状态修改
    // if (kind === BranchProjectLevelConstant.de && replaceLine.rcjFlag !== 1) {
    //   let dao = this.app.appDataSource.manager.getRepository(updateInfo.is2022 ? BaseDe2022 : BaseDe);
    //   let result = await dao.findOne({
    //     where: { sequenceNbr: selectId }
    //   });
    //   /*        let sql = "select library_code as code from base_de where sequence_nbr = ?";
    //                 let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all(selectId);*/
    //   let libCode = result.libraryCode;
    //   let mainLibCode = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId).mainDeLibrary;
    //   if (!replaceLine.appendType) {
    //     replaceLine.appendType = [];
    //   }
    //   replaceLine.appendType = replaceLine.appendType.filter(a => a !== '借');
    //   // 新替换的数据一定是标准人材机
    //   replaceLine.appendType = replaceLine.appendType.filter(a => a !== '换');
    //   if (libCode !== mainLibCode) {
    //     if (!replaceLine.isCostDe || replaceLine.isCostDe == 0) {
    //       replaceLine.appendType.push('借');
    //     }
    //   }
    // }


    return replaceLine;
  }

  costFileChanged(costFile, costFileCode, costMajorName) {
    return costFile.code !== costFileCode || costFile.name !== costMajorName;
  }

  async updateByList(constructId, singleId, unitWorkId, pointLineId, upDateInfo, type) {
    let updateStrategy = new UpdateStrategy({ constructId, singleId, unitId: unitWorkId, pageType: type });
    return await updateStrategy.execute({ pointLineId, upDateInfo });

  }

  /**
   * type csxm fbfx
   */
  async updateByListOld(constructId, singleId, unitWorkId, pointLineId, upDateInfo, type) {
    let allData;
    if (type === 'fbfx') {
      allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitWorkId);
    } else {
      allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitWorkId);
    }
    let pointLine = allData.filter(e => e.sequenceNbr === pointLineId)[0];
    let upDateOk = true;
    if (upDateInfo.bdName !== undefined || upDateInfo.name !== undefined) { // 名称
      if (type === 'fbfx') {
        pointLine.bdName = upDateInfo.bdName;
        pointLine.name = upDateInfo.bdName;

        //解决zxz分页查询中覆盖 name的操作
        pointLine.orhName = upDateInfo.bdName;
      } else {
        pointLine.name = upDateInfo.name;
        pointLine.bdName = upDateInfo.name;

        //解决zxz分页查询中覆盖 name的操作
        pointLine.orhName = upDateInfo.name;
      }
      if (pointLine.rcjFlag && pointLine.rcjFlag === 1) {
        let rcj = PricingFileFindUtils.getRcjList(constructId, singleId, unitWorkId).filter(f => f.deId === pointLine.sequenceNbr);
        if (rcj) {
          rcj = rcj[0];
          rcj.materialName = upDateInfo.name ? upDateInfo.name : upDateInfo.bdName;
        }

      }
    }
    if (upDateInfo.projectAttr !== undefined) { // 项目特征
      pointLine.projectAttr = upDateInfo.projectAttr;
      pointLine.oriAttr = upDateInfo.projectAttr;
    }
    if (upDateInfo.unit !== undefined && upDateInfo.unit !== pointLine.unit) { // 单位 (单位修改 导致工程量 变动 导致人材机合价变动)
      let orgUnitNum = Number.parseInt(pointLine.unit);
      if (Number.isNaN(orgUnitNum)) {
        orgUnitNum = 1;
      }
      let nowUnitNum = Number.parseInt(upDateInfo.unit);
      if (Number.isNaN(nowUnitNum)) {
        nowUnitNum = 1;
      }
      pointLine.unit = upDateInfo.unit;
      if (pointLine.rcjFlag && pointLine.rcjFlag === 1) {
        let rcj = PricingFileFindUtils.getRcjList(constructId, singleId, unitWorkId).filter(f => f.deId === pointLine.sequenceNbr);
        if (rcj) {
          rcj = rcj[0];
          rcj.unit = upDateInfo.unit ? upDateInfo.unit : upDateInfo.unit;
        }

      }
      if (orgUnitNum !== nowUnitNum) {
        nowUnitNum = nowUnitNum ? nowUnitNum : 1;
        pointLine.quantity = NumberUtil.divide(pointLine.quantityExpressionNbr, nowUnitNum);
        this.service.rcjProcess.reSetquantity(constructId, singleId, unitWorkId, pointLine);
        this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitWorkId, pointLine.sequenceNbr, true,
          allData);
      }
    }
    if (upDateInfo.costFile && upDateInfo.costFile.code && upDateInfo.costFile.name && this.costFileChanged(upDateInfo.costFile, pointLine.costFileCode, pointLine.costMajorName)) { // 取费文件 会导致重新计算定价
      let isMain = 'SUIZHUGONGCHENG' == upDateInfo.costFile.code;
      pointLine.qfCode = upDateInfo.costFile.code;//这里指的是取费文件对应的单价模板code
      pointLine.costMajorName = upDateInfo.costFile.name;
      if (pointLine.kind === BranchProjectLevelConstant.de) {
        //如果是系统模版的情况下，需要重新计算取费文件
        let qfCode = upDateInfo.costFile.code;
        if (UPCContext.qfCodeMap.has(constructId + qfCode)) {
          qfCode = UPCContext.qfCodeMap.get(constructId + qfCode);
        }
        //获取取费文件
        let feeFile = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId).feeFiles.filter(f => {
          //如果是随主工程取费文件，那么找主取费文件的code
          return isMain ? (f.defaultFeeFlag && f.defaultFeeFlag === 1) : (f.feeFileCode === qfCode);
        })[0];
        if (!feeFile) {
          feeFile = await this.service.baseFeeFileService.updateFBFXFeeFile(constructId, singleId, unitWorkId, qfCode);
        }
        pointLine.costFileCode = feeFile.feeFileCode;//这个是当前选择的取费文件的qfcode
        //证明是增量模板
        if (qfCode == upDateInfo.costFile.code) {
          pointLine.qfCode = feeFile.feeFileCode;//这里指的是单间构成的模板code
        }
        this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitWorkId, pointLine.sequenceNbr, true, allData, isMain);
      }
    }

    if (upDateInfo.description !== undefined) { //  备注
      pointLine.description = upDateInfo.description;
    }
    //修改措施类别
    if (upDateInfo.itemCategory && upDateInfo.itemCategory != '') {
      pointLine.itemCategory = upDateInfo.itemCategory;
      pointLine.constructionMeasureType = upDateInfo.itemCategory === '单价措施项目' ? ConstructionMeasureTypeConstant.DJCS : ConstructionMeasureTypeConstant.ZJCS;
    }
    // 单价修改
    if (pointLine.kind === BranchProjectLevelConstant.de && upDateInfo.zjfPrice && upDateInfo.zjfPrice !== pointLine.zjfPrice) {
      let rate = upDateInfo.zjfPrice / pointLine.zjfPrice;
      // if (rate != 1) {
      //   if (!pointLine.appendType) {
      //     pointLine.appendType = [];
      //   } else {
      //     pointLine.appendType = pointLine.appendType.filter(f => f !== '换');
      //   }
      //   pointLine.appendType.push('换');
      // }
      let orgResQty = {};
      let rcjs = this.service.rcjProcess.queryRcjDataByDeId(pointLine.sequenceNbr, constructId, singleId, unitWorkId);

      if (pointLine.rcjFlag === 1 && rcjs[0].rcjDetailsDTOs && rcjs[0].rcjDetailsDTOs.length > 0) { //如果是人材机，需要修改二级配比材料
        if (ObjectUtils.isNotEmpty(rcjs[0].rcjDetailsDTOs)) {
          rcjs[0].rcjDetailsDTOs.forEach(d => {
            d.resQty = NumberUtil.numberScale6(d.resQty * rate);
            d.totalNumber = NumberUtil.numberScale4(d.resQty * rcjs[0].totalNumber);
            d.total = NumberUtil.numberScale2(d.marketPrice * d.totalNumber);
          });
        }
      } else { //定额，不需要修改配比子级材料
        for (let i = 0; i < rcjs.length; ++i) {
          let baseNum = 1;
          if (pointLine.baseNum) {
            if (!ObjectUtils.isEmpty(pointLine.baseNum['def'])) {
              baseNum = pointLine.baseNum['def'];
            }
            if (!ObjectUtils.isEmpty(pointLine.baseNum[rcjs[i].kind])) {
              baseNum = pointLine.baseNum[pointLine.kind];
            }
          }
          let percent = 1;
          if (rcjs[i].unit === '%') {
            percent = 0.01;
          }

          orgResQty[rcjs[i].sequenceNbr] = rcjs[i].resQty;
          rcjs[i].resQty = NumberUtil.numberScale(NumberUtil.numberScale6(rcjs[i].resQty * rate), 4);
          // 合计数量
          rcjs[i].totalNumber = NumberUtil.multiply(NumberUtil.multiply(rcjs[i].resQty, pointLine.quantity), baseNum);
          rcjs[i].totalNumber = NumberUtil.multiply(rcjs[i].totalNumber, percent);
          // 合价
          rcjs[i].total = NumberUtil.numberScale(NumberUtil.multiply(rcjs[i].totalNumber, rcjs[i].marketPrice));
        }
      }

      let orgFees = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId).feeBuild[pointLine.sequenceNbr];
      this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitWorkId, pointLine.sequenceNbr,
        true, allData);

      let nowFees = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId).feeBuild[pointLine.sequenceNbr];
      if ((orgFees[1].unitPrice !== 0 && nowFees[1].unitPrice === 0) ||
        (orgFees[2].unitPrice !== 0 && nowFees[2].unitPrice === 0) ||
        (orgFees[3].unitPrice !== 0 && nowFees[3].unitPrice === 0) ||
        (orgFees[4].unitPrice !== 0 && nowFees[4].unitPrice === 0)) {
        // 如果调整后价格算出来了0，则回退 人材机消耗量，然后重新计算
        for (let i = 0; i < rcjs.length; ++i) {
          rcjs[i].resQty = orgResQty[rcjs[i].sequenceNbr];
        }
        this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitWorkId, pointLine.sequenceNbr,
          true, allData);
        upDateOk = false;
      }

      this.service.rcjProcess.reCaculateRcjPrice(pointLine, rcjs, constructId, singleId, unitWorkId);
    }
    // 工程量
    if (upDateInfo.quantityExpression && upDateInfo.quantityExpression !== pointLine.quantityExpression) {
      pointLine.quantityExpression = upDateInfo.quantityExpression.toUpperCase();
      this.caculateQuantityExpressionAndQuantity(constructId, singleId, unitWorkId, pointLine);

      if (pointLine.kind === BranchProjectLevelConstant.de) {
        //处理kind =3
        this.service.conversionDeService.deQuantityChaneToKind3b(constructId, singleId, unitWorkId, allData, pointLine);
        let rcjs = this.service.rcjProcess.getRcjListByDeId(pointLine.sequenceNbr, constructId, singleId, unitWorkId);
        this.service.rcjProcess.reCaculateRcjPrice(pointLine, rcjs, constructId, singleId, unitWorkId);
        this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitWorkId, pointLine.sequenceNbr,
          true, allData);
      } else if (pointLine.kind === BranchProjectLevelConstant.qd) {
        //处理QDL 联动
        await this.updateQdToDeQDL(constructId, singleId, unitWorkId, allData, pointLine.sequenceNbr);
        this.service.unitPriceService.caculateQDUnitPrice(constructId, singleId, unitWorkId, pointLine.sequenceNbr,
          true, allData);
      }
      if (pointLine.quantityExpression.indexOf('GCLMXHJ') < 0) {
        this.service.quantitiesService.initDatas(pointLine);
      }
    }

    pointLine.index = upDateInfo.index;
    if (upDateInfo.measureType !== undefined) { // 施工组织措施类别
      pointLine.measureType = upDateInfo.measureType;
    }
    //触发自动记取
    await this.service.autoCostMathService.autoCostMath({ constructId, singleId, unitId: unitWorkId });
    //this.service.management.trigger("itemChange");

    return {
      'enableUpdatePrice': upDateOk,
      'lineInfo': pointLine
    };
  }

  /**
   * 修改清单工程量 如果定额工程量表达式中有QDL三个字母 修改定额工程量
   * @param constructId
   * @param singleId
   * @param unitId
   * @param allData 分部分项或者措施项目数据
   * @param id  清单id
   * @returns {Promise<void>}
   */
  async updateQdToDeQDL(constructId, singleId, unitId, allData, id) {
    let allNode = allData.getAllNodes();
    //清单
    let qdLine;
    //定额
    let deLines = [];
    for (let index = 0; index < allNode.length; ++index) {
      if (allNode[index].sequenceNbr === id) {
        qdLine = allNode[index];
      }
      if (qdLine && allNode[index].parentId === id) {
        let quantityExpression = allNode[index].quantityExpression + '';
        if (quantityExpression.includes('QDL')) {
          deLines.push(allNode[index]);
        }
      }
    }


    if (!ObjectUtils.isEmpty(deLines)) {
      for (let deLine of deLines) {
        this.caculateQuantityExpressionAndQuantity(constructId, singleId, unitId, deLine);
        //处理kind等于3的情况
        this.service.conversionDeService.deQuantityChaneToKind3b(constructId, singleId, unitId, allData, deLine);
        let rcjs = this.service.rcjProcess.getRcjListByDeId(deLine.sequenceNbr, constructId, singleId, unitId);
        //解决复制人材机 其他材料费计算错误问题
        //this.service.rcjProcess.reCaculateRcjPrice(deLine, rcjs, constructId, singleId, unitId);
        this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitId, deLine.sequenceNbr,
          true, allData);
      }
    }
  }


  /**
   * 从索引界面修改数据
   * @param allDatas      分部分项或措施项目全数据
   * @param constructId
   * @param singleId
   * @param unitWorkId
   * @param pointLine     当前行
   * @param upDateInfo    {{unit, indexId}}
   * @param isInvokeCountCostCodePrice  是否调用费用汇总计算的方法
   */
  async updateFromIndexPage(allDatas, constructId, singleId, unitWorkId, pointLine, upDateInfo, notCaculataPrice, caculateParam, type, isInvokeCountCostCodePrice) {
    // 获取修改的节点的每个父节点  由下到上排序 最大到分部 ； 如选择定额，则 parentLines = [清单, 子分部1， 子分部2,  分部]
    //let res = this._getPatents(allDatas, pointLine);
    //let parentLines = res.parentLines;
    let currentUpdateLine = allDatas.getNodeById(pointLine.sequenceNbr); //res.currentUpdateLine;
    if (currentUpdateLine.rcjFlag && currentUpdateLine.rcjFlag === 1) {
      // 人材 机
      await this.doUpdateRcjLine({ 'newData': pointLine }, constructId, singleId, unitWorkId, upDateInfo.indexId, upDateInfo.is2022);
      //主要是为了处理新插入人材机的合计数量计算
      let rcjs = this.service.rcjProcess.getRcjListByDeId(pointLine.sequenceNbr, constructId, singleId, unitWorkId);
      this.service.rcjProcess.reCaculateRcjPrice(pointLine, rcjs, constructId, singleId, unitWorkId);
      this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitWorkId, currentUpdateLine.sequenceNbr, true, allDatas, true);
    } else {
      // 清单 或 定额 执行修改逻辑，填充当前行，填充 特征，人材机等
      upDateInfo.constructId = constructId;
      upDateInfo.singleId = singleId;
      upDateInfo.unitId = unitWorkId;
      await this._doUpdateFronIndexPage(allDatas, constructId, singleId, unitWorkId, currentUpdateLine, [], upDateInfo, notCaculataPrice, caculateParam, type, isInvokeCountCostCodePrice);
      if (currentUpdateLine.relationDeId) {
        let relationLine = this.findFromAllById(constructId, singleId, unitWorkId, currentUpdateLine.relationDeId).item;
        relationLine.createDeId = null;
        upDateInfo.relationDeId = null;
      }
      // 借
      // if (currentUpdateLine.kind === BranchProjectLevelConstant.de && currentUpdateLine.rcjFlag !== 1) {
      //   let baseDe = await this.service.baseQdDeProcess.selectBaseQdOrDe(upDateInfo.indexId, BranchProjectLevelConstant.de, upDateInfo.is2022);
      //   /*       let sql = "select library_code as code from base_de where sequence_nbr = ?";
      //                  let sqlRes = this.app.betterSqlite3DataSource.prepare(sql).all(upDateInfo.indexId);*/
      //   let libCode = baseDe.libraryCode;
      //   let mainLibCode = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId).mainDeLibrary;
      //   if (!currentUpdateLine.appendType) {
      //     currentUpdateLine.appendType = [];
      //   }
      //   currentUpdateLine.appendType = currentUpdateLine.appendType.filter(a => a !== '借');
      //   if (libCode !== mainLibCode) {
      //     if (!currentUpdateLine.isCostDe || currentUpdateLine.isCostDe == 0) {
      //       currentUpdateLine.appendType.push('借');
      //     }
      //   }
      // }
      // if (currentUpdateLine.kind === BranchProjectLevelConstant.de){
      //     //处理费用定额重新计算问题
      //     this.service.autoCostMathService.autoCostMath({constructId:constructId, singleId:singleId, unitId:unitWorkId, de:currentUpdateLine});
      // }
    }
  }

  async upDateRcjLine(allData, pointLineId,
                      constructId, singleId, unitWorkId,
                      indexId, is2022) {
    let pointLine = this._findLineById(allData, pointLineId);
    let param = { 'newData': pointLine };
    await this.doUpdateRcjLine(param, constructId, singleId, unitWorkId, indexId, is2022);

    return pointLine.sequenceNbr;
  }

  async doUpdateRcjLine(pointLine, constructId, singleId, unitId, indexId, is2022) {
    // 1.获取人材机 (增加22定额)
    let {
      rcj,
      pb
    } = await this.service.rcjProcess.addRcjLineOnOptionMenu(constructId, singleId, unitId, indexId, null, is2022);
    // 2.删除旧的人材机关系 人材机删除 人材机关系表删除
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    if (unit.constructProjectRcjs && unit.constructProjectRcjs.length > 0) {
      let lastRcj = unit.constructProjectRcjs.find(r => r.parentId === pointLine.newData.sequenceNbr);
      unit.constructProjectRcjs = unit.constructProjectRcjs.filter(r => r.parentId !== pointLine.newData.sequenceNbr);
      if (lastRcj && unit.rcjDetailList && unit.rcjDetailList.length > 0) {
        unit.rcjDetailList = unit.rcjDetailList.filter(f => f.rcjId !== lastRcj.sequenceNbr);
      }
    }

    // 3.存入新的人材机
    await this._fillRcjLine(pointLine.newData, rcj, constructId, singleId, unitId, indexId, is2022);
    // 处理工程量表达式
    let qdLine = pointLine.newData.parent;// this._findLineById(all, pointLine.newData.parentId);
    if (this._isSameUnit(qdLine.unit, rcj.unit)) {
      rcj.quantityExpression = qdLine.quantityExpression;
      pointLine.newData.quantityExpression = qdLine.quantityExpression;
      this.caculateQuantityExpressionAndQuantity(constructId, singleId, unitId, pointLine.newData);
      rcj.quantityExpressionNbr = pointLine.newData.quantityExpressionNbr;
      rcj.quantity = pointLine.newData.quantity;
    } else {
      rcj.quantityExpression = '0';
      pointLine.newData.quantityExpression = '0';
      rcj.quantityExpressionNbr = 0;
      pointLine.newData.quantityExpressionNbr = 0;
      rcj.quantity = 0;
      pointLine.quantity = 0;
    }
    // 4.将人材机在人材机存放区域再存一份
    let rcjCopy = ConvertUtil.deepCopy(rcj);
    rcjCopy.sequenceNbr = Snowflake.nextId();
    rcjCopy.parentId = pointLine.newData.sequenceNbr;
    rcjCopy.deId = pointLine.newData.sequenceNbr;
    rcjCopy.resQty = 1; // 在操作区加入的人材机的消耗量定死为1
    rcjCopy.labelDe = true;
    rcjCopy.referenceRecord = ''.concat(rcjCopy.kind, rcjCopy.materialName,
      rcjCopy.specification, rcjCopy.unit, rcjCopy.dePrice);
    if (!PricingFileFindUtils.getUnit(constructId, singleId, unitId).constructProjectRcjs) {
      PricingFileFindUtils.getUnit(constructId, singleId, unitId).constructProjectRcjs = [];
    }
    PricingFileFindUtils.getUnit(constructId, singleId, unitId).constructProjectRcjs.push(rcjCopy);
    if (pb && pb.length > 0) {
      if (!unit.rcjDetailList) {
        unit.rcjDetailList = [];
      }
      for (let i = 0; i < pb.length; ++i) {
        pb[i].rcjId = rcjCopy.sequenceNbr;
        pb[i].totalNumber = NumberUtil.numberScale4(pb[i].resQty * rcjCopy.resQty);
        pb[i].total = NumberUtil.numberScale2(pb[i].marketPrice * pb[i].totalNumber);
        unit.rcjDetailList.push(pb[i]);
      }
    }
  }

  async wfillRcjLine(newData, baseRcj, constructId, singleId, unitWorkId, standardId) {
    await this._fillRcjLine(newData, baseRcj, constructId, singleId, unitWorkId, standardId);
  }

  async _fillRcjLine(newData, baseRcj, constructId, singleId, unitWorkId, standardId, is2022) {
    newData.unitCoefficient = baseRcj.unitCoefficient;
    newData.quantityExpression = newData.quantityExpression ? newData.quantityExpression : '0';
    if(newData.unit!=baseRcj.unit){
      let unitNum = Number.parseInt(baseRcj.unit);
      if (Number.isNaN(unitNum)) {
        unitNum = 1;
      }
      newData.quantity = NumberUtil.divide(newData.quantityExpressionNbr, unitNum);
    }
    newData.unit = baseRcj.unit;
    newData.kind = '04';
    newData.zjfPrice = baseRcj.marketPrice;
    newData.zjfTotal = baseRcj.total;
    newData.standardId = standardId;
    newData.bdCode = baseRcj.materialCode;
    newData.bdName = baseRcj.materialName;
    newData.fxCode = baseRcj.materialCode;
    newData.name = baseRcj.materialName;
    newData.remark = baseRcj.libraryCode;
    newData.price = baseRcj.dePrice;
    newData.unitId = unitWorkId;
    newData.constructId = constructId;
    newData.extend1 = baseRcj.libraryCode;
    newData.description = baseRcj.description;
    newData.rcjKind = _.clone(this.RCJKIN[baseRcj.kind]);
    newData.levelMark = baseRcj.levelMark;

        //取费文件   当前单位的主取费文件
        let unitWork = PricingFileFindUtils.getUnit(constructId, singleId, unitWorkId);
        let mainDeLib = unitWork.mainDeLibrary;
        let constructMajorType = unitWork.constructMajorType;
        let feeFile = await this._getDefaultFeeFile(constructId, singleId, unitWorkId, mainDeLib, constructMajorType, PricingFileFindUtils.is22Unit(unitWork));
        newData.majorName = feeFile.feeFileName;
        newData.costFileCode = feeFile.feeFileCode;
        newData.costMajorName = feeFile.feeFileName;
        // newData.measureType = feeFile.rateName;

    // 施工组织措施类别
    // if (constructMajorType === "安装工程") {
    newData.measureType = unitWork.secondInstallationProjectName;
    // } else {
    //     newData.measureType = feeFile.rateName;
    // }
    //人材机标识
    newData.rcjFlag = 1;
  }

  async _getDefaultFeeFile(constructId, singleId, unitWorkId, mainDeLib, constructMajorType, is2022) {
    let table_name;
    if (is2022) {
      table_name = 'base_speciality_de_fee_relation_2022';
    } else {
      table_name = 'base_speciality_de_fee_relation';
    }
    // 根据 主定额库 和 工程专业 获取取费文件code
    let querySql = 'select default_qf_code as feecode\n' +
      'from ' + table_name + '\n' +
      'where library_code = ?\n' +
      '  and unit_project_name = ?';
    let sqlRes = this.app.betterSqlite3DataSource.prepare(querySql).all(mainDeLib, constructMajorType);
    let feeCode = sqlRes[0].feecode;
    // 根据 feeCode 查询取费文件
    let feeFile = await this.service.baseFeeFileService.updateFBFXFeeFile(constructId, singleId, unitWorkId, feeCode);

    return feeFile;
  }

  /**
   * @param isInvokeCountCostCodePrice  是否调用费用汇总计算的方法
   */
  async _doUpdateFronIndexPage(allDatas, constructId, singleId, unitWorkId, currentUpdateLine, parentLines, upDateInfo, notCaculataPrice, caculateParam, type, isInvokeCountCostCodePrice) {
    let is22De = PricingFileFindUtils.is22UnitById(constructId, singleId, unitWorkId);
    // 2. 查定额或清单索引添加行数据   和 人材机数据建立联系 upDateInfo中有索引id，可以用来查索引
    await this._fillCurrentLineData(constructId, singleId, unitWorkId, currentUpdateLine, parentLines, upDateInfo);
    await this._doOtherBusiness(currentUpdateLine, upDateInfo);
    // 1. 填充 工程量表达式，工程量 默认值  若清单 表达式 = 1， 工程量 = 1； 若是定额 表达式 继承清单的,  工程量  从 UnitConversion根据单位取
    if (upDateInfo.unit) {
      currentUpdateLine.unit = upDateInfo.unit;
    }
    if (currentUpdateLine.kind === BranchProjectLevelConstant.qd) {
      currentUpdateLine.quantityExpression = currentUpdateLine.quantityExpression || currentUpdateLine.quantityExpression == 0 ? currentUpdateLine.quantityExpression : '1';
      let unitNum = Number.parseInt(currentUpdateLine.unit);
      if (Number.isNaN(unitNum)) {
        unitNum = 1;
      }
      currentUpdateLine.quantityExpressionNbr = currentUpdateLine.quantityExpressionNbr || currentUpdateLine.quantityExpressionNbr === 0 ?
        currentUpdateLine.quantityExpressionNbr : this.getQuantityExpressionNbr(constructId, singleId, unitWorkId, currentUpdateLine);
      //如果替换的时候工程量明细不为空的情况工程量不需要初始化赋值
      let count = _.filter(currentUpdateLine.quantities, (item) => !_.isEmpty(item.mathFormula));
      if (!count.length) {
        currentUpdateLine.quantity = NumberUtil.multiply(currentUpdateLine.quantityExpressionNbr, unitNum);
      }
    } else if (currentUpdateLine.kind === BranchProjectLevelConstant.de) {
      //下面是zxz 逻辑 感觉目前不符合需求 先注释掉
      /*//如果传递了工程量，表达式，值，用传递的
            if ( currentUpdateLine.quantityExpression != "HSGCL"
                && (currentUpdateLine.quantityExpression ||
                    currentUpdateLine.quantityExpressionNbr ||
                    currentUpdateLine.quantity ||
                    currentUpdateLine.quantity===0)) {
                // currentUpdateLine.quantity = (currentUpdateLine.quantityExpressionNbr/ this.getUnitNum(currentUpdateLine)).toFixed(6);
                // doNothing
            } else {
                //如果没有传
                let qdLine = this._findLineById(allDatas, currentUpdateLine.parentId);
                let unitQd = qdLine.unit;
                let unitDe = currentUpdateLine.unit;
                // 单位一致 继承清单
                if (this._isSameUnit(unitQd, unitDe)) {
                    //TODO 处理清单量需求
                    //currentUpdateLine.quantityExpression = qdLine.quantityExpression;
                    currentUpdateLine.quantityExpression = "QDL";
                    currentUpdateLine.quantityExpressionNbr = qdLine.quantityExpressionNbr;
                    currentUpdateLine.quantity = (currentUpdateLine.quantityExpressionNbr / this.getUnitNum(currentUpdateLine)).toFixed(6);
                } else {
                    // 不一致，给 0
                    currentUpdateLine.quantityExpression = "0";
                    currentUpdateLine.quantityExpressionNbr = 0;
                    currentUpdateLine.quantity = 0;
                }
            }
            let rcjList = this.service.rcjProcess.getRcjListByDeId(currentUpdateLine.sequenceNbr, constructId, singleId, unitWorkId);
            this.service.rcjProcess.reCaculateRcjPrice(currentUpdateLine, rcjList);*/

      let qdLine = this._findLineById(allDatas, currentUpdateLine.parentId);
      let unitQd = qdLine.unit;
      let unitDe = currentUpdateLine.unit;


      if (currentUpdateLine.isCostDe == DePropertyTypeConstant.CG_DE && is22De) {
        currentUpdateLine.quantityExpression = ConstantUtil.EXP_CG_RGHJ;
        currentUpdateLine.quantityExpressionNbr = await this.initCurrentUpdateLine4cg(constructId, singleId, unitWorkId, currentUpdateLine, upDateInfo, caculateParam);

        if (currentUpdateLine.quantityExpression) {
          let quantity = 0;
          const regex = /\b\d+\b/;
          let match = currentUpdateLine.unit.match(regex);
          if (match) {
            const number = parseInt(match[0]);
            quantity = NumberUtil.numberScale(NumberUtil.divide(currentUpdateLine.quantityExpressionNbr, number), 6);
          } else {
            quantity = NumberUtil.numberScale(NumberUtil.divide(currentUpdateLine.quantityExpressionNbr, 1), 6);
          }

          currentUpdateLine.quantity = quantity;
        }
      }

      if ((((ObjectUtils.isEmpty(currentUpdateLine.isCostDe) || currentUpdateLine.isCostDe == 0)) ||
        (!ObjectUtils.isEmpty(type) && type == true))
      ) {
        if (type != false) { // 单位一致 继承清单
          if (this._isSameUnit(unitQd, unitDe) || ObjectUtils.isEmpty(unitQd)) {
            //TODO 处理清单量需求
            //currentUpdateLine.quantityExpression = qdLine.quantityExpression;
            currentUpdateLine.quantityExpression = 'QDL';
            currentUpdateLine.quantityExpressionNbr = qdLine.quantityExpressionNbr;
            currentUpdateLine.quantity = NumberUtil.numberScale4((currentUpdateLine.quantityExpressionNbr / this.getUnitNum(currentUpdateLine)));
          } else {
            // 不一致，给 0
            currentUpdateLine.quantityExpression = 'QDL*0';
            currentUpdateLine.quantityExpressionNbr = 0;
            currentUpdateLine.quantity = 0;
          }
        } else {
          if (!ObjectUtils.isEmpty(this.getUnitNum(currentUpdateLine)) && this.getUnitNum(currentUpdateLine) != 0) {
            currentUpdateLine.quantity = NumberUtil.numberScale2((currentUpdateLine.quantityExpressionNbr / this.getUnitNum(currentUpdateLine)));
          }

          if (currentUpdateLine.quantityExpression == 'HSGCL') {

            currentUpdateLine.quantityExpression = currentUpdateLine.quantityExpressionNbr;
          }

        }
      }

      let rcjList = this.service.rcjProcess.getRcjListByDeId(currentUpdateLine.sequenceNbr, constructId, singleId, unitWorkId);
      this.service.rcjProcess.reCaculateRcjPrice(currentUpdateLine, rcjList, constructId, singleId, unitWorkId);
    }
    //此处特殊处理“其他材料”、“其他材料费”、“其他机械费”、“其他机械“
    this.service.rcjProcess.handleSpecialRcj(constructId, singleId, unitWorkId, currentUpdateLine.sequenceNbr);
    // 3. 处理单价构成
    if (currentUpdateLine.kind === BranchProjectLevelConstant.de) {
      if (notCaculataPrice) {
        return;
      }
      if (caculateParam) {
        this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitWorkId, currentUpdateLine.sequenceNbr, true, allDatas,
          false, caculateParam.getPrice, caculateParam.getQty, caculateParam.baseNum, caculateParam.getBaseNum);
      } else {
        this.service.unitPriceService.caculataDEUnitPrice(constructId, singleId, unitWorkId, currentUpdateLine.sequenceNbr, true, allDatas);


      }
    }

    this.service.management.trigger('itemChange');
    await this.service.autoCostMathService.autoCostMath({
      constructId: constructId,
      singleId: singleId,
      unitId: unitWorkId
    });
  }

  async _doOtherBusiness(currentUpdateLine, upDateInfo) {
    // 其他业务处理
    if (currentUpdateLine.kind === BranchProjectLevelConstant.qd) {
      // 清单
      let code = currentUpdateLine.fxCode;
      if (!code) {
        code = currentUpdateLine.bdCode;
      }
      return await this.service.listFeatureProcess.saveBatchToFbFxQdFeature(currentUpdateLine.libraryCode, code,
        currentUpdateLine.sequenceNbr, upDateInfo.constructId,
        upDateInfo.singleId, upDateInfo.unitId);
    }
    if (currentUpdateLine.kind === BranchProjectLevelConstant.de) {
      // 定额
      return await this.service.rcjProcess.batchSaveRcjData(currentUpdateLine, upDateInfo.constructId, upDateInfo.singleId, upDateInfo.unitId, upDateInfo.type);
      // 定额
    }
    return true;
  }

  /**
   *
   */

  /**
   * 清单编码获取
   * @param qdCode
   */
  getQdCode(constructId, singleId, unitId, qdCode, includeSelf, sxId) {
    let strCode = '' + qdCode;
    // 1 判断是否标准清单
    if (this._isBaseQdCode(strCode)) {
      let baseCode = strCode.length > 9 ? strCode.substring(0, 9) : strCode;
      // 标准清单
      let maxCodeNum = this._getMaxQdCodeNum(constructId, singleId, unitId, baseCode, includeSelf, sxId);
      let newCode = maxCodeNum + 1;
      if (includeSelf) {
        newCode -= 1;
      }
      if (newCode < 10) {
        return baseCode + '00' + newCode;
      } else if (newCode < 100) {
        return baseCode + '0' + newCode;
      } else {
        return baseCode + '' + newCode;
      }
    } else {
      return qdCode;
    }
  }

  async _fillCurrentLineData(constructId, singleId, unitWorkId, currentUpdateLine, parentLines, upDateInfo) {

    // 填充当前行数据
    // 清单定额索引的sequenceNbr
    let sequenceNbr = upDateInfo.indexId;
    let unit = upDateInfo.unit;
    let kind = currentUpdateLine.kind;


    currentUpdateLine.isSupplement = 0;
    let baseQdOrDe = await this.service.baseQdDeProcess.selectBaseQdOrDe(sequenceNbr, kind, upDateInfo.is2022);
    // 根据前端传的base国标清单/国标定额id查base数据并填充到itemBill

    if (kind === BranchProjectLevelConstant.qd) {
      // 清单
      currentUpdateLine.unit = unit ? unit : baseQdOrDe.unit;
      //措施项目单位给到项
      if (ObjectUtils.isEmpty(currentUpdateLine.unit)) {
        currentUpdateLine.unit = '项';
      }

      currentUpdateLine.unitList = baseQdOrDe.unit;
      currentUpdateLine.kind = BranchProjectLevelConstant.qd;
      // 同时适配分部分项和措施项目
      currentUpdateLine.fxCode = this.getQdCode(constructId, singleId, unitWorkId, baseQdOrDe.bdCodeLevel04);
      currentUpdateLine.name = baseQdOrDe.bdNameLevel04;
      currentUpdateLine.bdCode = this.getQdCode(constructId, singleId, unitWorkId, baseQdOrDe.bdCodeLevel04);
      currentUpdateLine.bdName = baseQdOrDe.bdNameLevel04;
      //该属性在标准换算中用到 需要进行赋值 否则在原来的空清单上修改项目名称后,
      // 再进行清单指引的插入,新插入的清单项目名称还会保留原来的
      currentUpdateLine.orhName = currentUpdateLine.bdName;
      currentUpdateLine.isStandard = 1;
      currentUpdateLine.standardId = baseQdOrDe.sequenceNbr;
      currentUpdateLine.libraryCode = baseQdOrDe.libraryCode;
      currentUpdateLine.unitId = upDateInfo.unitId;
      currentUpdateLine.projectAttr = '';
      currentUpdateLine.zjcsClassCode = baseQdOrDe.zjcsClassCode;
      currentUpdateLine.zjcsClassName = baseQdOrDe.zjcsClassName;
    }
    if (kind === BranchProjectLevelConstant.de) {
      let cslbCode = baseQdOrDe.cslbCode;
      // let unitIs2022 = PricingFileFindUtils.getConstructDeStandard(constructId) == ConstantUtil.DE_STANDARD_22;
      let unitIs2022 = PricingFileFindUtils.is22UnitById(constructId, singleId, unitWorkId);
      //如果主工程使用22定额，而当前单位使用12定额，需要转换
      if (unitIs2022 && !upDateInfo.is2022) {
        cslbCode = get2022BY2012Cslb(cslbCode);
      }

      let baseFeeFileRelationDTO = await this.service.baseFeeFileRelationService.queryFeeFileRelationByRateCodeGroupByQfCode(cslbCode, unitIs2022);
      // 定额
      currentUpdateLine.unit = baseQdOrDe.unit;
      currentUpdateLine.kind = BranchProjectLevelConstant.de;
      currentUpdateLine.standardId = baseQdOrDe.sequenceNbr;
      currentUpdateLine.fxCode = baseQdOrDe.deCode;
      currentUpdateLine.name = baseQdOrDe.deName;
      currentUpdateLine.isStandard = 1;
      currentUpdateLine.bdCode = baseQdOrDe.deCode;
      currentUpdateLine.bdName = baseQdOrDe.deName;
      currentUpdateLine.unitId = upDateInfo.unitId;
      currentUpdateLine.description = baseQdOrDe.libraryName;
      currentUpdateLine.remark = baseQdOrDe.libraryName;
      currentUpdateLine.libraryCode = baseQdOrDe.libraryCode;
      // 把这里改了 由直接拿rateName 改成拿baseQdOrDe的某一种信息查某张表得到要的信息
      currentUpdateLine.measureType = baseFeeFileRelationDTO ? baseFeeFileRelationDTO.cslbName : '随主工程';
      currentUpdateLine.rateName = baseFeeFileRelationDTO ? baseFeeFileRelationDTO.cslbName : '随主工程';
      currentUpdateLine.classifyLevel1 = baseQdOrDe.classifyLevel1;
      currentUpdateLine.classifyLevel2 = baseQdOrDe.classifyLevel2;
      currentUpdateLine.classifyLevel3 = baseQdOrDe.classifyLevel3;
      currentUpdateLine.classifyLevel4 = baseQdOrDe.classifyLevel4;
      // 9.16补充
      currentUpdateLine.zjcsClassCode = baseQdOrDe.zjcsClassCode;
      currentUpdateLine.zjcsClassName = baseQdOrDe.zjcsClassCode;
      currentUpdateLine.sgzzcslb = baseQdOrDe.sgzzcslb;
      currentUpdateLine.value = baseQdOrDe.value;
      currentUpdateLine.chargeName = baseQdOrDe.chargeName;


      if (upDateInfo.isCostDe) {
        currentUpdateLine.isCostDe = upDateInfo.isCostDe;
      } else {
        currentUpdateLine.isCostDe = this.service.constructCostMathService.costDeByDe(currentUpdateLine);
      }


      // 添加取费文件， 获取取费文件数据
      let unitFeeFileDTO = await this.service.baseFeeFileService.handleUnitAddFeeFile(upDateInfo.constructId, upDateInfo.singleId, upDateInfo.unitId, currentUpdateLine.standardId, upDateInfo.is2022);
      if (!ObjectUtils.isEmpty(unitFeeFileDTO)) {
        currentUpdateLine.costMajorName = unitFeeFileDTO.feeFileName;
        currentUpdateLine.costFileCode = unitFeeFileDTO.feeFileCode;
        currentUpdateLine.feeFileId = unitFeeFileDTO.feeFileId;
        currentUpdateLine.qfCode = unitFeeFileDTO.feeFileCode;
      } else {
        currentUpdateLine.costMajorName = '随主工程';
        Log.error('_fillCurrentLineData()添加定额时取费文件为空，国标定额id:', currentUpdateLine.standardId);
      }

    }
  }

  /**
   * 此处本方法用于重新根据一定规则计算 工程量,
   * 当前case 如下,当本费用定额单位为得单位为 xx工日时,则需要从单位工程中找出对应超高相关的定额,并汇总其下人 的工程量,填充到当前定额的表达式里
   */
  async initCurrentUpdateLine4cg(constructId, singleId, unitWorkId, currentUpdateLine, upDateInfo, caculateParam) {
    //1查找当前单位工程的定额,并过滤出超高相关的定额
    //2取得上一步定额下挂人才机中的人的单位为'工日'的rcj数据(kind==1 unit==工日),并累加
    //3用累加的值赋值给当前行的表达式
    let quantity = 0;
    if (!ObjectUtils.isEmpty(caculateParam) && !ObjectUtils.isEmpty(caculateParam.groupByUp)) {
      let baseDeList = caculateParam.groupByUp.get(currentUpdateLine.fxCode);
      // let cgCaculateTimes = 1;
      if (!ObjectUtils.isEmpty(baseDeList)) {
        for (let de of baseDeList) {
          //这里要判断该定额是否为超高相关的定额
          let rcjs = this.service.constructProjectRcjService.getRcjListByDeId(de.sequenceNbr, constructId, singleId, unitWorkId).filter(item => item.kindBackUp == RcjTypeEnum['Rengong'].code && item.unit == ConstantUtil.UNIT_RENGONG);
          for (let rcj of rcjs) {
            quantity += rcj.totalNumber;
          }
          // cgCaculateTimes = de.value;
        }
      }
      // return quantity * cgCaculateTimes;
      return quantity;
    } else {
      return 1;
    }
  }

  _getPatents(allData, pointLine) {
    let parentLines = [];
    let currentUpdateLine;
    // 寻找当前行及父节点
    let pointIndex;
    for (let index = 0; index < allData.length; ++index) {
      if (pointLine.sequenceNbr === allData[index].sequenceNbr) {
        currentUpdateLine = allData[index];
        pointIndex = index;
      }
    }
    if (currentUpdateLine.kind !== BranchProjectLevelConstant.fb) {
      let parentLine = currentUpdateLine;
      let indexUp = pointIndex - 1;
      while (indexUp > 0) {
        if (parentLine.kind === BranchProjectLevelConstant.fb) {
          break;
        }
        if (allData[indexUp].sequenceNbr === parentLine.parentId) {
          parentLines.push(allData[indexUp]);
          parentLine = allData[indexUp];
        }
        --indexUp;
      }
    }

    return {
      'parentLines': parentLines,
      'currentUpdateLine': currentUpdateLine
    };
  }

  /*batchDelete(allData,sequenceNbrs){

        for (let i = 0; i <sequenceNbrs.length ; i++) {
            let line  = this._findLineById(allData, sequenceNbrs[i]);
            if(line.kind==BranchProjectLevelConstant.de){
              //调用删除行
                this.removeLine(line,allData);
            }else {
              //调用删除块
                this.removeLineBlock(line,allData);
            }
        }

    }*/

  /**
   * 删除一行
   * @param pointLine
   * @param mockData
   */
  removeLine(pointLine, mockData, args) {
    // 查询全部数据
    let allData = mockData;
    let influenceLines = this._getInfluence(allData, pointLine).datas;
    if (undefined !== influenceLines) { //需要处理子数据parentId，display； 如果是删除分部，分部下一层是子分部，则子分部还要升级为分部
      this.doChangeLineDisplay(influenceLines, BranchProjectDisplayConstant.open);      // 处理display
      for (let index = 0; index < influenceLines.length; ++index) {
        if (influenceLines[index].parentId === pointLine.sequenceNbr) {               // 处理parentId 如果是直接子数据，则让parentId指向当前行的父
          influenceLines[index].parentId = pointLine.parentId;
          if (pointLine.kind === BranchProjectLevelConstant.fb && influenceLines[index].kind === BranchProjectLevelConstant.zfb) {
            influenceLines[index].kind = BranchProjectLevelConstant.fb;
          }
        }
      }
    }
    // 删除当前行,小箭头
    let res = []; // 删除后的数据
    let hasBrother = false;
    let parent;
    for (let index = 0; index < allData.length; ++index) {
      if (allData[index].sequenceNbr === pointLine.parentId) {
        parent = allData[index];
      }
      if (allData[index].sequenceNbr !== pointLine.sequenceNbr) {
        res.push(allData[index]);
      } else {
        if (allData[index].parentId === pointLine.parentId) {  // 存在兄弟节点，不处理箭头，如果删完后没有兄弟节点了，父节点的小箭头去掉
          hasBrother = true;
        }
      }
    }
    if (!hasBrother && !_.isEmpty(parent)) {
      parent.displaySign = BranchProjectDisplayConstant.noSign;
    }
    // 重构编码
    this._reSortDisplayNum(res, 0);
    if (args.type) {
      PricingFileFindUtils.getUnit(args['constructId'], args['singleId'], args['unitId']).itemBillProjects = res;
    } else {
      PricingFileFindUtils.getUnit(args['constructId'], args['singleId'], args['unitId']).measureProjectTables = res;
    }
    // 费用代码
    if (args != null) {
      this.service.unitCostCodePriceService.countCostCodePrice({
        constructId: args['constructId'],
        singleId: args['singleId'],
        unitId: args['unitId']
      });
    } else {
      this.service.management.trigger('itemChange');
    }


    return res;
  }

  /**
   * 删除一块
   * @param pointLine
   * @param mockAllData
   */
  removeLineBlock(pointLine, mockAllData, dealInfluenceLines, args) {
    let allData = mockAllData;
    let influenceLines = this._getInfluence(allData, pointLine).datas;
    dealInfluenceLines(influenceLines);
    let parent;
    // 从allData 中剔除 influenceLines
    let map = {};
    for (let index = 0; index < influenceLines.length; ++index) {
      map[influenceLines[index].sequenceNbr] = 1;
    }

    let res = [];
    for (let index = 0; index < allData.length; ++index) {
      let lineData = allData[index];
      if (influenceLines && lineData.sequenceNbr === influenceLines[0].parentId) {
        parent = lineData;
      }
      if (undefined === map[lineData.sequenceNbr]) {
        res.push(lineData);
      }
    }
    // 父节点下如果没有子数据了，则删除小箭头
    if (parent) {
      if (!this._haschild(parent, res)) {
        parent.displaySign = BranchProjectDisplayConstant.noSign;
      }
    }
    // 重构编码
    this._reSortDisplayNum(res, 0);
    if (args.type) {
      PricingFileFindUtils.getUnit(args['constructId'], args['singleId'], args['unitId']).itemBillProjects = res;
    } else {
      PricingFileFindUtils.getUnit(args['constructId'], args['singleId'], args['unitId']).measureProjectTables = res;
    }

    this.service.management.trigger('itemChange');

    return res;
  }

  /**
   * 展开行
   * @param pointLine
   * @param allData
   */
  openLine(pointLine, allData) {
    let node = allData.getNodeById(pointLine.sequenceNbr);
    node.displaySign = BranchProjectDisplayConstant.open;
    /*    let influenceLines = this._getInfluence(allData, pointLine).datas; // 包含选中行，及其下的子项目
        this.doChangeLineDisplay(influenceLines, BranchProjectDisplayConstant.open);*/
  }

  getAllSelectData(pointLine, mockAllData, selectData) {
    let allData = mockAllData;
    if (pointLine.kind == BranchProjectLevelConstant.qd) {
      let influenceLines = this._getInfluence(allData, pointLine).datas;
      influenceLines.forEach(item => {
        selectData.push(item);
      });
    } else if (pointLine.kind != BranchProjectLevelConstant.de) {
      let childs = allData.filter(item => item.parentId === pointLine.sequenceNbr);
      childs.forEach(item => {
        selectData.push(item);
      });
    }

    let parent = allData.find(item => item.sequenceNbr === pointLine.parentId);
    if (parent) {
      this.getAllSelectData(parent, allData, selectData);
    }
  }

  openLineAndParent(pointLine, mockAllData) {
    let allData = mockAllData;
    let selectData = [];
    this.getAllSelectData(pointLine, mockAllData, selectData);
    let parent = allData.find(item => item.sequenceNbr === pointLine.parentId);
    this.doChangeLineDisplayOne(selectData, parent, BranchProjectDisplayConstant.open);
  }

  /**
   * 关闭行
   * @param pointLine
   */
  closeLine(pointLine, mockAllData) {
    let allData = mockAllData;
    let node = allData.getNodeById(pointLine.sequenceNbr);
    node.displaySign = BranchProjectDisplayConstant.close;
    //let influenceLines = this._getInfluence(allData, pointLine).datas; // 包含选中行，及其下的子项目
    //this.doChangeLineDisplay(influenceLines, BranchProjectDisplayConstant.close);
  }

  searchForsequenceNbr(sequenceNbr, mockAllData) {
    let orgallData = mockAllData; // 全量数据
    let pointLine = mockAllData.getNodeById(sequenceNbr);
    orgallData = mockAllData.flattenTreeOpen(pointLine);
    /*   let pointLine = orgallData.filter(line => line.sequenceNbr === sequenceNbr);
        let info = this._getInfluence(orgallData, pointLine[0]);
        let allData = info.datas;*/
    return orgallData;
  }
  searchPonitAndChild(sequenceNbr, mockAllData) {
    let pointLine = mockAllData.getNodeById(sequenceNbr);
    return mockAllData.flattenTree(pointLine);
  }
  pageSearch(pageNum, pageSize, sequenceNbr, mockAllData, disPlayType, isAllFlag, screenCondition) {
    let orgallData = [];
    if (sequenceNbr) {
      let pointLine = mockAllData.getNodeById(sequenceNbr);
      orgallData = mockAllData.flattenTreeOpen(pointLine, screenCondition);
    } else {
      orgallData = mockAllData.flattenTreeOpen(mockAllData.root, screenCondition); // 全量数据
    }
    let beginIndex = pageSize * (pageNum - 1);
    let endIndex = pageSize * (pageNum - 1) + pageSize;

    let returnArray = orgallData.slice(beginIndex, endIndex);


    return {
      'data': returnArray,
      'total': orgallData.length,
      'pageNum': pageNum,
      'pageSize': pageSize
    };
  }

  /**
   * 颜色过滤删选数据
   * @param pageNum
   * @param pageSize
   * @param sequenceNbr
   * @param mockAllData
   * @param disPlayType
   * @param isAllFlag
   * @param screenCondition
   * @returns {{total: number, data: *[], pageSize: *, pageNum: *}}
   */
  pageSearchFilter(pageNum, pageSize, sequenceNbr, mockAllData, disPlayType, isAllFlag, color) {
    let orgallData = [];
    if (sequenceNbr) {
      let pointLine = mockAllData.getNodeById(sequenceNbr);
      orgallData = mockAllData.flattenTreeOpen(pointLine);
    } else {
      orgallData = mockAllData.flattenTreeOpen(mockAllData.root); // 全量数据
    }
    let beginIndex = pageSize * (pageNum - 1);
    let endIndex = pageSize * (pageNum - 1) + pageSize;
    let returnArray = orgallData.slice(beginIndex, endIndex);
    //根据选择的颜色过滤数据

    return {
      'data': returnArray,
      'total': orgallData.length,
      'pageNum': pageNum,
      'pageSize': pageSize
    };
  }

  pageSearchForAllData(pageNum, pageSize, sequenceNbr, mockAllData, disPlayType, isAllFlag, screenCondition) {
    let orgallData = [];
    if (sequenceNbr) {
      let pointLine = mockAllData.getNodeById(sequenceNbr);
      orgallData = mockAllData.flattenTree(pointLine);
    } else {
      orgallData = mockAllData.flattenTree(mockAllData.root); // 全量数据
    }
    let beginIndex = pageSize * (pageNum - 1);
    let endIndex = pageSize * (pageNum - 1) + pageSize;
    let returnArray = orgallData.slice(beginIndex, endIndex);
    return {
      'data': returnArray,
      'total': orgallData.length,
      'pageNum': pageNum,
      'pageSize': pageSize
    };
  }

  /**
   * 分页查询
   * @param pageNum
   * @param pageSize
   */
  pageSearchOld(pageNum, pageSize, sequenceNbr, mockAllData, disPlayType, isAllFlag) {
    let orgallData = mockAllData; // 全量数据
    let allData;  // 点击的数据及子集
    let displauMaxNum = BranchProjectDisplayConstant.displayMax;
    let info = {
      'begin': 0
    };
    // 根据 sequenceNbr 过滤子数据
    if (sequenceNbr) {
      let pointLine = orgallData.filter(line => line.sequenceNbr === sequenceNbr);
      displauMaxNum = pointLine[0].displayStatu;
      info = this._getInfluence(orgallData, pointLine[0]);
      allData = info.datas;
    } else {
      allData = orgallData;
    }

    // 记录每个显示的数据在真实数据的下标
    let seq2Index = null;
    let beginIndex = pageSize * (pageNum - 1);
    let endIndex = pageSize * (pageNum - 1) + pageSize;
    // 过滤显示displaySign
    let showDatas = [];
    for (let index = 0; index < allData.length; ++index) {
      if (allData[index].displayStatu === displauMaxNum) {
        if (!seq2Index) {
          seq2Index = {};
        }
        seq2Index[allData[index].sequenceNbr] = index;
        showDatas.push(allData[index]);
      }
      if (index < allData.length - 1) {
        if (Number.parseInt(allData[index].kind) >= Number.parseInt(allData[index + 1].kind)
          && allData[index].kind === BranchProjectLevelConstant.qd) { // 处理 超高多次记取问题
          if (allData[index].displaySign === 1) {
            allData[index].displaySign = 0;
          }
        }
        if (index === allData.length - 1) {
          allData[index].displaySign = 0;
        }
      }
      allData[allData.length - 1].displaySign = 0;
    }

    let returnArray = showDatas.slice(beginIndex, endIndex);

    //returnArray 为当前分页数据 为清单下的定额添加 isFirst和isLast属性 用来说明是否是清单的第一条和最后一条定额
    let map = new Map();

    //是否可以上下移动判断
    for (let i = 0; i < returnArray.length; i++) {
      let element = returnArray[i];

      let filter = [];
      if (map.get(element.parentId) != null) {
        filter = map.get(element.parentId);
      } else {
        filter = orgallData.filter(item => item.parentId == element.parentId);
        map.set(element.parentId, filter);
      }
      if (filter.indexOf(element) == 0) {
        element['isFirst'] = true;
      } else {
        element['isFirst'] = false;
      }

      if (filter.indexOf(element) == filter.length - 1) {
        element['isLast'] = true;
      } else {
        element['isLast'] = false;
      }
      //分部是否可以上调下调判断
      //给所有的数据设置不可以升降级
      element.isUpFb = true;
      element.isDownFb = true;
      //如果是分部 不可升级
      if (element.kind == BranchProjectLevelConstant.fb) {
        element.isUpFb = false;
        //获取同层级数据 如果没有同层级其他数据将不可以进行降级操作
        //如果同层级有数据  但是当前分部是第一行数据 不可降级操作
        let ts = returnArray.filter(fb => fb.parentId == element.parentId);
        if (ts.length > 1) {
          if (ts[0].sequenceNbr == element.sequenceNbr) {
            element.isDownFb = false;
          } else {
            element.isDownFb = true;
          }
        } else {
          element.isDownFb = false;
        }
      }

      //判断若下移会导致分部层级超过四层则【降级】按钮置灰  暂未处理TODO SUNPO
      if (element.kind == BranchProjectLevelConstant.zfb) {
        let ts = returnArray.filter(fb => fb.parentId == element.parentId);
        if (ts.length > 1) {
          if (ts[0].sequenceNbr == element.sequenceNbr) {
            element.isDownFb = false;
          } else {
            element.isDownFb = true;
          }
        } else {
          element.isDownFb = false;
        }
        //如果选中分部的层级结构是第四级 或者分部中包含第四级分部数据不可以降级
        let fourLeve = this.isFourLeve(element, returnArray);
        if (fourLeve) {
          element.isDownFb = false;
        }

      }
    }


    // 填充操作菜单
    this._fillOptionMenu(orgallData,
      seq2Index[returnArray[0].sequenceNbr] + info.begin,
      seq2Index[returnArray[returnArray.length - 1].sequenceNbr] + info.begin);

    // 单位数据单独处理
    if (returnArray[0].kind === BranchProjectLevelConstant.top) {
      if (undefined === returnArray[0].displaySign) {
        returnArray[0].displaySign = 1;
      }
    }

    // 设置显示类型
    for (let i = 0; i < returnArray.length; ++i) {
      if (returnArray[i].rcjFlag === 1 && returnArray[i].type === '定') {
        returnArray[i].type = returnArray[i].rcjKind;
      }
      if (!returnArray[i].type && returnArray[i].type !== '') {
        returnArray[i].type = disPlayType[returnArray[i].kind];
        if (returnArray[i].rcjFlag === 1) {
          returnArray[i].type = returnArray[i].rcjKind;
        }
        // zjcsClassCode 费用定额用 不空就费
        /*if (returnArray[i].zjcsClassCode && returnArray[i].zjcsClassCode !== "") {
                    if (returnArray[i].kind === BranchProjectLevelConstant.de) {
                        returnArray[i].type = "费";
                    }
                }*/
      }
      if (returnArray[i].isCostDe && returnArray[i].isCostDe > 0 && returnArray[i].isCostDe != 4 && returnArray[i].isCostDe != 6) {
        returnArray[i].type = '费';
      }
    }

    // 特殊处理分部 子分部 顶层的几个展示
    returnArray.forEach(a => {
      if (a.kind === BranchProjectLevelConstant.top || a.kind === BranchProjectLevelConstant.fb || a.kind === BranchProjectLevelConstant.zfb) {
        a.zjfPrice = null;// 直接费单价 ZJF displayUnitPrice
        a.zjfTotal = null;// 直接费合价 ZJF displayAllPrice
        a.price = null;// 综合单价(工程造价的单价) ZJ displayUnitPrice
        a.rfee = null;// 人工费单价 RGF
        a.cfee = null;// 材料费单价 CLF
        a.jfee = null;// 机械费单价 JXF
        a.managerFee = null;// 管理费单价 FY1
        a.profitFee = null;// 利润费单价 FY2
        a.zcfee = null;// 主材费单价 ZCF
      }
    });

    // 处理标准换算
    let constructId = ParamUtils.getPatram('commonParam').constructId;
    let singleId = ParamUtils.getPatram('commonParam').singleId;
    let unitId = ParamUtils.getPatram('commonParam').unitId;
    let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    let usingRules = unitProject.conversionInfoList;
    returnArray.forEach(r => {
      r.ruleMatchs = undefined;
      r.blackArray = [];
      r.redArray = [];
      if (r.orhName && r.orhName != '') {
        r.bdName = r.orhName;
        r.name = r.orhName;
      }
    });

    if (usingRules && usingRules.length > 0) {
      for (let i = 0; i < returnArray.length; ++i) {
        let xxxname = returnArray[i].name;
        if (!xxxname || xxxname == '') {
          xxxname = returnArray[i].bdName;
        }
        if (!returnArray[i].orhName) {
          returnArray[i].orhName = xxxname;
        } else {
          returnArray[i].name = returnArray[i].orhName;
          returnArray[i].bdName = returnArray[i].orhName;
        }
        returnArray.rName = returnArray.name;
        if (returnArray[i].kind === BranchProjectLevelConstant.de) {
          returnArray[i].ruleMatchs = [];
          returnArray[i].blackArray = [];
          returnArray[i].redArray = [];
          returnArray[i].blackArrayName = [];
          returnArray[i].redArrayName = [];
          let urules = usingRules.filter(f => f.deId === returnArray[i].sequenceNbr);
          if (urules && urules.length > 0) {
            for (let k = 0; k < urules.length; ++k) {
              let ur = urules[k];
              if (ur.kind == 0) {
                returnArray[i].blackArray.push(urules[k].displayMath);
                returnArray[i].blackArrayName.push(urules[k].conversionExplain);
              } else if (ur.kind == 2) {
                //CS-kind=2标准换算后，定额编码处展示有误 BUG 修复 jw
                returnArray[i].redArray.push(urules[k].conversionString);
                //returnArray[i].redArray.push(urules[k].displayMath);
                returnArray[i].redArrayName.push(urules[k].conversionExplain);
              } else {
                returnArray[i].redArray.push(urules[k].displayMath);
                returnArray[i].redArrayName.push(urules[k].conversionExplain);
              }
              returnArray[i].ruleMatchs.push(urules[k].displayMath);
            }
          }
          if (returnArray[i].ruleMatchs.length == 0) {
            returnArray[i].ruleMatchs = undefined;
          }

          for (let ik = 0; ik < returnArray[i].redArrayName.length; ++ik) {
            if (!`${returnArray[i].bdName}`.includes(returnArray[i].redArrayName[ik])) {
              returnArray[i].bdName += '\n';
              returnArray[i].bdName += returnArray[i].redArrayName[ik];
            }
            returnArray[i].name += '\n';
            returnArray[i].name += returnArray[i].redArrayName[ik];
          }
          for (let ik = 0; ik < returnArray[i].blackArrayName.length; ++ik) {
            returnArray[i].bdName += '\n';
            returnArray[i].bdName += returnArray[i].blackArrayName[ik];
            returnArray[i].name += '\n';
            returnArray[i].name += returnArray[i].blackArrayName[ik];
          }

        }
      }
    }


    return {
      'data': returnArray,
      'total': showDatas.length,
      'pageNum': pageNum,
      'pageSize': pageSize
    };
  }


  /**
   * 判断分部是不是第四级  或者是否包含四级
   * @param selectElement
   * @param allData
   */
  isFourLeve(selectElement, allData) {

    //是不是第四级
    let levelNum = this.getLevelNum(1, selectElement, allData);
    if (levelNum == 4) {
      return true;
    } else {
      //判断内部数据是否包含第四层
      let filter = allData.filter(fb => fb.parentId === selectElement.sequenceNbr && fb.kind == '02');
      if (levelNum == 2) {
        if (ObjectUtils.isNotEmpty(filter)) {
          for (const zfb of filter) {
            let zfbList = allData.filter(fb => fb.parentId === zfb.sequenceNbr && fb.kind == '02');
            if (ObjectUtils.isNotEmpty(zfbList)) {
              return true;
            }
          }
        }
      }
      if (levelNum == 3) {
        if (ObjectUtils.isNotEmpty(filter)) {
          return true;
        }
      }
    }

    return false;


  }

  /**
   * 获取层级数
   * @param selectElement
   * @param allData
   */
  getLevelNum(levelNum, selectElement, allData) {
    // 检查终止条件：当前元素的kind等于"01"
    if (selectElement.kind === '01') {
      return levelNum;
    }
    // 查找父级元素
    const parentId = selectElement.parentId;
    const parentFilter = allData.find(fb => fb.sequenceNbr === parentId);
    // 如果找不到父级元素，说明已经到达最顶层，返回当前层级
    if (!parentFilter) {
      return levelNum;
    }

    // 继续向上递归查找
    return this.getLevelNum(levelNum + 1, parentFilter, allData);
  }


  /**
   * 获取指定分部下的数据
   * @param pageNum
   * @param pageSize
   * @param sequenceNbr
   * @param mockAllData
   * @param disPlayType
   * @param isAllFlag
   * @returns {*[]}
   */
  getFbDataByFbId(pageNum, pageSize, sequenceNbr, mockAllData, disPlayType, isAllFlag) {
    let orgallData = mockAllData; // 全量数据
    let allData;  // 点击的数据及子集
    let displauMaxNum = BranchProjectDisplayConstant.displayMax;
    let info = {
      'begin': 0
    };
    // 根据 sequenceNbr 过滤子数据
    if (sequenceNbr) {
      let pointLine = orgallData.filter(line => line.sequenceNbr === sequenceNbr);
      displauMaxNum = pointLine[0].displayStatu;
      info = this._getInfluence(orgallData, pointLine[0]);
      allData = info.datas;
    } else {
      allData = orgallData;
    }

    // 记录每个显示的数据在真实数据的下标
    let seq2Index = null;
    let beginIndex = pageSize * (pageNum - 1);
    let endIndex = pageSize * (pageNum - 1) + pageSize;
    // 过滤显示displaySign
    let showDatas = [];
    for (let index = 0; index < allData.length; ++index) {
      if (allData[index].displayStatu === displauMaxNum) {
        if (!seq2Index) {
          seq2Index = {};
        }
        seq2Index[allData[index].sequenceNbr] = index;
        showDatas.push(allData[index]);
      }
      if (index < allData.length - 1) {
        if (Number.parseInt(allData[index].kind) >= Number.parseInt(allData[index + 1].kind)
          && allData[index].kind === BranchProjectLevelConstant.qd) { // 处理 超高多次记取问题
          if (allData[index].displaySign === 1) {
            allData[index].displaySign = 0;
          }
        }
        if (index === allData.length - 1) {
          allData[index].displaySign = 0;
        }
      }
      // allData[allData.length - 1].displaySign = 0;
    }

    let returnArray = showDatas.slice(beginIndex, endIndex);

    return returnArray;
  }


  /**
   * 获取措施项目中指定分部下的数据
   */
  getCsxmDataByFbId(selectElement, allData) {
    let selectAll = [];
    selectAll.push(selectElement);
    //分部
    if (selectElement.kind == '01') {
      let filter = allData.filter(cs => cs.perentId == selectElement.sequenceNbr);
      if (ObjectUtils.isNotEmpty(filter)) {
        for (const zfb of filter) {
          selectAll.push(zfb);
          //清单
          let qdList = allData.filter(cs => cs.perentId == zfb.sequenceNbr);
          if (ObjectUtils.isNotEmpty(qdList)) {
            for (const qd of filter) {
              selectAll.push(qd);
              //定额
              let deList = allData.filter(de => de.perentId == qd.sequenceNbr);
              selectAll.concat(deList);
            }
          }

        }
      }
    } else {
      //子分部 获取清单
      let filter = allData.filter(cs => cs.perentId == selectElement.sequenceNbr);
      if (ObjectUtils.isNotEmpty(filter)) {
        for (const qd of filter) {
          selectAll.push(qd);
          //定额
          let deList = allData.filter(de => de.perentId == qd.sequenceNbr);
          selectAll.concat(deList);
        }
      }

    }

    return selectAll;
  }


  /**
   * 填充返回数据的操作权限
   * [begin,end)
   */
  _fillOptionMenu(allData, beginIndex, endIndex) {
    let current = beginIndex;
    let secLine = allData[1]; // 用于判断单位下有没有分部

    while (current <= endIndex) {
      let lineData = allData[current];
      let nextLine = allData[current + 1];
      if (lineData.isLocked === 1) {
        ++current;
        lineData.optionMenu = BranchProjectOptionMenuConstant.getDefaultMenu(lineData.kind);
        lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.removeBlock
          && menu !== BranchProjectOptionMenuConstant.removeLine);
        continue;
      }
      // 获取默认列表
      lineData.optionMenu = BranchProjectOptionMenuConstant.getDefaultMenu(lineData.kind);
      // 单位   [加分部, 加清单]  	如果下一行数据是分部 则不能加清单
      if (lineData.kind === BranchProjectLevelConstant.top) {
        if (nextLine && nextLine.kind === BranchProjectLevelConstant.fb) {
          lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.addListItem);
        }
      }
      // 分  部 [添加分部 添加子分部 添加清单 删除行 删除块]      如果下一行数据的parentId不是本行的id 去掉删除块  ； 若有子分部 去掉添加清单
      // 子分部 [添加分部 添加子分部 添加清单 删除行 删除块]      如果下一行数据的parentId不是本行的id 去掉删除块  ； 若有子分部 去掉添加清单
      if (lineData.kind === BranchProjectLevelConstant.fb || lineData.kind === BranchProjectLevelConstant.zfb) {
        if (!nextLine || nextLine.parentId !== lineData.sequenceNbr) {
          lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.removeBlock);
        }
        if (nextLine && nextLine.kind === BranchProjectLevelConstant.zfb && nextLine.parentId === lineData.sequenceNbr) {
          lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.addListItem);
        }
        // 分部总层数大于等于3时候，不能再创建子分部
        if (lineData.kind === BranchProjectLevelConstant.zfb) {
          let findTime = 0;
          let lastParent = lineData;
          let lastIndex = current - 1;
          while (lastIndex > 0) {
            let lastData = allData[lastIndex];
            if (lastParent.parentId === lastData.sequenceNbr) {
              lastParent = lastData;
              ++findTime;
            }
            if (lastData.kind === BranchProjectLevelConstant.fb) {
              break;
            }
            --lastIndex;
          }
          if (findTime >= this.ZFB_MAXLEVEL) {
            lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.addChildDivision);
          }
        }
        // 若是分部 且分部下一层为清单 且分部有兄弟节点
        if (nextLine && nextLine.kind === BranchProjectLevelConstant.qd) {
          if (this._hasBrother(allData, lineData)) {
            lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.removeLine);
          }
        }
      }

      // 清单   [添加分部 添加清单 添加定额 删除块]          单位下有分部 去掉 添加分部 ; 如果下一行数据的parentId不是本行的id 去掉删除块
      if (lineData.kind === BranchProjectLevelConstant.qd) {
        if (secLine && secLine.kind === BranchProjectLevelConstant.fb) {
          lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.addDivision);
        }
        if (!nextLine || nextLine.parentId !== lineData.sequenceNbr) {
          lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.removeBlock);
        }
        if (nextLine && nextLine.kind === BranchProjectLevelConstant.de) {
          lineData.optionMenu = lineData.optionMenu.filter(menu => menu !== BranchProjectOptionMenuConstant.removeLine);
        }
      }
      // 定额   [添加定额 删除]   没有改变的场景
      if (lineData.kind === BranchProjectLevelConstant.de) {
        ++current;
        continue;
      }
      ++current;
    }

    return true;
  }

  /**
   *
   * @param allData ItemBillProject   类型
   * @param pointLine ItemBillProject 类型
   * @param newLine ItemBillProject   类型
   * @returns {*}
   */
  _getSplitIndex(allData, pointLine, newLine, org, bzhs) {

    let pointType = pointLine.kind;
    let newLineType = newLine.kind;
    // 获取pointLine 的下标
    let pointLineIndex = this._findLineIndex(allData, pointLine);
    // 特殊： 子分部插入分部 返回之前子分部结束
    if (org) {
      return this._findNextIndex(allData, this._findLineIndex(allData, org),
        // 若下一行的级别更高或pid与选中行pid相同则说明当前级别数据结束
        data => {
          let parents = this._findParents(allData, data);
          return Number.parseInt(data.kind) < Number.parseInt(pointType) || parents[data.parentId];
        });
    }
    // 特殊： 清单插入分部 直接返回第二行
    if (pointType === BranchProjectLevelConstant.qd && newLineType === BranchProjectLevelConstant.fb) {
      return 1;
    }
    // 特殊2： 选中清单 插入子分部 返回上级分部下一个
    if (pointType === BranchProjectLevelConstant.qd && newLineType === BranchProjectLevelConstant.zfb) {
      for (let j = 0; j < allData.length; ++j) {
        if (pointLine.parentId === allData[j].sequenceNbr) {
          return j + 1;
        }
      }
    }

    if (Number.parseInt(newLineType) === Number.parseInt(pointType)) { // 平级插入，返回当前数据块结束  ; 清单插清单 定额插定额 分部插分部
      // 平级中特殊的 子分部插子分部 插在下一行
      if (newLineType === BranchProjectLevelConstant.zfb) {
        return pointLineIndex + 1;
      }
      if (newLineType === BranchProjectLevelConstant.de) {
        if (bzhs) {
          return pointLineIndex + 1;
        } else {
          return pointLineIndex;
        }
      } else {
        return this._findNextIndex(allData, pointLineIndex,
          data => Number.parseInt(data.kind) <= Number.parseInt(pointType));
      }
    } else if (Number.parseInt(newLineType) > Number.parseInt(pointType)) {  // 插入行级别低于选中行 返回下一行
      // 定位到清单最下面的定额索引  进行后续分割
      if (pointType == ConstantUtil.QD_KIND) { //即选中清单插定额
        return this._findLastDeIndexBelowQd(allData, pointLine) + 1;
      }
      return pointLineIndex + 1;
    } else {                                                                 // 插入行级别高于选中行
      // 选中子分部插入 分部   选中 定额插入清单
      return this._findNextIndex(allData, pointLineIndex,
        // 若下一行的级别更高或pid与选中行pid相同则说明当前级别数据结束
        data => Number.parseInt(data.kind) < Number.parseInt(pointType) || data.sequenceNbr === pointLine.parentId);
    }
  }


  _findLineIndex(allData, pointLine) {
    let pointLineIndex = 0;
    for (let index = 0; index < allData.length; ++index) {
      if (allData[index].sequenceNbr === pointLine.sequenceNbr) {
        pointLineIndex = index;
        break;
      }
    }
    return pointLineIndex;
  }

  _findLastDeIndexBelowQd(allData, pointLine) {
    //默认为清单的索引 在清单没有定额的情况下
    let lastDeIndex = this._findLineIndex(allData, pointLine);
    let sentinel = false;
    for (let index = lastDeIndex; index < allData.length; ++index) {
      if (allData[index].parentId === pointLine.sequenceNbr) {
        sentinel = true;
        lastDeIndex = index;
      }
      if (sentinel && allData[index].parentId != pointLine.sequenceNbr) {
        break;
      }
    }
    return lastDeIndex;
  }

  /**
   * 为新增的行数据填充数据
   * 包括 id, parentId, displauStatu,
   * @param allData      [ItemBillProject]
   * @param splitIndex        int
   * @param newLine      ItemBillProject
   * @param pointLine    ItemBillProject
   */
  _fillNewLine(allData, splitIndex, newLine, pointLine) {
    // 主逻辑开始
    let pointType = pointLine.kind, newLineType = newLine.kind;
    let pointTypeInt = Number.parseInt(pointLine.kind),
      newLineTypeInt = Number.parseInt(newLine.kind);
    // 1. 填充id
    if (ObjectUtils.isEmpty(newLine.sequenceNbr)) {
      newLine.sequenceNbr = Snowflake.nextId();
    }
    // 2. 填充parentId
    if (pointType === BranchProjectLevelConstant.zfb && newLineType === BranchProjectLevelConstant.zfb) { // 单独处理子分部添加子分部
      newLine.parentId = pointLine.sequenceNbr;
    } else {
      if (pointTypeInt === newLineTypeInt) { // 选中与新增同级 分部添加分部 清单添加清单 定额添加定额
        newLine.parentId = pointLine.parentId;
      } else if (pointTypeInt < newLineTypeInt) { // 选中行级别更高 分部添加子分部 分部添加清单 子分部添加清单 清单添加定额
        newLine.parentId = pointLine.sequenceNbr;
      } else {                              // 新增行的级别更高  定额加清单  子分部添加分部
        if (newLineType === BranchProjectLevelConstant.zfb) {
          newLine.parentId = pointLine.parentId;
        } else {
          if (newLine.kind === BranchProjectLevelConstant.fb && pointLine.kind === BranchProjectLevelConstant.qd) {
            newLine.parentId = pointLine.parentId;
          } else {
            newLine.parentId = allData.filter(d => d.sequenceNbr === pointLine.parentId)[0].parentId;
          }
        }
      }

    }
    // 3. 填充display
    newLine.displayStatu = pointLine.displayStatu; // displayMax 用于查询，只有 display = displayMax 的数据才会被查询到
    // 4. 填充num
    if (newLineType === BranchProjectLevelConstant.fb || newLineType === BranchProjectLevelConstant.zfb) {
      // 分部, 子分部 不需要num
      newLine.dispNo = null;
    } else if (newLineType === BranchProjectLevelConstant.qd) {
      // 找最近的清单
      this._findLastListItem(splitIndex, allData, newLine);
    } else if (newLineType === BranchProjectLevelConstant.de) {
      // 找最近的定额
      this._findLastQuota(splitIndex, allData, newLine);
    }
    // 5. 清单处理表达式
    if (newLineType === BranchProjectLevelConstant.qd) {
      if (!newLine.quantityExpression && newLine.quantityExpression !== 0) {
        newLine.quantityExpression = '1';
      }
      if (!newLine.quantityExpressionNbr && newLine.quantityExpressionNbr !== 0) {
        newLine.quantityExpressionNbr = 1;
      }
      if (!newLine.quantity && newLine.quantity !== 0) {
        newLine.quantity = 1;
      }
    }
    // 6. 定额处理人材机标识字段
    if (!newLine.rcjFlag) {
      newLine.rcjFlag = 0;
    }

    // 7.标识普通定额
    if (!newLine.isCostDe) {
      newLine.isCostDe = 0;
    }
  }

  /**
   * 重构下半部分数据 包括parentId， num
   * @param newLine 新的行 ItemBillProject
   * @param below   全数据根据插入位置的截取  [重构下半部分数据 包括parentId， num]
   */
  _rebuildBelowDatas(newLine, below) {
    this._rebuildparentId(newLine, below);
    this._rebuildNum(newLine, below);
  }

  /**
   * 只在加清单或者定额时候会产生重构需求
   * 加清单要把全部数据的改一遍 之后所有清单+1, 定额 +0.([0]*)1
   * 加定额 只把当前清单下的定额+=0.1
   * @param newLine
   * @param below
   * @private
   */
  _rebuildNum(newLine, below) {
    if (newLine.kind === BranchProjectLevelConstant.qd) {
      this._reSortDisplayNum(below, Number.parseInt(newLine.dispNo));
    }
    if (newLine.kind === BranchProjectLevelConstant.de) {
      let index = 0;
      let strArry = newLine.dispNo.split('.');
      let listItemNum = strArry[0];
      let quotaNum = Number.parseInt(strArry[1]);
      while (index < below.length &&
      below[index].kind !== BranchProjectLevelConstant.qd &&
      below[index].kind !== BranchProjectLevelConstant.zfb &&
      below[index].kind !== BranchProjectLevelConstant.fb) {  // 从below第一个元素开始 寻找定额，每个定额 +0.([0]*)1
        below[index].dispNo = listItemNum + '.' + (++quotaNum);
        ++index;
      }
    }
  }

  /**
   * 重构下半部分的parentId
   * 若新增数据 newLine 是分部 或者 子分部 或者 清单 则可能导致插入行下面的部分数据parentId变动
   * newLine 是 分部 下一行是清单，需要处理  将之后的清单的parentId指向当前行
   * newLine 是 子分部 下一行是清单 需要处理 将之后的清单的parentId指向当前行
   * newLine 是 清单 下一行是定额 需要处理
   * @param newLine
   * @param below
   */
  _rebuildparentId(newLine, below) {
    if (!below || below.length === 0) {
      return;
    }
    let newLineType = newLine.kind;
    // 加分部 加分部时候，有两种情况，下一行是清单  下一行是分部，若下一行是分部，不用处理
    if (newLineType === BranchProjectLevelConstant.fb) {
      if (below[0].kind === BranchProjectLevelConstant.qd) {
        this._doRebuildparentId(below, newLine,
          cline => cline.kind === BranchProjectLevelConstant.fb,     // 遇到分部跳出循环
          cline => cline.kind === BranchProjectLevelConstant.qd); // 遇到清单重置清单的parentId
      }
    }
    // 加子分部 下面如果是清单，每个清单parentId重置
    if (newLineType === BranchProjectLevelConstant.zfb) {
      if (below[0].kind === BranchProjectLevelConstant.qd) {
        this._doRebuildparentId(below, newLine,
          cline => cline.kind === BranchProjectLevelConstant.fb || cline.kind === BranchProjectLevelConstant.zfb,   // 遇到分部或子分部跳出循环
          cline => cline.kind === BranchProjectLevelConstant.qd); // 遇到清单重置清单的parentId
      }
    }
    // 加清单
    if (newLineType === BranchProjectLevelConstant.qd) {
      if (below[0].kind === BranchProjectLevelConstant.de) {
        this._doRebuildparentId(below, newLine,
          cline => cline.kind === BranchProjectLevelConstant.fb || BranchProjectLevelConstant.zfb || BranchProjectLevelConstant.qd,   // 遇到分部或子分部或者清单跳出循环
          cline => cline.kind === BranchProjectLevelConstant.de); // 遇到定额重置清单的parentId
      }
    }
  }

  /**
   * 重构parentId
   * 循环 below，如果满足 shoudRebuildparentId 则 below[i].parentId = newLine.sequeNum
   *            如果满足 shouldBreak 则跳出循环
   * @param below 需要重构的部分
   * @param newLine 新的行数据
   * @param shouldBreak 循环退出条件
   * @param shoudRebuildparentId 重置parentId条件
   */
  _doRebuildparentId = (below, newLine, shouldBreak, shoudRebuildparentId) => {
    let index = 0;
    while (index < below.length) {
      if (shouldBreak(below[index])) {
        break;
      }
      if (shoudRebuildparentId(below[index])) {
        below[index].parentId = newLine.sequenceNbr;
      }
      ++index;
    }
  };

  /**
   * 找最近一个定额，并根据最近的定额填充newLine的展示序号
   * 向上找上一个定额，如果找到定额之前赵大鹏清单，则应该返回 清单Num.0
   * @param splitIndex
   * @param allData 全部数据集合  [ItemBillProject]
   * @param newLine 新的行数据    ItemBillProject
   */
  _findLastQuota(splitIndex, allData, newLine) {
    let y = splitIndex - 1;
    let lastNum;
    while (y >= 0) {
      let lastLine = allData[y];
      if (lastLine.kind === BranchProjectLevelConstant.de) {
        lastNum = lastLine.dispNo;
        break;
      } else if (lastLine.kind === BranchProjectLevelConstant.qd) {
        lastNum = lastLine.dispNo + '.0';
        break;
      }
      --y;
    }
    let nowArray = lastNum.split('.');
    let pri = Number.parseInt(nowArray[1]) + 1;
    newLine.dispNo = nowArray[0] + '.' + pri;
  }

  /**
   * 找最近一个清单，并根据最近的清单的序号填充newLine的展示序号
   * 向上找上一个清单，然后当前清单 num = 上一个清单Num+1
   * @param allData 全部数据集合 [ItemBillProject]
   * @param newLine 新的行数据   ItemBillProject
   */
  _findLastListItem(splitIndex, allData, newLine) {
    let x = splitIndex - 1;
    let lastNum = '0';
    while (x >= 0) {
      if (allData[x].kind === BranchProjectLevelConstant.qd) {
        lastNum = allData[x].dispNo;
        break;
      }
      --x;
    }
    if (x > 0) {
      lastNum = allData[x].dispNo == null ? '0' : allData[x].dispNo;
    }
    newLine.dispNo = Number.parseInt(lastNum) + 1 + '';
  }

  /**
   * 找下一个 如果没有下一个，返回length
   * @param allData
   * @param pointLineIndex
   * @returns {*}
   */
  _findNextIndex(allData, pointLineIndex, isNext) {
    let index = pointLineIndex + 1;
    while (index < allData.length) {
      if (isNext(allData[index])) {
        return index;
      }
      ++index;
    }
    return index;
  }

  /**
   * 处理展开收起的箭头
   * 新数据创建默认无箭头
   * 平级数据添加 不需要考虑父层箭头
   * 高级别添加低级别，需要考虑箭头
   *
   * @param top
   * @param pointLine
   * @param newLine
   */
  _dealDisplaySignWhenInsert(top, pointLine, newLine, below) {
    newLine.displaySign = BranchProjectDisplayConstant.noSign;

    // 低级别添加高级别 或 同级别添加
    if (Number.parseInt(pointLine.kind) >= Number.parseInt(newLine.kind) && newLine.kind != BranchProjectLevelConstant.zfb) {
      return;
    }

    let realPointLine;  // 新增数据的父级
    let index = top.length - 1;
    while (index >= 0) {
      if (top[index].sequenceNbr === newLine.parentId) {
        realPointLine = top[index];
      }
      --index;
    }
    // 如果是高级别添加低级别，且父数据为无箭头,处理父数据
    if (realPointLine.displaySign === BranchProjectDisplayConstant.noSign) {
      realPointLine.displaySign = BranchProjectDisplayConstant.open;
    }
    // 如果添加了新的数据，且新数据的下一条数据需要挂在新数据上，新数据需要添加箭头
    if (below && below.length > 0 && Number.parseInt(newLine.kind) < Number.parseInt(below[0].kind)) {
      newLine.displaySign = BranchProjectDisplayConstant.open;
    }
  }

  /**
   * 根据条件获取单价措施下的数据
   * @param allData
   * @param pointLine
   * @return {*}
   */
  getDJCSConditionList(allData, pointLine) {
    return this._getInfluence(allData.getAllNodes(), pointLine);
  }


  /**
   * 根据 pointLine 返回 pointLine的所有子项目
   * 例： pointLine 为分部 返回子 分部 ，清单，定额
   *     pointLine 为清单 返回子 清单，定额
   * @param allData    分部分项或者措施项目全量数据
   * @param pointLine  父行
   * @param unInfluce  要剔除的行，可以不传
   * @return {{datas: *[], end: number, begin: number}|{datas, end, begin: number}|{datas: *, end: number, begin: number}}
   * @private
   */
  _getInfluence(allData, pointLine, unInfluce) {
    if (!Array.isArray(allData)) {
      allData = allData.getAllNodes();
    }
    if (pointLine.kind === BranchProjectLevelConstant.top) {
      return {
        'begin': 0,
        'end': allData.length,
        'datas': allData
      };
    }
    if (pointLine.kind === BranchProjectLevelConstant.fb ||
      pointLine.kind === BranchProjectLevelConstant.zfb ||
      pointLine.kind === BranchProjectLevelConstant.qd) {
      let begin = -1;
      let end = allData.length;
      let parents = this._findParents(allData, pointLine);
      for (let index = 0; index < allData.length; ++index) {
        if (allData[index].sequenceNbr === pointLine.sequenceNbr) {
          begin = index;
          continue;
        }
        if (begin > -1) {
          // 1.下一行级别高于当前行，2.parentId在当前行的 [parentIds] 中 则认为结束
          if (Number.parseInt(pointLine.kind) > Number.parseInt(allData[index].kind) ||
            parents[allData[index].parentId]) {
            end = index;
            break;
          }
        }
      }
      return {
        'begin': begin,
        'end': end,
        'datas': allData.slice(begin, end)
      }; // [bengin, end) bengin为被选中的行
    }
    return {
      'begin': 0, 'end': 0, 'datas': this._findLine(allData, pointLine)
    };
  }

  doChangeLineDisplayOne(influenceLines, parent, sign) {
    for (let index = 0; index < influenceLines.length; ++index) {
      if (influenceLines[index].kind != BranchProjectLevelConstant.de) {
        if (influenceLines[index].sequenceNbr == parent.sequenceNbr) {
          influenceLines[index].displaySign = sign;
          influenceLines[index].displayStatu = 10;
        } else if (influenceLines[index].parentId == parent.parentId) {
          influenceLines[index].displayStatu = 10;
        } else {
          influenceLines[index].displaySign = sign;
          influenceLines[index].displayStatu = 10;
        }
        continue;
      }
      if (sign === BranchProjectDisplayConstant.open) {
        if (influenceLines[index].displayStatu < 10) {
          influenceLines[index].displayStatu = 10;
        }
      }
      if (sign === BranchProjectDisplayConstant.close) {
        influenceLines[index].displayStatu -= 1;
      }
    }

  }

  doChangeLineDisplay(influenceLines, sign) {
    for (let index = 0; index < influenceLines.length; ++index) {
      if (index === 0) {  // 点击的那一行需要修改小箭头的方向
        influenceLines[index].displaySign = sign;
        influenceLines[index].displayStatu = 10;
        continue;
      }
      if (sign === BranchProjectDisplayConstant.open) {
        if (influenceLines[index].displayStatu < 10) {
          influenceLines[index].displayStatu = 10;
        }
      }
      if (sign === BranchProjectDisplayConstant.close) {
        influenceLines[index].displayStatu -= 1;
      }
    }
  }

  _reSortDisplayNum(res, beginListItemNum) {
    let lastListItemNum = 0 + beginListItemNum;
    let lastQuotaLastNum = 0;

    for (let index = 0; index < res.length; ++index) {
      let lineData = res[index];
      if (lineData.kind === BranchProjectLevelConstant.qd) {
        lastListItemNum += 1;
        lineData.dispNo = lastListItemNum + '';
        lastQuotaLastNum = 0;
        continue;
      }
      if (lineData.kind === BranchProjectLevelConstant.de) {
        lastQuotaLastNum += 1;
        lineData.dispNo = lastListItemNum + '.' + lastQuotaLastNum;
      }
    }
  }

  _rewriteJsonFile(top, newLine, below) {
    let mergedRes = top.concat(newLine).concat(below);
    //console.log(mergedRes);

    return mergedRes;
  }

  _findLine(allData, pointLine) {
    let res = [];
    for (let i = 0; i < allData.length; ++i) {
      if (pointLine.sequenceNbr === allData[i].sequenceNbr) {
        res.push(allData[i]);
      }
    }
    return res;
  }

  _findLineById(allData, id) {
    return allData.getNodeById(id);
    /*for (let i = 0; i < allData.length; ++i) {
            if (id === allData[i].sequenceNbr) {
                return allData[i];
            }
        }*/
  }

  _findParents(allData, line) {
    let parents = {};
    parents[line.parentId] = true;
    let lineIndex;
    let buffer = line;
    for (let index = 0; index < allData.length; ++index) {
      if (line.sequenceNbr === allData[index].sequenceNbr) {
        lineIndex = index;
        break;
      }
    }
    while (lineIndex > 0) {
      if (allData[lineIndex].sequenceNbr === buffer.parentId) {
        buffer = allData[lineIndex];
        parents[buffer.parentId] = true;
      }
      --lineIndex;
    }

    return parents;
  }

  _haschild(parent, allData) {
    let obj = {};
    obj[parent.sequenceNbr] = 'buffer';
    for (let i = 0; i < allData.length; ++i) {
      if (obj[allData[i].parentId]) {
        return true;
      }
    }
    return false;
  }

  /**
   * 求新增行相对于展示根节点
   * @param allDatas
   * @param splitIndex
   * @param rootLineId
   * @returns {number}
   * @private
   */
  _getInsertPage(allDatas, pointLine, splitIndex, rootLineId) {
    let displayIndex = 0;
    let begin = false;
    for (let index = 0; index < allDatas.length; ++index) {
      if (!rootLineId || allDatas[index].sequenceNbr === rootLineId) {
        begin = true;
      }
      if (allDatas[index].displayStatu === pointLine.displayStatu) { // 计算显示中的行
        if (begin) {
          displayIndex += 1;
        }
        if (splitIndex === index) {
          break;
        }
      }
    }
    return displayIndex;
  }

  _hasBrother(allData, lineData) {
    let arry = [];
    for (let i = 0; i < allData.length; ++i) {
      if (allData[i].parentId === lineData.parentId &&
        allData[i].sequenceNbr !== lineData.sequenceNbr) {
        arry.push(allData[i]);
      }
    }
    return arry.length;
  }

  _isBaseQdCode(strCode) {
    if (strCode.length < 9) {
      return false;
    }
    if (strCode.length > 9) {
      strCode = strCode.substring(0, 9);
    }
    let querySql = 'select bd_code_level04 from base_list where bd_code_level04 = ?';
    let sqlRes = this.app.betterSqlite3DataSource.prepare(querySql).all(strCode);
    return sqlRes.length > 0;
  }

  _getMaxQdCodeNum(constructId, singleId, unitId, baseCode, a, b) {
    let fbfxs = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
    let csxms = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
    if (b) {
      fbfxs = fbfxs.filter(f => f.sequenceNbr !== b);
      csxms = csxms.filter(c => c.sequenceNbr !== b);
    }

    let max = 0;

    for (let i = 0; i < fbfxs.length; ++i) {
      let code = '' + fbfxs[i].bdCode;
      if (code.length < 9) {
        continue;
      } else {
        let unitCode = code.substring(0, 9);
        if (unitCode === baseCode) {
          let num = Number.parseInt(code.substring(9));
          if (Number.isNaN(num)) {
            continue;
          }
          if (num > max) {
            max = num;
          }
        }
      }
    }
    for (let j = 0; j < csxms.length; ++j) {
      let code = '' + csxms[j].fxCode;
      if (code.length < 9) {
        continue;
      } else {
        let unitCode = code.substring(0, 9);
        if (unitCode === baseCode) {
          let num = Number.parseInt(code.substring(9));
          if (Number.isNaN(num)) {
            continue;
          }
          if (num > max) {
            max = num;
          }
        }
      }
    }

    return max;
  }

  /**
   * 计算工程量表达式值
   * @param constructId
   * @param singleId
   * @param unitWorkId
   * @param pointLine
   * @returns {any}
   */
  getQuantityExpressionNbr(constructId, singleId, unitWorkId, pointLine) {
    let { varMap, variables } = this._getVariables(constructId, singleId, unitWorkId, pointLine);
    let evalStr = pointLine.quantityExpression;
    if (!evalStr) {
      evalStr = '0';
    } else {
      evalStr = evalStr.toString();
    }
    let bsfDeExpression;
    if (evalStr.includes('QDL')) {
      let qdByDeId = PricingFileFindUtils.getQdByDeId(constructId, singleId, unitWorkId, pointLine.sequenceNbr);
      if (!ObjectUtils.isEmpty(qdByDeId)) {
        evalStr = evalStr.replaceAll('QDL', qdByDeId.quantity);
      }
      //子定额修改之前工程量表达式中含有GCL的时候，修改之后还可以允许有GCL
    } else if (evalStr.includes('GCL')) {
      let parentDe = PricingFileFindUtils.getDeById(constructId, singleId, unitWorkId, pointLine.parentDeId);
      if (!ObjectUtils.isEmpty(parentDe)) {
        evalStr = evalStr.replaceAll('GCL', parentDe.quantityExpressionNbr);
      }
    } else if (ObjectUtils.isNotEmpty(bsfDeExpression = this._hasBsfDeExpression(evalStr))) {
      // 如果表达式中包含泵送费定额表达式  那么就需要使用泵送费定额的bsfBaseValue进行计算
      evalStr = evalStr.replaceAll(bsfDeExpression, pointLine.bsfBaseValue);
    } else {
      //如果表达式是 CGRGHJ
      if (evalStr == ConstantUtil.EXP_CG_RGHJ || evalStr == 'DXZSGR' || evalStr == 'DSZSGR') {
        return pointLine.quantityExpressionNbr;
      } else {
        for (let j = 0; j < variables.length; ++j) {
          let reg = new RegExp('\\b' + variables[j] + '\\b', 'g');
          evalStr = evalStr.replaceAll(reg, varMap.get(variables[j]));
        }
      }

    }

    //处理表达式中包含中文括号问题
    let s = evalStr.replace(/（/g, '(').replace(/）/g, ')');
    return eval(s);
  }

  _hasBsfDeExpression(expressionStr) {
    let expression = '';
    for (const str of PumpingAddFeeExpressionConstant.BSHNTL_LIST) {
      if (expressionStr.includes(str)) {
        expression = str;
        break;
      }
    }
    return expression;
  }

  /**
   * 计算工程量表达式和工程量
   * @param constructId
   * @param singleId
   * @param unitWorkId
   * @param pointLine
   */
  caculateQuantityExpressionAndQuantity(constructId, singleId, unitWorkId, pointLine) {
    let res = this.getQuantityExpressionNbr(constructId, singleId, unitWorkId, pointLine);

    pointLine.quantityExpressionNbr = res;
    let unit = this.getUnitNum(pointLine);
    if(pointLine.kind=="04"){ 
      pointLine.quantity = NumberUtil.numberScale(NumberUtil.divide(pointLine.quantityExpressionNbr, unit),getDeUnitFormatEnum(pointLine.unit).value);
    }else{
      pointLine.quantity = NumberUtil.numberScale(NumberUtil.divide(pointLine.quantityExpressionNbr, unit),getUnitFormatEnum(pointLine.unit).value);
    }
  }

  /**
   * 获取工程量明细
   * @param constructId
   * @param singleId
   * @param unitWorkId
   * @param pointLine
   * @returns {{varMap: Map<any, any>, variables: string[]}}
   * @private
   */
  _getVariables(constructId, singleId, unitWorkId, pointLine) {
    let quantities = pointLine.quantities;
    if (!quantities) {
      quantities = [];
    }

    let res = 0;
    for (let i = 0; i < quantities.length; ++i) {
      if (quantities[i].mathResult && quantities[i].accumulateFlag === 1) {
        res += quantities[i].mathResult;
      }
    }

    let varMap = new Map();
    varMap.set('GCLMXHJ', res);

    return {
      'varMap': varMap,
      'variables': ['GCLMXHJ']
    };

  }

  getUnitNum(currentUpdateLine) {
    let unit = Number.parseFloat(currentUpdateLine.unit);
    if (Number.isNaN(unit) || unit == 0) {
      unit = 1;
    }
    return unit;
  }

  findFromAllById(constructId, singleId, unitId, id) {
    let res = {
      allData: null,
      item: null
    };
    let fbfx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
    let fbfxFind = fbfx.filter(f => f.sequenceNbr === id);
    if (fbfxFind && fbfxFind.length > 0) {
      res.allData = fbfx;
      res.item = fbfxFind[0];
      return res;
    }
    let csxm = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
    res.item = csxm.filter(f => f.sequenceNbr === id)[0];
    res.allData = csxm;
    return res;


  }

  _isSameUnit(unitQd, unitDe) {
    if (!unitQd || !unitDe) {
      return false;
    }
    return unitQd.endsWith(unitDe) || unitDe.endsWith(unitQd);
  }

  dealQuantityExpression(constructId, singleId, unitId, deId) {
    let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
    let finds = allData.filter(f => f.sequenceNbr === deId);
    if (!finds || finds.length === 0) {
      allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
      finds = allData.filter(f => f.sequenceNbr === deId);
    }

    let de = finds[0];
    let qd = allData.filter(f => f.sequenceNbr === de.parentId)[0];

    if (this._isSameUnit(qd.unit, de.unit)) {
      de.quantityExpression = qd.quantityExpression;
      de.quantityExpressionNbr = qd.quantityExpressionNbr;
      de.quantity = qd.quantityExpressionNbr / this.getUnitNum(qd);
    } else {
      // 不一致，给 0
      de.quantityExpression = '0';
      de.quantityExpressionNbr = 0;
      de.quantity = 0;
    }
  }


  batchRmoveMainQd(constructId, singleId, unitId, type) {
    //获取所有分部分项  措施项目数据
    if (type == 1) {
      //获取所有的单位
      let unitList = PricingFileFindUtils.getUnitList(constructId);
      if (ObjectUtils.isNotEmpty(unitList)) {
        for (const unit of unitList) {
          this.rmoveMainQd(unit);
        }
      }
    } else {
      let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
      this.rmoveMainQd(unit);
    }
  }

  rmoveMainQd(unit) {
    //获取所有数据
    let allFbFx = unit['itemBillProjects'].getAllNodes();
    let allCs = unit['measureProjectTables'].getAllNodes();
    allFbFx = allFbFx || [];
    allCs = allCs || [];
    let all = [...allFbFx, ...allCs];
    if (!all || all.length == 0) {
      return;
    } else {
      for (let i = 0; i < all.length; ++i) {
        all[i].ifMainQd = false;
      }
    }
  }


  delAnnotations(unit) {
    //获取所有数据
    let allFbFx = unit['itemBillProjects'].getAllNodes();
    let allCs = unit['measureProjectTables'].getAllNodes();
    allFbFx = allFbFx || [];
    allCs = allCs || [];
    let all = [...allFbFx, ...allCs];
    if (!all || all.length == 0) {
      return;
    } else {
      for (let i = 0; i < all.length; ++i) {
        all[i].annotations = null;
        all[i].isShowAnnotations = false;
      }
    }
  }


  delBatchAnnotations(constructId, singleId, unitId, type) {
    //获取所有分部分项  措施项目数据
    if (type == 1) {
      //获取所有的单位
      let unitList = PricingFileFindUtils.getUnitList(constructId);
      if (ObjectUtils.isNotEmpty(unitList)) {
        for (const unit of unitList) {
          this.delAnnotations(unit);
        }
      }
    } else {
      let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
      this.delAnnotations(unit);
    }
  }


  setItemDtaStatus(mockAllData) {

    let returnArray = mockAllData;

    let map = new Map();
    //是否可以上下移动判断
    for (let i = 0; i < returnArray.length; i++) {
      let element = returnArray[i];

      let filter = [];
      if (map.get(element.parentId) != null) {
        filter = map.get(element.parentId);
      } else {
        filter = returnArray.filter(item => item.parentId == element.parentId);
        map.set(element.parentId, filter);
      }
      if (filter.indexOf(element) == 0) {
        element['isFirst'] = true;
      } else {
        element['isFirst'] = false;
      }

      if (filter.indexOf(element) == filter.length - 1) {
        element['isLast'] = true;
      } else {
        element['isLast'] = false;
      }
      //分部是否可以上调下调判断
      //给所有的数据设置不可以升降级
      element.isUpFb = true;
      element.isDownFb = true;
      //如果是分部 不可升级
      if (element.kind == BranchProjectLevelConstant.fb) {
        element.isUpFb = false;
        //获取同层级数据 如果没有同层级其他数据将不可以进行降级操作
        //如果同层级有数据  但是当前分部是第一行数据 不可降级操作
        let ts = returnArray.filter(fb => fb.parentId == element.parentId);
        if (ts.length > 1) {
          if (element.index == 0) {
            element.isDownFb = false;
          } else {
            let b = this.isOnlyQd(ts[element.index-1], returnArray);
            element.isDownFb = b ? false : true;
          }
        } else {
          element.isDownFb = false;
        }
      }

      //判断若下移会导致分部层级超过四层则【降级】按钮置灰  暂未处理TODO SUNPO
      if (element.kind == BranchProjectLevelConstant.zfb) {
        let ts = returnArray.filter(fb => fb.parentId == element.parentId);
        if (ts.length > 1) {
          if (element.index == 0) {
            element.isDownFb = false;
          } else {
            let b = this.isOnlyQd(element.parent, returnArray);
            element.isDownFb = b ? false : true;
          }
        } else {
          element.isDownFb = false;
        }
        //如果选中分部的层级结构是第四级 或者分部中包含第四级分部数据不可以降级
        let fourLeve = this.isFourLeve(element, returnArray);
        if (fourLeve) {
          element.isDownFb = false;
        }

      }
    }

  }


  /**
   * 分部整理
   * @param constructId
   * @param singleId
   * @param unitId
   * @param type
   */
  async branchArrange(constructId, singleId, unitId, typeList) {

    //如果整理后超过四层就不进行整理
    let result=true;
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //获取所有分部数据
    let fbFx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes();
    let top = fbFx.find(top=>top.kind==BranchProjectLevelConstant.top);
    //获取所有分部数据
    let fb = fbFx.filter(fb=>fb.kind==BranchProjectLevelConstant.fb || fb.kind==BranchProjectLevelConstant.zfb);
    //获取所有自动生成的分部数据
    let autoFb = fbFx.filter(fb=>(fb.kind==BranchProjectLevelConstant.fb || fb.kind==BranchProjectLevelConstant.zfb) && fb.autogenerate);
    //如果不含规则4 就需要对分部的层级进行判断
    if(ObjectUtils.isNotEmpty(fb)){
      if(!typeList.includes(FbZlEnum.TYPE4)){
        //处理数据 含有自动生成的分部
        if(ObjectUtils.isNotEmpty(autoFb)){
          fbFx=this.batchAutogenerate(top, PricingFileFindUtils.getFbFx(constructId, singleId, unitId));
        }
        //判断是不是第2层
        if(typeList.length==3){
          result= this.isLeveNum(2,fb,fbFx);
        }
        //判断是不是第3层
        if(typeList.length==2){
          result=this.isLeveNum(3,fb,fbFx);
        }
        //判断是不是第4层
        if(typeList.length==1){
          result=this.isLeveNum(4,fb,fbFx);
        }
      }
    }

    if(result){
      //保存单位的分部整理数据
      unit.fbArrangeList=typeList;
      //删除所有的空清单以及下挂定额数据
      let qdList = fbFx.filter(qd=>qd.kind==BranchProjectLevelConstant.qd && ObjectUtils.isEmpty(qd.fxCode));
      if(ObjectUtils.isNotEmpty(qdList)){
        for (const qd of qdList) {
          await this.service.itemBillProjectOptionService.removeLine(constructId, singleId, unitId, qd, true);
        }
        //获取所有分部数据
        fbFx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes();
      }
      let newDateList=ObjectUtils.cloneDeep(fbFx);
      //执行分部整理
      if(typeList.includes(FbZlEnum.TYPE4)){
        newDateList=newDateList.filter(d=>d.kind!=BranchProjectLevelConstant.fb && d.kind!=BranchProjectLevelConstant.zfb);
        let qdList = newDateList.filter(d=>d.kind==BranchProjectLevelConstant.qd);
        let top = newDateList.find(d=>d.kind==BranchProjectLevelConstant.top);
        qdList.forEach(qd=>{
          qd.parentId=top.sequenceNbr;
        })
      }
      if(typeList.includes(FbZlEnum.TYPE1)){
        await this.arrangeType1(constructId, singleId, unitId,newDateList,2);
      }
      if(typeList.includes(FbZlEnum.TYPE2)){
        await this.arrangeType1(constructId, singleId, unitId,newDateList,4);
      }
      if(typeList.includes(FbZlEnum.TYPE3)){
        await this.arrangeType1(constructId, singleId, unitId,newDateList,6);
      }

      //重新设置下挂清单定额数据的dispNo
      this.againDispNoV1(newDateList);
      //状态数据处理
      this.setItemDtaStatus(newDateList);
      //对数据进行排序处理
      // newItemList.sort((a, b) => a.index - b.index);
      //转为树
      unit.itemBillProjects = arrayToTree(newDateList);
      //序号重排
      unit.itemBillProjects.refreshAssignDispNos();
      await this.againFbHj(unit.itemBillProjects, 'fbfx', constructId, singleId, unitId);
      let fbList = unit.itemBillProjects.filter(top=>top.kind==BranchProjectLevelConstant.fb || top.kind==BranchProjectLevelConstant.zfb);
      // let nodesByKind = unit.itemBillProjects.getNodeById(itemBillProject.sequenceNbr);
      // OptionMenuHandler.setOptionMenu(nodesByKind);
      for(const fb of fbList){
        OptionMenuHandler.setOptionMenu(fb);
      }

    }else {
      return  "整理子目后分部层级超过4级，请先调整分部结构！";
    }
    return result;
  }


  batchAutogenerate(node,fbfx){
      //找到所有的自动生成的分部数据
     let  zdyFb=[];
    function queryZdyFb(node){
        //获取子数据
        let filter = fbfx.filter(fb=>fb.parentId==node.sequenceNbr);
        if(ObjectUtils.isNotEmpty(filter)){
           if(filter[0].kind!=BranchProjectLevelConstant.qd){
             for(const fb of filter){
                   if(fb.autogenerate){
                     zdyFb.push(fb);
                   }else {
                     queryZdyFb(fb);
                   }
             }
           }

        }
     }

    queryZdyFb(node);
    let zdyFbAll=[];
    //处理掉所有的自动生成的分部   同时修改清单的parentId
    for(const fb of zdyFb){
      let findAllSubsets = fbfx.findAllSubsets(fb);
      //过滤所有的清单数据
      let filter = findAllSubsets.filter(qd=>qd.kind==BranchProjectLevelConstant.qd);
      filter.forEach(qd=>{
        qd.parentId=fb.parentId;
        qd.parent=fb.parent;
      });
      let filter1 = findAllSubsets.filter(qd=>qd.kind!=BranchProjectLevelConstant.qd && qd.kind!=BranchProjectLevelConstant.de );
      zdyFbAll.push(...filter1);
    }
    const idSet = new Set(zdyFbAll.map(item => item.sequenceNbr));
    fbfx= fbfx.filter(item1 => !idSet.has(item1.sequenceNbr));
    return fbfx;
  }

  //按专业整理分部
  async arrangeType1(constructId, singleId, unitId,newDateList,index){
    //获取所有的分部
    let fb1 = newDateList.filter(fb=>fb.kind==BranchProjectLevelConstant.fb || fb.kind==BranchProjectLevelConstant.zfb);
    let parentId;
    //没有分部
    if(ObjectUtils.isEmpty(fb1)){
      let top = newDateList.find(d=>d.kind===BranchProjectLevelConstant.top);
      parentId=top.sequenceNbr;
      //区分标准数据和补充数据
      let standardQd = newDateList.filter(qd=>qd.kind===BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(qd.isStandard) && qd.isStandard===1);
      let bcQd= newDateList.filter(qd=>qd.kind===BranchProjectLevelConstant.qd && (ObjectUtils.isEmpty(qd.isStandard) || qd.isStandard!==1));
      await this.arrangeQd(standardQd,bcQd,newDateList,index,"01",unitId,parentId,top,constructId);

    }
    if(ObjectUtils.isNotEmpty(fb1)){
      for(const fb of fb1){
        //获取分部下的所有清单数据
        let qdList = newDateList.filter(qd=>qd.parentId===fb.sequenceNbr && qd.kind===BranchProjectLevelConstant.qd);
        if(ObjectUtils.isNotEmpty(qdList)){
          let standardQd = qdList.filter(qd=>ObjectUtils.isNotEmpty(qd.isStandard) && qd.isStandard===1);
          let bcQd= qdList.filter(qd=>(ObjectUtils.isEmpty(qd.isStandard) || qd.isStandard!==1));
          await this.arrangeQd(standardQd,bcQd,newDateList,index,"02",unitId,fb.sequenceNbr,fb,constructId);
        }
      }
    }


    //如果有直接在单位工程下的清单, 处理
    //let noTopList = ObjectUtils.cloneDeep(newDateList);
    let top = newDateList.find(d=>d.kind===BranchProjectLevelConstant.top);
    parentId = top.sequenceNbr;
    let topQd = newDateList.filter(fb => fb.kind===BranchProjectLevelConstant.top || (fb.kind===BranchProjectLevelConstant.qd && fb.parentId===parentId));
    if(ObjectUtils.isNotEmpty(topQd)){
      let standardQd = topQd.filter(qd=>qd.kind===BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(qd.isStandard) && qd.isStandard===1);
      let bcQd= topQd.filter(qd=>qd.kind===BranchProjectLevelConstant.qd && (ObjectUtils.isEmpty(qd.isStandard) || qd.isStandard!==1));
      await this.arrangeQd(standardQd,bcQd,newDateList,index,"01",unitId,parentId,top,constructId);
    }

  }


  // //按章整理分部
  // async arrangeType2(constructId, singleId, unitId,newDateList){
  //   //获取所有的分部
  //   let fb1 = newDateList.filter(fb=>fb.kind==BranchProjectLevelConstant.fb || fb.kind==BranchProjectLevelConstant.zfb);
  //   let parentId;
  //   //没有分部
  //   if(ObjectUtils.isEmpty(fb1)){
  //     let top = newDateList.find(d=>d.kind==BranchProjectLevelConstant.top);
  //     parentId=top.sequenceNbr;
  //     //区分标准数据和补充数据
  //     let standardQd = newDateList.filter(qd=>qd.kind==BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(qd.isStandard) && qd.isStandard==1);
  //     let bcQd= newDateList.filter(qd=>qd.kind==BranchProjectLevelConstant.qd && (ObjectUtils.isEmpty(qd.isStandard) || qd.isStandard!=1));
  //     await this.arrangeQd(standardQd,bcQd,newDateList,2,"01",unitId,parentId,top);
  //
  //   }else {
  //     for(const fb of fb1){
  //       //获取分部下的所有清单数据
  //       let qdList = newDateList.filter(qd=>qd.parentId==fb.sequenceNbr);
  //       if(ObjectUtils.isNotEmpty(qdList)){
  //         let standardQd = qdList.filter(qd=>ObjectUtils.isNotEmpty(qd.isStandard) && qd.isStandard==1);
  //         let bcQd= qdList.filter(qd=>(ObjectUtils.isEmpty(qd.isStandard) || qd.isStandard!=1));
  //         await this.arrangeQd(standardQd,bcQd,newDateList,2,"02",unitId,fb.sequenceNbr,fb);
  //       }
  //     }
  //   }
  // }
  //
  // //按节整理分部
  // async arrangeType3(constructId, singleId, unitId,newDateList){
  //   //获取所有的分部
  //   let fb1 = newDateList.filter(fb=>fb.kind==BranchProjectLevelConstant.fb || fb.kind==BranchProjectLevelConstant.zfb);
  //   let parentId;
  //   //没有分部
  //   if(ObjectUtils.isEmpty(fb1)){
  //     let top = newDateList.find(d=>d.kind==BranchProjectLevelConstant.top);
  //     parentId=top.sequenceNbr;
  //     //区分标准数据和补充数据
  //     let standardQd = newDateList.filter(qd=>qd.kind==BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(qd.isStandard) && qd.isStandard==1);
  //     let bcQd= newDateList.filter(qd=>qd.kind==BranchProjectLevelConstant.qd && (ObjectUtils.isEmpty(qd.isStandard) || qd.isStandard!=1));
  //     await this.arrangeQd(standardQd,bcQd,newDateList,4,"01",unitId,parentId,top);
  //
  //   }else {
  //     for(const fb of fb1){
  //       //获取分部下的所有清单数据
  //       let qdList = newDateList.filter(qd=>qd.parentId==fb.sequenceNbr);
  //       if(ObjectUtils.isNotEmpty(qdList)){
  //         let standardQd = qdList.filter(qd=>ObjectUtils.isNotEmpty(qd.isStandard) && qd.isStandard==1);
  //         let bcQd= qdList.filter(qd=>(ObjectUtils.isEmpty(qd.isStandard) || qd.isStandard!=1));
  //         await this.arrangeQd(standardQd,bcQd,newDateList,6,"02",unitId,fb.sequenceNbr,fb);
  //       }
  //     }
  //   }
  // }


  async arrangeQd(standardQd,bcQd,newDateList,index,kind,unitId,parentId,parent,constructId){
    let param;
    let fbIndex=0;
    if(ObjectUtils.isNotEmpty(standardQd)){
      let qdGroupBy1 = this.qdGroupBy(index,standardQd);
      let keys =Object.keys(qdGroupBy1).sort((a, b) => Number(a) - Number(b));
      for(const k of keys){
        let zyqd = qdGroupBy1[k];
        switch (index) {
          case  2:
            param={bdCodeLevel01:k};
            break;
          case  4:
            param={bdCodeLevel02:k};
            break;
          case  6:
            param={bdCodeLevel03:k};
            break;
        }
        let baseList = await this.service.baseListService.queryQd(param);
        if(ObjectUtils.isNotEmpty(baseList)) {
          let name;
          //创建分部
          switch (index) {
            case  2:
              name= baseList.bdNameLevel01;
              break;
            case  4:
              name= baseList.bdNameLevel02;
              break;
            case  6:
              name= baseList.bdNameLevel03;
              break;
          }
          let newFb1 = this.newFb(kind, parentId,parent,name, unitId);
          newFb1.index= fbIndex;
          fbIndex++;
          //带上编码
          newFb1.bdCode = k;
          //通用设置编码
          newFb1.bdCode = this.convertChapter(k, constructId);
          newDateList.push(newFb1);
          let b1 = this.isFourLeve(newFb1,newDateList);
          newFb1.optionMenu = BranchProjectOptionMenuConstant.getDefaultMenu(newFb1.kind);
          if(b1){
            newFb1.optionMenu =newFb1.optionMenu.filter(op=>op!=BranchProjectOptionMenuConstant.addChildDivision);
          }
          zyqd.forEach(qd =>{
            qd.parentId = newFb1.sequenceNbr;
            qd.parent = newFb1;

          } );
        }
      }
    }
    //补充清单
    if(ObjectUtils.isNotEmpty(bcQd)){
      let newFb1 = this.newFb(kind, parentId,parent, "补充清单", unitId);
      newFb1.index= fbIndex;
      fbIndex++;
      newDateList.push(newFb1);
      let b1 = this.isFourLeve(newFb1,newDateList);
      newFb1.optionMenu = BranchProjectOptionMenuConstant.getDefaultMenu(newFb1.kind);
      if(b1){
        newFb1.optionMenu =newFb1.optionMenu.filter(op=>op!=BranchProjectOptionMenuConstant.addChildDivision);
      }
      bcQd.forEach(qd =>{
        qd.parentId = newFb1.sequenceNbr;
        qd.parent = newFb1;

      } );
    }
  }






  qdGroupBy(index,standardQdList){
    const groupedByMap = standardQdList.reduce((acc, item) => {
      let code=item.fxCode.substring(0,index);
      // 如果 acc 中还没有对应的 type 键，则创建一个空数组
      if (!acc[code]) {
        acc[code] = [];
      }
      // 将当前项添加到对应类型的数组中
      acc[code].push(item);
      return acc;
    }, {});

    return groupedByMap;
  }





  newFb(kind,parentId,parent,name,unitId){
    return {
      kind:kind,
      sequenceNbr:Snowflake.nextId(),
      type:"部",
      appendType:["部"],
      displayStatu:10,
      unitId:unitId,
      parentId:parentId,
      name:name,
      fxName:name,
      parent:parent,
      displaySign:1,
      autogenerate:true,//自动生成的分部
    }

  }













  isLeveNum(leveNum,fbData,allData){
    let result=true;
    for(const fb of fbData){
      let num = this.getLevelNum(1, fb, allData);
      if(leveNum==num){
        //查询当前分部下是否有清单数据
        let find = allData.find(qd=>qd.parentId==fb.sequenceNbr);
        if(ObjectUtils.isNotEmpty(find)){
          if(find.kind!=BranchProjectLevelConstant.qd){
            result=false;
            break;
          }else {
            let find1 = allData.find(qd=>qd.parentId==fb.sequenceNbr && ObjectUtils.isNotEmpty(qd.fxCode));
            if(ObjectUtils.isNotEmpty(find1)){
              result=false;
              break;
            }
          }

        }
      }
    }
    return result;
  }


  /**
   a. 按“当前清单一借用清单一补充清单"顺序排序;
   b. 对当前清单按章节输入排序:
   c. 对相同清单按输入的先后顺序排序:
   d. 对补充清单按编码排序。
   * @param type   sort 排序   sortSave保存清单顺序  sortReset 还原清单顺序
   * @returns {*}
   */
  qdSort(constructId, singleId, unitId, type) {
    // ConstantUtil.QD_SORT;
    // ConstantUtil.QD_SORT_SAVE;
    // ConstantUtil.QD_SORT_RESET;
    switch (type) {
      case ConstantUtil.QD_SORT:
        this.doQdSort(constructId, singleId, unitId);
        break;

      case ConstantUtil.QD_SORT_SAVE:

        this.doQdSortSave(constructId, singleId, unitId);
        break;

      case ConstantUtil.QD_SORT_RESET:

        this.doQdSortReset(constructId, singleId, unitId);
        break;
    }
    let result=true;
    return result;
  }

  doQdSort(constructId, singleId, unitId){
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //获取所有分部数据
    let fbFx = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
    //获取所有分部数据
    let fb = fbFx.filter(fb=>fb.kind==BranchProjectLevelConstant.fb || fb.kind==BranchProjectLevelConstant.zfb);
    if(ObjectUtils.isEmpty(fb)){
      let top = fbFx.find(d=>d.kind==BranchProjectLevelConstant.top);
      //区分标准数据和补充数据
      let standardQd = fbFx.filter(qd=>qd.kind==BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(qd.isStandard) && qd.isStandard==1 && ObjectUtils.isNotEmpty(qd.fxCode));
      let nullQd = fbFx.filter(qd=>qd.kind==BranchProjectLevelConstant.qd && ObjectUtils.isEmpty(qd.fxCode));
      let bcQd= fbFx.filter(qd=>qd.kind==BranchProjectLevelConstant.qd &&  ObjectUtils.isNotEmpty(qd.fxCode) && (ObjectUtils.isEmpty(qd.isStandard) || qd.isStandard!=1));
      //修改索引
      this.updateQdIndex(standardQd,nullQd,bcQd);
      //重新排序
      fbFx.sortChildren(top);
    }else {
      for(const fb1 of fb){
        //获取分部下的所有清单数据
        let qdList = fbFx.filter(qd=>qd.parentId==fb1.sequenceNbr && qd.kind==BranchProjectLevelConstant.qd);
        if(ObjectUtils.isNotEmpty(qdList)){
          let standardQd = qdList.filter(qd=>ObjectUtils.isNotEmpty(qd.isStandard) && qd.isStandard==1 && ObjectUtils.isNotEmpty(qd.fxCode));
          let nullQd = fbFx.filter(qd=>qd.kind==BranchProjectLevelConstant.qd && ObjectUtils.isEmpty(qd.fxCode));
          let bcQd= qdList.filter(qd=>(ObjectUtils.isEmpty(qd.isStandard) || qd.isStandard!=1) &&  ObjectUtils.isNotEmpty(qd.fxCode));
          this.updateQdIndex(standardQd,nullQd,bcQd);
          //重新排序
          fbFx.sortChildren(fb1);
        }
      }
    }
    //重新设置下挂清单定额数据的dispNo
    this.againDispNoV1(fbFx);
    this.setItemDtaStatus(fbFx.getAllNodes());
  }


  doQdSortSave(constructId, singleId, unitId){
      let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
      if(ObjectUtils.isEmpty(unit.qdSortData)){
        unit.qdSortData={};
      }
      //获取所有qd
    let qdList = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes().filter(qd=>qd.kind==BranchProjectLevelConstant.qd);
    //保存清单顺序
    let newArr=[];
    let index=0;
    for(const qd of qdList){
      let obj={
        sequenceNbr:qd.sequenceNbr,
        index:index
      }
      newArr.push(obj);
      index++;
    }

    unit.qdSortData[unitId]=newArr;
    unit.qdSortSaveFlag=true;
  }

  async doQdSortReset(constructId, singleId, unitId){
    let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    //获取清单排序数据
    let qdSortData = unit.qdSortData[unitId];
    if(ObjectUtils.isNotEmpty(qdSortData)){
      let allData = treeToArray(unit.itemBillProjects);
      //获取当前单位数据
      //获取所有分部数据
      let qdList;
      let top;
      let fb = allData.filter(fb=>fb.kind==BranchProjectLevelConstant.fb || fb.kind==BranchProjectLevelConstant.zfb);
      if(ObjectUtils.isNotEmpty(fb)){
        //删除所有分部数据  并将所有的清单parentId改为父节点id
        let itemBillProjects = allData.filter(fb=>fb.kind!=BranchProjectLevelConstant.fb && fb.kind!=BranchProjectLevelConstant.zfb);
        top = itemBillProjects.filter(fb=>fb.kind==BranchProjectLevelConstant.top);
        qdList = itemBillProjects.filter(fb=>fb.kind==BranchProjectLevelConstant.qd);
        for (const qd of qdList){
          qd.parentId=top[0].sequenceNbr;
        }
      }else{
        top = allData.filter(fb=>fb.kind==BranchProjectLevelConstant.top);
        qdList = allData.filter(fb=>fb.kind==BranchProjectLevelConstant.qd);
      }
      //不一样的清单
      const idSet = new Set(qdSortData.map(item => item.sequenceNbr));
      const noSame = qdList.filter(item1 => !idSet.has(item1.sequenceNbr));
      //一样的清单
      const same = qdList.filter(item1 =>
          qdSortData.some(item2 => {
            if(item1.sequenceNbr == item2.sequenceNbr){
              item1.index=item2.index;
              return true;
            }else {
              return false;
            }
          })
      );


      let index=same[same.length-1].index+1;
      for(const qd of noSame){
        qd.index=index;
        index++;
      }
      //重组数据
      let newList=[];
      newList.push(...top);
      newList.push(...same);
      newList.push(...noSame);

      //转为树
      unit.itemBillProjects = arrayToTree(newList);
      let itemBillProject = unit.itemBillProjects.find(top=>top.kind==BranchProjectLevelConstant.top);
      //重新排序
      unit.itemBillProjects.sortChildren(itemBillProject);
      //序号重排
      unit.itemBillProjects.refreshAssignDispNos();
      await this.againFbHj(unit.itemBillProjects, 'fbfx', constructId, singleId, unitId);
      let nodesByKind = unit.itemBillProjects.getNodeById(itemBillProject.sequenceNbr);
      OptionMenuHandler.setOptionMenu(nodesByKind);



    }


  }


  updateQdIndex(standardQd,nullQd,bcQd){
    let index=0
    if( ObjectUtils.isNotEmpty(standardQd)){
      standardQd.sort((a, b) => Number(a.fxCode.substring(0,9)) - Number(b.fxCode.substring(0,9)));
      for(const qd of standardQd){
        qd.index=index;
        index++;
      }
    }
    if( ObjectUtils.isNotEmpty(nullQd)){
      for(const qd of nullQd){
        qd.index=index;
        index++;
      }
    }
    if( ObjectUtils.isNotEmpty(bcQd)){
      // bcQd.sort((a, b) => BigInt(a.sequenceNbr) - BigInt(b.sequenceNbr));
      //按照字典排序
      bcQd.sort((a, b) => a.sequenceNbr.localeCompare(b.sequenceNbr));
      for(const bq of bcQd){
         bq.index=index;
         index++;
      }
    }


  }







  isOnlyQd(node, allData) {
    //获取node下的所有数据
    let children = allData.filter(fb => fb.parentId == node.sequenceNbr);
    if (ObjectUtils.isNotEmpty(children)) {
      if (children[0].kind == BranchProjectLevelConstant.qd) {
        return true;
      }
    }
    return false;
  }


  statusMainAndLock(pointLine,allData,type){
    let flag=true;
    let id=pointLine.parentId;
   if(type==1){
     while(flag){
       let parent = allData.find(d=>id==d.sequenceNbr);
       if(ObjectUtils.isEmpty(parent)){
         break;
       }
       let filter = allData.filter(d=>d.parentId==parent.sequenceNbr && d.ifMainQd);
       if(ObjectUtils.isNotEmpty(filter)){
         //父级全选状态   子集全选引起的父级全选
         parent.mainQdParentAllCheckFlag=true;
       }else {
         let filter2 = allData.filter(d=>d.parentId==parent.sequenceNbr && d.mainQdParentAllCheckFlag);
         if(ObjectUtils.isNotEmpty(filter2)){
           //父级全选状态   子集全选引起的父级全选
           parent.mainQdParentAllCheckFlag=true;
         }else {
           parent.mainQdParentAllCheckFlag=false;
         }
       }


       let filter1 = allData.filter(d=>d.parentId==parent.sequenceNbr && !d.ifMainQd);
       if(ObjectUtils.isEmpty(filter1)){
         parent.ifMainQd=true;
       }else {
         parent.ifMainQd=false;
       }
       id=parent.parentId;
     }

   }else {
     while(flag){
       let parent = allData.find(d=>id==d.sequenceNbr);
       if(ObjectUtils.isEmpty(parent)){
         break;
       }
       let filter = allData.filter(d=>d.parentId==parent.sequenceNbr && d.lockPriceFlag);
       if(ObjectUtils.isNotEmpty(filter)){
         //父级全选状态   子集全选引起的父级全选
         parent.parentLockPriceFlag=true;
       }else {
         let filter2 = allData.filter(d=>d.parentId==parent.sequenceNbr && d.parentLockPriceFlag);
         if(ObjectUtils.isNotEmpty(filter2)){
           //父级全选状态   子集全选引起的父级全选
           parent.parentLockPriceFlag=true;
         }else {
           parent.parentLockPriceFlag=false;
         }
       }


       let filter1 = allData.filter(d=>d.parentId==parent.sequenceNbr && !d.lockPriceFlag);
       if(ObjectUtils.isEmpty(filter1)){
         parent.lockPriceFlag=true;
       }else {
         parent.lockPriceFlag=false;
       }
       id=parent.parentId;
     }
   }


  }


  /**
   *复制
   * 1. 获取选中行
   * 2. 根据前端参数，看是否需要选中行的子集
   * （情况1： 复制分部，带子集，那么 需要存分部下的清单及定额，不包括分部自身及子分部）
   * （情况2： 复制清单，带子集，那么 需要存清单及定额）
   * （情况3： 复制只有定额，存定额）
   * （情况4： 复制包括分部 清单 定额 ； 根据分部查询到子清单 然后根据所有清单过滤定额，删除掉孤立定额，然后将每个清单下的定额查询出来存储清单+定额）
   * （情况5： 复制包括清单 定额 ；      根据所有清单过滤定额，删除掉孤立定额，然后将每个清单下的定额查询出来存储清单+定额）
   * 总结：操作区最终复制出的内容应该是
   *      清单1
   *          定额1
   *          定额2
   *          定额3
   *      清单2
   *          定额1
   *          定额2
   *          定额3
   *      清单3
   *          定额1
   *          定额2
   *          定额3
   *
   * 3. 复制每个定额对应的人材机数据
   * 4. 复制每个定额的换算信息数据
   * 5. 复制每个定额的人材机换算规则数据
   * 6. 复制每个清单的特征及内容
   * 7. 复制每个数据的工程量明细
   */

  /**
   * 粘贴
   *
   * 1.遍历复制的结构，pointLine为前端传入选中行，然后根据复制的结构，一条条增加结构
   * 2.利用for in 语句 将复制的数据的属性填充进入每一行（编号行，seqNum行不动）
   * 3.将
   *      人材机，    项目下 rcjDetailList 拿到，接着，过滤当前复制的de的对应人材机 然后改 rcjDetailList 的deId
   *      人材机的配合比， 项目下 rcjDetailList 同上 ，但是对应的不是定额 是人材机
   *      换算信息数据, 拿到原来项目的  项目下 这个属性 conversionInfoList  做法同人材机 改 deId  conversionInfo.deId = 新行的.sequenceNbr;
   *      材机换算规则数据  项目下 rcjRules , 做法同上 deId  但是内存存的是个 unit.rcjRules = {}; // Map: {ruleId : [...rule]} 这里注意一下
   *      清单的特征及内容  项目下拿 listFeatureList 改 qdId,
   *      工程量明细    pointLine.quantities; 清单下 自带这个属性，理论上insertLine时候直接过来的 如果没有 看一下 清单行的 quantities
   *      以上内容 修改关联的deid然后存入新项目
   * 4.从新计算单价构成
   */




  //对应编码为章节码
  convertChapter(bdCode, constructId){
      let result = bdCode;
      let construct = PricingFileFindUtils.getProjectObjById(constructId);
      let m = construct.mainSetting;
      if(ObjectUtils.isEmpty(m) || m.bdCode === '1'){
          //为空默认流水码  不做设置
          return result;
      }
      if(m.bdCode === '2'){
          if(bdCode.length ===2){
            result = Object.values(ChapterEnum).find(item => item.code == bdCode).desc;
          }
          if(bdCode.length ===4){
            let a = bdCode.slice(0, 2);
            result = Object.values(ChapterEnum).find(item => item.code == a).desc+'.'+bdCode.slice(3, 4);
          }
          if(bdCode.length ===6){
            let b = bdCode.slice(0, 2);
            result = Object.values(ChapterEnum).find(item => item.code == b).desc+'.'+bdCode.slice(3, 4)+'.'+bdCode.slice(5, 6);
          }
          return result;
      }
  }
}

BaseBranchProjectOptionService.toString = () => '[class BaseBranchProjectOptionService]';
module.exports = BaseBranchProjectOptionService;
