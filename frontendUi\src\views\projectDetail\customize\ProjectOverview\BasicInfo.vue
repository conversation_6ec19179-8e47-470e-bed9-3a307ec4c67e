<template>
  <div class="table-content">
    <vxe-table
      align="center"
      height="98%"
      :style="{ width: columnWidth(450) + 'px', maxWidth: '100%' }"
      ref="basicInfoTable"
      :loading="loading"
      :column-config="{ resizable: true }"
      :row-config="{
        isHover: true,
        isCurrent: true,
        keyField: cacheAll.rowKeyFiled,
      }"
      :data="tableData"
      :tree-config="{
        expandAll: true,
        children: cacheAll.treeChildrenKey,
        reserve: true,
      }"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
        showIcon: false,
        showStatus: false,
      }"
      @cell-click="
        cellData => {
          useCellClickEvent(cellData);
        }
      "
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
      class="table-edit-common table-no-outer-border"
      :cell-class-name="
        ({ $columnIndex, row, column }) => {
          const selectName = selectedClassName({ $columnIndex, row, column });
          if (
            column.field === 'name' &&
            ['基本信息', '投标信息', '招标信息'].includes(row.name) &&
            row.addFlag === 0
          ) {
            return 'title-bold ' + selectName;
          }

          if (column.field === 'name' && row.type === 'title') {
            return 'title-bold ' + selectName;
          }
          if (
            column.field === 'name' &&
            ['招标人(发包人)', '招标人(发包人)法人或其授权人'].includes(
              row.name
            )
          ) {
            return 'color-red ' + selectName;
          }
          return selectName;
        }
      "
      :row-class-name="
        ({ row }) => {
          if (row.lockFlag == 1) {
            return 'row-lock-color';
          }
        }
      "
    >
      <vxe-column
        field="dispNo"
        :width="columnWidth(50)"
        title="序号"
      ></vxe-column>
      <vxe-column
        field="name"
        align="left"
        title="名称"
        :width="columnWidth(196)"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-if="row.addFlag && !row.lockFlag"
            placeholder="请输入名称"
            v-model="row.name"
            type="text"
            name="name"
            @blur="inputFinish(row, $event, 'name')"
          ></vxe-input>
          <span v-else>{{ row.name }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="remark"
        align="left"
        title="备注"
        :width="columnWidth(196)"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxeTableEditSelect
            :filedValue="row.remark"
            :list="row.jsonStr"
            :isNotLimit="true"
            v-if="ifShowSelect(row)"
            @update:filedValue="
              newValue => {
                saveCustomInput(newValue, row, 'remark', $rowIndex);
              }
            "
          ></vxeTableEditSelect>
          <span v-else>{{ row.remark }}</span>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script setup>
import {
  onMounted,
  onUpdated,
  onActivated,
  ref,
  watch,
  inject,
  getCurrentInstance,
} from 'vue';
import xeUtils from 'xe-utils';
import { message, Modal } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@/api/csProject';
import operateList from '../operate';
import { insetBus } from '@/hooks/insetBus';
import { comBasicInfoFun } from './comBasiciInfo';
import { columnWidth } from '@/hooks/useSystemConfig';

import { useCellClick } from '@/hooks/useCellClick';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } =
  useCellClick();
const props = defineProps({
  activeKey: {
    type: Number,
    default: 1,
  },
});
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
let lockData = operateList.value.find(item => item.name === 'lock');
console.log('lockData', lockData);
let basicInfoTable = ref();
let loading = ref(false);
let constructLevel = ref(true);
const activeKey = ref(1);
const inputMaxLength = ref(50);
const projectStore = projectDetailStore();

let tableData = ref([]);

const createRequestParams = () => {};
const {
  getPageData,
  delOperateFun,
  saveAndUpdateOperate,
  lockOperate,
  dafaultParams,
} = comBasicInfoFun({ activeKey, pageType: 'jbgcxx' });
const flashFun = () => {
  console.log('*****************基本信息');
  if (
    projectStore.asideMenuCurrentInfo?.key === '11' &&
    props.activeKey === 1 &&
    projectStore.currentTreeInfo?.levelType !== 2 &&
    ['项目概况', '工程概况'].includes(projectStore.tabSelectName)
  ) {
    getBasicInfo();
  }
};

const ifShowSelect = row => {
  return (
    (projectStore.currentTreeInfo?.levelType === 1 && !row.lockFlag) ||
    row.addFlag ||
    (projectStore.currentTreeInfo?.levelType === 3 &&
      !['工程专业'].includes(row.name))
  );
};
let isRefresh = ref(false);
const saveCustomInput = (newValue, row, name, index) => {
  const list = [null, undefined, ''];
  if (
    (list.includes(row[name]) && list.includes(newValue)) ||
    row[name] === newValue
  )
    return;
  row[name] = newValue;
  if (row.name === '工程名称' || row.name === '单位工程名称') {
    isRefresh.value = true;
  } else {
    isRefresh.value = false;
  }
  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true);
};

watch(() => projectStore.asideMenuCurrentInfo, flashFun);

watch(() => projectStore.currentTreeInfo, flashFun);
watch(() => projectStore.isRefreshBaseInfo, flashFun);
const getBasicInfo = async () => {
  let levelType = projectStore.currentTreeInfo?.levelType;
  if (!levelType) {
    return;
  }
  let res = await getPageData();
  if (res.status === 200) {
    loading.value = false;
    if (
      projectStore.type === 'jieSuan' &&
      projectStore.currentTreeInfo.levelType === 1
    ) {
      res.result = res.result.filter(i => i.groupCode === 1);
    }
    tableData.value = res.result;
    cacheAll.currentRecord =
      cacheAll.newAddRowSeq === null
        ? tableData.value[0]
        : findLastAddRow(tableData.value);
    resetCurrentRow();
    cacheAll.newAddRowSeq = null;
    console.log('********getBasicInfo', res.result);
  }
};

const findLastAddRow = tree => {
  console.log('cacheAll.newAddRowSeq', cacheAll.newAddRowSeq, tree);
  let targetBeforeIdx = tree.findIndex(
    i => i.sequenceNbr === cacheAll.newAddRowSeq
  );
  let target = tree[targetBeforeIdx];
  return target;
};

const deleteRowForBasicInfo = async sequenceNbr => {
  let res = await delOperateFun(sequenceNbr);
  if (res.status === 200) {
    message.success('删除成功');
    getBasicInfo();
  }
};

const saveOrUpdateBasicInfo = async (param, isUpdate = false) => {
  let res = await saveAndUpdateOperate(param.data);
  console.log('saveOrUpdateBasicInfo', res);
  res.status === 200 ? message.success('操作成功') : '';
  if (res.status === 200 && !isUpdate) {
    getBasicInfo();
  }
  if (res.status === 200 && isRefresh.value) {
    projectStore.SET_IS_REFRESH_PROJECT_TREE(true);
  }
};
let lockStatus = ref(0);
watch(
  () => lockStatus.value,
  val => {
    lockData.label = val ? '解锁' : '锁定';
    console.log(lockData);
  }
);
const cacheAll = {
  newAddRowSeq: null,
  currentRecord: null,
  treeChildrenKey: 'childrenList',
  rowKeyFiled: 'sequenceNbr',
  newRecord: function (parentId, groupCode) {
    return {
      sequenceNbr: Date.now(),
      name: '',
      remark: null,
      addFlag: 1,
      lockFlag: 0,
      parentId: parentId,
      groupCode: groupCode,
    };
  },
  copyRow: function (row) {
    let newRow = {};
    Object.assign(newRow, row);
    newRow.sequenceNbr = Date.now();
    newRow.childrenList = null;
    newRow.addFlag = 1;
    newRow.lockFlag = 0;
    newRow.recDate = null;

    return newRow;
  },
};

const inputFinish = (row, e, attr) => {
  let value = xeUtils.trim(e.value);
  if (value.length > 50) {
    value = value.slice(0, 50);
    row[attr] = value;
    message.warning('输入过长，请输入50个字符范围内');
  }
  row[attr] = value;

  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true);

  // emits('saveOrUpdateBasicInfo', { data: tableData.value });
};

const insertInData = (tree, selectNode, newNode) => {
  const key = cacheAll.rowKeyFiled;

  let levelType = projectStore.currentTreeInfo?.levelType;
  if (levelType === 3) {
    const index = tree.findIndex(item => item[key] === selectNode[key]) + 1;
    tree.splice(index, 0, newNode);
    return;
  }

  for (let pos = 0; pos < tree.length; pos++) {
    let node = tree[pos];
    if (node[key] === selectNode[key]) {
      node.childrenList.splice(0, 0, newNode);
      return;
    } else {
      let index = node.childrenList.findIndex(
        item => item[key] === selectNode[key]
      );
      if (index !== -1) {
        node.childrenList.splice(index + 1, 0, newNode);
        return;
      }
    }
  }
};

const resetCurrentRow = () => {
  cacheAll.currentRecord =
    cacheAll.currentRecord || (tableData.value ? tableData.value[0] : null);
  basicInfoTable.value?.setCurrentRow(cacheAll.currentRecord);
};

function modalTip(content) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: '',
      content: content,
      onOk: () => {
        resolve(true);
      },
      onCancel() {
        resolve(false);
      },
    });
  });
}

async function insertHandle(posRow, newRow) {
  const selectRecord = posRow || basicInfoTable.value?.getCurrentRecord();
  console.log(basicInfoTable.value?.getCurrentRecord());
  if (!selectRecord) {
    await modalTip('请选中要插入的位置');
    return;
  }

  let allData = tableData.value;
  let newRecord =
    newRow ||
    cacheAll.newRecord(
      projectStore.currentTreeInfo?.levelType === 3
        ? null
        : selectRecord.parentId || selectRecord.sequenceNbr,
      projectStore.currentTreeInfo?.levelType === 3
        ? allData.length + 1
        : selectRecord.groupCode
    );

  cacheAll.newAddRowSeq = newRecord.sequenceNbr;
  console.log('=========11====: ', cacheAll.newAddRowSeq);
  insertInData(allData, selectRecord, newRecord);
  // cacheAll.currentRecord = newRecord
  // basicInfoTable.value.loadData(allData)
  // basicInfoTable.value.setEditRow(cacheAll.currentRecord)
  // resetCurrentRow()
  saveOrUpdateBasicInfo({ data: xeUtils.clone(allData, true) });
  // emits('saveOrUpdateBasicInfo', { data: allData });
}

const resetLockHandle = async () => {
  let res = await lockOperate(lockStatus.value);
  if (res.status === 200) {
    message.success('操作成功');
    getBasicInfo();
    lockStatus.value = !lockStatus.value;
  }
};

async function deleteHandle(row) {
  const selectRecord = row || basicInfoTable.value?.getCurrentRecord();
  if (!selectRecord?.addFlag) {
    //await modalTip('默认信息不能删除');
    message.warning('默认信息不能删除');
    return;
  }

  if (selectRecord.lockFlag) {
    //await modalTip('锁定行不能被删除');
    message.warning('锁定行不能被删除');
    return;
  }

  const status = await modalTip('确定要删除选中行？');
  if (!status) {
    return;
  }

  deleteRowForBasicInfo(selectRecord[cacheAll.rowKeyFiled]);
}

const menuConfig = ref({
  body: {
    options: [
      [
        { code: 'insertRow', name: '插入行', disabled: false },
        { code: 'copyRow', name: '复制行', disabled: false },
        { code: 'deleteRow', name: '删除行', disabled: false },
      ],
    ],
  },
  visibleMethod({ row, type, options }) {
    const $table = basicInfoTable.value;
    $table.setCurrentRow(row);
    if ($table) {
      if (type === 'body') {
        options.forEach(list => {
          list.forEach(item => {
            if (item.code === 'deleteRow') {
              if (row && (!row.addFlag || row.lockFlag)) {
                item.disabled = true;
              } else {
                item.disabled = false;
              }
            }
          });
        });
      }
    }
    return true;
  },
});

const contextMenuClickEvent = ({ menu, row, column }) => {
  const $table = basicInfoTable.value;
  if ($table) {
    switch (menu.code) {
      case 'insertRow':
        insertHandle(row);
        break;
      case 'copyRow':
        let newRow = cacheAll.copyRow(row);
        insertHandle(row, newRow);
        break;
      case 'deleteRow':
        deleteHandle(row);
        break;
    }
  }
};

onUpdated(() => {
  //设置展开所有节点
  basicInfoTable.value.setAllTreeExpand(true);

  //设置选中行为第一行
  resetCurrentRow();

  constructLevel.value = projectStore.currentTreeInfo?.levelType === 1;

  //重置锁定状态
  lockStatus.value = tableData.value[1]?.lockFlag;
});
onActivated(() => {
  insetBus(bus, projectStore.componentId, 'basicInfo', async data => {
    if (data.name === 'insert') insertHandle(null);
    if (data.name === 'lock') resetLockHandle(null);
    if (data.name === 'delete') deleteHandle(null);
  });
});
onMounted(() => {
  getBasicInfo();
});
defineExpose({
  insertHandle,
  resetLockHandle,
  deleteHandle,
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.table-content {
  height: calc(100%);
}
.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }
  .color-red {
    color: #de3f3f;
  }

  .row-lock-color {
    background-color: #bfbfbf;
  }
  .vxe-cell .vxe-cell--label {
    // ::selection {
    user-select: none;
    // }
  }
}
</style>
