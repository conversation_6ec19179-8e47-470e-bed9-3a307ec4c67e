<!--
 * @Descripttion:措施项目
 * @Author: liuxia
 * @Date: 2023-05-25 17:52:23
 * @LastEditors: wangqiaoxin <EMAIL>
 * @LastEditTime: 2025-06-13 10:51:11
-->
<template>
  <div class="subItem-project custom-tree-table">
    <split
      horizontal
      ratio="5/3"
      :horizontalBottom="35"
      style="height: 100%"
      mode="vertical"
      @onDragHeight="dragHeight"
    >
      <template #one>
        <div class="table-content">
          <s-table
            size="small"
            ref="stableRef"
            class="s-table"
            :columns="showColumns"
            bordered
            :loading="loading"
            :delay="200"
            :rangeSelection="true"
            :scroll="{ y: stableHeight }"
            :row-selection="rowSelection"
            :custom-row="customRow"
            :custom-cell="customCell"
            :custom-header-cell="customHeaderCell"
            :rowClassName="(row, index) => rowClassName(row, index, tableData)"
            :animateRows="false"
            :pagination="false"
            :data-source="tableData"
            @closeEditor="setCloseEditor"
            @openEditor="openEditor"
            @beforeOpenEditor="tableCellClickEvent"
            @mouseup="cellMouseup"
            @cellKeydown="cellKeydown"
            :formatRangeCellText="formatRangeCellText"
          >
            <!-- 自定义头部 -->
            <template #headerCell="{ title, column }">
              <span class="custom-header" style="font-weight: bold">
                <i class="vxe-icon-edit" v-show="column.editable"></i>&nbsp;{{
                  title
                }}
                <CloseOutlined
                  class="icon-close-s"
                  @click="closeColumn({ column })"
                />
              </span>
            </template>
            <!--自定义内容 -->
            <template
              #bodyCell="{
                text,
                record: row,
                index,
                column,
                key,
                openEditor,
                closeEditor,
              }"
            >
              <!-- 序号 -->
              <!-- <div v-if="column.field === 'dispNo'">
                <span>{{ row.dispNo || '‎' }}</span>
              </div> -->
              <!-- 编码 -->
              <div
                class="cell-line-break-el"
                style="text-align: left"
                v-if="column.field === 'deCode'"
              >
                <icon-font
                  v-if="row.isLocked"
                  type="icon-qingdan-suoding"
                ></icon-font>
                <i
                  @click.stop="changeStatus(row)"
                  v-if="row.displaySign === 1 && tableData.length > 1"
                  class="vxe-icon-caret-down"
                ></i>
                <i
                  @click.stop="changeStatus(row)"
                  v-if="row.displaySign === 2"
                  class="vxe-icon-caret-right"
                ></i>
                <span class="code">
                  <a-tooltip>
                    <template #title
                      >{{ row.deCode }}
                      {{ row.redArray?.length > 0 ? row.redArray.join(',') : ''
                      }}{{
                        row.blackArray?.length > 0
                          ? row.blackArray.join(',')
                          : ''
                      }}</template
                    >
                    {{ row.deCode }}
                    {{ row.redArray?.length > 0 ? row.redArray.join(',') : '' }}
                  </a-tooltip>
                </span>
                <span class="code-black" v-if="row.blackArray?.length > 0">{{
                  row.blackArray.join(',')
                }}</span>
              </div>
              <!-- 类型 -->
              <template v-if="column.dataIndex === 'type'">
                <span
                  v-if="
                    (!row.borrowFlag && !row.changeFlag) || row.type === '费'
                  "
                  >{{
                    row.type === '05' || row.type === '06' || row.type === '09'
                      ? deMapFun.rcjMap[row.deResourceKind]
                      : row.type === '04' ||
                        row.type === '07' ||
                        row.type == '10'
                      ? row.displayType
                      : deMapFun.csDeMap[row.type]
                  }}</span
                >
                <span v-if="row.type === '-1'">定</span>
                <span class="code-flag" v-if="row.type !== '费'"
                  >{{ row.changeFlag ? row.changeFlag : '' }}
                </span>
                <span
                  class="code-flag"
                  v-if="row.type !== '费' && !row.changeFlag"
                  >{{ row.borrowFlag ? row.borrowFlag : '' }}
                </span>
              </template>
              <!-- 名称 -->
              <div v-if="column.field == 'deName'">
                <div
                  v-if="row.kind !== '05'"
                  class="note-tips-csxm"
                  v-show="row.annotations"
                  @mouseover="cellMouseEnterEvent({ row, column })"
                  @mouseout="cellMouseLeaveEvent({ row, column })"
                ></div>
                <a-popover
                  v-if="row.annotationsVisible"
                  placement="rightTop"
                  v-model:visible="row.annotationsVisible"
                  trigger="click"
                  overlayClassName="annotations-pop"
                  @visibleChange="val => visibleChange(val, row)"
                  :getPopupContainer="triggerNode => deNameRef(triggerNode)"
                >
                  <div></div>
                  <template #content>
                    <div style="width: 250px; height: 140px">
                      <Annotations
                        @close="v => closeAnnotations(v, row)"
                        @onfocusNode="onFocusNode(row)"
                        :note="row.annotations"
                        style="left: 10px"
                        :isDisabled="row?.noteEditVisible"
                        :ref="el => getAnnotationsRef(el, row)"
                        :type="1"
                      ></Annotations>
                    </div>
                  </template>
                </a-popover>
                <div
                  class="nameEdit"
                  @mouseover="cellMouseEnterEvent({ row, column })"
                  @mouseout="cellMouseLeaveEvent({ row, column })"
                >
                  <pre class="pre-name" v-html="row.deName"></pre>
                  <icon-font
                    v-if="
                      row.isTempRemove !== 1 &&
                      !(row.type == '06' && row.isFyrcj == 0) &&
                      ![11].includes(row.isCostDe)
                    "
                    type="icon-bianji"
                    class="more-icon"
                    v-show="!row.isLocked && row.kind != '00'"
                    @click="
                      () => {
                        if (['主材费', '设备费'].includes(row.type)) {
                          openDEHangZCSB();
                        } else {
                          openEditDialog('deName');
                        }
                      }
                    "
                  ></icon-font>
                </div>
              </div>
              <!-- 单位 -->
              <template v-if="column.dataIndex == 'unit'">
                <div>{{ row.unit }}</div>
              </template>
              <template v-if="column.dataIndex == 'price'">
                <div>
                  {{
                    isChangeAva(row)
                      ? '-'
                      : decimalFormatPriceValue(row.price, row, column)
                  }}
                </div>
              </template>
              <template v-if="column.dataIndex == 'baseJournalPrice'">
                <div>
                  {{
                    isChangeAva(row)
                      ? '-'
                      : decimalFormatPriceValue(
                          row.baseJournalPrice,
                          row,
                          column
                        )
                  }}
                </div>
              </template>
              <!-- 工程量表达式 -->
              <!-- <template v-if="column.field == 'quantityExpression'">
                <div class="nameEdit">
                  <span class="name">{{row.quantityExpression}}</span>
                  <icon-font
                    v-show="row.kind=='04'"
                    type="icon-bianji"
                    class="more-icon"
                    @click="openEditDialog('quantityExpression')"
                  ></icon-font>
                </div>
              </template> -->
              <!-- 工程量 -->
              <template v-if="column.dataIndex == 'originalQuantity'">
                <span>{{ decimalFormatValue(row.quantity, row, column) }}</span>
              </template>
              <template v-if="column.field == 'adjustmentCoefficient'">
                <span v-if="['00', '01', '02', '03'].includes(row.kind)">{{
                  row.adjustmentCoefficient
                }}</span>
                <span v-else></span>
              </template>
              <!-- 组价方式 -->
              <template v-if="column.dataIndex == 'pricingMethod'">
                <span
                  v-if="
                    ['01', '02'].includes(row.kind) &&
                    [1, 2, 3].includes(row.pricingMethod)
                  "
                ></span>

                <a-tooltip v-else>
                  <template #title>{{
                    row.pricingMethod == 1
                      ? '计算公式组价'
                      : row.pricingMethod == 2
                      ? '定额组价'
                      : row.pricingMethod == 3
                      ? '子措施组价'
                      : ''
                  }}</template>
                  {{
                    row.pricingMethod == 1
                      ? '计算公式组价'
                      : row.pricingMethod == 2
                      ? '定额组价'
                      : row.pricingMethod == 3
                      ? '子措施组价'
                      : ''
                  }}
                </a-tooltip>
              </template>
              <!-- 措施类别 -->
              <template v-if="column.field == 'itemCategory'">
                <span>{{
                  row.itemCategory == '空' ? '' : row.itemCategory
                }}</span>
              </template>
              <!-- 取费专业 -->
              <template
                v-if="
                  column.dataIndex == 'costMajorName' &&
                  (['0', '05'].includes(row.type) ||
                    (row.type == '03' && row.pricingMethod != 1))
                "
              >
                <span></span>
              </template>
              <template
                v-if="
                  row.kind === '-1' &&
                  [
                    'originalQuantity',
                    'price',
                    'baseJournalPrice',
                    'totalNumber',
                    'costMajorName',
                  ].includes(column.dataIndex)
                "
              >
                <div></div>
              </template>
              <template
                v-if="
                  [
                    'RDSum',
                    'CDSum',
                    'JDSum',
                    'ZDSum',
                    'SDSum',
                    'RSum',
                    'CSum',
                    'JSum',
                    'ZSum',
                    'SSum',
                    'rate',
                    'rdTotalSum',
                    'cdTotalSum',
                    'jdTotalSum',
                    'zdTotalSum',
                    'sdTotalSum',
                    'rTotalSum',
                    'jTotalSum',
                    'zTotalSum',
                    'cTotalSum',
                    'sTotalSum',
                    'baseJournalTotalNumber',
                    'totalNumber',
                  ].includes(column.dataIndex)
                "
              >
                <span>{{
                  decimalFormatPriceValue(row[column.dataIndex], row, column)
                }}</span>
              </template>
            </template>
            <!--自定义编辑 -->
            <template
              #cellEditor="{
                column,
                modelValue,
                save,
                closeEditor,
                record: row,
                editorRef,
                getPopupContainer,
              }"
            >
              <!-- 名称 -->
              <template v-if="column.field == 'deName'">
                <Annotations
                  @close="v => closeAnnotations(v, row)"
                  @onfocusNode="onFocusNode(row)"
                  v-if="
                    row?.noteViewVisible ||
                    row?.isShowAnnotations ||
                    row?.noteEditVisible
                  "
                  :note="row.annotations"
                  :isDisabled="row?.noteEditVisible || row?.isShowAnnotations"
                  :ref="el => getAnnotationsRef(el, row)"
                ></Annotations>
                <vxe-pulldown
                  ref="bdNamePulldownRef"
                  transfer
                  v-if="['03'].includes(row.kind)"
                >
                  <template #default>
                    <a-textarea
                      className="custom-input"
                      placeholder="请输入名称"
                      :ref="el => setInputRef(el, 'deName')"
                      @compositionstart="onCompositionStart(row)"
                      @compositionend="onCompositionEnd(row)"
                      @change="e => bdNameKeyupEvent(row, modelValue.value)"
                      :rows="1"
                      :bordered="false"
                      :value="modelValue.value"
                      @update:value="
                        v => {
                          modelValue.value = v;
                        }
                      "
                      @blur="
                        () => {
                          timeCLose(
                            closeEditor,
                            { row, column },
                            modelValue.value,
                            row.deName
                          );
                        }
                      "
                    />
                  </template>
                  <template #dropdown>
                    <div class="my-dropdown4">
                      <vxe-grid
                        border
                        auto-resize
                        :show-header="false"
                        height="auto"
                        width="500"
                        :row-config="{ isHover: true }"
                        :data="bdNameTableList"
                        :columns="tableColumn"
                        @cell-click="
                          ({ row }) => {
                            (isNameOpen = true), dbNameCellClickEvent({ row });
                          }
                        "
                      >
                      </vxe-grid>
                    </div>
                  </template>
                </vxe-pulldown>

                <a-textarea
                  v-else
                  className="custom-input"
                  placeholder="请输入名称"
                  :ref="el => setInputRef(el, 'deName')"
                  :rows="1"
                  :bordered="false"
                  :value="modelValue.value"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      timeCLose(
                        closeEditor,
                        { row, column },
                        modelValue.value,
                        row[column.field]
                      );
                    }
                  "
                />
              </template>
              <template v-if="column.dataIndex === 'deCode'">
                <a-input
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  :value="modelValue.value"
                  :get-popup-container="getPopupContainer"
                  :readOnly="
                    row.isTempRemove == 1 ||
                    ['00', '07'].includes(row.kind) ||
                    [11].includes(row.isCostDe)
                  "
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      deCodeTimeCLose(
                        closeEditor,
                        { row, column },
                        modelValue.value,
                        row.deCode
                      );
                    }
                  "
                  @click="cellDBLClickEvent({ row, column })"
                />
                <a-popover
                  overlayClassName="association-popover"
                  placement="bottom"
                  v-model:visible="showRepeat"
                  trigger="click"
                  :autoAdjustOverflow="false"
                >
                  <template #content>
                    <vxe-table
                      border
                      auto-resize
                      v-if="showRepeat"
                      height="auto"
                      :treeConfig="{
                        rowField: 'sequenceNbr',
                        childrenField: 'children',
                        expandAll: true,
                      }"
                      :row-config="{ isHover: true }"
                      :data="codeTable"
                      @cell-click="cellClickEvent"
                    >
                      <vxe-column
                        tree-node
                        title="编码"
                        field="deCode"
                        width="120"
                      ></vxe-column>
                      <vxe-column title="类别" field="type" width="60">
                        <template #default="{ row }">
                          <span
                            v-if="
                              (!row.borrowFlag && !row.changeFlag) ||
                              row.type === '费'
                            "
                            >{{
                              row.type === '05' ||
                              row.type === '06' ||
                              row.type === '09'
                                ? deMapFun.rcjMap[row.deResourceKind]
                                : row.type === '03' || row.type === '04'
                                ? row.displayType
                                : deMapFun.deMap[row.type]
                            }}</span
                          >
                          <span v-if="row.type === '-1'">定</span>
                          <span class="code-flag" v-if="row.type !== '费'"
                            >{{ row.changeFlag ? row.changeFlag : '' }}
                          </span>
                          <span
                            class="code-flag"
                            v-if="row.type !== '费' && !row.changeFlag"
                            >{{ row.borrowFlag ? row.borrowFlag : '' }}
                          </span>
                        </template>
                      </vxe-column>
                      <vxe-column
                        title="名称"
                        field="deName"
                        width="100"
                      ></vxe-column>
                      <vxe-column
                        title="单位"
                        field="unit"
                        width="60"
                      ></vxe-column>
                      <vxe-column
                        v-if="projectStore.taxMade == 1"
                        title="单价"
                        field="baseJournalPrice"
                        width="100"
                      >
                        <template #default="{ row }">
                          <span
                            >{{
                              decimalFormatPriceValue(
                                row.baseJournalPrice,
                                row,
                                { dataIndex: 'baseJournalPrice' }
                              )
                            }}
                          </span>
                        </template>
                      </vxe-column>
                      <vxe-column v-else title="单价" field="price" width="100">
                        <template #default="{ row }">
                          <span
                            >{{
                              decimalFormatPriceValue(row.price, row, {
                                dataIndex: 'price',
                              })
                            }}
                          </span>
                        </template>
                      </vxe-column>
                    </vxe-table>
                  </template>
                </a-popover>
              </template>
              <!-- 类型 -->
              <template v-if="column.field === 'type'">
                <a-select
                  :value="row.deResourceKind"
                  :options="typeList"
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  :field-names="{ label: 'name', value: 'value' }"
                  size="small"
                  open
                  @blur="closeEditor()"
                  className="custom--inner"
                  transfer
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.deResourceKind
                      );
                      closeEditor();
                    }
                  "
                >
                </a-select>
              </template>
              <!-- 单位 -->
              <template v-if="column.field == 'unit'">
                <a-select
                  size="small"
                  :bordered="false"
                  show-search
                  :notFoundContent="null"
                  :ref="el => setInputRef(el, 'unit')"
                  :showArrow="false"
                  :value="modelValue.value"
                  :get-popup-container="
                    getTableScrollContainer || getPopupContainer
                  "
                  :filter-option="
                    input => {
                      modelValue.value = input;
                    }
                  "
                  :options="
                    projectStore.unitListString.split(',').map(a => {
                      return { value: a, label: a };
                    })
                  "
                  open
                  @blur="
                    editClosedEvent(
                      { row, column },
                      modelValue.value,
                      row.unit
                    ),
                      closeEditor()
                  "
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.unit
                      );
                      closeEditor();
                    }
                  "
                ></a-select>
              </template>
              <!-- 调整系数 -->
              <template v-if="column.field == 'adjustmentCoefficient'">
                <a-input
                  :bordered="false"
                  :maxlength="100"
                  :ref="el => setInputRef(el, column.field)"
                  :value="modelValue.value"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = (v + '').replace(/[^\-0-9.]/g, '');
                    }
                  "
                  @blur="
                    v => {
                      modelValue.value = formatNumberOnBlur(v.target.value);
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row[column.field]
                      );
                      closeEditor();
                    }
                  "
                />
              </template>
              <!-- 工程量表达式 -->
              <template v-if="column.field == 'quantityExpression'">
                <a-input
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  :value="modelValue.value"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      timeCLoselQuantity(
                        closeEditor,
                        { row, column },
                        modelValue.value,
                        row.quantityExpression
                      );
                      // editClosedEvent(
                      //   { row, column },
                      //   modelValue.value,
                      //   row.quantityExpression
                      // );
                      // closeEditor();
                    }
                  "
                />
                <div class="rateEdit">
                  <icon-font
                    @click.stop="quantityEdit('quantityExpression')"
                    type="icon-bianji"
                    class="more-icon"
                    v-show="!['05', '07'].includes(row.kind)"
                  ></icon-font>
                </div>
              </template>
              <!-- 规格型号 -->
              <template v-if="column.field === 'specification'">
                <a-input
                  :bordered="false"
                  v-if="isSpecificationEdit(row)"
                  :ref="el => setInputRef(el, column.field)"
                  :value="modelValue.value"
                  :maxlength="50"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row[column.field]
                      );
                      closeEditor();
                    }
                  "
                />
                <span v-else>{{ row.specification }}</span>
              </template>
              <!-- 工程量 -->
              <template v-if="column.field == 'originalQuantity'">
                <a-input
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  :value="modelValue.value"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      if (row.type == '05') {
                        modelValue.value = (v.match(/\d{0,8}(\.\d*)?/) || [
                          '',
                        ])[0];
                      } else {
                        modelValue.value = v;
                      }
                    }
                  "
                  @blur="
                    () => {
                      timeCLoselQuantity(
                        closeEditor,
                        { row, column },
                        modelValue.value,
                        row.originalQuantity
                      );
                    }
                  "
                />
                <div class="rateEdit">
                  <icon-font
                    @click.stop="quantityEdit('originalQuantity')"
                    type="icon-bianji"
                    class="more-icon"
                    v-show="!['05', '07'].includes(row.kind)"
                  ></icon-font>
                </div>
              </template>
              <!-- 计算基数 -->
              <template v-if="column.field == 'calculateBase'">
                <a-input
                  :value="modelValue.value"
                  :ref="el => setInputRef(el, column.field)"
                  :bordered="false"
                  :controls="false"
                  :get-popup-container="getPopupContainer"
                  :maxlength="100"
                  @update:value="
                    v => {
                      modelValue.value = v.replace(/[^\w\-\+\*\/\d.]/g, '');
                    }
                  "
                  @blur="
                    () => {
                      calbaseQuantity(
                        closeEditor,
                        { row, column },
                        modelValue.value,
                        row[column.field]
                      );
                    }
                  "
                />
                <div class="rateEdit">
                  <icon-font
                    @click.stop="calbaseModel = true"
                    type="icon-bianji"
                    class="more-icon"
                  ></icon-font>
                </div>
              </template>
              <!-- 组价方式 -->
              <template v-if="column.field == 'pricingMethod'">
                <a-select
                  :value="row.pricingMethod"
                  :options="
                    row.customParent.kind == '03'
                      ? pricingMethodList2
                      : pricingMethodList
                  "
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  size="small"
                  open
                  @blur="closeEditor()"
                  className="custom--inner"
                  transfer
                  @update:value="
                    v => {
                      modelValue.value = v;
                      changePricingMethod(
                        closeEditor,
                        { row, column },
                        modelValue.value,
                        row.pricingMethod
                      );
                    }
                  "
                >
                </a-select>
              </template>
              <!-- 费率 -->
              <template v-if="column.field == 'rate'">
                <a-input
                  :value="modelValue.value"
                  :ref="el => setInputRef(el, column.field)"
                  :bordered="false"
                  :controls="false"
                  :get-popup-container="getPopupContainer"
                  @focus="handleFocus(modelValue, row, column.field)"
                  :maxlength="100"
                  @update:value="
                    v => {
                      modelValue.value = (v + '').replace(/[^\d.]/g, '');
                    }
                  "
                  @blur="
                    v => {
                      modelValue.value = pureNumber(v.target.value, 100);
                      timeCLoselQuantity(
                        closeEditor,
                        { row, column },
                        modelValue.value,
                        row[column.field],
                        200,
                        modelValue
                      );
                    }
                  "
                />
                <div class="rateEdit">
                  <icon-font
                    @click.stop="comModel = true"
                    type="icon-bianji"
                    class="more-icon"
                  ></icon-font>
                </div>
              </template>
              <!-- 单价 -->
              <template
                v-if="
                  column.field == 'price' ||
                  column.field == 'baseJournalPrice' ||
                  column.field == 'RDSum' ||
                  column.field == 'CDSum' ||
                  column.field == 'JDSum' ||
                  column.field == 'ZDSum' ||
                  column.field == 'SDSum' ||
                  column.field == 'RSum' ||
                  column.field == 'CSum' ||
                  column.field == 'JSum' ||
                  column.field == 'ZSum' ||
                  column.field == 'SSum'
                "
              >
                <a-input
                  :bordered="false"
                  :maxlength="100"
                  :ref="el => setInputRef(el, column.field)"
                  :value="modelValue.value"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = v === '' ? '' : v;
                    }
                  "
                  @blur="
                    v => {
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row[column.field]
                      );
                      closeEditor();
                    }
                  "
                />
              </template>
              <!-- 单价构成文件 -->
              <template v-if="column.field == 'qfCode'">
                <a-select
                  :value="row.qfCode"
                  :options="djgcFileList"
                  :field-names="{ label: 'qfName', value: 'qfCode' }"
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  size="small"
                  open
                  @blur="closeEditor()"
                  className="custom--inner"
                  transfer
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.qfCode
                      );
                      closeEditor();
                    }
                  "
                >
                </a-select>
              </template>
              <!-- 措施类别 -->
              <template v-if="column.field == 'itemCategory'">
                <a-select
                  v-if="row.kind === '03'"
                  :value="row.itemCategory"
                  :options="measureList"
                  :field-names="{ label: 'label', value: 'value' }"
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  size="small"
                  open
                  @blur="closeEditor()"
                  className="custom--inner"
                  transfer
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.itemCategory
                      );
                      closeEditor();
                    }
                  "
                >
                </a-select>
              </template>
              <!-- 取费专业 -->
              <template v-if="column.field == 'costMajorName'">
                <a-select
                  :value="row.costFileCode"
                  :options="feeFileList"
                  :field-names="{ label: 'qfName', value: 'qfCode' }"
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  size="small"
                  open
                  @blur="closeEditor()"
                  className="custom--inner"
                  transfer
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.costFileCode
                      );
                      closeEditor();
                    }
                  "
                >
                </a-select>
              </template>
              <!-- 施工组织措施类别 -->
              <template v-if="column.field == 'measureType'">
                <a-select
                  :value="row.measureType"
                  :options="csTypeList"
                  :field-names="{ label: 'cslbName', value: 'cslbName' }"
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  size="small"
                  open
                  @blur="closeEditor()"
                  className="custom--inner"
                  transfer
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row.measureType
                      );
                      closeEditor();
                    }
                  "
                >
                </a-select>
              </template>

              <!-- 备注 -->
              <template v-if="column.field == 'description'">
                <a-textarea
                  :maxlength="2000"
                  :clearable="false"
                  :auto-size="{ minRows: 1 }"
                  @focus="projectAttrFocus(row)"
                  @change="projectAttrChange(row)"
                  :ref="el => setInputRef(el, column.field)"
                  :bordered="false"
                  :value="modelValue.value"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      modelValue.value = v;
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row[column.field]
                      );
                      closeEditor();
                    }
                  "
                ></a-textarea>
              </template>
              <!-- 最高限价 -->
              <template v-if="column.field == 'ceilingPrice'">
                <a-input
                  v-if="
                    row.kind === '01' || row.kind === '02' || row.kind === '03'
                  "
                  :value="modelValue.value"
                  :ref="el => setInputRef(el, column.field)"
                  class="ceilingPrice-wrap"
                  :bordered="false"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = (v + '').replace(/[^\d.]/g, '');
                    }
                  "
                  @blur="
                    v => {
                      modelValue.value = pureNumber(v.target.value, 2);
                      editClosedEvent(
                        { row, column },
                        modelValue.value,
                        row[column.field]
                      );
                      closeEditor();
                    }
                  "
                />
                <span v-else>
                  {{ row.ceilingPrice }}
                </span>
              </template>
            </template>
            <!-- 右键菜单 -->
            <template #contextmenuPopup="args">
              <sub-menu
                :args="args"
                :copyData="copyData"
                v-if="args.record.awfType !== 2"
                type="csxm"
                :tableData="tableData"
                :hangMenuList="hangMenuList"
                :deleteStateFn="deleteStateFn"
                @handleNote="handleNote"
                @handleMainList="handleMainList"
                @hangMenuDisabledHandler="hangMenuDisabledHandler"
                @contextMenuClickEvent="
                  ({ menu, row }) => contextMenuClickEvent({ menu, row }, args)
                "
                v-model:currentInfo="currentInfo"
              ></sub-menu>
            </template>
          </s-table>
        </div>
      </template>
      <template #two>
        <div class="quota-content">
          <quota-info
            :tableDataList="tableData"
            :currentInfo="currentInfo"
            :isAttrContent="isAttrContent"
            :type="2"
            :fatherLoading="tableLoading"
            :isUpdateFile="isUpdateFile"
            :isUpdateQuantities="isUpdateQuantities"
            @refreshCurrentInfo="refreshCurrentInfo"
            @tabClickBefore="showInfo"
            @onDbClickFile="onDbClickFile"
            :isComplete="isComplete"
            :isUpdate="isUpdate"
            ref="quotaInfoRef"
          ></quota-info>
        </div>
      </template>
    </split>
    <!-- 定额索引弹窗 -->
    <inventory-and-quota-index
      v-model:indexVisible="indexVisible"
      @currentQdDeInfo="currentQdDeInfo"
      @currentInfoReplace="currentInfoReplace"
      :dataType="dataType"
      :indexLoading="indexLoading"
      :originInfo="currentInfo"
      :isCsxm="true"
    ></inventory-and-quota-index>
    <common-modal
      v-model:modelValue="deleteVisible"
      className="dialog-comm"
      title="删除操作"
      width="400px"
    >
      <div class="content" v-if="existRelationDe">
        删除主子目时将删除对应关联子目，是否继续
      </div>
      <div v-else>
        <div class="content" v-if="deleteList.length === 2">
          执行本操作将会删除本项及其下所有数据和关联关系
        </div>
        <div class="content" v-if="deleteList[0] === 5">
          是否确定删除当前措施项及其下所有数据？
        </div>
        <div
          class="content"
          v-if="deleteList.length === 1 && deleteList[0] === 4"
        >
          是否确定删除？
        </div>
      </div>
      <div class="footer-btn-list">
        <div class="content" v-if="isBatchDelete && !existRelationDe">
          是否确定删除选中所有数据？
        </div>
        <a-button type="primary" ghost @click="cancel">取消</a-button>
        <a-button
          v-if="existRelationDe"
          type="primary"
          @click="delFbData('deleteRelation')"
          :disabled="deleteLoading"
          >确定</a-button
        >
        <a-button
          v-else
          type="primary"
          @click="delFbData(true)"
          :disabled="deleteLoading"
          >确定</a-button
        >
      </div>
    </common-modal>
    <common-modal
      v-model:modelValue="isPriceModel"
      className="dialog-comm"
      :title="showPriceTitle"
      :width="
        showModelType === 'csfy'
          ? '1000'
          : showModelType === 'azfy'
          ? 1200
          : 800
      "
      height="auto"
      @close="closePriceModel"
      :destroy-on-close="true"
    >
      <keep-alive>
        <component
          :is="components.get(showModelType)"
          path="csxm"
          @updateData="updateData"
          @close="closePriceModel"
        ></component>
      </keep-alive>
      <!-- <zscy-content
        v-if="showModelType === 'zscy'"
        @updateData="updateData"
      ></zscy-content>
      <zscg-content v-if="showModelType === 'zscg'"></zscg-content>
      <azfy-content v-if="showModelType === 'azfy'"></azfy-content>
      <csfy-content
        v-if="showModelType === 'csfy'"
        @updateData="updateData"
      ></csfy-content> -->
    </common-modal>
    <common-modal
      v-model:modelValue="isShowModel"
      className="dialog-comm"
      :title="showModelTitle"
      width="700"
      height="350"
    >
      <a-textarea
        v-model:value="editContent"
        placeholder="请输入编辑内容"
        :rows="8"
        class="edit-content"
      />
      <div class="footer-btn-list">
        <a-button @click="editCancel">取消</a-button>
        <a-button type="primary" @click="saveContent()">确定</a-button>
      </div>
    </common-modal>
    <info-modal
      v-model:infoVisible="infoVisible"
      :infoText="infoText"
      :isSureModal="isSureModal"
      :iconType="iconType"
      @updateCurrentInfo="updateCurrentInfo"
    ></info-modal>
    <bcQd
      v-model:qdVisible="qdVisible"
      :code="bdCode"
      :type="2"
      :currentInfo="currentInfo"
      @saveData="saveData"
      @bcCancel="bcCancel"
    ></bcQd>
    <bcDe
      v-model:visible="deVisible"
      :code="bdCode"
      :type="2"
      :currentInfo="currentInfo"
      @deSaveData="deSaveData"
      @bcCancel="bcCancel"
    ></bcDe>
    <bcRcj
      :code="bdCode"
      v-model:visible="rcjVisible"
      @rcjSaveData="rcjSaveData"
      @bcCancel="bcCancel"
    ></bcRcj>

    <div class="unit-radio" v-if="showUnitTooltip">
      <div class="title">选择单位</div>
      <a-radio-group v-model:value="selectUnit">
        <a-radio
          v-for="(unit, index) in addCurrentInfo?.unit"
          :key="index"
          :value="unit"
        >
          {{ unit }}
        </a-radio>
      </a-radio-group>
      <a-button type="link" @click="selectHandler(addCurrentInfo)"
        >确定</a-button
      >
    </div>
    <set-standard-type
      v-model:standardVisible="standardVisible"
      :type="2"
      :indexLoading="indexLoading"
      :currentInfo="addDeInfo"
      @refreshCurrentInfo="refreshCurrentInfo"
      @closeDialog="closeDialog"
    ></set-standard-type>
    <!-- 主材 -->
    <set-main-material
      v-model:materialVisible="materialVisible"
      :indexLoading="indexLoading"
      :currentInfo="addDeInfo"
      :mainMaterialTableData="mainMaterialTableData"
      @setUpdate="setUpdate"
      :deOrRcj="deOrRcj"
      :materialRow="materialRow"
      :materialType="materialType"
      :key="addDeInfo?.sequenceNbr"
    >
    </set-main-material>
    <common-modal
      v-model:modelValue="comMatchModal"
      className="dialog-comm"
      title="组价方案匹配"
      width="600"
      height="400"
    >
      <component-matching @closeComMatch="closeComMatch"></component-matching>
    </common-modal>
    <CodeReset
      v-model:visible="codeResetVisible"
      @refreshCallback="
        () => {
          page = 1;
          queryBranchDataById();
        }
      "
    ></CodeReset>
    <schedule-file
      v-model:dialogVisible="showSchedule"
      strokeColor="#54a1f3"
      :percent="percent"
      :desc="percentInfo?.dec"
      :pageType="'comMatch'"
      @isContinue="isContinue"
      :isNoClose="isNoClose"
      :percentInfo="percentInfo"
      :width="600"
    ></schedule-file>
    <common-modal
      className="titleNoColor noHeaderHasclose"
      v-model:modelValue="resetModal"
      title=" "
      width="400"
      height="200"
    >
      <div class="reCheck">
        <p style="font-weight: 600">
          <icon-font
            class="icon"
            type="icon-querenshanchu"
            style="margin-right: 5px"
          />组价进行中，是否确定关闭？
        </p>
        <p style="padding-left: 20px; margin-bottom: 26px">
          当前数据已发生变化是否应用组价后数据
        </p>

        <a-button style="margin: 0 30px 0 20px" @click="recover(true)"
          >否，恢复至组价前数据
        </a-button>
        <a-button type="primary" @click="recover(false)"> 确定</a-button>
      </div>
    </common-modal>
    <common-modal
      className="dialog-comm"
      title="组价方案匹配"
      width="550"
      height="400
    "
      v-model:modelValue="reportModel"
      @cancel="reportModel = false"
      @close="reportModel = false"
    >
      <match-pic
        @lookView="lookView"
        :startMatchData="startMatchData"
      ></match-pic>
    </common-modal>
    <!--    <combined-search-->
    <!--      @filterData="filterData"-->
    <!--      :id="'combined-search'"-->
    <!--    ></combined-search>-->
  </div>

  <!-- 复用组价 -->
  <ReuseGroupPriceDialog
    ref="ReuseGroupPriceRef"
    @refresh="queryBranchDataById('Refresh', '', false)"
    type="csxm"
    :currentInfo="currentInfo"
    :lockBtnStatus="lockBtnStatus"
  ></ReuseGroupPriceDialog>

  <!-- 清单快速组价 -->
  <qdQuickPricing
    ref="qdQuickPricingRef"
    @refresh="queryBranchDataById('Refresh', '', false)"
    @posRow="posRow"
    :currentInfo="currentInfo"
    :lockBtnStatus="lockBtnStatus"
  ></qdQuickPricing>

  <glj-batch-delete
    v-model:batchDeleteVisible="batchDeleteVisible"
    :batchDataType="batchDataType"
    @updateData="queryBranchDataById"
    :type="'csxm'"
  ></glj-batch-delete>
  <PageColumnSetting
    :columnOptions="handlerColumns"
    ref="columnSettingRef"
    title="页面显示列设置"
    @save="updateColumns"
    :getDefaultColumns="getDefaultColumns"
  />

  <!-- 局部汇总 -->
  <gljPartialSummary
    ref="gljPartialSummaryRef"
    :type="'csxm'"
    @closeDialog="gljPartialSummaryStatus = false"
    v-if="gljPartialSummaryStatus"
  ></gljPartialSummary>

  <areaModal
    v-if="areaStatus"
    :type="areaVisibleType"
    @closeDialog="closeAreaModal"
  ></areaModal>
  <DEHangZCSB
    v-model:visible="DEHangZCSBVisible"
    :currentInfo="currentInfo"
    @updateData="
      rcjParams => {
        if ([94, 95].includes(currentInfo.kind)) {
          updateConstructRcj(currentInfo, null, rcjParams);
        } else {
          itemUpdate(currentInfo, null, rcjParams);
        }
      }
    "
  ></DEHangZCSB>
  <syncNameToDE
    v-model:visible="syncToDEVisible"
    :currentInfo="currentInfo"
    @updateData="itemUpdate"
  ></syncNameToDE>
  <material-machine-index
    v-model:indexVisible="rcjIndexVisible"
    :currentMaterialInfo="currentInfo"
    :indexLoading="rcjIndexLoading"
    @addChildrenRcjData="
      row => {
        addChildrenRcjData(row, currentInfo);
      }
    "
    @currentInfoReplace="
      row => {
        retailAreaRcjReplace(row, currentInfo);
      }
    "
  ></material-machine-index>
  <!-- <AssociateSubQuotas
    v-model:visible="associateSubQuotasVisible"
    :currentInfo="addDeInfo"
    @successHandler="successHandler"
    :type="2"
    @quotasCancel="quotasCancel"
  ></AssociateSubQuotas> -->
  <!-- 费率信息 -->
  <common-modal
    className="dialog-comm noMask"
    title="费率信息"
    v-model:modelValue="comModel"
    @cancel="cancel"
    @close="comModelCLose"
    :mask="false"
    style="position: releative"
  >
    <SearchFee ref="comArea" @onUse="comModelsureData"></SearchFee>
  </common-modal>
  <!-- 计算基数 -->
  <common-modal
    className="dialog-comm noMask"
    title="计算基数编辑"
    width="1200"
    height="450"
    v-model:modelValue="calbaseModel"
    @cancel="cancel"
    @close="calbaseModel = false"
    :mask="false"
    style="position: releative"
    destroy-on-close
  >
    <calbaseEdit
      :isTextArea="true"
      :calbaseModel="calbaseModel"
      :textValue="currentInfo"
      ref="calbaseRef"
    ></calbaseEdit>
    <span class="btns">
      <a-button @click="calbaseCancelData()">取消</a-button>
      <a-button type="primary" @click="calbaseSureData()">确定</a-button>
    </span>
  </common-modal>
  <!-- 查找 -->
  <lookFilterGlj
    v-model:lookupVisible="lookupVisible"
    v-model:lookupConfig="lookupConfig"
    @lookupCallback="lookupCallback"
    @changeCurrentInfo="changeCurrentInfo"
    @updateData="queryBranchDataById"
    :type="'csxm'"
  />
  <!-- 过滤 -->
  <FiltErate
    v-if="filterateInit"
    ref="filtErateRef"
    v-model:filterateVisible="filterateVisible"
    @updateData="queryBranchDataById"
    @queryBack="filtQuery"
    @changeCurrentInfo="changeCurrentInfo"
    :type="'csxm'"
    @filterOperate="filterOperate"
  />
  <!-- 子目关联弹窗 -->
  <gljAssociateSubQuotas
    :zmAssociateData="zmAssociateData"
    v-model:visible="associateSubQuotasVisible"
    :currentInfo="currentInfo"
    @successHandler="successHandler"
    @insertZmDe="insertZmDe"
    @updateZmData="updateZmData"
    :type="2"
    @quotasCancel="quotasCancel"
  ></gljAssociateSubQuotas>
  <!-- 工程量，工程量表达式编辑 -->
  <common-modal
    className="dialog-comm noMask"
    :title="quantityEditModalTitle"
    width="800"
    height="450"
    v-model:modelValue="quantityEditModalVisible"
    @cancel="cancel"
    @close="quantityEditModelCLose"
    :mask="false"
    style="position: releative"
  >
    <IntroductionQuantity
      :textValue="quantityEditModalTextValue"
      :visible="quantityEditModalVisible"
      ref="quantityArea"
    ></IntroductionQuantity>
    <span class="btns">
      <a-button @click="quantityEditModelCLose()">取消</a-button>
      <a-button type="primary" @click="quantityEditModelsureData()"
        >确定</a-button
      >
    </span>
  </common-modal>
  <civilConstructionJq
    :activeKind="activeKind"
    v-model:visible="isFxtjfy"
    @refreshTableList="queryBranchDataById"
  ></civilConstructionJq>
  <price-model
    v-model:visible="priceVisible"
    :currentInfo="currentInfo"
    @updateData="queryBranchDataById"
    :type="'csxm'"
  ></price-model>
</template>

<script setup>
import {
  onActivated,
  reactive,
  inject,
  ref,
  watch,
  nextTick,
  onMounted,
  onBeforeUnmount,
  getCurrentInstance,
  markRaw,
  onDeactivated,
  defineAsyncComponent,
  toRaw,
  watchEffect,
  provide,
  computed,
} from 'vue';
import QuotaInfo from '../quotaInfo/index.vue';
import { useFormatTableColumns } from '@gongLiaoJi/hooks/useGljFormatTableColumns.js';
import {
  quantityExpressionHandler,
  pureNumber,
  everyNumericHandler,
} from '@/utils/index';
import InventoryAndQuotaIndex from '../inventoryAndQuotaIndex/index.vue';
import api from '@gongLiaoJi/api/projectDetail.js';
import { checkisOnline } from '@/utils/publicInterface';
import ProjectAttrAssociation from '@/components/ProjectAttrAssociation/ProjectAttrAssociation.vue';
import ComponentMatching from './componentMatching.vue';
import MatchPic from './MatchPic.vue';
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import xeUtils from 'xe-utils';
import infoMode from '@/plugins/infoMode.js';
import SetStandardType from '../quotaInfo/setStandardType.vue';
import SetMainMaterial from '../quotaInfo/setMainMaterial.vue';
import { useCheckBefore } from '@gongLiaoJi/hooks/useGljCheckBefore';
// const { checkUnit, showInfo, isComplete } = useCheckBefore();
const { showInfo, isComplete } = useCheckBefore();
import { useCellClick } from '@gongLiaoJi/hooks/useGljCellClick';
import { useReversePosition } from '@gongLiaoJi/hooks/useReversePosition.js';
import { useAttrAssociation } from '@gongLiaoJi/hooks/useAttrAssociationStable.js';
import {
  useSubItem,
  decimalLimitationPathArray,
} from '@gongLiaoJi/hooks/useSubItemStable.js';
import { useRoute } from 'vue-router';
import deMapFun from '../deMap';
import VxeTable from '@/plugins/table/index.js';
import qdQuickPricing from '@/components/qdQuickPricing/index.vue';
import bdNameSelect from '@/components/bdNameSelect/index.vue';
import Annotations from '@/components/Annotations/index.vue';
import areaModal from '@/components/areaModal/index.vue';
import { setGlobalLoading } from '@gongLiaoJi/hooks/publicGljApiData';
import getTableColumns from './tableColumns';
import gljBatchDelete from '@/components/batchDelete/gljIndex.vue';
import SearchFee from '@gongLiaoJi/views/projectDetail/customize/FeeWithDrawalTable/SearchFee.vue';
import gljPartialSummary from '@/components/gljPartialSummary/index.vue';
import {
  customCell,
  rowClassName,
  customHeaderCell,
} from './classAndStyleMethod';
import subMenu from './sub-menu.vue';
import { stableHook } from './stableHook.js';
import { useConstructRcj } from '@/hooks/useConstructRcj';
import { hangZCSB } from '../subItemProject/hangZCSB.js';
import DEHangZCSB from '../subItemProject/components/DEHangZCSB.vue';
import syncNameToDE from '../subItemProject/components/syncNameToDE.vue';
import MaterialMachineIndex from '../materialMachineIndex/index.vue';
import { onClickOutside } from '@vueuse/core';
import calbaseEdit from './calbaseEdit.vue';
import IntroductionQuantity from '../subItemProject/components/IntroductionQuantity.vue';
import civilConstructionJq from '../measuresItem/civilConstructionJq.vue';
import priceModel from '../subItemProject/components/priceModel.vue';
import { recordProjectData } from '@gongLiaoJi/hooks/recordProjectOperat.js';
import { formatNumberOnBlur } from '@/gongLiaoJiProject/utils/index.js';
let {
  // gljCheckTab,
  updateGljSelrowId,
} = recordProjectData();

const CodeReset = defineAsyncComponent(() =>
  import('../subItemProject/components/CodeReset.vue')
);

const vLineBreak = el => {
  function unitTransform(unit) {
    return Number(unit.slice(0, -2));
  }
  function setLineClamp() {
    const { height, lineHeight, paddingTop } = window.getComputedStyle(
      el.closest('td'),
      null
    );
    const line = Math.floor(
      (unitTransform(height) - unitTransform(paddingTop) * 2) /
        unitTransform(lineHeight)
    );
    el.style['-webkit-line-clamp'] = line;
  }
  setLineClamp();
};
const route = useRoute();
const typeList = ref([
  {
    name: '材料费',
    value: 2,
  },
  {
    name: '主材费',
    value: 5,
  },
  {
    name: '设备费',
    value: 4,
  },
]);
const { dataSearchPosition } = useReversePosition();
const ReuseGroupPriceDialog = defineAsyncComponent(() =>
  import('@/components/ReuseGroupPriceDialog/index.vue')
);
const {
  useCellClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  isSelectedCell,
  resetCellData,
} = useCellClick();

const components = markRaw(new Map());
components.set(
  'zscy',
  defineAsyncComponent(() => import('./zscyContent.vue'))
);
components.set(
  'zscg',
  defineAsyncComponent(() => import('./zscgContent.vue'))
);
components.set(
  'csfy',
  defineAsyncComponent(() => import('./csfyContent.vue'))
);
components.set(
  'azfy',
  defineAsyncComponent(() => import('./azfyContent.vue'))
);
import frameSelect from '@/components/frameSelect/index.vue';
import zscyContent from './zscyContent.vue';
import zscgContent from './zscgContent.vue';
import csfyContent from './csfyContent.vue';

import { insetBus } from '@/hooks/insetBus';
import operateList from '../operate';
import split from '@/components/split/index.vue';
import { insetBus as insetBusGlj } from '@gongLiaoJi/hooks/insetBus';
import { useDecimalPoint } from '@gongLiaoJi/hooks/useDecimalPoint.js';
const { decimalFormat } = useDecimalPoint();

const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;

const emits = defineEmits(['getCurrentInfo', 'updateMenuList']);
let comModel = ref(false);
let calbaseModel = ref(false);
const calbaseRef = ref();
const stableHeight = ref(400);
const stableRef = ref();
const quotaInfoRef = ref();
let frameSelectRef = ref();
let vexTable = ref();
const projectStore = projectDetailStore();
let contextmenuList = ref([]);
let measureList = ref([]);
let dataType = ref('03');
let isAttrContent = ref(false);
let unitList = ref([]);
let deleteVisible = ref(false);
let filterateVisible = ref(false);
const activeKind = ref(0);
const isFxtjfy = ref(false);
let menuList = ref([
  {
    type: 0,
    name: '添加标题',
    kind: '01',
    isValid: false,
  },
  {
    type: 1,
    name: '添加子项',
    kind: '02',
    isValid: false,
  },
  {
    type: 2,
    name: '添加措施项',
    kind: '03',
    isValid: false,
  },
  {
    type: 3,
    name: '添加定额',
    kind: '04',
    isValid: false,
  },
]);
let bcMenuList = ref([
  // {
  //   type: 2,
  //   name: '补充清单',
  //   kind: '03',
  //   isValid: false,
  // },
  {
    type: 3,
    name: '补充定额',
    kind: '04',
    isValid: false,
  },
  {
    type: 3,
    name: '补充人材机',
    kind: '05',
    isValid: false,
  },
]);
let deleteList = ref([]);
let page = ref(1);
let limit = ref(300000);
let scrollSwitch = ref(false);
let loading = ref(false);
let deleteLoading = ref(false);
let showModelType = ref('');
let isPriceModel = ref(false);
let showPriceTitle = ref('');
let isEditEnabled = ref(true); // 是否可编辑

const pulldownRef = ref(); // 编码推荐数据ref
const pulldownRefAttr = ref(null); // 项目特征数据ref;
let indexLoading = ref(false); // 索引页面loading
const csTypeList = ref([]); //施工组织措施类别下拉列表
const existRelationDe = ref(false);
const existRelationDe2 = ref(false);
const currentInsertRow = ref(null);
const currentInsertIsRcj = ref('');
const currentMaterial = ref(null);
const deOrRcj = ref(1);
const addDeOrQdFn = ref(() => {});

const bcQd = defineAsyncComponent(() =>
  import('../subItemProject/components/bcQd.vue')
);
const bcDe = defineAsyncComponent(() =>
  import('../subItemProject/components/bcDe.vue')
);
const bcRcj = defineAsyncComponent(() =>
  import('../subItemProject/components/bcRcj.vue')
);
const isFilter = ref(false);
let filterateBtn = operateList.value.find(item => item.name == 'filterate'); //过滤按钮
watchEffect(() => {
  filterateBtn['activation'] = isFilter.value;
});

// 监听如果左侧树层级修改，则清空过滤条件
let filterateInit = ref(true);
watch(
  () => projectStore.currentTreeInfo.type,
  () => {
    if (projectStore.tabSelectName == '措施项目') {
      isFilter.value = false;
      filterateInit.value = false;
      nextTick(() => {
        filterateInit.value = true;
      });
    }
  },
  { immediate: true, deep: true }
);

let copyData = ref(null);
const selectState = reactive({
  selectedRowKeys: [],
  selectedRows: [],
});
let isBatchDelete = ref(false); // 是否批量删除
let standardData = ref([]); // 定额数据下挂的标准换算数据,如若为空,则不展示设置标准换算弹框
// let mainMaterialTableData = ref([]); // 定额数据下挂的主材数据,如若为空,则不展示设置主材价格弹框
let isUpdate = ref(false); // 修改主材市场价刷新人材机明细数据

const checkList = ref([]); // 组价方案匹配筛选选中值
watch(
  () => projectStore.currentTreeInfo,
  () => {
    if (
      projectStore.tabSelectName === '措施项目' &&
      projectStore.currentTreeInfo.type === 3
    ) {
      page.value = 1;
      initColumns({
        columns: getTableColumns(emits, 'csxm'),
        pageName: 'csxm',
      });
      if (!projectStore.isAutoPosition) {
        // 不是自动定位的才调用接口
        queryBranchDataById('other');
      }
    }
  }
);
watch(
  () => projectStore.setOption.isScj,
  () => {
    if (projectStore.tabSelectName === '措施项目') {
      initColumns({
        columns: getTableColumns(emits, 'csxm'),
        pageName: 'csxm',
      });
      page.value = 1;
      queryBranchDataById('other');
    }
  }
);
watch(
  () => projectStore.positionId,
  () => {
    console.log('措施项目定位', projectStore.positionId);
    if (
      projectStore.tabSelectName === '措施项目' &&
      projectStore.positionId &&
      projectStore.currentTreeInfo.levelType === 3
    ) {
      // currentInfo.value = { sequenceNbr: projectStore.positionId };在queryBranchDataById会给currentInfo.value赋值，此处不赋值，不然会出现12518bug
      queryBranchDataById('other', projectStore.positionId);
    }
  }
);
watch(
  () => projectStore.combinedSearchList,
  () => {
    if (
      projectStore.tabSelectName === '措施项目' &&
      projectStore.combinedSearchList &&
      projectStore.currentTreeInfo.levelType === 3
    ) {
      filterData(projectStore.combinedSearchList);
    }
  }
);
const codeTable = ref([]);
const showRepeat = ref();
const repeatCode = ref({
  deRowId: '',
  deStandardId: '',
  row: {},
});
const isClickRepeat = ref(false);
watch(
  () => showRepeat.value,
  val => {
    if (!val) {
      if (isClickRepeat.value) {
        console.log('点击重复弹窗关闭');
        isClickRepeat.value = false;
        selectRepeatDe();
      } else {
        console.log('不选择数据重复弹窗关闭');
        stableRef.value.closeEditor();
      }
    }
  }
);

const getTableScrollContainer = () => {
  return (
    stableRef.value?.$el?.querySelector('.surely-table-body-container') ||
    document.body
  );
};
const decimalFormatValue = (value, row, column) => {
  let path = '';
  if (column.dataIndex === 'originalQuantity') {
    if (row.type === '05') {
      path = 'EDIT_DEXZS_QUANTITY_PATH';
    } else if (row.type === '06' || row.type === '09') {
      path = 'EDIT_DERCJ_QUANTITY_PATH';
    } else {
      path = 'EDIT_DE_QUANTITY_PATH';
    }
  }
  return decimalFormat(value, path);
};
const decimalFormatPriceValue = (value, row, column) => {
  const array = [{ property: 'rate', pathKey: 'EDIT_DE_FREERATE_PATH' }];
  let fileArray = decimalLimitationPathArray(row, array);
  const path = fileArray.find(
    item => item.property === column.dataIndex
  )?.pathKey;
  const res = decimalFormat(value, path);
  return res;
};
const deCodeTimeCLose = (
  closeEditor,
  { row, column },
  modelValue,
  name,
  late = 200
) => {
  if (['01', '02', '03'].includes(row.kind)) {
    editClosedEvent({ row, column }, modelValue, name);
    closeEditor();
    return;
  }
  if (showRepeat.value) return;
  if (row[column.field] === modelValue) return closeEditor();
  if (!modelValue) return closeEditor();
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    deCode: modelValue,
  };
  api.csCheckAndQueryDe(apiData).then(res => {
    if (res.code == 500 && !showRepeat.value) {
      editClosedEvent({ row, column }, modelValue, name);
      closeEditor();
    } else {
      showRepeat.value = true;
      updateIsReplaceRow(currentInfo.value);
      let arr = [];
      if (res.result.db.length > 0) {
        arr.push({
          deCode: '定额库中子目',
          children: res.result.db,
        });
      }
      if (res.result.local.length > 0) {
        let localArr = JSON.parse(JSON.stringify(res.result.local));
        // 如果是按市场价组价并且为一般计税
        if (projectStore.setOption.isScj && projectStore.taxMade == 1) {
          localArr.forEach((item, index) => {
            item.baseJournalPrice = item.price;
          });
        }
        // 如果不是按市场价组价并且为一般计税则显示不含税基期价
        if (!projectStore.setOption.isScj && projectStore.taxMade == 1) {
          localArr.forEach((item, index) => {
            item.price = item.baseJournalPrice;
          });
        }
        // 如果是按市场价组价并且为简易计税
        if (projectStore.setOption.isScj && projectStore.taxMade == 1) {
          localArr.forEach((item, index) => {
            item.baseJournalPrice = item.price;
          });
        }
        // 如果不是按市场价组价并且为简易计税则显示不含税基期价
        if (!projectStore.setOption.isScj && projectStore.taxMade == 0) {
          localArr.forEach((item, index) => {
            item.price = item.baseJournalPrice;
          });
        }
        arr.push({
          deCode: '当前工程中子目',
          children: localArr,
        });
      }
      repeatCode.value.deRowId = row.sequenceNbr;
      if (res.result.db.length > 0) {
        repeatCode.value.deStandardId = res.result.db[0].sequenceNbr;
      } else {
        repeatCode.value.deStandardId = res.result.local[0].sequenceNbr;
      }
      codeTable.value = arr;
      inputRefs[column.field + 'Input'].focus();
    }
  });
};
const selectRepeatDe = () => {
  api
    .csSelectDe({
      unitId: projectStore.currentTreeInfo?.id,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      deRowId: repeatCode.value.deRowId,
      deRow: JSON.parse(JSON.stringify(repeatCode.value.row)),
      deStandardId: repeatCode.value.deStandardId,
    })
    .then(res => {
      const deId = repeatCode.value.row?.sequenceNbr;
      repeatCode.value.deRowId = '';
      repeatCode.value.deStandardId = '';
      repeatCode.value.row = {};
      stableRef.value.closeEditor();
      getGlgSettingData(res.result.standardId);
      setTimeout(() => {
        queryBranchDataById();
      }, 300);

      currentInsertRow.value = res.result;
    });
};
const timeCLose = (
  closeEditor,
  { row, column },
  modelValue,
  name,
  late = 200
) => {
  setTimeout(() => {
    !isNameOpen.value ? editClosedEvent({ row, column }, modelValue, name) : '';
    isNameOpen.value = false;
  }, 200);
  setTimeout(() => {
    closeEditor();
  }, late);
};
//项目特征关联
let {
  associationVisible,
  associationRef,
  dblClickHandler,
  projectAttrChange,
  projectAttrFocus,
  popupContainer,
} = useAttrAssociation({ type: 'csxm' });

// 关联数据双击应用回调
const associationDblClick = (data, row) => {
  dblClickHandler({
    data,
    row,
    callback: () => {
      // queryBranchDataById();
      setTimeout(() => {
        quotaInfoRef.value.manualTabChange('groupSchemeTable');
      }, 500);
    },
  });
};

//组价部分
const scheduleFile = defineAsyncComponent(() =>
  import('@/components/schedule/schedule.vue')
);
const $ipc = cxt.appContext.config.globalProperties.$ipc;
let comMatchModal = ref(false); //组价方案弹框
let percentInfo = ref(); //进度条描述
let percent = ref(0); //进度条百分比
let resetModal = ref(false); //是否确认关闭进度条
let isNoClose = ref(false); //进度条关闭前执行函数
let showSchedule = ref(false);
let reportModel = ref(false); //组价饼图弹框
let startMatchData = ref();
let {
  updateConstructRcj,
  rcjIndexVisible,
  rcjIndexLoading,
  addChildrenRcjData,
  retailAreaRcjReplace,
  batchDeleteRcj,
} = useConstructRcj({
  pageType: 'csxm',
  $table: vexTable,
  bcRcjCallback: row => {
    rcjVisible.value = true;
    bdCode.value = row.materialCode;
    // currentUpdateData.value = row;
  },
  refreshList: (EnterTypes = 'Refresh', posId = '', clearSelect = true) => {
    queryBranchDataById(EnterTypes, posId, clearSelect);
  },
});
let {
  updateQdByName,
  dbNameCellClickEvent,
  bdNameTableList,
  bdNamePulldownRef,
  showUnitTooltipType,
  bdNameKeyupEvent,
  onCompositionEnd,
  onCompositionStart,
  editClosedEvent,
  initVirtual,
  getScroll,
  renderedList,
  init,
  loading: tableLoading,
  EnterType,
  scrollToPosition,
  currentChangeEvent,
  mainMaterialTableData,
  updateFbData: itemUpdate,
  queryBranchDataById,
  queryFeeFileData,
  isNameOpen,

  saveContent,
  openEditDialog,
  showModelTitle,

  currentInfo,
  isShowModel,
  editContent,
  editKey,
  infoVisible,
  infoText,
  iconType,
  isSureModal,

  ishasRCJList,
  isClearEdit,
  isSortQdCode,
  bdCode,
  rcjVisible,
  deVisible,
  bcDeRow,
  qdVisible,

  isUpdateFile,
  indexVisible,
  isUpdateQuantities,
  selectUnit,
  showUnitTooltip,
  addCurrentInfo,
  isIndexAddInfo,
  addDataSequenceNbr,
  lockFlag,
  feeFileList,
  pricingMethodList,
  pricingMethodList2,
  djgcFileList,
  tableData,
  originalTableData,
  materialVisible,
  DJGCrefreshFeeFile,
  updateDelTempStatusColl,
  updateCancelDelTempStatusColl,
  batchDelByTypeOfColl,
  batchDeleteVisible,
  handleNewTable,
  codeType,
  radioStyle,
  isOpenLockedStatus,
  batchDataType,
  selectData,
  addDeInfo,
  handleNote,
  handleNoteClick,
  areaStatus,
  areaVisibleType,
  handleMainList,
  handleMainListClick,
  closeAreaModal,
  closeAnnotations,
  getAnnotationsRef,
  cellMouseEnterEvent,
  cellMouseLeaveEvent,
  formatRangeCellText,
  onFocusNode,
  standardVisible,
  queryRule,
  renderLine,
  isNotCostDe,
  deleteStateFn,
  needAddQDandFB,
  tableKeydown,
  setTableKeydownEnd,
  getTypeText,
  queryProjectConvenientSetColl,
  isNextOpen,
  zmList,
  openLevelCheckList,
  getOpenLevelList,
  zmAssociateData,
  associateSubQuotasVisible,
  getGlgSettingData,
  updateZmAssociateData,
  updateIsReplaceRow,
  isReplaceRow,
  materialType,
  materialRow,
  addZmlistRes,
  currentZmDe,
  priceVisible,
  handleDataCustom,
} = useSubItem({
  operateList,
  frameSelectRef: frameSelectRef,
  resetCellData,
  // checkUnit,
  vexTable: stableRef,
  emits,
  codeField: 'deCode',
  nameField: 'name',
  pageType: 'csxm',
  updateConstructRcj,
  api: {
    updateData: api.itemUpdate,
    getList: api.itemPage,
  },
});

provide('subItemProjectData', {
  subItemProjectData: computed(() => tableData.value),
});

const {
  hangMenuList,
  hangMenuDisabledHandler,
  DEHangZCSBVisible,
  openDEHangZCSB,
  hangZCSBMenuClick,
  syncToDEVisible,
  isSpecificationEdit,
  addMxqBcRcjData,
} = hangZCSB({
  pageType: 'csxm',
  refreshList: queryBranchDataById,
});
watch(
  () => currentInfo.value,
  newVal => {
    deleteStateFn();
    if (newVal) {
      updateGljSelrowId(newVal.sequenceNbr, '措施项目', 'selRowId');
      if (
        selectState.selectedRowKeys.includes(newVal.sequenceNbr) &&
        selectState.selectedRowKeys.length > 0
      ) {
        selectState.selectedRowKeys = Array.from(
          new Set([...selectState.selectedRowKeys, ...[newVal.sequenceNbr]])
        );
        selectState.selectedRows = [
          ...selectState.selectedRows,
          ...[newVal],
        ].filter(
          (item, index, arr) =>
            arr.findIndex(i => i.sequenceNbr === item.sequenceNbr) === index
        );
      } else {
        selectState.selectedRowKeys = [newVal.sequenceNbr];
        selectState.selectedRows = [newVal];
      }
      // stableRef.value?.scrollTo(
      //     { rowKey: currentInfo.value.key },
      //     'auto'
      // );
      // 判断过滤按钮是否置灰
      // let options = operateList.value.find(
      //   item => item.name === 'rcj-color-sign'
      // );
      // if (newVal.kind === '05') {
      //   options.disabled = true;
      // } else {
      //   options.disabled = false;
      // }
    }
  }
);
watch(
  () => tableLoading.value,
  () => {
    setGlobalLoading(tableLoading.value, '加载中，请稍后');
  }
);
// watch(
//   () => lockFlag.value,
//   () => {
//     operateList.value.find(item => item.name === 'code-reset').disabled =
//       lockFlag.value;
//   }
// );

// 分部分项，判断展开的状态
watchEffect(() => {
  if (
    projectStore.componentId == 'measuresItem' &&
    projectStore.currentTreeInfo?.id
  ) {
    getOpenLevelList();
  }
});
const isContinue = type => {
  console.log('进度条点击时间', type);
  if (type === '关闭') {
    isNoClose.value = true;
    resetModal.value = true;
  } else if (type === '暂停') {
    console.log('继续', projectStore.currentTreeGroupInfo?.constructId);

    api
      .pauseMerge({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
      })
      .then(res => {});
  } else if (type === '继续') {
    console.log('继续', JSON.parse(JSON.stringify(startMatchData.value)));
    api
      .startMerge(JSON.parse(JSON.stringify(startMatchData.value)))
      .then(res => {});
  }
};
const closeComMatch = data => {
  //关闭组价方案匹配
  console.log('关闭closeComMatch');
  startMatchData.value = data;
  // comMatchModal.value = false;
  // reportModel.value = true; //饼图弹框
  startMatch(data);
};
const visibleChange = (val, row) => {
  console.log('visibleChange');
  if (
    !val &&
    (row?.noteViewVisible || row?.isShowAnnotations || row?.noteEditVisible)
  ) {
    row.annotationsVisible =
      row?.noteViewVisible || row?.isShowAnnotations || row?.noteEditVisible;
  }
};
const deNameRef = node => {
  return node.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode
    .parentNode;
};
const startMatch = async data => {
  isNoClose.value = true;
  let formData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  //初始化进度条
  percentInfo.value = {
    finish: 0,
    noFinish: 0,
    total: 0,
    dec: '组价方案匹配中，请稍后…',
  };
  percent.value = 0;
  console.log('startMerge', data);
  comMatchModal.value = false;
  // setTimeout(() => {
  showSchedule.value = true; //组价进度条开始

  $ipc.on(formData.constructId, (event, arg) => {
    // console.log('constructId', arg);
    if (arg.percent >= percent.value) {
      percentInfo.value = {
        finish: arg.succeed,
        noFinish: arg.notSuccess,
        total: arg.total,
        dec: arg.percent >= 100 ? '组价方案完成' : '组价方案匹配中，请稍后…',
      };
      percent.value = arg.percent;
    }
    if (arg.percent >= 100) {
      $ipc.removeAllListeners('formData.constructId'); //监听事件移除
      if (isopenReport.value) {
        console.log('ReuseGroupPriceRef.value', ReuseGroupPriceRef.value);
        isopenReport.value = false;
        closeSchedule();
      }
    }
  });
  let res = await api.startMerge(data).then();
  if (res.status === 500 && percent.value === 0) {
    setTimeout(() => {
      percentInfo.value = {
        finish: 0,
        noFinish: 0,
        total: 0,
        dec: '组价方案完成',
      };
      percent.value = 100;
      // console.log('startMerge返回', res, percent.value);
      closeSchedule();
    }, 1000);
  }
};
const closeSchedule = () => {
  isopenReport.value = false;
  setTimeout(() => {
    isNoClose.value = false;
    showSchedule.value = false; //组价进度条关闭
    if (resetModal.value) {
      resetModal.value = false;
    }
    $ipc.removeAllListeners('formData.constructId'); //监听事件移除
    if (projectStore.tabSelectName === '措施项目') {
      reportModel.value = true; //饼图弹框
      queryBranchDataById('Refresh');
    }
  }, 2000);
};
const recover = async bol => {
  //否，恢复至组价前数据
  //  bol--为true恢复
  let formData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  let getRes;
  if (bol) {
    getRes = await api.beforeRestoring(formData).then();
  } else {
    getRes = await api.determine(formData).then();
  }
  console.log('recover', bol, getRes);
  isNoClose.value = false;
  resetModal.value = false;
  showSchedule.value = false;
  queryBranchDataById('Refresh'); //点击是或否都更新数据
};
//扇形图点击查看功能
const lookView = data => {
  checkList.value = [];
  reportModel.value = false; //饼图弹框
  console.log('组价-扇形图点击查看部分', data, startMatchData.value);
  switch (data.name) {
    case '精准组价':
      checkList.value.push('1');
      break;
    case '近似组价':
      checkList.value.push('2');
      break;
    case '未匹配组价':
      checkList.value.push('0');
      break;
  }
  dataSearchPosition({
    treeId: startMatchData.value.selectedUnitIdList[0],
    tabMenuName: '分部分项',
    type: checkList.value,
  });
  // filterData(checkList.value)
};
onMounted(() => {
  projectStore.isOpenIndexModal = {
    open: false,
    tab: null,
  };
  if (!projectStore?.measuresItemProjectAutoPosition) {
    projectStore.measuresItemProjectAutoPosition = {
      queryBranchDataById,
      getTableData: zrMb,
      copyAndPaste,
      posRow,
    };
  }
  if (projectStore.isAutoPosition) {
    currentInfo.value = { sequenceNbr: projectStore.positionId };
    queryBranchDataById('other', projectStore.positionId);
  }
});
// onBeforeUnmount(() => {
//   window.removeEventListener('keydown', copyAndPaste);
// });
// const copyAndPastea = inject('copyAndPaste');

// const triggerCopyAndPaste = () => {
//   if (copyAndPastea) {
//     copyAndPastae(/* 传递需要的参数 */);
//   }
// };

// ---------- 局部汇总 start ---------
let gljPartialSummaryRef = ref();
let gljPartialSummaryStatus = ref(false);

let lockBtnStatus = ref(false);
onActivated(() => {
  isFilter.value = false;
  initColumns({ columns: getTableColumns(emits, 'csxm'), pageName: 'csxm' });
  lockBtnStatus.value = false;
  // initVirtual(vexTable.value);
  if (!projectStore.isAutoPosition) {
    // 不是自动定位的才调用接口
    queryBranchDataById('other');
  }
  queryFeeFileData();
  // querySzType();
  getMeasureTypes();
  getQueryMeasureTypeData();
  bus.on('handleCopyEvent', ({ event, name }) => {
    // queryUnit();
    bus.off('handleCopyEvent');
    if (name === 'measuresItem') copyAndPaste(event);
  });
  bus.off('moveDeData');
  bus.on('moveDeData', ({ state, type }) => {
    moveDeData({ state, type });
  });
  console.log('measuresItem', operateList.value);
  operateList.value.find(
    item => item.name === 'rcj-color-sign'
  ).disabled = false;
  insetBusGlj(bus, projectStore.componentId, 'measuresItem', async data => {
    if (data.name == 'expandTo') {
      if (data.activeKind !== undefined) {
        openData(data.activeKind);
      }
    }
    if (data.name === 'filterate') {
      // 过滤
      filterateVisible.value = true;
    }
    if (data.name === 'lookup') {
      // 查找
      openLookup();
    }
    if (data.name === 'rcj-color-sign') {
      // 颜色设置
      changeRowColor(data);
    }

    if (data.name === 'partial-summary-glj') {
      gljPartialSummaryStatus.value = true;
      nextTick(() => {
        gljPartialSummaryRef.value.open(tableData.value[0]?.deRowId, 'cxxm');
      });
      return;
    }
    //房修土建费用
    if (data.name === 'civil-construction-costs-of-houses-JQ') {
      if (data.activeKind) {
        isFxtjfy.value = true;
        activeKind.value = data.activeKind;
      }
    }
  });
  insetBus(bus, projectStore.componentId, 'measuresItem', async data => {
    if (data.name === 'insert-subItem') {
      if (!data.activeKind) {
        contextMenu();
      } else {
        addData(data.activeKind);
      }
    }
    if (data.name === 'supplement') {
      if (!data.activeKind) {
        bcContextMenu();
        console.log('执行补充');
      } else {
        bcData(data);
        console.log('执行补充子菜单');
      }
    }
    if (data.name === 'select-color') {
      if (!data.activeKind) {
      } else {
        updateDataColorColl(data);
      }
    }
    // operateList.value.find(item => item.name === 'lock-subItem').label =
    //   lockFlag.value ? '整体解锁' : '整体锁定';
    // operateList.value.find(item => item.name === 'code-reset').disabled =
    //   lockFlag.value;
    if (data.name === 'delete-subItem') deleteType(null);
    if (data.name === 'code-reset') {
      openCodeResetDialog();
    }
    if (data.name === 'vertical-transport') showModel('zscy');
    if (data.name === 'superelevation') showModel('zscg');
    if (data.name === 'installation-costs') showModel('azfy');
    if (data.name === 'calculation-measures') showModel('csfy');
    if (data.name === 'lock-subItem') allLock();
    if (data.name === 'component-matching') checkOnline(data);
    if (data.name === 'reuse-group-price') openReuseGroupPrice(data);
    if (data.name === 'qd-group-price') openQdQuickPricing(data);
    if (data.name === 'qd-library' || data.name === 'de-library') {
      indexVisible.value = true;
      dataType.value = currentInfo.value.kind;
    }
  });
});

/**
 * 展开到
 */
const openData = type => {
  const postData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    type: openLevelCheckList.value[type]?.type,
  };
  console.log('展开到', postData);
  api.measuresItemOpenLevel(postData).then(res => {
    if (res.status == 200) {
      message.success('展开成功');
      queryBranchDataById();
    }
  });
};

onDeactivated(() => {
  lockBtnStatus.value = true;
  qdQuickPricingRef.value?.cancel(false);
  lookupVisible.value = false;
  isFilter.value = false;
  window.removeEventListener('keydown', openLookup);
});
let isopenReport = ref(false);
const checkOnline = async data => {
  if (Object.prototype.hasOwnProperty.call(data, 'activeKind')) {
    if (data.activeKind === '01') {
      //点击组价方案匹配
      comMatchModal.value = true;
      isopenReport.value = true;
    } else if (data.activeKind === '02') {
      //点击组件筛选
      if (!projectStore.combinedVisible)
        projectStore.SET_COMBINED_VISIBLE(true);
    }
  } else {
    const hasOnline = await checkisOnline(true);
    hasOnline
      ? data.options.forEach(item => (item.isValid = true))
      : data.options.forEach(item => (item.isValid = false));
  }
};
const scrollTo = (x, y) => {
  vexTable.value.scrollTo(0, vexTable.value.getScroll().scrollTop + y);
};
const getSelectData = val => {
  // console.log('selectData',val)
  selectData.value = val;
};

let {
  sTableState,
  setInputRef,
  inputRefs,
  openEditor,
  setCloseEditor,
  clickOutside,
  cellMouseup,
  rowSelection,
  customRow,
  pasteRowVisible,
  cellKeydown,
  copyAndPaste,
  copyFun,
  pasteFun,
  isAllSelectedZCSB,
} = stableHook(
  {
    selectState: selectState,
    stableRef: stableRef,
    currentInfo: currentInfo,
    tableData: tableData,
    originalData: tableData,
    copyData: copyData,
  },
  'csxm',
  msg => {
    if (msg === 'refresh') queryBranchDataById();
    if (msg === 'projectAttr-open') {
      isAttrContent.value = true;
      setTimeout(() => {
        isAttrContent.value = false;
      }, 100);
    }
  }
);
//点击编辑区之外的位置
onClickOutside(stableRef, clickOutside);
// /**
//  *
//  * @param {*} event
//  * @param {*} isHandCopy 是否手动执行复制操作
//  */
// const copyAndPaste = (event, isHandCopy = false) => {
//   console.log('event', event.code);
//   console.log('选中的', selectData.value);
//   console.log('当前的', currentInfo.value);
//   if (['input', 'textarea'].includes(event.target.nodeName.toLowerCase()))
//     return;
//   // 如果选中数据为空，情景1，刚开始进入页面，2点击了input,然后点击空白处
//   if (!selectData.value || !selectData.value?.data?.length) {
//     frameSelectRef.value?.isBranchCopy([currentInfo.value?.sequenceNbr]);
//   }

//   if (isHandCopy) {
//     copyFun();
//   }

//   if (event.ctrlKey && event.code === 'KeyC') {
//     copyFun();
//   }
//   if (event.ctrlKey && event.code === 'KeyV') {
//     if (!vexTable.value.getSelectedCell()) return; //vexTable.value.getSelectedCell()如果当前表格不是选中，就不进行ctr+v
//     pasteFun();
//   }
// };

// 编码重刷
let codeResetVisible = ref(false);
const openCodeResetDialog = () => {
  codeResetVisible.value = true;
};

// const copyFun = () => {
//   const $table = vexTable.value;
//   // 判断行是否为激活编辑状态
//   console.log(
//     '$table.isUpdateByRow(currentInfo.value)',
//     $table.isEditByRow(currentInfo.value)
//   );
//   if ($table.isEditByRow(currentInfo.value)) return;
//   if (!selectData.value) {
//     message.error('暂无选中数据');
//   } else {
//     if (selectData.value.isCopy) {
//       console.log('111111111111');
//       copyData.value = selectData.value;
//       copyBranchDataQdDe();
//     } else {
//       message.error(selectData.value.msg);
//     }
//   }
// };

// const pasteFun = async () => {
//   console.log('粘贴方法');
//   const $table = vexTable.value;
//   // 判断行是否为激活编辑状态
//   console.log(
//     '$table.isUpdateByRow(currentInfo.value)',
//     $table.isEditByRow(currentInfo.value)
//   );
//   if ($table.isEditByRow(currentInfo.value)) return;
//   let clipboardText;
//   const clipPromise = navigator.clipboard.readText();
//   await clipPromise.then(function (clipText) {
//     //粘贴板粘贴的数据
//     clipboardText = clipText;
//     if (!copyData.value && clipboardText) {
//       copyData.value = clipboardText;
//     }
//   });
//   if (!copyData.value) {
//     message.error('暂无复制数据');
//   } else {
//     if (
//       projectStore.standardGroupOpenInfo.isOpen &&
//       copyData.value.data.find(i => i.kind === '03')
//     ) {
//       message.error('标准组价不可粘贴包含清单行数据');
//       return;
//     }
//     console.log(frameSelectRef.value.getRowCurrent());
//     if (!frameSelectRef.value.getRowCurrent()) {
//       return message.error('请选中需要粘贴行！');
//     } else {
//       let row = frameSelectRef.value.getRowCurrent();
//       console.log('else方法', row);
//       try {
//         await frameSelectRef.value.frameSelectJs.isPasteBranch(
//           row,
//           copyData.value
//         );
//         console.log('粘贴数据到此页面：', copyData.value, currentInfo.value);
//         batchPasteQdDeData();
//         // frameSelectRef.value.clearSelect();
//         // copyData.value = null;
//         // selectData.value = null;
//       } catch (error) {
//         console.log('周这儿了', error);
//         // message.error(error);
//       }
//     }
//   }
// };

// // 批量粘贴数据
// const batchPasteQdDeData = () => {
//   console.log('这儿进来了不');
//   let apiData = {
//     constructId: projectStore.currentTreeGroupInfo?.constructId,
//     singleId: projectStore.currentTreeGroupInfo?.singleId,
//     unitId: projectStore.currentTreeInfo?.id,
//     pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
//   };
//   api.batchPasteQdDeData(apiData).then(res => {
//     if (res.status === 200 && res.result) {
//       message.success('粘贴成功');
//       queryBranchDataById();
//     } else {
//       message.error('粘贴失败');
//     }
//   });
// };

// 定位方法
const posRow = sequenceNbr => {
  // 块料楼地面 bdCode: "011102003006"
  // let testId = '1725055299942461634'
  // currentInfo.value = { sequenceNbr };在queryBranchDataById会给currentInfo.value赋值，此处不赋值，不然会出现12518bug
  queryBranchDataById('other', sequenceNbr);
};

const addType = () => {
  console.log('插入类型');
};
const queryExistsGlDeColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: 'csxm',
    idList: JSON.parse(JSON.stringify(selectState.selectedRowKeys)),
  };
  console.log('关联子定额删除参数', apiData);
  api.queryExistsGlDeColl(apiData).then(res => {
    console.log('批量删除数据', res);
    if (res.status === 200) {
      if (res.result) {
        infoMode.show({
          iconType: 'icon-qiangtixing',
          infoText: '删除主子目时将删除对应关联子目，是否继续?',
          descText: projectStore.standardGroupOpenInfo.modalTip,
          confirm: () => {
            if (selectState.selectedRowKeys.length > 1) {
              let index = tableData.value.findIndex(
                x => x.sequenceNbr === selectState.selectedRowKeys[0]
              );
              page.value =
                index === 1 ? 1 : Math.ceil((index - 1) / limit.value);
              delBatchData();
            } else {
              delSingleFbData();
            }
            infoMode.hide();
          },
          close: () => {
            infoMode.hide();
          },
        });
      } else {
        deleteInfo();
      }
    }
  });
};

const deleteType = () => {
  console.log('删除类型');
  isBatchDelete.value = false;
  if (
    selectState.selectedRowKeys.length == 1 &&
    (currentInfo.value.kind === '00' ||
      (currentInfo.value.kind === '01' &&
        currentInfo.value.deName == '不可竞争措施项目'))
  ) {
    message.warning('该行不可删除');
    return;
  }
  deleteList.value = [];
  let tempList = [];
  tableData.value.forEach(item => {
    if (selectState.selectedRowKeys.includes(item.sequenceNbr)) {
      tempList.push(item);
    }
  });
  // let status = tempList.some(x => x.kind === '04');
  // if (status) {
  //   queryExistsGlDeColl();
  //   return;
  // }
  deleteInfo();
  console.log('delete', deleteList.value);
};

const deleteInfo = () => {
  let selDateList = JSON.parse(JSON.stringify(selectState.selectedRowKeys));
  if (selDateList.length > 1) {
    isBatchDelete.value = true;
  } else {
    currentInfo.value.optionMenu.forEach(item => {
      if (item === 4 || item === 5) {
        deleteList.value.push(item);
      }
    });
    // 如果是主材或者设备行删除
    if (currentInfo.value.optionMenu.length == 0) {
      deleteList.value.push(4);
    }
  }
  const params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    deRowIdList: selDateList,
  };
  api.existRelationDeMeasuresItem(params).then(res => {
    if (res.status === 200) {
      if (selDateList.length == 1) {
        existRelationDe.value = res.result;
      } else {
        existRelationDe2.value = res.result;
      }
      deleteVisible.value = true;
    }
  });
};

const delSingleFbData = type => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitWorkId: projectStore.currentTreeInfo?.id,
    isBlock: type,
    sequenceNbr: currentInfo.value.sequenceNbr,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    ...(existRelationDe.value ? { isRemoveRelationDe: true } : {}),
  };
  let index = tableData.value.findIndex(
    x => x.sequenceNbr === currentInfo.value.sequenceNbr
  );
  api
    .itemRemove(apiData)
    .then(res => {
      console.info(333333333333, res);
      if (res.status === 200 && res.result) {
        deleteVisible.value = false;
        deleteLoading.value = false;
        message.success('删除成功');
        page.value = Math.ceil((index + 1) / limit.value);

        queryBranchDataById('noPosition', '', true, index);
      } else {
        message.error(res.message);
        deleteVisible.value = false;
        deleteLoading.value = false;
        selectData.value.data = [];
      }
    })
    .catch(err => {
      message.error('删除失败');
      selectData.value.data = [];
      deleteLoading.value = false;
      deleteVisible.value = false;
    });
};

const cancel = () => {
  deleteVisible.value = false;
};

const addData = (kind, formData) => {
  console.log('2222222222', kind);
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: {
      kind: currentInfo.value.kind,
      sequenceNbr: currentInfo.value.sequenceNbr,
      parentId: currentInfo.value.parentId,
      displayStatu: currentInfo.value.displayStatu,
      displaySign: currentInfo.value.displaySign,
    },
    newLine: {
      kind: kind,
    },
  };
  api.itemSave(apiData).then(res => {
    console.log('============', res);
    if (res.status === 200 && res.result) {
      currentInfo.value = res.result.data;
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      message.success('操作成功');
      console.log('page.value', page.value);
      queryBranchDataById('noPosition');
    }
  });
};

/**
 * 点击index事件 为什么从上面单独拿下来，因为收起分部，然后这时候多次点击，没有触发currentChangeEvent事件。所以拿不到当前行子级数据了就是空数据了
 * @param {*} row
 */
const clickIndex = row => {
  const $table = vexTable.value;
  // 判断单元格值是否被修改
  if ($table.isUpdateByRow(currentInfo.value)) return;
  currentInfo.value = row;
  projectStore.SET_SUB_CURRENT_INFO(row);
  nextTick(() => {
    if (row.kind === '04') return;
    // 等选中的样式更新完，
    queryAllDataByBranchId();
  });
};

// 是否编辑处理
const setEditEnabled = row => {
  const { isCostDe, kind } = row;
  if (
    kind === '03' &&
    row.hasOwnProperty('zjcsClassCode') &&
    row.zjcsClassCode !== null &&
    row.zjcsClassCode !== undefined &&
    Number(row.zjcsClassCode) === 0
  ) {
    isEditEnabled.value = false;
    return;
  }
  if (kind === '04' && isCostDe === 1) {
    isEditEnabled.value = false;
    return;
  }
  isEditEnabled.value = true;
};

/**
 * @param {*} v
 *
 * type:'groupSchemeTable', 业务
    filed:'projectAttr', 字段
    value: row.projectAttr 值
    isRefresh: 是否刷新
 */
const onDbClickFile = v => {
  itemUpdate(
    {
      ...currentInfo.value,
      projectAttr: v.value,
    },
    'projectAttr'
  );
};

// 弹框提示点击确定按钮事件
const updateCurrentInfo = () => {
  itemUpdate(currentInfo.value, 'quantityExpression');
};

const closeDialog = () => {
  // 在子目定额循环弹窗中，如果当前行为子目定额则不调用获取标准计算方法接口
  if (currentZmDe.value) {
    modalPop();
  } else {
    getGlgSettingData(currentInfo.value?.standardId);
  }
};

// 工程量明细更改后刷新当前行数据
const refreshCurrentInfo = () => {
  if (projectStore.tabSelectName !== '措施项目') return;
  addDataSequenceNbr.value = addCurrentInfo.value
    ? addCurrentInfo.value.sequenceNbr
    : currentInfo.value.sequenceNbr;
  // getGlgSettingData(currentInfo.value?.standardId);
  queryBranchDataById('noPosition');
};

/**
 * 设置主材市场价弹框操作更新
 * @param type 1 关闭弹框, 2 刷新数据
 */
const setUpdate = type => {
  materialVisible.value = false;
  if (materialType.value) {
    addDeOrQdFn.value();
  } else if (type === 2) {
    isUpdate.value = true;
    setTimeout(() => {
      isUpdate.value = false;
    }, 100);
    addDataSequenceNbr.value = addCurrentInfo.value
      ? addCurrentInfo.value.sequenceNbr
      : currentInfo.value.sequenceNbr;
    // queryBranchDataById();
  }
  queryBranchDataById('noPosition');
  if (currentZmDe.value) {
    standardPop();
  } else {
    queryRule();
  }
  materialType.value = '';
};

const changeStatus = row => {
  let index = tableData.value.findIndex(x => x.sequenceNbr === row.sequenceNbr);
  page.value = Math.ceil((index + 1) / limit.value);
  if (row.displaySign === 1) {
    row.displaySign = 2;
    closeTree(row);
  } else if (row.displaySign === 2) {
    row.displaySign = 1;
    openTree(row);
  }
  // itemUpdate(row, 'seq');
  console.log('row', row);
};
const openTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  console.log('打开节点参数', apiData);
  api.itemOpen(apiData).then(res => {
    console.log('打开节点', res);
    if (res.status === 200 && res.result) {
      queryBranchDataById();
    }
  });
};
const closeTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  console.log('关闭节点参数', apiData);
  api.itemClose(apiData).then(res => {
    console.log('关闭节点', res);
    if (res.status === 200 && res.result) {
      queryBranchDataById();
    }
  });
};

const toggleMethod = ({ expanded, row }) => {
  console.log('操作树展开与关闭', row);
  if (expanded) {
    console.log('展开');
  } else {
    console.log('关闭');
  }
  // row.closeFlag = expanded
  // itemUpdate(row)
  return true;
};

const cellDBLClickEvent = ({ row, column }, db = '0') => {
  currentInfo.value = row;
  console.log('🚀 ~ cellDBLClickEvent ~ currentInfo.value:', currentInfo.value);
  // || [1,3].incluse(row.pricingMethod)
  if (!sTableState.prevDbTime && db === '0') return;
  if (column.field === 'deCode') {
    if (['05'].includes(row.kind)) {
      quotaInfoRef.value.cellDBLClickEvent(row, 1);
    } else {
      indexVisible.value = true;
      dataType.value = row.kind;
    }
    console.log('清单定额双击', indexVisible.value, dataType.value);
  } else if (column.field === 'projectAttr' && !associationVisible.value) {
    isAttrContent.value = true;
    setTimeout(() => {
      isAttrContent.value = false;
    }, 100);
  }
};

// 表格单击事件
const tableCellClickEvent = ({ record, column }) => {
  if (
    (record.isLocked && !['deCode'].includes(column.field)) ||
    record.tempDeleteFlag
  )
    return false;
  return true;
};

const successHandler = addList => {
  addDataSequenceNbr.value = addList[0]?.data?.sequenceNbr;
  queryBranchDataById();
};

const quotasCancel = () => {
  associateSubQuotasVisible.value = false;
  currentMaterial.value = null;
  // if (
  //   zmList.value[zmList.value.length - 1]?.data?.sequenceNbr !==
  //   addDeInfo.value.sequenceNbr
  // ) {
  //   isNextOpen.value = true;
  // }
};
const queryRcjDataByDeId = (bol = true, deItem = null) => {
  let apiData = {
    deRowId: addDeInfo.value?.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    type: addDeInfo.value?.type,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  console.log('// 获取人材机明细数据', addDeInfo.value, deItem, apiData);
  if (!apiData.deRowId) return;
  api.getAllRcjDetail(apiData).then(res => {
    console.log('🚀 ~ api.getAllRcjDetail ~ res:', res);
    if (res.status === 200) {
      if (res.result?.length > 0) {
        ishasRCJList.value = true;
      }
      console.log(
        '  mainMaterialTableData.value',
        res,
        mainMaterialTableData.value
      );
      if (bol) {
        mainMaterialTableData.value = res.result.filter(
          x => x.kind == 5 || x.kind == 4
        );
        const showUnpricedPop =
          projectStore.convenienceSettings.get('UNPRICED')?.unPricedPop;
        if (mainMaterialTableData.value.length > 0 && showUnpricedPop) {
          materialVisible.value = true;
        } else {
          queryRule();
        }
      } else {
        queryRule();
      }
    }
  });
};
/**
 * 查询定额主材设备
 * @param rowType 1 定额, 2 人材机
 * @param type 'replace' / 'add'
 */
const getMainMaterialAndEquipment = (row, rowType, type) => {
  return new Promise((resolve, reject) => {
    const apiParams = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      unitId: projectStore.currentTreeInfo?.id,
      deStandardId: row.sequenceNbr,
    };
    api
      .getMainMaterialAndEquipment(apiParams)
      .then(res => {
        deOrRcj.value = rowType;
        console.info(8888888888, res.result);

        if (res.result.length > 0) {
          console.log('mainMaterialTableData', row, res.result);
          addDeInfo.value = currentInfo.value;
          if (addDeInfo.value.deName == undefined) {
            addDeInfo.value.deName = row.deName;
          }
          mainMaterialTableData.value = res.result;
          materialType.value = type;
          materialRow.value = row;
          currentMaterial.value = row;
          console.log('mainMaterialTableData', mainMaterialTableData.value);
          // updateMainMaterialAndEquipment(res.result).then(() => {
          //   console.log(res, row.sequenceNbr, 'getMainMaterialAndEquipment');
          // resolve();
          // });
          const showUnpricedPop =
            projectStore.convenienceSettings.get('UNPRICED')?.unPricedPop;
          if (showUnpricedPop) {
            materialVisible.value = true;
          } else {
            if (materialType.value) {
              setUpdate(deOrRcj.value == 1 ? 1 : 2, materialRow.value);
            } else {
              setUpdate(1);
            }
          }
        } else {
          addDeOrQdFn.value();
        }
      })
      .catch(error => {
        reject(error);
      });
  });
};
const currentQdDeInfo = (row, isRcj, isReplace) => {
  // getMainMaterialAndEquipment(row, isRcj, isReplace);
  // addDeOrQdFn.value = () => {
  //   fillMeasureFromIndexPage(row, isRcj, isReplace);
  // };
  fillMeasureFromIndexPage(row, isRcj, isReplace);
};
// 插入定额
const fillMeasureFromIndexPage = (row, isRcj, isReplace) => {
  let pointLine = JSON.parse(JSON.stringify(currentInfo.value));
  if (pointLine.kind == '03') {
    pointLine.parentId = pointLine.sequenceNbr;
  }
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine,
    kind: row.kind,
    indexId: row.sequenceNbr,
    unit: row.unit,
    rcjFlag: isRcj == 1 ? 0 : 1,
    libraryCode: row.libraryCode,
    fbfxOrCsxm: 'csxm',
  };
  console.log('插入参数', apiData);
  let apiName = 'fillMeasureFromIndexPage';

  if (row.deArray?.length) {
    // 清单指引点击插入子目保存
    apiData.deArray = row.deArray;
    apiName = 'saveDeArray';
  }

  if (row.baseListModel?.sequenceNbr) {
    // 清单指引点击插入清单
    apiData.baseListModel = row.baseListModel;
    apiName = 'saveQdAndDeArray';
  }

  indexLoading.value = true;
  api[apiName](apiData)
    .then(res => {
      console.log('插入数据', res);
      if (res.status === 200 && res.result) {
        indexLoading.value = false;
        isIndexAddInfo.value = true;
        if (apiName === 'saveDeArray') {
          // 插入子目
          zmList.value = res.result;
          res.result.forEach((item, index) => {
            if (index === 0) {
              addDataSequenceNbr.value = item.data.sequenceNbr;
              addDeInfo.value = item.data;
              queryRcjDataByDeId();
            }
          });
        } else if (apiName === 'saveQdAndDeArray') {
          addDataSequenceNbr.value =
            res.result?.saveQdResult?.data?.sequenceNbr;
        } else {
          addDataSequenceNbr.value = res.result.sequenceNbr;
          page.value = Math.ceil((res.result.index + 1) / limit.value);
          if (row.kind === '04' && !row.rcjFlag) {
            addDeInfo.value = res.result;
            // standardVisible.value = true;
            queryRcjDataByDeId();
          }
          currentInsertRow.value = res.result;
          currentInsertIsRcj.value = isRcj;
          // getGlgSettingData(res.result.standardId);
        }
        currentInfo.value = res.result;
        message.success('操作成功');
        queryBranchDataById('noPosition');
      } else {
        indexLoading.value = false;
      }
    })
    .finally(() => {
      indexLoading.value = false;
    });
};

// 替换功能 rcjFlag 0 定额  1 人材机
const currentInfoReplace = (row, isRcj, status) => {
  // getMainMaterialAndEquipment(row, isRcj, status);
  // addDeOrQdFn.value = () => {
  //   currentInfoReplaceOpe(row, isRcj, status);
  // };
  currentInfoReplaceOpe(row, isRcj, status);
};
const currentInfoReplaceOpe = (row, isRcj, status) => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitWorkId: projectStore.currentTreeInfo?.id,
    selectId: row.sequenceNbr,
    replaceId: currentInfo.value.sequenceNbr,
    type: 1,
    conversionCoefficient: row.conversionCoefficient,
    kind: row.kind,
    unit: row.unit,
    libraryCode: row?.libraryCode,
    fbfxOrCsxm: 'csxm',
    rootLineId: route.query.constructSequenceNbr,
    rcjFlag: isRcj == 1 ? 0 : 1,
  };
  indexLoading.value = true;

  let apiName = 'itemReplaceFromIndexPage';
  api[apiName](apiData)
    .then(res => {
      if (res.status === 200 && res.result) {
        if (currentInfo.value?.kind !== '-1') {
          // 不是替换空行的时候
          updateIsReplaceRow(currentInfo.value);
        }
        addDataSequenceNbr.value = res.result.sequenceNbr;
        indexLoading.value = false;
        message.success('操作成功');
        if (apiData.deArray?.length) {
          queryRcjDataByDeId();
        } else if (row.kind === '04' && !row.rcjFlag) {
          addDeInfo.value = res.result;
          queryRcjDataByDeId();
        }
        // getGlgSettingData(res.result.standardId);
        queryBranchDataById('noPosition');
        //插入判断是否显示子目弹窗 standardId
        currentInsertRow.value = row;
        currentInsertIsRcj.value = isRcj;
      } else {
        indexLoading.value = false;
      }
    })
    .finally(() => {
      indexLoading.value = false;
    });
};

const contextMenu = () => {
  let tempList = xeUtils.clone(menuList.value, true);
  tempList.forEach(item => {
    currentInfo.value.optionMenu.forEach(child => {
      if (child === item.type) {
        item.isValid = true;
      }
    });
  });
  contextmenuList.value = tempList;
  operateList.value.find(item => item.name === 'insert-subItem').options =
    contextmenuList.value;
};

const bcContextMenu = () => {
  console.log('补充', currentInfo.value);
  let tempList = xeUtils.clone(bcMenuList.value, true);
  tempList.forEach(item => {
    item.isValid = false;
    currentInfo.value.optionMenu.forEach(child => {
      if (child === item.type) {
        // ([94, 95].includes(currentInfo.value.kind) && item.name === '补充人材机')
        item.isValid = true;
      }
    });
  });
  contextmenuList.value = tempList;
  operateList.value.find(item => item.name === 'supplement').options = tempList;
};

const bcData = item => {
  bdCode.value = '';
  if (item.activeKind === '03') {
    qdVisible.value = true;
  } else if (item.activeKind === '04') {
    deVisible.value = true;
  } else {
    rcjVisible.value = true;
  }
};

// const querySzType = () => {
//   api
//     .querySzType({ constructId: route.query.constructSequenceNbr })
//     .then(res => {
//       if (res.status === 200 && res.result) {
//         szTypeList.value = res.result;
//       }
//     });
// };

const getMeasureTypes = () => {
  api.getMeasureTypes().then(res => {
    if (res.status === 200 && res.result) {
      measureList.value = [];
      res.result.map(i => {
        measureList.value.push({ label: i, value: i });
      });
    }
  });
};
// 获取措施组织类别下拉列表
const getQueryMeasureTypeData = () => {
  api.queryMeasureTypeData().then(res => {
    console.info(8888888888, res);
    if (res.status === 200 && res.result) {
      csTypeList.value = res.result;
    }
  });
};

const queryUnit = () => {
  api.queryUnit().then(res => {
    if (res.status === 200 && res.result) {
      unitList.value = res.result;
    }
  });
};

const delFbData = type => {
  if (existRelationDe2.value && type == true) {
    existRelationDe.value = true;
    return;
  }
  if (deleteLoading.value) return;
  // 如果是主材费、设备费行删除调用明细区删除操作
  if (
    selectState.selectedRowKeys.length === 1 &&
    currentInfo.value.kind == '05'
  ) {
    delRcjData(currentInfo.value);
    return;
  }
  deleteLoading.value = true;
  if (isAllSelectedZCSB()) {
    batchDeleteRcj(selectState.selectedRowKeys, () => {
      deleteVisible.value = false;
      deleteLoading.value = false;
    });
    return;
  }
  if (isBatchDelete.value) {
    let index = tableData.value.findIndex(
      x => x.sequenceNbr === selectState.selectedRowKeys[0]
    );
    console.log('index', index, typeof index);
    page.value = index === 1 ? 1 : Math.ceil((index - 1) / limit.value);
    delBatchData();
    return;
  }

  delSingleFbData(type);
};
// 删除人材机数据
const delRcjData = row => {
  let apiData = {
    rcjDetailId: row ? row.sequenceNbr : currentInfo.value.sequenceNbr,
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    deId: row ? row.parentId : props.currentInfo?.sequenceNbr,
  };
  api.delRcjData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('删除成功');
      deleteVisible.value = false;
      deleteLoading.value = false;
      queryBranchDataById('noPosition');
    }
  });
};
const contextMenuClickEvent = async ({ menu, row }, args) => {
  console.log('menu, row', menu, row);
  if (menu?.fatherCode === 'noteList' || menu.code == 'noteList') {
    if (menu.code != 'noteList') {
      handleNoteClick(menu, row);
    }
  } else if (menu?.fatherCode === 'MainList' || menu.code == 'MainList') {
    if (menu.code != 'MainList') {
      handleMainListClick(menu, row);
    }
  } else if (menu.code === 'delete') {
    deleteType();
  } else if (menu.code === 'copy') {
    copyFun();
  } else if (menu.code === 'paste') {
    console.log('执行粘贴方法');
    pasteFun();
  } else if (menu.code === 'lock') {
    if (row.isLocked === 1) {
      csUnLockQd();
    } else {
      csLockQd();
    }
  } else if (menu.code === 'pageColumnSetting') {
    showPageColumnSetting();
  } else if (menu.code === 'copyCell') {
    copyClick(args);
  } else if (menu.code === 'tempDelete') {
    updateDelTempStatusColl(
      selectState.selectedRows,
      selectState.selectedRowKeys
    );
  } else if (menu.code === 'cancelTempDelete') {
    updateCancelDelTempStatusColl(
      selectState.selectedRows,
      selectState.selectedRowKeys
    );
  } else if (
    menu.code === 'batchDelete-child1' ||
    menu.code === 'batchDelete-child2' ||
    menu.code === 'batchDelete-child3'
  ) {
    batchDeleteVisible.value = true;
    batchDataType.value = menu.type;
  } else if (menu.code === 'zcsbAdd') {
    rcjIndexVisible.value = true;
  } else if (['supplement-de', 'supplement-rcj'].includes(menu.code)) {
    menu.activeKind = menu.kind;
    bcData(menu);
  } else if (menu.code !== 'add' && menu.kind && menu.code !== 'batchDelete') {
    addData(menu.kind);
  }
  hangZCSBMenuClick(menu, row);
};
const copyClick = (args, type = 'cell') => {
  if (type === 'cell') {
    copyData.value = [];
    stableRef.value.copySelectedRange();
    message.success('复制单元格成功');
  }
  args.hidePopup();
};
/**
 * 拖动了高度
 */
const dragHeight = h => {
  stableHeight.value = h - 50;
};
window.addEventListener('resize', function () {
  let tableEl = document.querySelector('.table-content');
  stableHeight.value = tableEl.clientHeight - 50;
});
// const rowClassName = ({ row }) => {
//   let ClassStr = 'normal-info';
//   if (row.kind === '0') {
//     ClassStr = 'row-unit';
//   } else if (row.kind === '01' || row.kind === '02') {
//     ClassStr = 'row-sub';
//   } else if (row.kind === '03') {
//     ClassStr = 'row-qd';
//   }
//   if (row.tempDeleteFlag) {
//     ClassStr = 'temp-delete';
//   }

//   if (
//     row.sequenceNbr ==
//     renderedList.value[renderedList.value.length - 1]?.sequenceNbr
//   ) {
//     ClassStr += ' last-row';
//   }

//   return ClassStr;
// };

const cellStyle = ({ row, column }) => {
  if (['deCode'].includes(column.field)) {
    return {
      paddingLeft: row.customLevel * 12 + 'px',
    };
  }
};

const cellClassName = ({ $columnIndex, column, row }) => {
  let className = selectedClassName({ $columnIndex, column, row });
  if (column.field === 'deCode') {
    className += ` code-color Virtual-pdLeft${row.customLevel}`;
  } else if (column.field === 'index') {
    className += ' index-bg ';
  }

  // 批注提示
  if (column.field == 'deName' && row?.annotations) {
    className += ' note-tips ';
  }
  if (column.field === 'deCode' && row.kind !== '04') {
    const line = row.maxLine || 1;
    className += ` cell-line-break-${line} `;
  }
  // 添加默认两行类名
  if (
    [
      'qfCode',
      'measureType',
      'description',
      'costMajorName',
      'projectAttr',
    ].includes(column.field) ||
    (column.field === 'deCode' && row.kind === '04')
  ) {
    const line = row.maxLine || 2;
    className += ` cell-line-break-${line} `;
  }
  if (['qfCode', 'measureType', 'costMajorName'].includes(column.field)) {
    className += ` single-item `;
  }
  if (['projectAttr'].includes(column.field)) {
    className += ` projectAttr-item `;
  }
  return className;
};
const showModel = type => {
  isPriceModel.value = true;
  switch (type) {
    case 'zscy':
      // 装饰垂运
      showModelType.value = 'zscy';
      showPriceTitle.value = '设置装饰垂运';
      break;
    case 'zscg':
      // 装饰超高
      showModelType.value = 'zscg';
      showPriceTitle.value = '设置装饰超高降效';
      break;
    case 'azfy':
      // 安装费用
      showModelType.value = 'azfy';
      showPriceTitle.value = '安装费用计取';
      break;
    case 'csfy':
      // 自动计算措施费用
      showModelType.value = 'csfy';
      showPriceTitle.value = '自动计算措施费用';

      break;
  }
};

const saveCustomInput = (newValue, row, name, index) => {
  if (newValue) {
    row[name] = newValue;
  }
  console.log('newValue', newValue, 'row', row, 'name', name, 'index', index);
};

// 编辑弹框关闭方法
const editCancel = () => {
  isShowModel.value = false;
};

// 记取费用数据更新
const updateData = () => {
  isPriceModel.value = false;
  queryBranchDataById();
};

const closePriceModel = () => {
  isPriceModel.value = false;
};

const tableColumn = ref([
  { field: 'bdCodeLevel04', title: '项目编码' },
  { field: 'bdNameLevel04', title: '项目名称' },
  { field: 'unit', title: '单位' },
]);
const tableList = ref([]);

const keyupEvent = (row, value) => {
  // if (row.kind !== '03') return;
  if (row.kind !== '04') return;
  if (value.length > 1) {
    const $pulldown = pulldownRef.value;
    console.log('keyupEvent', $pulldown);
    if ($pulldown) {
      $pulldown?.showPanel();
    }
    searchQdByCode(value);
  }
};
const keyupAttrEvent = row => {
  if (row.kind !== '03' || row.projectAttr) return;
  const $pulldown = pulldownRefAttr.value;
  console.log('keyupEvent', $pulldown);
  if ($pulldown) {
    if (
      row.zjcsClassCode &&
      row.customParent?.itemCategory === '其他总价措施项目'
    ) {
      // 其他总价措施项目下zjcsClassCode没值为新插入定额不显示下拉
      $pulldown.hidePanel();
      return;
    }
    $pulldown?.togglePanel();
  }
};
// 计算基数点击右侧按钮
const calbaseQuantity = (
  closeEditor,
  { row, column },
  modelValue,
  oldval,
  late = 200
) => {
  setTimeout(() => {
    if (!calbaseModel.value) {
      editClosedEvent({ row, column }, modelValue, oldval);
      closeEditor();
    }
  }, late);
};
const calbaseCancelData = () => {
  calbaseModel.value = false;
  stableRef.value.closeEditor();
};
const calbaseSureData = v => {
  editClosedEvent(
    { row: currentInfo.value, column: { field: 'calculateBase' } },
    calbaseRef.value.value,
    currentInfo.value.calculateBase
  );
  stableRef.value.closeEditor();
  calbaseModel.value = false;
};
const timeCLoselQuantity = (
  closeEditor,
  { row, column },
  modelValue,
  oldval,
  late = 200,
  modelValueProxy
) => {
  setTimeout(() => {
    // 编辑时显示处理过小数位数的值
    // if(column.field == 'rate') {
    //   const res = blurEventPre(closeEditor, modelValueProxy, oldval);
    //   if (!res) return;
    // }
    if (!comModel.value && !quantityEditModalVisible.value) {
      editClosedEvent({ row, column }, modelValue, oldval);
      closeEditor();
    }
  }, late);
};

// 编辑时显示处理过小数位数的值
let formateTempValue = ref('');
const handleFocus = (modelValue, row, field) => {
  // const array = [{ property: 'rate', pathKey: 'EDIT_DE_FREERATE_PATH' }];
  // let fileArray = decimalLimitationPathArray(row, array);
  // const path = fileArray.find(
  //   item => item.property === field
  // )?.pathKey;
  // modelValue.value = decimalFormat(modelValue.value, path);
  // formateTempValue.value = modelValue.value;
};

const blurEventPre = (closeEditor, modelValue, oldval) => {
  if (modelValue.value == formateTempValue.value) {
    modelValue.value = oldval;
    formateTempValue.value = '';
    closeEditor();
    return false;
  }
  return true;
};
//颜色设置是否可点击
const handleColorSign = data => {
  let isAllDe = true;
  if (data?.length) {
    for (let i of selectState.selectedRowKeys) {
      let rowType = tableData.value.find(a => a.sequenceNbr == i)?.kind;
      // if (['00', '01', '02'].includes(rowType)) {
      //   isAllDe = false;
      //   break;
      // }
    }
  } else {
    isAllDe = false;
  }

  let options = operateList.value.find(
    item => item.name === 'rcj-color-sign'
  )?.options;
  options.forEach(item => {
    item.isValid = isAllDe;
  });
};

watch(
  () => selectState.selectedRowKeys,
  (newV, oldV) => {
    handleColorSign(newV);
  },
  {
    immediate: true,
  }
);
// 设置颜色
const changeRowColor = data => {
  let selectdata = toRaw(selectState.selectedRowKeys);
  let datalist = selectdata
    .map(item => {
      return tableData.value.find(a => {
        if (a.sequenceNbr == item && a.kind != '05') {
          return a;
        }
      })?.sequenceNbr;
    })
    .filter(item => item);
  let apiData = {
    levelType: projectStore.currentTreeInfo.type,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    deRowId: datalist,
  };
  if (data.activeKind) {
    console.log(data, currentInfo.value);
    apiData.color = 'default';
    if (data.activeKind !== 'default') {
      apiData.color = data.options.find(a => a.kind == data.activeKind).color;
    }
    console.log('666666666666', apiData);
    api.csxmSetDeColor(apiData).then(res => {
      console.log('666666666666', res);
      queryBranchDataById();
    });
  }
};
// 查找功能
let lookupVisible = ref(false);
const openLookup = event => {
  if (event) {
    if (event.ctrlKey && event.code === 'KeyF') {
      lookupVisible.value = true;
    }
  } else {
    lookupVisible.value = true;
  }
};

const lookupConfig = reactive({
  columns: [
    {
      field: 'deName',
      label: '名称',
      type: 'input',
      value: '',
      placeholder: '请输入名称',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'deCode',
      label: '编码',
      type: 'input',
      placeholder: '请输入编码',
      value: '',
      config: {
        allowClear: true,
      },
    },
    {
      field: 'unit',
      label: '单位',
      type: 'select',
      placeholder: '请输入或选择单位',
      value: null,
      config: {
        options: (projectStore.unitListString || []).split(',').map(item => {
          return { label: item, value: item };
        }),
        allowClear: true,
      },
    },
    {
      field: 'quantity',
      label: '工程量',
      type: 'area',
      startValue: '',
      endValue: '',
    },
    {
      type: 'areaList',
      icon: 'icon-danjia',
      name: '单价',
      index: 1,
      fieldList: [
        {
          field: 'price',
          label: '单价',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
        {
          field: 'RSum',
          label: '人工',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
        {
          field: 'CSum',
          label: '材料',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
        {
          field: 'JSum',
          label: '机械',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
      ],
    },
    {
      type: 'areaList',
      icon: 'icon-hejia',
      name: '合价',
      index: 2,
      fieldList: [
        {
          field: 'totalNumber',
          label: '合价',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
        {
          field: 'rTotalSum',
          label: '人工',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
        {
          field: 'cTotalSum',
          label: '材料',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
        {
          field: 'jTotalSum',
          label: '机械',
          startValue: '',
          endValue: '',
          config: {
            allowClear: true,
          },
        },
      ],
    },
  ],
  conditionType: 'OR',
  tableData: tableData,
});
// 查找确认回调
const lookupCallback = async (rows, selectedRow) => {
  if (!rows || !rows.length) {
    tableData.value = xeUtils.clone(originalTableData.value, true);
  } else {
    await handleNewTable(rows);
  }

  // if(tableData.value.length!==originalTableData.length){
  //   isFiltErate.value=true
  // }else{
  //   isFiltErate.value=false
  // }
  nextTick(() => {
    let info;
    if (selectedRow) {
      info =
        tableData.value?.find(
          item => item.sequenceNbr === selectedRow.sequenceNbr
        ) || tableData.value?.[0];
    } else {
      info = tableData.value && tableData.value[0];
    }
    if (!info) {
      return;
    }
    selectState.selectedRowKeys = [info.sequenceNbr];
    selectState.selectedRows = [info];
    stableRef.value.scrollTo(
      {
        rowKey: info.sequenceNbr,
      },
      'smooth'
    );
    currentInfo.value = info;
  });
};
const filterOperate = filter => {
  isFilter.value = filter;
};
// 过滤确认回调
const filtQuery = row => {
  tableData.value = row;
  // oldTableData.value = row;
  nextTick(() => {
    const info = row[0];
    selectState.selectedRowKeys = [info.sequenceNbr];
    selectState.selectedRows = [info];
    stableRef.value.scrollTo(
      {
        rowKey: info.sequenceNbr,
      },
      'smooth'
    );
    currentInfo.value = info;
    // 刷新详情左侧树
    // emits('updateMenuList');
  });
};
// 切换过滤选中
const changeCurrentInfo = async (row, tableList) => {
  console.log('row', row);
  console.log('table', tableData.value);
  if (tableList?.length) {
    await handleDataCustom(tableList);
  }
  nextTick(() => {
    if (row) {
      selectState.selectedRowKeys = [row.sequenceNbr];
      selectState.selectedRows = [row];
      stableRef.value.scrollTo(
        {
          rowKey: row.sequenceNbr,
        },
        'auto'
      );
      currentInfo.value = row;
    }
  });
};
// 切换组价方式
const changePricingMethod = (
  closeEditor,
  { row, column },
  modelValue,
  oldval,
  late = 200
) => {
  setTimeout(() => {
    if (!comModel.value) {
      closeEditor();
      infoMode.show({
        iconType: 'icon-querenshanchu',
        infoText:
          '切换组价方式将清空原组价方式的所有组价内容，不能再恢复，是否继续？',
        confirm: () => {
          editClosedEvent({ row, column }, modelValue, oldval);
          infoMode.hide();
        },
        close: () => {
          infoMode.hide();
        },
      });
    }
  }, late);
};

let quantityArea = ref();
const quantityEditeField = ref('');
const quantityEditModalTitle = ref('');
const quantityEditModalTextValue = ref('');
const quantityEditModalVisible = ref(false);

const quantityEdit = field => {
  quantityEditeField.value = field;
  quantityEditModalVisible.value = true;
  if (field === 'originalQuantity') {
    quantityEditModalTitle.value = '工程量编辑';
    quantityEditModalTextValue.value = currentInfo.value.originalQuantity;
  } else if (field === 'quantityExpression') {
    quantityEditModalTitle.value = '工程量表达式编辑';
    quantityEditModalTextValue.value = currentInfo.value.quantityExpression;
  }
};
const quantityEditModelsureData = () => {
  editClosedEvent(
    { row: currentInfo.value, column: { field: quantityEditeField.value } },
    quantityArea.value.value?.trim(),
    currentInfo.value.originalQuantity
  );
  stableRef.value.closeEditor();
  quantityEditModalVisible.value = false;
};

const quantityEditModelCLose = () => {
  quantityEditModalVisible.value = false;
  stableRef.value.closeEditor();
};

const comModelCLose = () => {
  comModel.value = false;
  stableRef.value.closeEditor();
};
const comModelsureData = v => {
  editClosedEvent(
    { row: currentInfo.value, column: { field: 'rate' } },
    v,
    currentInfo.value.rate
  );
  stableRef.value.closeEditor();
  comModel.value = false;
};
const cellClickEvent = ({ row }) => {
  if (!row.sequenceNbr) return;
  repeatCode.value.deStandardId = row.sequenceNbr;
  repeatCode.value.row = row;
  isClickRepeat.value = true;
  showRepeat.value = false;
};
// const cellClickEvent = ({ row }) => {
//   addCurrentInfo.value = row;
//   repeatCode.value.deStandardId = row.sequenceNbr;
//   repeatCode.value.row = row;
//   isClickRepeat.value = true;
//   showRepeat.value = false;
//   const unit = Array.isArray(row.unit) ? row.unit : row.unit?.split('/');
//   row.unit = unit;
//   isNameOpen.value = true;
//   const $pulldown = pulldownRef.value;
//   if ($pulldown) {
//     const $table = vexTable.value;
//     if ($table) {
//       console.log('22222222222222', $table);
//       isClearEdit.value = true;
//       $table.clearEdit();
//     }
//     if (row.unit && row.unit.length > 1) {
//       showUnitTooltip.value = true;
//     } else {
//       updateQdByCode(row.bdCodeLevel04, row.unit ? row.unit[0] : null);
//     }
//     $pulldown?.hidePanel();
//     isClearEdit.value = false;
//   }
// };

// 根据编码模糊搜索标准清单
const searchQdByCode = code => {
  api.searchQdByCode({ code: code }).then(res => {
    console.log('根据编码模糊搜索标准清单', res);
    if (res.status === 200 && res.result) {
      tableList.value = res.result;
    }
  });
};

// 通过标准编码插入清单
const updateQdByCode = (code, unit) => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    code: code,
    unit: unit,
    isSortQdCode: isSortQdCode.value,
  };
  api.updateQdByCode(apiData).then(res => {
    console.log('标准清单编码插入', res);
    if (res.status === 200 && res.result) {
      if (currentInfo.value.standardId) {
        message.success('清单操作成功');
      } else {
        message.success('清单操作成功');
      }
      selectUnit.value = '';
      queryBranchDataById();
    }
  });
};

const saveData = inputData => {
  if (bdCode.value) {
    updateQdByPage(inputData);
  } else {
    addBcQdData(inputData);
  }
};

const deSaveData = inputData => {
  if (bdCode.value) {
    addBcDeData(inputData);
  } else {
    updateDeByPage(inputData);
  }
};
const addBcDeData = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    deRowId: JSON.parse(JSON.stringify(bcDeRow.value)).sequenceNbr,
    userDe: JSON.parse(JSON.stringify(inputData)),
  };
  api.csxmAppendUserDe(apiData).then(res => {
    console.log('updateDeByPage', res);
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = bcDeRow.value.sequenceNbr;
      bdCode.value = '';
      bcDeRow.value = {};
      deVisible.value = false;
      message.success('补充定额新建成功');
      queryBranchDataById();
    }
  });
};
// 点击补充按钮补充定额数据
const updateDeByPage = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    prevDeRowId:
      currentInfo.value.kind != '05'
        ? currentInfo.value.sequenceNbr
        : currentInfo.value.parentId,
    userDe: JSON.parse(JSON.stringify(inputData)),
  };
  api.csxmAppendUserDeNextRow(apiData, inputData).then(res => {
    if (res.status === 200) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      bdCode.value = '';
      deVisible.value = false;
      message.success('补充定额新建成功');
      queryBranchDataById();
    }
  });
};
const rcjSaveData = inputData => {
  if ([94, 95].includes(currentInfo.value?.kind)) {
    // 主材设备走人材机明细添加
    addMxqBcRcjData(inputData, currentInfo.value, bdCode.value, () => {
      rcjVisible.value = false;
    });
    return;
  }
  if (bdCode.value) {
    spRcjByPage(inputData);
  } else {
    addBjqBcRcjData(inputData);
  }
};

// 通过修改编码补充清单替换当前行数据
const updateQdByPage = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 2,
  };
  console.log('补充清单参数', apiData);
  api.updateQdByPage(apiData, inputData).then(res => {
    console.log('通过界面信息插入清单', res);
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      bdCode.value = '';
      qdVisible.value = false;
      message.success('补充清单新建成功');
      queryBranchDataById();
    }
  });
};

// 点击补充按钮补充清单数据
const addBcQdData = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 2,
    rootLineId: '',
  };

  console.log('补充清单参数', apiData);
  api.addBcQdData(apiData).then(res => {
    console.log('通过界面信息插入清单', res);
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      bdCode.value = '';
      qdVisible.value = false;
      message.success('补充清单新建成功');
      queryBranchDataById();
    }
  });
};

// 判断输入的定额编码是否是标准定额
const isStandardDe = code => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    code: code,
  };
  api.isStandardDe(apiData).then(res => {
    if (res.status === 200) {
      if (res.result) {
        updateDeReplaceData(code);
      } else {
        infoMode.show({
          iconType: 'icon-querenshanchu',
          infoText: '标准定额库下未找到该定额，是否补充定额？',
          confirm: () => {
            deVisible.value = true;
            bdCode.value = code;
            infoMode.hide();
          },
          close: () => {
            infoMode.hide();
            currentInfo.value.deCode = currentInfo.value.originalDeCode;
          },
        });
      }
      console.log('判断输入的定额编码是否为主定额库编码', res);
    }
  });
};

// 分部分项 措施项目 替换定额数据
const updateDeReplaceData = code => {
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    code: code,
    type: 2,
  };
  console.log('通过标准编码插入定额', apiData);
  api.updateDeReplaceData(apiData).then(res => {
    console.log('通过标准编码插入定额结果', res);
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      deVisible.value = false;
      message.success('定额操作成功');
      queryBranchDataById();
    }
  });
};

// 分部分项 措施项目 补充界面替换人材机数据
const spRcjByPage = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 2,
    region: 0,
  };
  api.spRcjByPage(apiData).then(res => {
    console.log('1111111111', res);
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      message.success('人材机操作成功');
      rcjVisible.value = false;
      queryBranchDataById();
    }
  });
};
// 分部分项 措施项目 添加编辑区的人材机数据
const addBjqBcRcjData = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    unitId: projectStore.currentTreeInfo?.id,
    prevDeRowId:
      currentInfo.value.kind != '05'
        ? currentInfo.value.sequenceNbr
        : currentInfo.value.parentId,
    userResource: JSON.parse(JSON.stringify(inputData)),
  };
  api.csxmAppendUserResource(apiData, inputData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      rcjVisible.value = false;
      message.success('人材机新建成功');
      queryBranchDataById();
    }
  });
};

// 整体锁定
const allLock = () => {
  if (lockFlag.value) {
    csUnLockAll();
  } else {
    csLockAll();
  }
};

// 清单整体锁定
const csLockAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.unitLockAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单锁定成功');
      operateList.value.find(item => item.name === 'lock-subItem').label =
        '整体解锁';
      queryBranchDataById();
    }
  });
};

// 清单整体解锁
const csUnLockAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.unitUnLockAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单解锁成功');
      operateList.value.find(item => item.name === 'lock-subItem').label =
        '整体锁定';
      queryBranchDataById();
    }
  });
};

// 清单锁定
const csLockQd = () => {
  currentInfo.value.isLocked = 1;
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.csLockQd(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单锁定成功');
      queryBranchDataById();
    }
  });
};

// 清单解锁
const csUnLockQd = () => {
  currentInfo.value.isLocked = 0;
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.csUnLockQd(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单解锁成功');
      queryBranchDataById();
    }
  });
};

// 补充页面点击取消操作 type 1 清单 2 定额 3 人材机
const bcCancel = type => {
  if (type === 1) {
    qdVisible.value = false;
  } else if (type === 2) {
    deVisible.value = false;
  } else {
    rcjVisible.value = false;
  }
  if (bdCode.value) {
    currentInfo.value.deCode = currentInfo.value.originalDeCode;
  }
};

// 清单多单位时选择单位确定事件
const selectHandler = dataRef => {
  if (!selectUnit.value) {
    return message.warning('请选择单位');
  }
  showUnitTooltip.value = false;
  dataRef.unit = selectUnit.value;
  let obj = {
    bdCode: dataRef.bdCodeLevel04,
    bdName: dataRef.bdNameLevel04,
    sequenceNbr: dataRef.sequenceNbr,
    unit: dataRef.unit,
    quantityExpression: dataRef.quantityExpression,
    libraryCode: dataRef.libraryCode,
  };
  updateQdByCode(obj.bdCode, obj.unit);
};

// 获取当前点击行下挂所有数据
/**
 * 以前的这里也修改了用户选择的数据，用户选择了数据，然后又在这掉接口了，不知道为啥，待以后优化吧
 */
const queryAllDataByBranchId = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbr: currentInfo.value?.sequenceNbr,
    pageSize: 10000,
    pageNum: page.value,
  };
  api.itemSearchForSequenceNbr(apiData).then(async res => {
    console.log('================当前点击数据下挂所有数据', res);
    if (res.status === 200 && res.result) {
      // 后端给的所有的数据，需要过滤。
      const handleData =
        await frameSelectRef.value.frameSelectJs.filterAndRemoveInvalid(
          res.result
        );
      selectData.value.data = handleData;
      // 点击index事件 ，因为以前走的是公共处理方法，经过过滤之后没有数据了，默认不能复制了，所以需要在调接口之后处理下
      if (handleData.length) {
        selectData.value.isCopy = true;
      }
    }
  });
};

// 批量复制接口
const copyBranchDataQdDe = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbrs: selectData.value.data.map(i => i.sequenceNbr),
  };
  console.log('批量复制接口参数', apiData);
  let isAwfData = false;
  let awfObj = tableData.value.filter(x => x.constructionMeasureType === 2)[0];
  selectData.value.data.forEach(item => {
    awfObj.children.forEach(child => {
      if (item.sequenceNbr === child.sequenceNbr) {
        isAwfData = true;
      }
    });
  });
  if (isAwfData) {
    message.error('安全生产、文明施工费下挂数据不可进行复制操作~');
    return;
  }
  api.copyBranchDataQdDe(apiData).then(res => {
    console.log('res', res);
    if (res.status === 200 && res.result) {
      message.success('已复制');
    }
  });
};

// 批量删除
const delBatchData = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    deRowIdList: JSON.parse(JSON.stringify(selectState.selectedRowKeys)),
  };
  let index = -1;
  if (selectState.selectedRowKeys.length > 0) {
    let newTableData = JSON.parse(JSON.stringify(tableData.value));
    let newSelectData = newTableData
      .filter(a => selectState.selectedRowKeys.includes(a.sequenceNbr))
      .sort((a, b) => a?.index < b?.index)
      .map(a => a.sequenceNbr);
    index = tableData.value.findIndex(x => x.sequenceNbr === newSelectData[0]);
  }
  api.csxmBatchDeleteDe(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('批量删除成功');
      deleteVisible.value = false;
      deleteLoading.value = false;
      existRelationDe2.value = false;
      existRelationDe.value = false;
      queryBranchDataById('noPosition', '', true, index);
    } else {
      message.error(res.message);
      deleteVisible.value = false;
      deleteLoading.value = false;
      existRelationDe2.value = false;
      existRelationDe.value = false;
    }
  });
};

// 措施项目定额上移下移
const moveDeData = ({ state, type }) => {
  console.log(state, type);
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    upId: projectStore.currentTreeInfo?.id,
    selectId: currentInfo.value.sequenceNbr,
    operateAction: state === 1 ? 'up' : 'down',
    type: 'csxm',
  };
  if (type === 'move') {
    api.moveDeData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        message.success('移动成功');
        emits('updateMenuList');
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        queryBranchDataById();
      }
    });
  }
};

// 组价方案匹配条件筛选
const filterData = val => {
  let tempList = [];
  tableData.value = [];
  if (val.length === 0 || !val) {
    tableData.value = originalTableData.value;
    tableData.value.forEach((item, index) => {
      item.index = (page.value - 1) * limit.value + (index + 1);
    });
  } else {
    originalTableData.value.forEach(item => {
      if (val.includes(item.matchStatus)) {
        tempList.push(item.sequenceNbr);
      }
    });
    for (let i = 0; i < originalTableData.value.length; i++) {
      if (
        tempList.includes(originalTableData.value[i].sequenceNbr) ||
        tempList.includes(originalTableData.value[i].parentId)
      ) {
        tableData.value.push(originalTableData.value[i]);
      }
    }
    tableData.value.forEach((item, index) => {
      item.index = (page.value - 1) * limit.value + (index + 1);
    });
    // tableData.value = originalTableData.value.filter(x =>
    //   val.includes(x.matchStatus)
    // );
  }
  // const initList = init(tableData.value);
  // nextTick(() => {
  //   initList();
  // });
};

const zrMb = () => {
  page.value = 1;
  if (!projectStore.isAutoPosition) {
    // 不是自动定位的才调用接口
    queryBranchDataById('other');
  }
  // nextTick(() => {
  //   initVirtual(vexTable.value);
  // });
};

projectStore.measuresItemGetList = zrMb;

// 复用组价
let ReuseGroupPriceRef = ref(null);

const openReuseGroupPrice = ({ activeKind }) => {
  if ([0, 1, 2].includes(activeKind)) {
    ReuseGroupPriceRef.value.open(activeKind);
  }
};

// 清单快速组价
const qdQuickPricingRef = ref(null);
const openQdQuickPricing = () => {
  qdQuickPricingRef.value.open('csxm');
};

const updateDataColorColl = data => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitWorkId: projectStore.currentTreeInfo?.id,
    idList: JSON.parse(JSON.stringify(selectState.selectedRowKeys)),
    column: 'color',
    value: data.activeKind,
  };
  console.log('apiData', apiData);
  api.updateDataColorCsxmColl(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('颜色设置成功');
      projectStore.isRefreshProjectTree = true;
      queryBranchDataById();
    }
  });
};
// 费率
let showPull = ref(false);
let editRow = ref(null);
const toggleEvent = row => {
  editRow.value = row;
  showPull.value = !showPull.value;
};

const onUseRate = v => {
  editRow.value.rate = v;
  itemUpdate(editRow.value, 'rate', 'rate');
  closeEditor();
};
const {
  showColumns,
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns({
  type: 1004,
  initCallback: () => {
    renderLine();
  },
  initColumnsCallback: () => {
    initColumns({
      columns: getTableColumns(emits, 'csxm'),
      pageName: 'csxm',
    });
  },
});
// 页面列设置
let columnSettingRef = ref();
const showPageColumnSetting = () => {
  columnSettingRef.value.open();
};

const insertZmDe = (zmDeList, zmVariableRuleList) => {
  zmDeList?.sort((a, b) => a.dispNo - b.dispNo);
  let pointLine = JSON.parse(JSON.stringify(currentInfo.value));
  if (pointLine.kind == '03') {
    pointLine.parentId = pointLine.sequenceNbr;
  }
  let row = currentInsertRow.value || currentInfo.value;
  let params = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine,
    kind: row.kind,
    indexId: row.sequenceNbr,
    unit: row.unit,
    rcjFlag: currentInsertIsRcj.value == 1 ? 0 : 1,
    libraryCode: row.libraryCode,
    fbfxOrCsxm: 'csxm',
    zmDeList,
    fDeId: currentInfo.value.sequenceNbr,
    zmVariableRuleList,
  };
  console.log('插入子目定额数据参数', params);
  // 需要转一下，不然走不进后端接口
  const apiParams = JSON.parse(JSON.stringify(params));
  api.insertZmDeMeasuresItem(apiParams).then(async res => {
    if (res) {
      console.log('返回res 刷新页面数据', res);
      addZmlistRes.deList = res.result.deList;
      addZmlistRes.conversionList = res.result.conversionList;
      modalPop();
      queryBranchDataById('noPosition');
      associateSubQuotasVisible.value = false;
    }
  });
};
const standardPop = () => {
  const standardPop = projectStore.convenienceSettings.get(
    'STANDARD_CONVERSION'
  );
  if (standardPop && currentZmDe.value.conversion) {
    standardVisible.value = true;
  } else {
    modalPop();
  }
};

const modalPop = () => {
  if (addZmlistRes.deList?.length) {
    currentZmDe.value = addZmlistRes.deList.shift();
    currentZmDe.value = currentZmDe.value;
    addDeInfo.value = currentZmDe.value;
    currentZmDe.value.conversion =
      addZmlistRes.conversionList?.find(
        item => item.deId === currentZmDe.value.sequenceNbr
      ) || [];
    const showUnpricedPop =
      projectStore.convenienceSettings.get('UNPRICED')?.unPricedPop;
    if (showUnpricedPop && currentZmDe.value.unPriced?.length) {
      mainMaterialTableData.value = currentZmDe.value.unPriced;
      materialVisible.value = true;
    } else {
      standardPop();
    }
  } else {
    currentZmDe.value = null;
  }
};
// 更新子目关联数据
const updateZmData = data => {
  updateZmAssociateData(data);
};

// 基期价、市场价为“-
const isChangeAva = row => {
  return (
    row.isDataTaxRate !== null &&
    row.isDataTaxRate !== undefined &&
    Number(row.isDataTaxRate) == 0
  );
};

defineExpose({
  handlerColumns,
  updateColumns,
  queryBranchDataById,
  getTableData: zrMb,
  copyAndPaste,
  posRow,
  getDefaultColumns,
});
</script>

<style lang="scss" src="@/assets/css/subItemProject.scss" scoped></style>
<style lang="scss" scoped>
@import './s-table.scss';
.code-line-break {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.association-selected {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 100;
}
.normal-info .code-flag {
  color: #a73d3d;
}
.code-black {
  color: #2a2a2a;
}
.flag-green {
  background: #90c942;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.flag-orange {
  background: #ffaa09;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.flag-red {
  background: #de3f3f;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.multiple-select {
  width: 35px;
  height: 16px;
  line-height: 16px;
  margin-left: -10px;
  //text-indent: 10px;
  cursor: pointer;
}
.subItem-project {
  background: #ffffff;
  height: 100%;
  .head {
    display: flex;
    align-items: center;
    // height: 40px;
    padding: 0 10px;
  }
  .table-content {
    // height: calc(65%);
    height: 100%;
    //user-select: none;

    ::v-deep(.vxe-table .row-unit) {
      background: #e6dbeb;
    }
    ::v-deep(.vxe-table .row-sub) {
      background: #efe9f2;
    }
    ::v-deep(.vxe-table .row-qd) {
      background: #dce6fa;
    }
    ::v-deep(.vxe-body--row.row--current) {
      background: #a6c3fa;
    }
    ::v-deep(.vxe-table .row-qd .code-color) {
      color: #ce2929;
      .vxe-tree-cell {
      }
    }
    ::v-deep(.vxe-table .normal-info .code-color) {
      color: #ce2929;
      .vxe-tree-cell {
        // display: -webkit-box;
        // -webkit-line-clamp: 2;
        // -webkit-box-orient: vertical;
        // overflow: hidden;
        // text-overflow: ellipsis;
        //max-height: 3.0em; /* 高度为字体大小的两倍 */
        //line-height: 1.5em; /* 行高 */
        //height: auto; /* 高度为行高的两倍 */
      }
    }
    ::v-deep(.vxe-table .index-bg) {
      background-color: #ffffff;
    }
    ::v-deep(.vxe-table .temp-delete) {
      background: #f3f2f3;
      color: #a7a7a7;
      text-decoration: line-through;
    }
    ::v-deep(
        .vxe-table--render-default.is--tree-line
          .vxe-body--row
          .vxe-body--column
      ) {
      background-image: linear-gradient(#b9b9b9, #b9b9b9),
        linear-gradient(#b9b9b9, #b9b9b9);
    }
    ::v-deep(.vxe-table--render-default.is--tree-line .vxe-header--column) {
      background-image: linear-gradient(#b9b9b9, #b9b9b9),
        linear-gradient(#b9b9b9, #b9b9b9);
    }
  }
  .quota-content {
    // height: 35%;
    height: 100%;
    overflow: hidden;
    &:hover {
      overflow: auto;
    }
    //user-select: none;
  }
  .project-attr {
    white-space: pre-wrap;
    text-align: left;
  }
}
.content {
  margin-bottom: 20px;
}
.edit-content {
  margin-bottom: 20px;
}
.my-dropdown4 {
  width: 600px;
  height: 200px;
  background-color: #fff;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
}

.unit-radio {
  position: absolute;
  top: 50%;
  right: 50%;
  z-index: 999;
  padding: 15px;
  box-shadow: 0 0 15px -6px rgba(0, 0, 0, 0.4);
  width: 120px;
  background: #fff;
  .title {
    color: #333;
  }
}
.reCheck {
  margin-top: 10px;
}
.code {
  margin-left: 8px;
  padding: 6px 4px;
}
.custom-header .icon-close {
  right: 20px;
  background: #ffffff;
  z-index: 20;
  padding: 3px;
}
.btns {
  position: absolute;
  width: 200px;
  bottom: -20px;
  right: 40%;
  display: flex;
  justify-content: space-around;
}
.note-tips-csxm {
  position: relative;
  &::after {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid rgba(167, 61, 61, 1);
    top: -7px;
    right: -10px;
    z-index: 8;
    cursor: pointer;
    transform: rotate(44deg);
    position: absolute;
  }
}
</style>
