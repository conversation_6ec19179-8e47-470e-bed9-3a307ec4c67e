<!--
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-08-10 19:09:44
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-03-05 10:51:10
-->
<template>
  <Modal
    v-model:modelValue="props.infoVisible"
    :showHeader="false"
    className="info-modal-popup"
    :mask="true"
  >
    <div class="content">
      <icon-fonts
        v-if="isFunction"
        class="icon-font"
        :type="props.iconType || 'icon-querenshanchu'"
      ></icon-fonts>
      <icon-font
        v-else
        class="icon-font"
        :type="props.iconType || 'icon-querenshanchu'"
      ></icon-font>
      <div class="desc">
        <p class="info">{{ props.infoText }}</p>
        <p
          class="desc-info"
          v-if="props.descText"
          :style="props.descTextStyle"
        >
          {{ props.descText }}
        </p>
      </div>
    </div>
    <div
      class="footer-list"
      v-if="isFunction && props.isDelete"
    >
      <a-button
        type="primary"
        @click="onHandleConfirm"
        danger
      >删除</a-button>
      <a-button @click="onHandleClose">取消</a-button>
    </div>
    <div
      class="footer-list"
      v-else-if="isFunction && !props.isDelete"
    >
      <a-button
        v-if="!props.isSureModal"
        @click="onHandleClose"
      >{{
        props.cancelText
      }}</a-button>
      <a-button
        v-if="!!showCustom.name"
        :type="showCustom.type"
        @click="onCustomFunc"
      >{{ showCustom.name }}</a-button>
      <a-button
        type="primary"
        @click="onHandleConfirm"
      >{{
        props.confirmText
      }}</a-button>
    </div>
    <div
      class="footer-list"
      v-else
    >
      <a-button
        v-if="!props.isSureModal"
        @click="close"
      >{{
        props.cancelText
      }}</a-button>
      <a-button
        v-if="!!showCustom.name"
        :type="showCustom.type"
        @click="onCustomFunc"
      >{{ showCustom.name }}</a-button>
      <a-button
        type="primary"
        @click="onSubmit"
      >{{
        props.confirmText
      }}</a-button>
    </div>
  </Modal>
</template>

<script setup>
import { Modal } from 'vxe-table';
import { propTypes } from '@/utils/propTypes';
import { createFromIconfontCN } from '@ant-design/icons-vue';

const props = defineProps({
  test: propTypes.string.def(''),
  infoVisible: propTypes.bool.def(false),
  infoText: propTypes.string.def(''),
  descText: propTypes.string.def(''),
  descTextStyle: propTypes.object.def({}),
  confirmText: propTypes.string.def('确认'), //确定按钮文字--因个别需要显示备份 检测等字样-类似bug14039
  cancelText: propTypes.string.def('取消'),
  iconType: propTypes.string.def(''),
  isSureModal: propTypes.bool.def(false),
  isDelete: propTypes.bool.def(false),
  isFunction: propTypes.bool.def(false), //是否是函数式调用的
  onHandleClose: propTypes.func.def(() => {}),
  onHandleConfirm: propTypes.func.def(() => {}),
  showCustom: propTypes.object.def({
    name: '',
    type: '',
    callBack: () => {
      console.log('确定');
    },
  }),
  onCustomFunc: propTypes.func.def(() => {}),
});

const emits = defineEmits(['update:infoVisible', 'updateCurrentInfo']);

const close = () => {
  emits('update:infoVisible', false);
};
const onSubmit = () => {
  if (props.isSureModal && props.iconType === 'icon-ruotixing') {
    emits('updateCurrentInfo', 1);
  } else if (props.isSureModal) {
    emits('update:infoVisible', false);
  } else {
    emits('updateCurrentInfo', 2);
  }
};

let IconFonts;
const init = () => {
  if (props.isFunction) {
    IconFonts = createFromIconfontCN({
      scriptUrl: '//at.alicdn.com/t/c/font_4199803_in625nz60q8.js',
    });
  }
};
init();
</script>
<style lang="scss">
.info-modal-popup {
  .vxe-modal--content {
    padding: 0 !important;
  }
}
</style>
<style lang="scss" scoped>
.content {
  display: flex;
  padding: 30px 30px 30px 30px;
  text-align: left;
  font-size: 14px;
  color: #000000;
  min-width: 306px;
  max-width: 600px;
  p {
    font-size: 14px;
    line-height: 17px;
    color: #000000;
    line-height: 1.6;
  }
  .desc-info {
    margin-top: 8px;
    color: #333333;
  }
  .icon-font {
    font-size: 18px;
    margin-right: 10px;
    line-height: 1.5;
  }
}
.footer-list {
  margin-bottom: 32px;
  text-align: center;
  button + button {
    margin-left: 15px;
  }
}
</style>
