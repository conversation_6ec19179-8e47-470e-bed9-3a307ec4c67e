<!--
 * @Descripttion: 内容侧边菜单列表
 * @Author: renmingming
 * @Date: 2023-05-17 10:44:13
 * @LastEditors: wangru
 * @LastEditTime: 2025-04-18 15:54:57
-->
<template>
  <div
    class="aside-menu-list"
    style="position: relative;height: 100%"
    :style="`display:${props.isDisplay}`"
  >
    <div
      :class="['aside-list', isContract ? 'aside-contract' : 'aside-scroll']"
      :style="asideStyle"
      @click="titleContractHandle"
    >
      <div class="title">
        <icon-font
          class="icon"
          v-show="!isContract"
          type="icon-xiangmugaikuang"
        />{{ props.title }}
      </div>
      <!-- <ul class="menu-list" v-if="!isTreeData" v-show="!isContract">
        <li
          v-for="(item, index) in props.menuList"
          :key="item.name"
          @click="handleSelect(item, index)"
          :class="{ on: categoryIndex === index }"
        >
          <a-tooltip placement="right" :title="item.name">
            <span class="name-content">
              <icon-font
                class="icon-fee"
                v-if="
                  item.defaultFeeFlag === 1 &&
                  store.currentTreeInfo.levelType === 3
                "
                type="icon-zhufeiyongwenjianbiaoshi"
              />
              {{ item.name }}</span
            >
          </a-tooltip>
        </li>
      </ul> -->
      <!-- 增加右键功能 -->
      <!-- height="calc(100% - 35px)" -->
      <vxe-table
        v-if="!isTreeData && !isHumanData"
        v-show="!isContract"
        align="left"
        :loading="loading"
        :column-config="{ resizable: true }"
        :row-config="{ isCurrent: true, keyField: 'key', height: 25 }"
        :data="props.menuList"
        height="auto"
        ref="asideListTable"
        border="none"
        keep-source
        :menu-config="menuConfig"
        @menu-click="contextMenuClickEvent"
        @current-change="handleSelect"
        :show-header="false"
        show-overflow="tooltip"
      >
        <vxe-column
          field="name"
          width="100%"
          title=""
        >
          <template #default="{ row }">
            <span class="name-content">
              <icon-font
                class="icon-fee"
                v-if="
                  Number(row.defaultFeeFlag) === 1 &&
                  store.currentTreeInfo.levelType === 3
                "
                type="icon-zhufeiyongwenjianbiaoshi"
              />
              {{ row.name }}</span>
          </template>
        </vxe-column>
      </vxe-table>
      <div
        class="custom-menu"
        v-if="isHumanData"
        v-show="!isContract"
      >
        <div
          v-for="(item, index) in props.menuList"
          :key="index"
        >
          <div>
            <icon-font
              class="icon"
              v-if="item.key === '0'"
              type="icon-xiangmugaikuang"
            />
            <span
              class="menu-title"
              @click="handleSelect({ row: item, rowIndex: index })"
              :class="{ on: categoryIndex === index }"
            >{{ item.name }}</span>
            <div class="frequency-list">
              <span
                v-for="(child, childIndex) in item?.defaultFeeFlag
                  ?.frequencyList"
                @click.stop="handleChild(item, child, index, childIndex)"
                :key="childIndex"
                :class="{
                  checked: childIndex === stageIndex && categoryIndex === index,
                  numStyle: Array.isArray(child.scope),
                  numChecked:
                    childIndex === stageIndex &&
                    categoryIndex === index &&
                    Array.isArray(child.scope),
                }"
              >{{ child.num }}</span>
            </div>
          </div>
        </div>
      </div>
      <div
        class="selectTree"
        v-if="isTreeData"
        v-show="!isContract"
      >
        <a-directory-tree
          v-model:expandedKeys="expandedkeys"
          v-model:selectedKeys="selectedKeys"
          :tree-data="treeList"
          :draggable="isTreeData"
          block-node
          @dragenter="onDragEnter"
          @drop="onDrop"
          @dragover="onDragOver"
          @dragstart="dragstart"
          expandAction="false"
          @select="selectChildren"
          @rightClick="menuOpenChange"
          :field-names="{
            title: 'bdName',
            children: 'childTreeModel',
            key: 'sequenceNbr',
          }"
          class="table-scrollbar"
        >
          <!--          <template #icon="{ key }">-->
          <!--            <template v-if="key !== '0-0'"></template>-->
          <!--          </template>-->
          <template #title="item">
            <a-dropdown
              :trigger="['contextmenu']"
              v-if="contextMenuList?.length"
            >
              <div style="height: 100%;">{{ item.bdName }}</div>
              <template #overlay>
                <a-menu @click="({ key: menuKey }) => onContextMenuClick(item, menuKey)">
                  <a-menu-item
                    v-for="item of contextMenuList"
                    :key="item.value"
                    :disabled="item.disabled"
                  >{{ item.label }}</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <a-tooltip
              placement="bottom"
              v-else
            >
              <template #title>
                <span>{{ item.bdName }}</span>
              </template>
              <span class="show-name">{{ item.bdName }}</span>
            </a-tooltip>
          </template>
        </a-directory-tree>
      </div>
    </div>
    <div class="btnExpand">
      <div
        class="btn"
        :style="{ right: isContract ? '-7px' : '11px' }"
        @click.stop="contractHandle"
      >
        <img
          :src="isContract ? getUrl('expandnew.png') : getUrl('retractnew.png')"
          alt=""
        />
      </div>
    </div>
  </div>
  <SetClassification
    v-model:visible="classificationVisible"
    :asideMenuList="props.menuList"
    @successCallback="emits('updateMenuList')"
  />
</template>

<script setup>
import {
  ref,
  computed,
  watch,
  reactive,
  nextTick,
  getCurrentInstance,
} from 'vue';
import { projectDetailStore } from '../../../store/projectDetail';
import { getUrl } from '@/utils/index';
import feePro from '@/api/feePro';
import { message } from 'ant-design-vue';
import xeUtils from 'xe-utils';
import api from '@/api/projectDetail.js';
import operateList from './operate';
import infoMode from '@/plugins/infoMode.js';
import csProject from '@/api/csProject';

import {
  SetClassification,
  classificationVisible,
  openClassification,
  removeClassification,
} from '@/views/projectDetail/customize/HumanMachineSummary/setClassification.js';
import detailApi from '@/api/projectDetail.js';
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
const expandedkeys = ref(['0-0']);
const selectedKeys = ref(['1664092215682068481']);
const treeList = ref([]);

const store = projectDetailStore();
const props = defineProps({
  title: {
    type: String,
    default: '项目概况',
    // default: "其他项目",
  },
  isTreeData: {
    type: Boolean,
    default: false,
  },
  menuList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  updateStatus: {
    type: Boolean,
    default: false,
  },
  isDisplay: {
    type: String,
    default: 'black',
  },
  isHumanData: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits([
  'currentMenu',
  'update:updateStatus',
  'setMainFile',
  'updateMenuList',
]);
let dragNode = ref(null);
let isContract = ref(false); // 是否收缩
let categoryIndex = ref(0);
let stageIndex = ref(null);
const asideStyle = computed(() => {
  return {
    width: !isContract.value ? '100%' : '36px',
    // width: !isContract.value ? '188px' : '36px',
  };
});
watch(
  () => store.currentTreeInfo,
  () => {
    categoryIndex.value = 0;
  }
);
watch(
  () => store.tabSelectName,
  () => {
    if (props.isTreeData) {
      selectedKeys.value[0] = props.menuList[0]?.sequenceNbr;
      // expandedkeys.value[0] = props.menuList[0]?.sequenceNbr;

      expandOrNot();
    } else if (props.isHumanData) {
      categoryIndex.value = 0;
      stageIndex.value = null;
    } else {
      categoryIndex.value = 0;
    }
  }
);
watch(
  () => props.menuList,
  () => {
    console.log('props.menuList', props.menuList, !props.updateStatus);
    if (props.isTreeData) {
      treeList.value = xeUtils.clone(props.menuList, true);
      getContextMenuList();
      console.log('treeList', treeList.value);
      if (!props.updateStatus) {
        selectedKeys.value[0] = props.menuList[0]?.sequenceNbr;
        // expandedkeys.value[0] = props.menuList[0]?.sequenceNbr;
        // 是否展开
        expandOrNot();

        store.SET_ASIDE_MENU_CURRENT_INFO(props.menuList[0]);
        emits('update:updateStatus', false);
      } else {
        emits('update:updateStatus', false);
      }
      console.log('selectedKey', selectedKeys, props.updateStatus);
    } else if (props.isHumanData && !props.updateStatus) {
      categoryIndex.value = 0;
      stageIndex.value = null;
      store.SET_ASIDE_MENU_CURRENT_INFO(props.menuList[0]);
    } else if (props.isHumanData && props.updateStatus) {
      let currentMenu = props.menuList.filter(
        x => x.key === store.asideMenuCurrentInfo?.key
      )[0];
      store.SET_ASIDE_MENU_CURRENT_INFO(currentMenu);
      emits('update:updateStatus', false);
    } else if (props.updateStatus && store.tabSelectName === '人材机调整') {
      let currentMenu = props.menuList.filter(
        x => x.key === store.asideMenuCurrentInfo?.key
      )[0];
      store.SET_ASIDE_MENU_CURRENT_INFO(currentMenu);
      emits('update:updateStatus', false);
    } else {
      emits('update:updateStatus', false); // 人材机调整下为true，切换到分部分项未只改为false，导致分部分项列无数据
      store.SET_ASIDE_MENU_CURRENT_INFO(props.menuList[0]);
      nextTick(() => {
        if (!props.isTreeData && !props.isHumanData && !isContract.value)
          setMenuIsClick();
      });
    }
  },
  { deep: true }
);
// 是否展开的逻辑
const expandOrNot = () => {
  expandedkeys.value = [];
  function factorial(list) {
    for (let i = 0; i < list.length; i++) {
      if (list[i].displaySign == '1') {
        expandedkeys.value.push(list[i].sequenceNbr);
        if (
          Array.isArray(list[i].childTreeModel) &&
          list[i].childTreeModel.length > 0
        ) {
          factorial(list[i].childTreeModel);
        }
      } else if (list[i].displaySign == '2' || list[i].displaySign == '0') {
        break;
      }
    }
  }
  factorial(props.menuList);
};

/**
 * 设置level
 * @param {s} data
 * @param {*} level
 */
const setTreeLevel = (data, level = -1) => {
  level = level + 1;
  return data.map(item => {
    item.level = level;
    if (item.childTreeModel?.length) {
      item.childTreeModel = setTreeLevel(item.childTreeModel, level);
    }
    return item;
  });
};
// 右键菜单处理================
let contextMenuList = ref([]);
const getContextMenuList = () => {
  let list = [];
  if (store.tabSelectName === '分部分项') {
    list = [
      {
        label: '显示一级分部',
        value: 1,
        disabled: false,
      },
      {
        label: '显示二级分部',
        value: 2,
        disabled: false,
      },
      {
        label: '显示三级分部',
        value: 3,
        disabled: false,
      },
      {
        label: '显示四级分部',
        value: 4,
        disabled: false,
      },
      {
        label: '删除当前分部',
        value: 0,
        disabled: false,
      },
    ];
  }
  contextMenuList.value = list;
};

/**
 * 展开到 状态层级置灰
 */
const expansionLevelDisabled = async (dataOptions, node) => {
  try {
    let apiData = {
      constructId: store.currentTreeGroupInfo?.constructId,
      singleId: store.currentTreeGroupInfo?.singleId,
      unitId: store.currentTreeInfo?.id,
      pageType: 'fbfx',
    };
    console.log('getFbDeep-apiData', apiData);
    let res = await detailApi.getFbDeep(apiData);
    console.log('getFbDeep-apiData返回值', res);
    if (res.status === 200 && Array.isArray(dataOptions.value)) {
      const validKinds = ['1', '2', '3', '4'].map(Number); // 将字符串数组转换为数字数组以进行比较
      const maxKind = Number(res.result); // 将结果转换为数字
      // 更新选项的有效性
      const { kind, optionMenu, isLocked } = node;
      dataOptions.value.forEach(item => {
        if (
          Number(item.value) !== 0 &&
          validKinds.includes(Number(item.value))
        ) {
          // 假设 item.kind 是数字类型或可以安全转换为数字
          item.disabled = !(Number(item.value) <= maxKind);
        }
        // 单位工程，除去删除都可点击
        else if (Number(item.value) === 0) {
          item.disabled = !(
            (optionMenu.includes(4) || optionMenu.includes(5)) &&
            !isLocked
          );
        }
      });
    }
  } catch (error) {
    console.error('置灰过程中出现错误:', error);
  }
};
const menuOpenChange = async ({ event, node }) => {
  const { kind, optionMenu, isLocked, key } = node;
  console.log(node);
  selectedKeys.value = [key];
  selectChildren(selectedKeys.value, { node }); //需求 要右键功能弹出时，且要选中
  await expansionLevelDisabled(contextMenuList, node);
  // contextMenuList.value = contextMenuList.value.map(item => {
  //   // 单位工程，除去删除都可点击
  //   if (Number(item.value) === 0){
  //     item.disabled = !((optionMenu.includes(4) || optionMenu.includes(5)) && !isLocked);
  //   }
  //   // if (Number(item.value) === 0 && Number(kind) === 0){
  //   //   item.disabled = true;
  //   // }else if (Number(item.value) === 0){
  //   //   item.disabled = false;
  //   // }
  //   // if (kind != 0 && (item.value > kind || item.value === 0)) {
  //   //   item.disabled = false;
  //   // }
  //   return item;
  // });
};

const onContextMenuClick = (treeNode, menuKey) => {
  const { sequenceNbr } = treeNode;
  bus.emit('asideMenuRightClickHandler', {
    name: 'subItemProject',
    data: { sequenceNbr, menuKey, treeNode, expandOrNot },
    menuKey,
  });
};
const currentSelectedMenu = name => {
  if (!props.isTreeData) {
    setTimeout(() => {
      currentSelectedMenu(name);
    }, 1000);
    return;
  }
  if (props.menuList) {
    selectedKeys.value[0] = props.menuList[0].childTreeModel.filter(
      x => x.bdName === name
    )[0]?.sequenceNbr;
    expandedkeys.value[0] = props.menuList[0].childTreeModel.filter(
      x => x.bdName === name
    )[0]?.sequenceNbr;
    emits('currentMenu', { key: selectedKeys.value[0] });
  }
  console.log('侧边菜单栏进来不', selectedKeys.value);
};

const contractHandle = () => {
  isContract.value = !isContract.value;
};
const titleContractHandle = () => {
  if (isContract.value) {
    isContract.value = false;
  }
};
let unifyData = ref(); //统一应用按钮是否禁用---工程项目人材机汇总和取费表更改的豪华切换侧边栏需要保存
const saveHumanData = oldVal => {
  unifyData.value = operateList.value.find(
    item => item.name === store.humanUpdataData.name
  );
  let infoText =
    store.humanUpdataData.name === 'unify'
      ? '当前取费设置中存在费率或政策文件变更，是否统一修改并应用至所有单位工程？'
      : '人材机数据已修改，是否应用整个工程项目?';
  infoMode.show({
    isSureModal: false,
    iconType: 'icon-querenshanchu',
    infoText,
    confirm: () => {
      store.humanUpdataData?.name === 'unify'
        ? feeTotalSave(oldVal)
        : humanSave(oldVal);
      infoMode.hide();
    },
    close: () => {
      resetHumanData(oldVal);
      unifyData.value.disabled = true;
      infoMode.hide();
    },
  });
};
const resetHumanData = oldVal => {
  asideListTable.value.setCurrentRow(oldVal); //人材机数据有需要保存的，先确定是否要先保存
  store.SET_HUMAN_UPDATA_DATA(null);
  store.SET_ASIDE_MENU_CURRENT_INFO(oldVal);
  store.SET_CURRENT_STAGE_INFO(null);
  if (store.tabSelectName === '取费表') {
    store.SET_Fee_With_Drawal_Info(oldVal);
  }
  emits('currentMenu', oldVal);
};
const humanSave = oldVal => {
  let apiData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    constructProjectRcjList: JSON.parse(
      JSON.stringify(store.humanUpdataData.updataData)
    ),
    unitIdList: [...store.humanUpdataData.unitIdList],
  };
  csProject.changeRcjConstructProject(apiData).then(res => {
    console.log('asideTree统一应用接口返回结果', res, apiData);
    if (res.status === 200) {
      resetHumanData(oldVal);
      unifyData.value.disabled = true;
    }
  });
};
const feeTotalSave = oldVal => {
  if (store.humanUpdataData.updataData.policy) {
    feePro
      .checkPolicyDocument(
        JSON.parse(JSON.stringify(store.humanUpdataData.updataData.policy))
      )
      .then(res => {});
  }
  if (store.humanUpdataData.updataData.feeTotal) {
    feePro
      .unifiedUse(
        JSON.parse(JSON.stringify(store.humanUpdataData.updataData.feeTotal))
      )
      .then(res => {});
  }
  if (store.humanUpdataData.updataData.calEditData) {
    let apiData = {
      constructId:
        store.currentTreeInfo.levelType === 1
          ? store.currentTreeInfo?.id
          : store.currentTreeGroupInfo?.constructId,
      feeCalculateBaseList: JSON.parse(
        JSON.stringify(store.humanUpdataData.updataData.calEditData)
      ),
    };
    feePro.updateProjectUnitCalculateBaseApply(apiData).then(res => {
      if (res.status === 200) {
        console.log('updateProjectUnitCalculateBaseApply2', res, apiData);
      }
    });
  }
  resetHumanData(oldVal);
  unifyData.value.disabled = true;
};
const handleSelect = ({ row, rowIndex }) => {
  categoryIndex.value = rowIndex;
  console.log('row', row, rowIndex);
  if (store.humanUpdataData && store.humanUpdataData.isEdit) {
    saveHumanData(row);
    return;
  }
  if (store.tabSelectName === '取费表') {
    store.SET_Fee_With_Drawal_Info(row);
  }
  store.SET_ASIDE_MENU_CURRENT_INFO(row);
  store.SET_CURRENT_STAGE_INFO(null);
  emits('currentMenu', row);
};

const handleChild = (item, child, index, childIndex) => {
  stageIndex.value = childIndex;
  categoryIndex.value = index;
  store.SET_ASIDE_MENU_CURRENT_INFO(item);
  store.SET_CURRENT_STAGE_INFO(child);
  emits('currentMenu', item);
};

let asideListTable = ref();
let menuConfig = reactive({
  className: 'my-menus',
  enabled: true,
  body: {
    disabled: false,
    options: [[]],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    console.log('右键', row, rowIndex, props.menuList);
    if (!row) return;
    handleSelect({ row, rowIndex });
    categoryIndex.value = rowIndex;
    asideListTable.value.setCurrentRow(props.menuList[categoryIndex.value]);
    if (['取费表'].includes(store.tabSelectName)) {
      if (Number(row.defaultFeeFlag) === 1) {
        getMenuOption(options[0], 'change').disabled = true;
      } else {
        getMenuOption(options[0], 'change').disabled = false;
      }
    }
    rcjMenuVisibleMethod({ options, row });
    return true;
  },
});

/**
 * 人材机右键显示处理
 * @param {*} param0
 */
const rcjMenuVisibleMethod = ({ options, row }) => {
  if (
    !['人材机汇总'].includes(store.tabSelectName) ||
    store.currentTreeInfo.levelType !== 3
  )
    return;
  let removeItem = getMenuOption(options[0], 'removeClass');
  removeItem.disabled = Number(row.key) < 30; // 是否自定义类型
};

/**
 * 标签页区分显示右键菜单
 */
const setShowMenu = () => {
  const levelType = store.currentTreeInfo.levelType;
  menuConfig.body.options[0] = [];
  if (levelType === 3) {
    if (['取费表'].includes(store.tabSelectName)) {
      menuConfig.body.options[0] = [
        {
          name: '切换为主取费文件',
          code: 'change',
          disabled: false,
        },
      ];
    }
    if (['人材机汇总'].includes(store.tabSelectName)) {
      menuConfig.body.options[0] = [
        {
          name: '新建',
          code: 'addClass',
          disabled: false,
        },
        {
          name: '删除',
          code: 'removeClass',
          disabled: true,
        },
      ];
    }
  }
};

/**
 * 根据code获取右键数据
 * @param {*} options
 * @param {*} code
 */
const getMenuOption = (options, code) => {
  return options.find(item => item.code === code);
};

const contextMenuClickEvent = ({ menu, row }) => {
  console.log('右键点击', menu, row);
  const funMap = {
    change: feeContextMenuHandler,
    addClass: rcjAddClassContextMenuHandler,
    removeClass: rcjRemoveClassContextMenuHandler,
  };
  const fun = funMap[menu.code];
  if (fun) fun(row);
};
/**
 * 人材机汇总右键新建
 * @param {*} row
 */
const rcjAddClassContextMenuHandler = row => {
  openClassification();
};

/**
 * 人材机汇总右键删除
 */
const rcjRemoveClassContextMenuHandler = row => {
  const params = {
    kind: row.key,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  console.log(params);
  removeClassification(params, () => {
    emits('updateMenuList');
  });
};

/**
 * 取费表右键点击事件处理
 */
const feeContextMenuHandler = () => {
  let formData = {
    sequenceNbr: props.menuList[categoryIndex.value].key,
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeInfo?.parentId, //单项ID
    unitId: store.currentTreeInfo?.id, //单位ID
  };
  console.log('setMainFeeFile', formData, props.menuList);
  feePro.setMainFeeFile(formData).then(function (response) {
    console.log('setMainFeeFile', response, formData);
    if (response.status === 200) {
      message.success('设置成功');
      emits('setMainFile', props.menuList[categoryIndex.value].key);
    } else {
      message.error(response.message);
    }
  });
};
const setMenuIsClick = () => {
  setShowMenu();
  asideListTable.value.setCurrentRow(props.menuList[0]);
};
const selectChildren = (selectedKeys, e) => {
  console.log('选择', selectedKeys, e);
  store.SET_ASIDE_MENU_CURRENT_INFO(e.node.dataRef);
  if (selectedKeys[0] && selectedKeys[0].indexOf('qtxm') > -1) {
    emits('currentMenu', { key: selectedKeys[0] });
  }
};
const onDragEnter = info => {
  // console.log('onDragEnter',info);
  // expandedKeys 需要展开时
  // expandedKeys.value = info.expandedKeys;
};
const dragstart = info => {
  // console.log('dragstart',info);
  if (info.node.kind === 1) {
    info.event.preventDefault();
    return false;
  }
  dragNode.value = info.node;
};
function calculateDepth(node) {
  if (!node.childTreeModel || node.childTreeModel.length === 0) {
    return 1;
  } else {
    let maxChildDepth = 1;
    for (let child of node.childTreeModel) {
      const childDepth = calculateDepth(child);
      maxChildDepth = Math.max(maxChildDepth, childDepth);
    }
    return maxChildDepth + 1;
  }
}
function calculateLevelToRoot(nodeId, treeData) {
  // 辅助函数，用于递归地查找节点
  function findNode(currentNode, targetId, currentLevel) {
    if (currentNode.sequenceNbr === targetId) {
      return currentLevel;
    }
    if (currentNode.childTreeModel) {
      for (let child of currentNode.childTreeModel) {
        const result = findNode(child, targetId, currentLevel + 1);
        if (result !== -1) {
          return result;
        }
      }
    }
    return -1;
  }

  // 调用辅助函数来计算层级数
  const levelFromRoot = findNode(treeData, nodeId, 0);
  return levelFromRoot;
}
const onDragOver = info => {
  // info.event.dataTransfer.dropEffect = 'none'
  // 1. 单位工程限制
  // 1.1. 单位工程不能为单位工程子集
  // 1.2. 单位工程移动到目标单项时，此单项必须为最子节点
  // 1.3. 单位工程最根节点单项下
  // console.log('treeList.value',treeList.value)
};
const onDrop = info => {
  if (
    calculateDepth(info.dragNode) +
      calculateLevelToRoot(
        info.dropToGap ? info.node.parentId : info.node.sequenceNbr,
        treeList.value[0]
      ) >
    4
  ) {
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }
  // 如果分部底下，有清单，则不能将分部拖到
  if (
    !info.dropToGap &&
    'childrenIsQd' in info.node.dataRef &&
    info.node.dataRef.childrenIsQd
  ) {
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }

  const dropKey = info.node.key; // 目标节点的key
  const dragKey = info.dragNode.key; // 拖拽节点的key
  const dropPos = info.node.pos.split('-'); // 目标节点的pos
  const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]); // 目标节点的dropPosition

  console.log('onDrop', info);
  console.log('dropPosition', dropPosition);

  // 如果拖到分部想拖到项目上去，则禁止
  if (info.node.dataRef.kind == '0' && dropPosition !== 0) {
    info.event.dataTransfer.dropEffect = 'none';
    info.event.preventDefault();
    return false;
  }
  console.log('onDrop', info);

  const loop = (data, key, callback) => {
    data.forEach((item, index) => {
      // console.log('isitem.key === key', item.sequenceNbr,key,item);
      if (item.sequenceNbr === key) {
        return callback(item, index, data);
      }
      if (item.childTreeModel) {
        return loop(item.childTreeModel, key, callback);
      }
    });
  };
  const data = xeUtils.clone([...treeList.value]);
  console.log('treeList.value', data, dragKey);
  // Find dragObject
  let dragObj = null;
  loop(data, dragKey, (item, index, arr) => {
    console.log('arr1', item, arr, index);
    arr.splice(index, 1);
    dragObj = item;
  });

  if (!info.dropToGap) {
    // Drop on the content
    loop(data, dropKey, item => {
      item.childTreeModel = item.childTreeModel || [];
      /// where to insert 示例添加到头部，可以是随意位置
      item.childTreeModel.unshift(dragObj);
    });
  } else if (
    (info.node.childTreeModel || []).length > 0 && // Has children
    info.node.expanded && // Is expanded
    dropPosition === 1 // On the bottom gap
  ) {
    loop(data, dropKey, item => {
      item.childTreeModel = item.childTreeModel || [];
      // where to insert 示例添加到头部，可以是随意位置
      item.childTreeModel.unshift(dragObj);
    });
  } else {
    let ar = [];
    let i = 0;
    loop(data, dropKey, (_item, index, arr) => {
      console.log('ari', ar, i);
      ar = arr;
      i = index;
    });
    if (dropPosition === -1) {
      ar.splice(i, 0, dragObj);
    } else {
      ar.splice(i + 1, 0, dragObj);
    }
  }
  if (info.dropToGap) {
    // 移动到同级
    dragObj.parentId = info.node.parentId;
    if (info.node.parentId === treeList.value[0].sequenceNbr) {
      dragObj.kind = '01';
    }
  } else {
    // 移动到子级
    dragObj.parentId = info.node.sequenceNbr;
    if (info.node.sequenceNbr === treeList.value[0].sequenceNbr) {
      dragObj.kind = '01';
    } else {
      dragObj.kind = '02';
    }
  }
  traverseAndSetDisplaySign(data[0]);
  console.log('aa', data);
  treeList.value = data;
  // saveTree(JSON.parse(JSON.stringify(data[0])), dragObj);

  //dropKey:目标节点的key  ,dragKey:拖拽节点的key  ,dragObj:拖拽节点的key
  console.log(
    '参数过滤',
    { sourceFbId: dragKey, targetFbId: dropKey, positionFlag: dropPosition },
    dragObj
  ); //
  saveTree(
    { sourceFbId: dragKey, targetFbId: dropKey, positionFlag: dropPosition },
    dragObj
  );
  selectedKeys.value = [dragObj.sequenceNbr];
  // projectStore.SET_CURRENT_TREE_INFO(dragObj);
  // store.SET_ASIDE_MENU_CURRENT_INFO(dragObj);
  // setCheckRow(dragObj);
};
function traverseAndSetDisplaySign(node) {
  // 设置当前节点的displaySign属性
  node.displaySign = node.childTreeModel.length > 0 ? 1 : 0;

  // 遍历子节点
  for (let i = 0; i < node.childTreeModel.length; i++) {
    // 递归调用函数处理子节点
    traverseAndSetDisplaySign(node.childTreeModel[i]);
  }
}

const saveTree = (fbTreeModel, dragObj) => {
  // let {constructId,singleId,unitId,sourceFbId,targetFbId} = args;
  // sourceFbId 拖拽节点 ； targetFbId  目标节点
  let postData = {
    constructId: store.currentTreeGroupInfo?.constructId,
    singleId: store.currentTreeGroupInfo?.singleId,
    unitId: store.currentTreeInfo?.id,
    ...fbTreeModel,
  };
  api.fbDragMoveAdjustController(postData).then(res => {
    selectedKeys.value = [dragObj.sequenceNbr];
    console.log(selectedKeys.value);
    store.SET_ASIDE_MENU_CURRENT_INFO(dragObj);
    emits('updateMenuList');
    if (selectedKeys[0] && selectedKeys[0].indexOf('qtxm') > -1) {
      emits('currentMenu', { key: dragObj.sequenceNbr });
    }
  });
};
defineExpose({
  currentSelectedMenu,
  isContract,
});
</script>
<style lang="scss" scoped>
.aside-menu-list {
  &:hover .btnExpand .btn {
    display: block;
  }
}
.aside-list {
  position: relative;
  height: 100%;
  width: 100%;
  border-right: 1px solid #dcdfe6;
  border-left: 1px solid #dcdfe6;
  transition: all 0.4s;
  background: #f8fbff;
  overflow: hidden;
}
.aside-contract {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  .title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-85%, -50%);
    width: 14px;
    padding: 0;
    height: auto;
    border-bottom: none;
    white-space: normal;
  }
  .btn span {
    transform: rotate(180deg);
  }
}
.aside-scroll {
  overflow-y: hidden;
  &:hover {
    overflow-y: auto;
  }
  &::-webkit-scrollbar {
    width: 5px;
    height: 6px;
  }
}

::-webkit-scrollbar-thumb {
  //滚动条的设置
  background-color: rgba(24, 144, 255, 0.2);
  background-clip: padding-box;
  min-height: 28px;
  border-radius: 5px;
}
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(24, 144, 255, 0.8);
}
.btnExpand {
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  font-size: 12px;
  height: 80px;
  z-index: 100;
  text-align: center;
  transition: all 0.1s linear;
  cursor: pointer;
  user-select: none;
  .btn {
    display: none;
  }
  span {
    display: inline-block;
    transform: translateX(-1px);
    transition: all 0.4s;
  }
}
.btnExpand:hover .btn {
  display: block;
}
.title {
  display: flex;
  align-items: center;
  height: 35px;
  padding: 0 0 0 10px;
  transition: all 0.4s;
  border-bottom: 2px solid #dcdfe6;
  color: #131414;
  white-space: nowrap;
  .icon {
    width: 11px;
    height: 11px;
    margin-right: 7px;
    background-color: #dfdfdf;
  }
}
.menu-list {
  list-style: none;
  padding: 1px 0px 9px;
  li {
    text-align: left;
    // margin: 0 0 6px 6px;
    margin: 0 0 1px 6px;

    cursor: pointer;
    .name-content {
      display: block;
      padding: 0 20px;
      line-height: 1.6;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行；
    }
    &:hover {
      background-color: #dae7f4;
    }
  }
}
.selectTree {
  :deep(.ant-tree) {
    background-color: #f8fbff;
    max-height: calc(100vh - 220px);
    overflow-y: hidden;
    padding-bottom: 10px;
    width: 98%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #131414;
    //padding-left: 8px;
    &:hover {
      overflow-y: auto;
    }
    .ant-tree-treenode-selected {
      color: #131414 !important;
      background-color: #deeaff !important;
    }
    .ant-tree-node-selected {
      color: #131414 !important;
      background-color: #deeaff !important;
    }
    .ant-tree-switcher {
      color: #131414 !important;
      //width: 10px;
    }
    .ant-tree-treenode {
      padding: 0px !important;
    }
    .ant-tree-iconEle {
      width: auto !important;
    }
    .ant-tree-treenode-selected::before {
      background-color: #deeaff !important;
    }
    .ant-tree-node-content-wrapper {
      display: flex;
      width: 50%;
    }
    .ant-tree-title {
      width: 97%;
      padding-right: 10px;
    }
  }
}

.on {
  background-color: #deeaff;
}
:deep(.vxe-table) {
  width: 100%;
  ::v-deep(.vxe-body--row.row--current) {
    background: #a6c3fa;
  }
  .vxe-table--body-wrapper {
    background-color: transparent;
    overflow: hidden;
    font-size: 13px;
    color: black;
    table {
      background-color: transparent;
    }
  }
  .name-content {
    margin-left: 23px;
    position: relative;
    .icon-fee {
      position: absolute;
      left: -16px;
      top: 3px;
    }
  }
}
.show-name {
  display: block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-menu {
  padding: 0 15px;
  .icon {
    width: 16px;
    height: 16px;
    margin-right: 7px;
    background-color: #dfdfdf;
  }
  .menu-title {
    cursor: pointer;
  }
  .frequency-list {
    display: flex;
    flex-wrap: wrap;
    padding-left: 12px;
    cursor: pointer;
    span {
      display: inline-block;
      border: 1px solid #a0c6eb;
      padding: 0 6px;
      margin: 7px 7px 0 0;
    }
  }
}
.on {
  background-color: #deeaff;
}
.checked {
  background: #a0c6eb;
}
.numStyle {
  border: 1px solid #94c86c !important;
  background: #f2faec;
}
.numChecked {
  background: #94c86c !important;
}
</style>
