const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow,ipcMain, app
} = require('electron');
const {Service} = require("../../core");
const {ObjectUtils} = require("../utils/ObjectUtils");
const WinAddon = require("../../core/addon/window");
const {PricingFileFindUtils} = require("../utils/PricingFileFindUtils");
const _ = require("lodash");
const {Snowflake} = require("../utils/Snowflake");
const {DateUtils} = require("../utils/DateUtils");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {WinManageUtils} = require("../../common/WinManageUtils");

const path = require('path');
const {UPCContext} = require("../unit_price_composition/core/UPCContext");
const {arrayToTree} = require("../main_editor/tree");
const EE = require("../../core/ee");
const crypto = require('crypto');
const {ProjectFileUtils} = require("../../common/ProjectFileUtils");
const Fixs = require("../fixs");
const CalculationTool = require("../unit_price_composition/compute/CalculationTool");

/**
 * 系统操作工具类
 */
class SystemService extends Service {


    loadProject(obj) {
        let unitList = PricingFileFindUtils.getUnitList(obj.sequenceNbr);
        unitList.forEach(unit => {
            unit.itemBillProjects = arrayToTree(unit.itemBillProjects);
            unit.measureProjectTables = arrayToTree(unit.measureProjectTables);
        });
        this.fixs(obj.version||"1",unitList);
        // if (obj.unitProject) {
        //     obj.unitProject.itemBillProjects = arrayToTree(obj.unitProject.itemBillProjects);
        //     obj.unitProject.measureProjectTables = arrayToTree(obj.unitProject.measureProjectTables);
        // } else {
        //     if (!obj.singleProjects) return;
        //     obj.singleProjects.forEach(item => {
        //         if (item.unitProjects) {
        //             item.unitProjects.forEach(unit => {
        //                 unit.itemBillProjects = arrayToTree(unit.itemBillProjects);
        //                 unit.measureProjectTables = arrayToTree(unit.measureProjectTables);
        //             });
        //         }
        //     });
        // }
    }
     fixs(version,unitList){
        let v1 = version.split(".").join("");
         if(Number(v1)>=1045){
             return;
         }
         for (let i = 0; i < unitList.length; i++) {
             let unit = unitList[i];
             let measureProjectTables = unit.measureProjectTables;
             let itemBillProjects = unit.itemBillProjects;
             let fbFxDeList = itemBillProjects.filter(item=>item.kind=="04").map(item => item.sequenceNbr);
             let csxmDeList = measureProjectTables.filter(item=>item.kind=="04").map(item => item.sequenceNbr);
             if(fbFxDeList&&fbFxDeList.length>0){
                 let calculationTool = new CalculationTool({constructId:unit.constructId, singleId:unit.spId, unitId:unit.sequenceNbr, allData: itemBillProjects});
                 calculationTool.calculationDes(fbFxDeList);
             }

             if(csxmDeList&&csxmDeList.length>0){
                 let calculationTool1 = new CalculationTool({constructId:unit.constructId, singleId:unit.spId, unitId:unit.sequenceNbr, allData: measureProjectTables});
                 calculationTool1.calculationDes(csxmDeList);
             }

         }

     }

    /**
     * 打开项目详情窗口
     */
    async openWindowForProject(path) {

        //兼容概算项目的打开操作
        if(ObjectUtils.isNotEmpty(path) && path.toLowerCase().endsWith(".ygs")){
            //暂时处理
            return await this.service.PreliminaryEstimate.gsAppService.openGsWindowForProject(path);

        }

        //获取项目数据
        let obj =await PricingFileFindUtils.getProjectObjByPath(path);
        await  new Fixs(obj,obj.version).fix();
        obj.path = path;
        //用户的打开历史记录列表数据处理
        obj = PricingFileWriteUtils.writeUserHistoryListFile(obj);
        //将项目数据写入到内存当中
        PricingFileWriteUtils.writeToMemory(obj);
        if(obj.UPCContext){
            UPCContext.load(obj.UPCContext);
        }
        this.loadProject(obj);
        //将项目数据更新到ysf文件当中
        await this.service.ysfHandlerService.updateYsfFile(obj);
        let windowId = obj.sequenceNbr;
        let windowName = obj.constructName;
        if (!ObjectUtils.isEmpty(global.windowMap) && global.windowMap.has(windowId)){
            let newVar = global.windowMap.get(windowId);
            // 获取对应的窗口引用
            let win = BrowserWindow.fromId(newVar);
            if (win.isMinimized()){
                win.restore();
            }
            //将窗口移动到顶部
            win.moveTop();
            return;
        }
        //创建窗口
        let win = this.createWindowForProject(windowName,windowId);
        // 定义全局map
        if (ObjectUtils.isEmpty(global.windowMap)){
            global.windowMap = new Map();
        }
        windowMap.set(windowId,win.id);

        //监听内存对象是否发生改变
         /*let projectObj = PricingFileFindUtils.getProjectObjById(windowId);
         this.watch(projectObj, win,_.debounce(function(wins,key, newValue) {
             const channel = 'update.status';
             let windows = BrowserWindow.getAllWindows();
             let list = [];
             windows.forEach((item) => {
                 item.webContents.send(channel, true);
             });
             console.log("执行一次");
         },1000) );

         },1000) );*/

        return windowId;

    }
/*
     watch(obj,win, callback) {
         for (var key in obj) {
             if (obj.hasOwnProperty(key)) {
                 var val = obj[key];

                 if (typeof val === 'object') {
                     this.watch(val,win, callback);
                }

                 (function(win,key, val) {
                    Object.defineProperty(obj, key, {
                         get: function() {
                             return val;
                         },
                         set: function(newValue) {
                             if((_.isEmpty(val)||_.isUndefined(val))&&(_.isEmpty(newValue)||_.isUndefined(newValue))){
                                 val = newValue;
                                 return;
                             }
                             if (newValue!=val) {
                                 console.log(key);
                                 console.log(val);
                                 console.log(newValue);
                                 val = newValue;

                                 // 在此处添加你想要执行的回调函数
                                 callback(win,key, newValue);
                             }
                         }
                     });
                 })(win,key, val);
             }
         }

         if (Array.isArray(obj)) {
             var arrayMethods = ['push', 'pop', 'shift', 'unshift', 'splice', 'reverse'];

             arrayMethods.forEach(function(method) {
                 var original = Array.prototype[method];

                Object.defineProperty(obj, method, {
                     writable: true,
                     enumerable: false,
                     configurable: true,
                     value: function() {
                         var args = Array.from(arguments);
                         var result = original.apply(this, args);
                         console.log(method);
                        // 在此处添加你想要执行的回调函数
                         callback(method, args);

                         return result;
                     }
                 });
             });
         }
     }*/


    /**
     * 创建modal窗口
     * @param {Object} options 窗口参数
     * @param {string} options.windowName 窗口名称
     * @param {string} options.windowId 窗口id
     * @param {string} options.type 窗口类型
     * @param {Object} options.style 窗口样式
     * @return {void}
     */
      createModalWindow({ windowId, modal}) {
        return new Promise((resolve, reject) => {
          let childVar = global.windowMap.get(`${windowId}-modal-${modal.type}`)
          if(childVar){
           const otherwin = BrowserWindow.fromId(childVar)
           otherwin?.close()
          }
          let newVar = global.windowMap.get(windowId)
          const parentWindow = BrowserWindow.fromId(newVar)
          let addr = 'http://localhost:8080'
          if (this.config.env == 'prod') {
            const mainServer = this.app.config.mainServer;
            addr = mainServer.protocol + mainServer.host + ':' + mainServer.port;
          }
          if (!parentWindow) {
            console.error(
              `Could not find BrowserWindow instance with id ${windowId}`
            )
            return
          }
          const childWindow = new BrowserWindow({
            width: modal.style?.width || 880,
            height: modal.style?.height || 500,
            minWidth: modal.style?.minWidth || 600,
            minHeight: modal.style?.minHeight || 300,
            title: modal.windowName,
            parent: parentWindow,
            frame: false,
            show: false,
            webPreferences: {
              contextIsolation: false,
              nodeIntegration: true,
            },
          })
          // childWindow.once('ready-to-show', () => {
          //   childWindow.show()
          //   resolve()
          // })
          let content = `${addr}#/model?constructSequenceNbr=${windowId}&type=${modal.type}`
          // childWindow.webContents.on('postMsgToParent', (event,data) => {
          //   this.postMsgToParent(data)
          // })

          childWindow.loadURL(content)
          // window.webContents.send('type', 'hide');
          windowMap.set(`${windowId}-modal-${modal.type}`, childWindow.id)

          console.log('winMap', global.windowMap, childWindow)
        });
      }
      /**
       * 设置modal窗口
       * @param {String} windowId - 项目id
       * @param {String} type - modal类型
       * @param {String} showType - 显示类型 show,close,max
       */
      setModalState(windowId,type,showType) {
        let newVar = global.windowMap.get(`${windowId}-modal-${type}`)
        const window = BrowserWindow.fromId(newVar)
        if(showType == 'show'){
          ipcMain.on(`postMsgToParent-${type}`, (event,data) => {
            this.postMsgToParent(data)
          })
          // window.webContents.send('type', 'show');
          window.show()
        }
        if(showType == 'close'){
          // window.webContents.send('type', 'hide');
          window.close()
        }
        if(showType == 'max'){
          // window.webContents.send('type', 'hide');
          window.maximize()
        }
        if(showType == 'unmaximize'){
          // window.webContents.send('type', 'hide');
          window.unmaximize()
        }
        if(showType == 'hide'){
          ipcMain.on(`postMsgToParent-${type}`, (event,data) => {
            this.postMsgToParent(data)
          })
          // window.webContents.send('type', 'hide');
          window.hide()
        }
      }
      /**
       * 将信息post到父window
       * @param {Object} data  - 信息对象
       * @param {String} data.windowId - 项目id
       * @param {String} data.type - 信息类型
       * @param {String} data.funName - 信息对应的父window方法
       * @param {Object} data.data - 信息对象
       */
      postMsgToParent(data) {
        let newVar = global.windowMap.get(data.windowId)
        const window = BrowserWindow.fromId(newVar)
        window.webContents.send('emitFun', data)
      }

    /**
     * 创建项目信息窗口
     * @param windowName
     * @param windowId
     */
    createWindowForProject(windowName,windowId) {
        let addr = 'http://localhost:8080'
        if (this.config.env == 'prod') {
            const mainServer = this.app.config.mainServer;
            addr = mainServer.protocol + mainServer.host + ':' + mainServer.port;
        }
        const addonWindow = this.app.addon.window;
        let opt = {
            title: windowName,
            minWidth: 980,
            minHeight: 650,
            icon: this.config.windowsOption.icon
        }
        const name = windowId;
        const win = addonWindow.create(name, opt);
        //const winContentsId = win.webContents.id;
        let content = addr + '#/projectDetail/customize?constructSequenceNbr='+windowId;
        // load page
        win.loadURL(content);
        win.maximize();

        win.on ('closed', () => {
            // let childWin = BrowserWindow.fromId(global.windowMap.get(windowId));
            // let diffProject = await this.service.commonService.diffProject({constructId:windowId});
            // childWin.webContents.send(windowId, diffProject);

            global.windowMap.delete(windowId);
            global.constructProject[windowId] = null;
            if (ObjectUtils.isNotEmpty(global.multiplexMergeImportProject)) {
                global.multiplexMergeImportProject[windowId] = null;  //该字段表示 复用组价导入的临时历史成果文件
            }
        });
        if(EE.app.showDebugger){
            win.webContents.openDevTools();
        }
        return win;
    }


}

SystemService.toString = () => '[class SystemService]';
module.exports = SystemService;
