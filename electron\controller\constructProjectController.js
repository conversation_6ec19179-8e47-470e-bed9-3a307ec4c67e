
const ConstantUtil = require("../enum/ConstantUtil");
const ConstructBiddingTypeConstant = require("../enum/ConstructBiddingTypeConstant");
const {Controller} = require("../../core");
const {ResponseData} = require("../utils/ResponseData");


class ConstructProjectController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 删除
     * @param arg
     */
    deleteProject(arg){
        global.constructProject[arg.sequenceNbr] = null;
    }

    async importZipCheck(args){
        const result = await this.service.dirService.importZipAndGetResultTree(args);


        //const sequenceNbr = await this.service.analyzingExcelService.analysis(args)

        return ResponseData.success(args);
    }

    /**
     * 缺失单位工程时上传单位
     * @param args
     * @return {ResponseData}
     */
    importUploadAndCheckUnitProject(args){
        const result = this.service.dirService.uploadUnitCheckToConstruct(args);


        //const sequenceNbr = await this.service.analyzingExcelService.analysis(args)

        return ResponseData.success(result);
    }

    /**
     * 进入工程项目后导入单位工程
     * @param args
     *   {
     *       constructId: xxx,
     *       singleId: xxx,
     *       excels: [
     *         "D:\aaa.xls",
     *         "F:\bbb.xlsx"
     *      ]
     *   }
     * @returns {ResponseData}
     */
    async importUnitProjectCheck(args){
        const result = await this.service.dirService.importUnitProjectCheck(args);

        return ResponseData.success(result);
    }

    /**
     * 导入单位工程
     * @param args
     *   {
	 *       constructId: xxx,
	 *       singleId: xxx,
	 *       excels: [
	 *         "D:\aaa.xls",
	 *         "F:\bbb.xlsx"
	 *      ]
     *   }
     * @returns {ResponseData}
     */
    async importUnits(args){

        let code = await this.service.analyzingExcelService.analysisUnits(args);
        if(code === 500){
            return ResponseData.fail("文件解析出错");
        }

        const arg ={
            sequenceNbr:args.constructId
        }
        const result = await this.service.constructProjectService.generateLevelTreeNodeStructure(arg);
        return ResponseData.success(result);
    }

    /**
     * 导入保存xml,zip
     * @param args
     * @returns {ResponseData}
     */
    async importProject(args){
        let sequenceNbr = null;

        // 创建一个新的Date对象
        /*const now = new Date();

        // 获取当前的毫秒值
        const timestamp = now.getTime();

        console.log("导入开始---------",timestamp/1000)*/
        if(args.fileExtension === "zip"){
            sequenceNbr = await this.service.analyzingExcelService.analysis(args)
            if(sequenceNbr.code === 500){
                return ResponseData.fail("文件解析出错");
            }
            /*const now1 = new Date();
            const milliseconds = now1.getTime();

            console.log("导入方法  analyzingExcelService.analysis 用时 ---------",(milliseconds-timestamp)/1000);*/
        }else if(args.fileExtension === "xml" || args.fileExtension === "hebqzbx" || args.fileExtension === "hebqtbx"){
            sequenceNbr = await this.service.constructProjectService.importProject(args);
            if(sequenceNbr.code === 500){
                return sequenceNbr;
            }
        }else{
            return ResponseData.fail("导入文件格式不支持");
        }

        //const sequenceNbr = await this.service.constructProjectService.importProject(args);

        const arg ={
            sequenceNbr:sequenceNbr
        }
        const result = await this.service.constructProjectService.generateLevelTreeNodeStructure(arg);
        /*const now2 = new Date();
        const milliseconds2 = now2.getTime();


        console.log("导入总用时 ---------",(milliseconds2 - timestamp)/1000);*/

        // 设置默认 工程专业
        this.service.constructProjectService.setUnitProjectDefaultMajorType(sequenceNbr, result);

        return ResponseData.success(result);
    }
    /**
     * 导入之后 编辑项目接口包含 根据项目初始化 措施项目 取费表 造价分析 费用汇总数据
     */
    async editImportProjectAfter (arg) {
        /*const now = new Date();
        const milliseconds = now.getTime();*/

        const result = await this.service.constructProjectService.editProjectStructureForImport(arg);

        /*const now1 = new Date();
        const milliseconds1 = now1.getTime();

        console.log("导入之后 编辑项目接口包含 总用时 " + (milliseconds1 - milliseconds)/1000)*/
        return ResponseData.success(result);
    }
    /**
     * 新建预算项目
     */
    async newBudgetProject (arg) {
        const result = await this.service.constructProjectService.newBudgetProject(arg);
        return ResponseData.success(result);
    }


    /**
     * 最近打开文件记录
     */
    async recentlyOpenedProjectList (arg) {
        const result = await this.service.constructProjectService.recentlyOpenedProjectList(arg);
        return ResponseData.success(result);
    }


    /**
     * 打开项目
     */
    async openProject (arg) {
        return await this.service.constructProjectService.openProject(arg);

    }
    /**
     * 设置窗体（提前打开窗体）
    */
    async createModal (arg) {
      const result =  await this.service.constructProjectService.createModal(arg);
      return ResponseData.success(result);

    }
    /**
     * 显示窗体（显示，最大化，隐藏）
     */
    async setModalState (arg) {
      return await this.service.constructProjectService.setModalState(arg);
    }
    /**
     * 打开本地文件
     * @return {Promise<*|undefined|ResponseData>}
     */
    async openLocalFile () {
        return await this.service.constructProjectService.openLocalFile();

    }

    /**
     * 判断后缀
     * @param args
     * @returns {Promise<*>}
     */
    async judgeSuffix (args) {
        return await this.service.constructProjectService.judgeSuffix(args);

    }

    /**
     * 判断项目是否是只读项目
     * @param args
     * @returns {Promise<*>}
     */
    async fsIsReadOnly (args) {
        let res = await this.service.constructProjectService.fsIsReadOnly(args);
        return res;

    }



    /**
     * 打开线上项目
     * @return {Promise<*|undefined|ResponseData>}
     */
    async openOnlineProject(arg){
        return await this.service.constructProjectService.openOnlineProject(arg);

    }

    /**
     * 编辑项目层级结构
     */
    async editProjectLevelStructure (arg) {
        await this.service.constructProjectService.editProjectLevelStructure(arg);
    }

    /**
     * 批量修改名称
     * @param arg
     * @return {*}
     */
    batchModifyName(arg){
        return this.service.constructProjectService.batchModifyName(arg);
    }





    /**
     * 生成项目层级树结构
     */
    async generateLevelTreeNodeStructure (arg) {
        const result = await this.service.constructProjectService.generateLevelTreeNodeStructure(arg);
        return ResponseData.success(result);
    }


    /**
     * 拖拽项目结构(调整位置、调整顺序、批量复制、批量删除)
     */
    async dragDropProjectStructure (arg) {
        await this.service.constructProjectService.dragDropProjectStructure(arg);
        return ResponseData.success(true);
    }


    /**
     * 厂家下拉列表
     */
    xmlFactroyDropdownList(){
        return  ConstantUtil.XML_FACTORY;
    }

    /**
     * 文件类型下拉列表
     */
    biddingTypeDropdownList(){
        let res = new Array();
        res.push({
            key:ConstructBiddingTypeConstant.zbProject,
            value:'招标文件'
        });
        res.push({
            key:ConstructBiddingTypeConstant.tbProject,
            value:'投标文件'
        })
        return res;
    }

    getConstructMajorTypeByConstructId(args){
        const result =  this.service.constructProjectService.getConstructMajorTypeByConstructId(args);
        return ResponseData.success(result);
    }

    /**
     * 工作台底部价格汇总查询
     */
    getBottomSummary(args){
        const result =  this.service.constructProjectService.getBottomSummary(args);
        return ResponseData.success(result);
    }


    /**
     *  通用便捷性设置
     */
    async projectConvenientSetColl(args) {
        await this.service.constructProjectService.projectConvenientSet(args);
        return ResponseData.success(true);
    }

    /**
     *  工程项目便捷设置查询
     */
    async queryProjectConvenientSetColl(args) {
        let deGlTcFlag = await this.service.constructProjectService.queryProjectConvenientSet(args);
        return ResponseData.success(deGlTcFlag);
    }

    /**
     * 设置主材展示
     * @param args
     * @return {ResponseData}
     */
    mainRcjShowFlagColl(args){
        this.service.constructProjectService.updateMainRcjShowFlag(args);
        return ResponseData.success(true);
    }


    /**
     * 标准换算弹窗展示
     * @param args
     * @return {ResponseData}
     */
    standardConversionShowFlagColl(args){
        this.service.constructProjectService.updateStandardConversionShowFlag(args);
        return ResponseData.success(true);
    }


    /**
     * 获取项目数据
     * @param args
     * @return {ResponseData}
     */
    queryConstructProjectMessageColl(args){
        let result = this.service.constructProjectService.queryConstructProjectMessage(args);
        return ResponseData.success(result);
    }


    /**
     * 查询工程项目级别 是否有单位工程未设置取费专业 true 为 全部设置了 flase为最少有一个没设置
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getConstructUnitListTypeByConstructId(args){
        let res = await this.service.constructProjectService.getConstructUnitListTypeByConstructId(args);
        return ResponseData.success(res);
    }

    async getConstructFileMsg(args){
        let res = await this.service.constructProjectService.getConstructFileMsg(args);
        return ResponseData.success(res);
    }

    /**
     * 标段结构保护
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async optionLock(args){
        let res = await this.service.constructProjectService.optionLock(args);
        return ResponseData.success(res);
    }

    async getProjectXmlInfo(args){
        let res = await this.service.constructProjectService.analProjectXmlInfo(args.importUrl);
        return ResponseData.success(res);
    }

    /**
     * 设置项目设置中的 展示设置
     * @param args
     * @return {ResponseData}
     */
    async setMainSettingShow(args){
        let res = await this.service.constructProjectService.setMainSettingShow(args);
        return ResponseData.success(true);
    }

    /**
     * 展示设置回显
     * @param args
     * @return {ResponseData}
     */
    async getMainSettingShow(args){
        let res = await this.service.constructProjectService.getMainSettingShow(args);
        return ResponseData.success(res);
    }
}

ConstructProjectController.toString = () => '[class ConstructProjectController]';
module.exports = ConstructProjectController;
