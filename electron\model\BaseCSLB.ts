import {Column, Entity} from "typeorm";

const {BaseModel} = require("./BaseModel");

/**
 * 取费文件关联关系表
 */
@Entity({name: "base_cslb"})
export class BaseCSLB extends BaseModel {
    public libraryCode: string; // '所属定额册编码',
    @Column({nullable:true,name:"library_name"})
    public libraryName: string; // '所属定额册名称',
    @Column({nullable:true,name:"cslb_code"})
    public cslbCode: string; // '措施类别编码',
    @Column({nullable:true,name:"cslb_name"})
    public cslbName: string; // '措施类别名称',
    @Column({nullable:true,name:"unit_project_name"})
    public unitProjectName: string; // '',
    @Column({nullable:true,name:"sort_no"})
    public sortNo: string; // '排序',

}
@Entity({name: "base_cslb_2022"})
export class BaseCSLB2022 extends BaseModel {
    public libraryCode: string; // '所属定额册编码',
    @Column({nullable:true,name:"library_name"})
    public libraryName: string; // '所属定额册名称',
    @Column({nullable:true,name:"cslb_code"})
    public cslbCode: string; // '措施类别编码',
    @Column({nullable:true,name:"cslb_name"})
    public cslbName: string; // '措施类别名称',
    @Column({nullable:true,name:"unit_project_name"})
    public unitProjectName: string; // '',
    @Column({nullable:true,name:"sort_no"})
    public sortNo: string; // '排序',
}
