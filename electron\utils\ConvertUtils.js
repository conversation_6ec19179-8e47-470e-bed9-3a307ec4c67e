const lodash = require("lodash")
const {ObjectUtils} = require("./ObjectUtils");



class ConvertUtil{


    _isObjectNotNull(obj){
        return !ObjectUtils.isNull(obj) && ObjectUtils.isObject(obj);
        //return obj !== null && typeof obj === 'object';
    }
    _extractChineseNumbers(sentence) {
        // 中文数字正则表达式
        const chineseNumberRegex = /[\u4e00-\u9fa5\d]+/g;
        // 匹配中文数字并提取
        let matches = sentence.match(chineseNumberRegex) || [];
        // 返回匹配到的数字数组
        return matches.filter((item) => {
            // 过滤出纯中文数字
            return !(/[a-zA-Z]/).test(item);
        });
    }

    _arabicToSpecialCharacter(num) {
        if (typeof num !== 'number' || num < 1 || num > 99) {
            return '输入错误，请输入一个1到99之间的数字';
        }

        // Unicode码点中，①对应U+2460，②对应U+2461，以此类推
        const baseCharCode = 9311; // U+2460 - 1
        return String.fromCodePoint(baseCharCode + num);
    }
    _arabicToChinese(num) {
        if (typeof num !== 'number' || num < 1 || num > 99) {
            return '输入错误，请输入一个1到99之间的数字';
        }

        const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
        let result = '';

        // 处理十位数
        const tens = Math.floor(num / 10);
        if (tens > 0) {
            result += chineseNums[tens] + '十';
        }

        // 处理个位数
        const units = num % 10;
        if (units > 0) {
            // 如果十位为1，则不需要加“一”
            if (tens === 1) {
                result += chineseNums[units];
            } else {
                // 如果不是十位数且不是零，则加上“一”
                if (tens !== 0) {
                    result += '一';
                }
                result += chineseNums[units];
            }
        }

        // 如果结果为空（即输入为10的倍数），则返回“十”的倍数
        if (result === '') {
            return chineseNums[tens] + '十';
        }
        if(result.startsWith("一") && result.length >1)
        {
            result = result.substring(1,result.length);
        }
        return result;
    }
    _chineseToNum(chineseNum) {
        let result = '';
        for (let i = 0; i < chineseNum.length; i++) {
            result += numToChinese[chineseNum[i]];
        }
        return result;
    }

    countCharInSentence(sentence, char) {
        // 使用 split() 方法将句子分割成字符数组
        let chars = sentence.split('');

        // 使用 filter() 方法筛选出目标字符，并返回其数量
        return  chars.filter(c => c === char).length;
    }

    _getFirstChineseNumber(sentence) {
        const regex = /[\u4e00-\u9fa5]?\d+|^\d+[\u4e00-\u9fa5]?/;
        const match = sentence.match(regex);
        if (match) {
            const chineseNumber = match[0];
            // 将可能的中文字符去除，只返回数字部分
            return chineseNumber.replace(/[\u4e00-\u9fa5]/g, '');
        }
        return null;
    }

    /**
     * 根据dst包含属性，递归拷贝src中的值到dst中
     * @param src
     * @param dst
     */
    setDstBySrc(src,dst){
        return this._copyToByDst(src,dst);
    }

    /**
     * 根据dst包含属性，递归拷贝src中的值到dst中
     * @param src
     * @param dst
     */
    _copyToByDst(src,dst){
        if (!this._isObjectNotNull(dst) || !this._isObjectNotNull(src)) {
            return;
        }

        for (var key in dst) {
            if (!dst.hasOwnProperty(key)) {
                continue;
            }

            if(this._isObjectNotNull(dst[key])){
                this.setDstBySrc(src[key], dst[key]);
            }else{
                dst[key] = src[key];
            }
        }
    }

    /**
     * 递归拷贝src中属性到dst，相同属性覆盖
     * @param src
     * @param dst
     * @return {*}
     */
    recursiveCopyTo(src, dst) {
        if (typeof src!== 'object' || src === null) {
            return src;
        }

        for (let key in src) {
            if (src.hasOwnProperty(key)) {
                if (typeof src[key] === 'object' && src[key]!== null) {
                    if (!dst.hasOwnProperty(key)) {
                        dst[key] = Array.isArray(src[key])? [] : {};
                    }
                    this.recursiveCopyTo(src[key], dst[key]);
                } else {
                    dst[key] = src[key];
                }
            }
        }
        return dst;
    }

    deepCopy(obj) {
        // if (typeof obj !== 'object') return;
        // var newObj = obj instanceof Array ? [] : {};
        // for (var key in obj) {
        //     if (obj.hasOwnProperty(key)) {
        //         newObj[key] = typeof obj[key] === 'object' ? this.deepCopy(obj[key]) : obj[key];
        //     }
        // }
        // return newObj;
        return lodash.cloneDeep(obj);
    }






     deepCopyIgnore(source, target, excludedProps = []) {
        if (typeof source !== 'object' || source === null) {
            return source;
        }

        // 如果没有指定目标对象，则创建一个新对象
        if (!target) {
            target = Array.isArray(source) ? [] : {};
        }

        // 枚举原对象的属性
        Object.keys(source).forEach(prop => {
            if (excludedProps.includes(prop)) {
                return;
            }

            // 如果属性是一个对象或数组，则递归进行深拷贝并赋值给目标对象的相应属性
            if (typeof source[prop] === 'object' && source[prop] !== null) {
                target[prop] = deepCopy(source[prop], Array.isArray(source[prop]) ? [] : {}, excludedProps);
            } else {
                target[prop] = source[prop];
            }
        });

        return target;
    }

}



module.exports = {
    ConvertUtil: new ConvertUtil()
};