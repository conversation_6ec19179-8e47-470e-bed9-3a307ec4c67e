'use strict';



const {ConstructProject} = require("../model/ConstructProject");
const {Service} = require("../../core");
const {Snowflake} = require("../utils/Snowflake");
const fs = require('fs')
const xml2js = require('xml2js');
const ConstructBiddingTypeConstant = require("../enum/ConstructBiddingTypeConstant");
const BranchProjectLevelConstant = require("../enum/BranchProjectLevelConstant");
const BranchProjectDisplayConstant = require("../enum/BranchProjectDisplayConstant");
const OtherProjectCalculationBaseConstant = require("../enum/OtherProjectCalculationBaseConstant");
const PolicyDocumentTypeEnum = require("../enum/PolicyDocumentTypeEnum");
const {arrayToTree} = require("../main_editor/tree");
const {BasePolicyDocument} = require("../model/BasePolicyDocument");
const {NumberUtil} = require("../utils/NumberUtil");
const {ResponseData} = require("../utils/ResponseData");
const {MeasureProjectTable} = require("../model/MeasureProjectTable");
const {OtherProjectZygcZgj} = require("../model/OtherProjectZygcZgj");
const {PricingFileWriteUtils} = require("../utils/PricingFileWriteUtils");
const {ItemBillProject} = require("../model/ItemBillProject");
const {OtherProjectDayWork} = require("../model/OtherProjectDayWork");
const {OtherProjectServiceCost} = require("../model/OtherProjectServiceCost");
const {OtherProjectZgj} = require("../model/OtherProjectZgj");
const {OtherProjectProvisional} = require("../model/OtherProjectProvisional");
const {OtherProject} = require("../model/OtherProject");
const {UnitProject} = require("../model/UnitProject");
const {SingleProject} = require("../model/SingleProject");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ArrayUtil} = require("../utils/ArrayUtil");
const {ReadXmlUtil} = require("../utils/ReadXmlUtil");
const {getUnitFormatEnum} = require("../main_editor/rules/format");
const ConstantUtil = require("../enum/ConstantUtil");
class AnalyzingXMLServiceYCG extends Service{
    constructor(ctx) {
        super(ctx);

        this.qbExtraTableArray = new Array();
        this.qdMap = new Map();
        this.dispNo = 1;
    }


    async  analysis(constructProject,data){
        let 文件类别 = data.GCZJWJ.$.WJLB;

        let GCXM = data.GCZJWJ.GCSJ[0].GCXM[0].$;
        let DXGC = data.GCZJWJ.GCSJ[0].GCXM[0].DXGC;
        //单项工程
        let singleProjects = new Array();

        if("招标文件"===文件类别){
            if(ObjectUtils.isEmpty(constructProject.biddingType)){
                constructProject.biddingType =ConstructBiddingTypeConstant.zbProject
            }
        }else {
            constructProject.biddingType =ConstructBiddingTypeConstant.tbProject
            return ResponseData.fail('文件类型有误，请重新选择');
        }

        if(ObjectUtils.isEmpty(constructProject.sequenceNbr)){
            constructProject.sequenceNbr = Snowflake.nextId();
        }
        // constructProject.constructName = 工程项目.项目名称;
        constructProject.constructCode = GCXM.XMBM;
        constructProject.projectOverview = GCXM.BJSM;
        constructProject.total = GCXM.JE;
        constructProject.gfee = GCXM.QZGF;
        constructProject.safeFee = GCXM.QZAQWMF;
        constructProject.sbfsj = GCXM.QZSBF;
        // constructProject.fddbr = GCXM.发包人法定代表人;
        // constructProject.constructionUnit = GCXM.招投标单位名称;
        constructProject.gfId = '14';
        constructProject.awfId = '44';
        //constructProject.rgfId = '15';


        let jsType ; //1 一般 0 简易

        if(GCXM.JSFS === '增值税方式_一般计税'){
            jsType = 1;
        }else {
            jsType = 0;
        }
        //计税方式
        await this.service.projectTaxCalculationService.importXMLInitProjectTaxCalculation(constructProject,jsType);


        //人工费id
        let rgfPolicyDocumentList = await this.app.appDataSource.getRepository(BasePolicyDocument).find({
            where: {
                areaId:130100,
                fileType: PolicyDocumentTypeEnum.RGF.code,
            }
        });
        if (!ObjectUtils.isEmpty(rgfPolicyDocumentList)) {

            //时间倒叙排列
            rgfPolicyDocumentList.sort(function(a, b) {
                return b.fileDate.localeCompare(a.fileDate);
            });
            constructProject.rgfId=rgfPolicyDocumentList[0].sequenceNbr;
        }

        let map = this.convertConstructProjectJBXX(constructProject, data);
        // 编制说明 ---项目层级
        this.service.constructProjectService.initProjectOrUnitBZSM(1, constructProject);
        //解析单项工程
        await this.convertSingleProject(DXGC,constructProject,map);
        //放入内存
        PricingFileWriteUtils.writeToMemory(constructProject);
        return constructProject.sequenceNbr;
    }


    convertConstructProjectJBXX(constructProject, data) {
        //工程基本信息
        this.service.constructProjectService.initProjectOrUnitData(constructProject, 1);
        let constructProjectJBXX = constructProject.constructProjectJBXX;
        let GCSXZM = data.GCZJWJ.GCSJ[0].GCXM[0].GCSX[0].GCSXZM;
        let map = new Map();
        for (let i = 0; i < GCSXZM.length; i++) {
            let $ = GCSXZM[i].$;
            map.set($.MC, $.SXZ)
        }
        for (let i = 0; i < constructProjectJBXX.length; i++) {
            switch (constructProjectJBXX[i].name) {
                case '工程名称':
                    constructProjectJBXX[i].remark = constructProject.constructName;
                    break;
                case '招标人(发包人)':
                    constructProjectJBXX[i].remark =  map.get('招标人');
                    break;
                case '招标人(发包人)法人或其授权人':
                    constructProjectJBXX[i].remark =map.get('法定代表人');
                    break;
                case '工程造价咨询人':
                    constructProjectJBXX[i].remark = map.get('造价咨询人');
                    break;
                case '工程造价咨询人法人或其授权人':
                    constructProjectJBXX[i].remark = map.get('工程造价法人代表');
                    break;
                case '编制人':
                    constructProjectJBXX[i].remark = map.get('编制人');
                    break;
                case '编制时间':
                    constructProjectJBXX[i].remark = map.get('编制时间');
                    break;
                case '核对人(复核人)':
                    constructProjectJBXX[i].remark = map.get('复核人');
                    break;
                case '核对(复核)时间':
                    constructProjectJBXX[i].remark = map.get('复核时间');
                    break;
                default:
                    constructProjectJBXX[i].remark = map.get(constructProjectJBXX[i].name)
                    break;
            }

        }
        constructProject.constructProjectJBXX = constructProjectJBXX;
        return map;
    }

    /**
     * 解析单项工程
     * @param 单项工程
     * @param constructProject
     */
    async convertSingleProject(DXGC, constructProject,map) {
        if(!ObjectUtils.isObject(DXGC)){
            let singleProjects = new Array();
            for (let i = 0; i < DXGC.length; i++) {
                let singleProject = new SingleProject();
                let model = DXGC[i];
                let $ = model.$;
                singleProject.sequenceNbr = Snowflake.nextId();
                singleProject.constructId = constructProject.sequenceNbr;
                singleProject.projectCode = $.BM;
                singleProject.projectName = $.MC;
                singleProject.total = $.JE;
                singleProject.safeFee = $.QZAQWMF;
                singleProject.gfee = $.QZGF;
                singleProject.sbf = $.QZSBF;
                //判断单项下是否还有单项
                if(model.DWGC === undefined){
                    //还有单项, 递归去解析
                    await this.recursionSingleProject(model.DXGC, singleProject, map, constructProject);
                }else{
                    //解析单位工程
                    await this.convertUnitProject(model.DWGC, singleProject, map, constructProject.rgfId);
                }
                singleProjects.push(singleProject);
            }
            constructProject.singleProjects = singleProjects
        }
    }

    /**
     * 递归处理子单项
     */
    async recursionSingleProject(xmlSingleProjects, oldSingleProjects, map, constructProject) {
        let newSingleProjects = new Array();
        for (let i = 0; i < xmlSingleProjects.length; i++) {
            let singleProject = new SingleProject();
            let model = xmlSingleProjects[i];
            let $ = model.$;
            singleProject.sequenceNbr = Snowflake.nextId();
            singleProject.constructId = constructProject.sequenceNbr;
            singleProject.projectCode = $.BM;
            singleProject.projectName = $.MC;
            singleProject.total = $.JE;
            singleProject.safeFee = $.QZAQWMF;
            singleProject.gfee = $.QZGF;
            singleProject.sbf = $.QZSBF;
            //判断单项下是否还有单项
            if(model.DWGC === undefined){
                await this.recursionSingleProject(model.DXGC, singleProject, map, constructProject);
            }else{
                //解析单位工程
                await this.convertUnitProject(model.DWGC, singleProject, map, constructProject.rgfId);
            }
            newSingleProjects.push(singleProject);
        }
        oldSingleProjects.subSingleProjects = newSingleProjects;

    }


    /**
     * 解析单位工程
     * @param 单位工程
     * @param singleProject
     */
    async convertUnitProject(DWGC, singleProject,map,rgfId) {
        if(!ObjectUtils.isObject(DWGC)){

            let unitProjects = new Array();
            for (let i = 0; i < DWGC.length; i++) {
                let model = DWGC[i].$;
                let unitProject = new UnitProject();
                unitProject.sequenceNbr = Snowflake.nextId();
                unitProject.upCode = model.BM;
                unitProject.upName = model.MC;
                unitProject.uptotal = model.JE;
                unitProject.csxhj = model.QZCSF;
                unitProject.djcsxhj = model.QZDJCSF;
                unitProject.zjcsxhj = model.QZZJCSF;
                unitProject.safeFee = model.QZAQWMF;
                unitProject.gfee = model.QZGF;
                unitProject.sbf = model.QZSBF;
                unitProject.spId = singleProject.sequenceNbr;
                unitProject.constructId = singleProject.constructId;
                unitProject.rgfId = rgfId;
                // 添加工程基本信息 ---单位层级
                this.service.constructProjectService.initProjectOrUnitData(unitProject, 3,map);
                // 编制说明 ---单位层级
                this.service.constructProjectService.initProjectOrUnitBZSM(3, unitProject);
                //单位工程费用汇总（包含单位工程部分数据）
                await this.convertUnitProjectSummary(DWGC[i].DWGCFHZB,unitProject);
                //分部分项
                this.dispNo = 1;
                await this.convertItemBill(DWGC[i].FBFXGC,unitProject);
                this.dispNo = 1;
                //单价措施
                await this.convertMeasureTableDJ(DWGC[i].DJCSXM,unitProject);
                //总价措施
                await this.convertMeasureTableZJ(DWGC[i].ZJCSXM,unitProject);

                this.qbExtraTableArray = new Array();
                //暂列金额
                await this.convertProvisional(DWGC[i].QTXM[0].ZLJE,unitProject);
                //暂估价
                await this.convertZgjSums(DWGC[i].QTXM[0].ZGJ,unitProject);
                //总承包服务费
                await this.convertServiceCosts(DWGC[i].QTXM[0].ZCBFWF,unitProject);
                //计日工
                await this.convertDayWorks(DWGC[i].QTXM[0].JRG, unitProject);
                //暂估价
                await this.convertZgj(DWGC[i].QTXM[0].ZGJ, unitProject);
                //承包人
                await this.convertCbr(DWGC[i].CLJXSBZZSJSHZB[0].CJZM,unitProject);
                //其他项目 签证与索赔计价表 初始化
                let otherProjectQzSpJjbList = await this.service.otherProjectService.getInitOtherProjectQzSpJjb(unitProject);
                unitProject.otherProjectQzAndSuoPeis = otherProjectQzSpJjbList;
                //保存其他项目
                unitProject.otherProjects= ObjectUtils.isNotEmpty(this.qbExtraTableArray)?this.qbExtraTableArray:unitProject.otherProjects;;
                unitProject.constructProjectRcjs = [];
                unitProject.rcjDetailList = [];
                unitProjects.push(unitProject);
            }
            singleProject.unitProjects = unitProjects;
        }

    }

    /**
     * 费用汇总
     * @param DWGCFHZB
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertUnitProjectSummary(DWGCFHZB, unitProject) {
        if(!ObjectUtils.isEmpty(DWGCFHZB)){
            let XMFYZM = DWGCFHZB[0].XMFYZM;

            for (let i = 0; i < XMFYZM.length; i++) {
                let model = XMFYZM[i].$;
                switch(model.XH){
                    //分部分项工程量清单计价合计
                    case "1": {
                        unitProject.fbfxhj  = model.JE;
                        unitProject.fbfxrgf = model.QZRGF;
                        unitProject.fbfxclf = model.QZCLF;
                        unitProject.fbfxjxf = model.QZJXF;
                        break;
                    }

                    //措施项目清单计价合计
                    case "2": {
                        unitProject.csxhj  = model.JE;
                        unitProject.csxrgf = model.QZRGF;
                        unitProject.csxclf = model.QZCLF;
                        unitProject.csxjxf = model.QZJXF;
                        break;
                    }

                    //单价措施项目工程量清单计价合计
                    case "3": {
                        unitProject.djcsxhj = model.JE;
                        unitProject.djcsxrgf= model.QZRGF;
                        unitProject.djcsxclf= model.QZCLF;
                        unitProject.djcsxjxf= model.QZJXF;
                        break;
                    }

                    //其他总价措施项目清单计价合计
                    case "4": {
                        unitProject.zjcsxhj = model.JE;
                        unitProject.zjcsxrgf= model.QZRGF;
                        unitProject.zjcsxclf= model.QZCLF;
                        unitProject.zjcsxjxf= model.QZJXF;
                        break;
                    }

                    //其他项目清单计价合计
                    case "5": {
                        unitProject.qtxmhj = model.JE;
                        unitProject.qtxmrgf= model.QZRGF;
                        unitProject.qtxmclf= model.QZCLF;
                        unitProject.qtxmjxf= model.QZJXF;
                        break;
                    }

                    //规费
                    case "6": {
                        unitProject.gfee= model.JE;
                        break;
                    }

                    //安全生产、文明施工费
                    case "7": {
                        unitProject.safeFee= model.JE;
                        break;
                    }

                    //税前工程造价
                    case "8": {
                        unitProject.sqgczj= model.JE;
                        break;
                    }

                    //进项税额
                    case "9": {
                        unitProject.jxse= model.JE;
                        break;
                    }

                    //销项税额
                    case "10": {
                        unitProject.xxse= model.JE;
                        break;
                    }

                    //增值税应纳税额
                    case "11": {
                        unitProject.zzsynse= model.JE;
                        break;
                    }

                    //附加税费
                    case "12": {
                        unitProject.fjse= model.JE;
                        break;
                    }

                    //税金
                    case "13": {
                        unitProject.sj= model.JE;
                        break;
                    }
                    //工程造价
                    case "14": {
                        unitProject.uptotal= model.JE;
                        break;
                    }
                }



            }
        }


        
    }

    /**
     * 暂列金额
     * @param 暂列金额
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertProvisional(ZLJE, unitProject) {

        if(ObjectUtils.isEmpty(ZLJE)){
            //调用插入暂列金额默认值
            unitProject.otherProjectProvisionals = await this.service.otherProjectProvisionalService.importInitProjectProvisional();
            return
        }

        if(!ObjectUtils.isEmpty(ZLJE)){
            let model = ZLJE[0].$;
            //其他项目赋值
            let otherProject = new OtherProject();
            otherProject.sequenceNbr = Snowflake.nextId();
            otherProject.constructId = unitProject.constructId;
            otherProject.spId = unitProject.spId;
            otherProject.unitId = unitProject.sequenceNbr;
            otherProject.sortNo = 1;
            otherProject.extraName = '暂列金额';
            otherProject.type = OtherProjectCalculationBaseConstant.zljr;
            otherProject.markSj = 1;
            otherProject.markSafa = 1;
            otherProject.amount = 1;
            let qtJxTotal = 0;
            let qtCsTotal = 0;

            if(!ObjectUtils.isEmpty(model)){
                otherProject.dispNo = model.XH;
                otherProject.total = NumberUtil.costPriceAmountFormat(model.JE);
                otherProject.unit = model.JLDW;
                otherProject.description = model.BZ;
            }else {
                otherProject.dispNo = '1';
                // return;
            }

            if(!ObjectUtils.isEmpty(ZLJE[0].ZLJEZM)){
                let ZLJEZM = ZLJE[0].ZLJEZM;
                let otherProjectProvisionalArray = new Array();
                for (let i = 0; i < ZLJEZM.length; i++) {
                    let $ = ZLJEZM[i].$;
                    let otherProjectProvisional = new OtherProjectProvisional();
                    otherProjectProvisional.sequenceNbr = Snowflake.nextId();
                    otherProjectProvisional.name = $.XMMC;
                    otherProjectProvisional.unit = $.JLDW;
                    otherProjectProvisional.provisionalSum = NumberUtil.costPriceAmountFormat($.ZDJE);
                    otherProjectProvisional.sortNo = i+1;
                    otherProjectProvisional.dispNo = $.XH;
                    otherProjectProvisional.description = $.BZ;
                    otherProjectProvisional.constructId = unitProject.constructId;
                    otherProjectProvisional.spId = unitProject.spId;
                    otherProjectProvisional.unitId = unitProject.sequenceNbr;

                    otherProjectProvisional.amount = 1 ;
                    otherProjectProvisional.price = otherProjectProvisional.provisionalSum;//单价 没有单价所以直接默认赋值暂定金额
                    otherProjectProvisional.taxRemoval = 3 ; //除税系数(%)
                    // 进项合计 暂定金额*除税系数
                    otherProjectProvisional.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectProvisional.provisionalSum,otherProjectProvisional.taxRemoval/100)) ;
                    otherProjectProvisional.csPrice = NumberUtil.subtract(otherProjectProvisional.provisionalSum,otherProjectProvisional.jxTotal);
                    otherProjectProvisional.csTotal = otherProjectProvisional.csPrice;
                    otherProjectProvisionalArray.push(otherProjectProvisional);
                }
                unitProject.otherProjectProvisionals = otherProjectProvisionalArray;
                
            }
            otherProject.jxTotal = qtJxTotal;
            otherProject.csTotal = qtCsTotal;
            this.qbExtraTableArray.push(otherProject);
        }



    }

    /**
     * 暂估价
     * @param 暂估价
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertZgjSums(ZGJ, unitProject) {
        if(!ObjectUtils.isEmpty(ZGJ)){

            let otherProjectZgjArray =new Array();
            let model = ZGJ[0].$;
            //其他项目赋值
            let otherProject = new OtherProject();
            otherProject.sequenceNbr = Snowflake.nextId();
            otherProject.extraName = '暂估价';
            otherProject.sortNo = 2;
            otherProject.constructId = unitProject.constructId;
            otherProject.spId = unitProject.spId;
            otherProject.unitId = unitProject.sequenceNbr;
            otherProject.markSj = 1;
            otherProject.markSafa = 1;
            otherProject.amount = 1;
            if(!ObjectUtils.isEmpty(model)){
                otherProject.dispNo = model.XH;
                otherProject.total = NumberUtil.costPriceAmountFormat(model.JE);
                otherProject.unit = model.JLDW;
                otherProject.description = model.BZ;
            }else {
                otherProject.dispNo = '2';
            }
            let qtJxTotal = 0;
            let qtCsTotal = 0;
            this.qbExtraTableArray.push(otherProject);
            let sortNo= 0;
            let CLZGJ = ZGJ[0].CLZGJ;
            if(!ObjectUtils.isEmpty(CLZGJ)){
                model = CLZGJ[0].$;
                let otherProjectZgj = new OtherProjectZgj();
                //其他项目
                otherProject = new OtherProject();
                otherProject.sequenceNbr = Snowflake.nextId();
                otherProject.extraName = '材料暂估价';
                otherProject.sortNo = 3;
                otherProject.constructId = unitProject.constructId;
                otherProject.spId = unitProject.spId;
                otherProject.unitId = unitProject.sequenceNbr;
                otherProject.amount = 1;
                otherProject.markSj = 0;
                otherProject.markSafa = 0;
                if(!ObjectUtils.isEmpty(model)){
                    otherProject.dispNo = model.XH;
                    otherProject.total = NumberUtil.costPriceAmountFormat(model.JE);
                    otherProject.unit = model.JLDW;
                    otherProject.description = model.BZ;
                }else {
                    otherProject.dispNo = '2.1';
                }

                this.qbExtraTableArray.push(otherProject);
                let ZGCLZM = CLZGJ[0].ZGCLZM;
                if(!ObjectUtils.isEmpty(ZGCLZM)){
                    for (let i = 0; i < ZGCLZM.length; i++) {
                        model = ZGCLZM[i].$;
                        otherProjectZgj = new OtherProjectZgj();
                        otherProjectZgj.sequenceNbr = Snowflake.nextId();
                        otherProjectZgj.price   = NumberUtil.costPriceAmountFormat(model.SCJ);
                        otherProjectZgj.name   = model.MC;
                        otherProjectZgj.attr  =model.GGXH;
                        otherProjectZgj.unit  =model.JLDW;
                        otherProjectZgj.csTotal  =NumberUtil.costPriceAmountFormat(model.CSHJ);
                        // otherProjectZgj.parentId = id;
                        otherProjectZgj.dispNo   = ''+(i+1);
                        otherProjectZgj.sortNo   = sortNo++;
                        otherProjectZgj.description = model.BZ;
                        otherProjectZgj.constructId = unitProject.constructId;
                        otherProjectZgj.spId = unitProject.spId;
                        otherProjectZgj.unitId = unitProject.sequenceNbr;
                        otherProjectZgjArray.push(otherProjectZgj);
                    }
                }
            }
            unitProject.otherProjectClZgjs = otherProjectZgjArray;
            otherProjectZgjArray = new Array();
            let SBZGJ = ZGJ[0].SBZGJ;
            if(!ObjectUtils.isEmpty(SBZGJ)){
                model = SBZGJ[0].$;
                let otherProjectZgj = new OtherProjectZgj();
                //其他项目
                otherProject = new OtherProject();
                otherProject.sequenceNbr = Snowflake.nextId();
                otherProject.extraName = '设备暂估价';
                otherProject.sortNo = 4;
                otherProject.constructId = unitProject.constructId;
                otherProject.spId = unitProject.spId;
                otherProject.unitId = unitProject.sequenceNbr;
                otherProject.amount = 1;
                otherProject.markSj = 0;
                otherProject.markSafa = 0;
                if(!ObjectUtils.isEmpty(model)){
                    otherProject.dispNo = model.XH;
                    otherProject.total = NumberUtil.costPriceAmountFormat(model.JE);
                    otherProject.unit = model.JLDW;
                    otherProject.description = model.BZ;
                }else {
                    otherProject.dispNo = '2.2';
                }

                this.qbExtraTableArray.push(otherProject);
                let ZGGCZM = SBZGJ[0].ZGGCZM;
                if(!ObjectUtils.isEmpty(ZGGCZM)){
                    for (let i = 0; i < ZGGCZM.length; i++) {
                        model = ZGGCZM[i].$;
                        otherProjectZgj = new OtherProjectZgj();
                        otherProjectZgj.sequenceNbr = Snowflake.nextId();
                        otherProjectZgj.price   = NumberUtil.costPriceAmountFormat(model.JE);
                        otherProjectZgj.name   = model.MC;
                        otherProjectZgj.attr  =model.GGXH;
                        otherProjectZgj.unit  =model.JLDW;

                        // otherProjectZgj.parentId = id;
                        otherProjectZgj.dispNo   = ''+(i+1);
                        otherProjectZgj.sortNo   = sortNo++;
                        otherProjectZgj.description = model.BZ;
                        otherProjectZgj.constructId = unitProject.constructId;
                        otherProjectZgj.spId = unitProject.spId;
                        otherProjectZgj.unitId = unitProject.sequenceNbr;
                        otherProjectZgjArray.push(otherProjectZgj);
                    }
                }
            }
            unitProject.otherProjectSbZgjs = otherProjectZgjArray;
            otherProjectZgjArray = new Array();
            let ZYGCZGJ = ZGJ[0].ZYGCZGJ;
            if(!ObjectUtils.isEmpty(ZYGCZGJ)){
                model = ZYGCZGJ[0].$;
                let otherProjectZgj = new OtherProjectZygcZgj();
                //其他项目
                otherProject = new OtherProject();
                otherProject.sequenceNbr = Snowflake.nextId();
                otherProject.extraName = '专业工程暂估价';
                otherProject.sortNo = 5;
                otherProject.constructId = unitProject.constructId;
                otherProject.spId = unitProject.spId;
                otherProject.unitId = unitProject.sequenceNbr;
                otherProject.type = OtherProjectCalculationBaseConstant.zygczgj;
                otherProject.markSj = 1;
                // otherProject.markSafa = 1;
                otherProject.amount = 1;
                if(!ObjectUtils.isEmpty(model)){
                    otherProject.dispNo = model.XH;
                    otherProject.total = NumberUtil.costPriceAmountFormat(model.JE);
                    otherProject.unit = model.JLDW;
                    otherProject.description = model.BZ;
                }else {
                    otherProject.dispNo = '2.3';
                }
                // this.qbExtraTableArray.push(otherProject);
                let ZGGCZM = ZYGCZGJ[0].ZGGCZM;
                if(!ObjectUtils.isEmpty(ZGGCZM)){
                    for (let i = 0; i < ZGGCZM.length; i++) {
                        model = ZGGCZM[i].$;
                        otherProjectZgj = new OtherProjectZygcZgj();
                        otherProjectZgj.sequenceNbr = Snowflake.nextId();
                        otherProjectZgj.total   = NumberUtil.costPriceAmountFormat(model.JE);
                        otherProjectZgj.name   = model.GCMC;
                        otherProjectZgj.content   = model.GCNR;
                        otherProjectZgj.unit  =model.DW;
                        // otherProjectZgj.parentId = id;
                        otherProjectZgj.dispNo   = ''+(i+1);
                        otherProjectZgj.sortNo   = sortNo++;
                        otherProjectZgj.description = model.BZ;
                        otherProjectZgj.constructId = unitProject.constructId;
                        otherProjectZgj.spId = unitProject.spId;
                        otherProjectZgj.unitId = unitProject.sequenceNbr;

                        otherProjectZgj.amount = 1 ;
                        otherProjectZgj.price = otherProjectZgj.total;//单价 没有单价所以直接默认赋值暂定金额
                        otherProjectZgj.taxRemoval = 3 ; //除税系数(%)
                        // 进项合计 暂定金额*除税系数
                        otherProjectZgj.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectZgj.total,otherProjectZgj.taxRemoval/100)) ;
                        otherProjectZgj.csPrice = NumberUtil.subtract(otherProjectZgj.total,otherProjectZgj.jxTotal);
                        otherProjectZgj.csTotal = otherProjectZgj.csPrice;

                        qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectZgj.jxTotal);
                        qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectZgj.csTotal);
                        otherProjectZgjArray.push(otherProjectZgj);
                    }
                }
                unitProject.otherProjectZygcZgjs = otherProjectZgjArray;
                otherProject.jxTotal = qtJxTotal;
                otherProject.csTotal = qtCsTotal;
                this.qbExtraTableArray.push(otherProject);
            }
            if(ObjectUtils.isEmpty(unitProject.otherProjectZygcZgjs)){
                //调用插入专业工程额默认值
                unitProject.otherProjectZygcZgjs = await this.service.otherProjectZgjService.importInitOtherProjectZygcZgj()
            }



            this.qbExtraTableArray[1].jxTotal =this.qbExtraTableArray[4].jxTotal;
            this.qbExtraTableArray[1].csTotal =this.qbExtraTableArray[4].csTotal;

        }
    }


    /**
     * 总承包服务费
     * @param 总承包服务费
     * @param unitProject
     * @returns {Promise<void>}
     */
    async  convertServiceCosts(ZCBFWF, unitProject) {


        let model = ZCBFWF[0].$;

        let otherProject = new OtherProject();
        otherProject.sequenceNbr = Snowflake.nextId();
        otherProject.extraName = '总承包服务费';
        otherProject.sortNo = 6;

        otherProject.constructId = unitProject.constructId;
        otherProject.spId = unitProject.spId;
        otherProject.unitId = unitProject.sequenceNbr;
        otherProject.type = OtherProjectCalculationBaseConstant.zcbfwf;
        otherProject.markSj = 1;
        otherProject.markSafa = 1;
        otherProject.amount = 1;
        if(!ObjectUtils.isEmpty(model)){
            otherProject.description = model.BZ;
            otherProject.unit = model.JLDW;
            otherProject.dispNo = model.XH;
            otherProject.total = NumberUtil.costPriceAmountFormat(model.JE);
        }else {
            otherProject.dispNo = '3';
        }


        if(ObjectUtils.isEmpty(ZCBFWF)){
            this.qbExtraTableArray.push(otherProject);
            //调用初始化总承包服务接口
            unitProject.otherProjectServiceCosts = await this.service.otherProjectService.getInitOtherProjectZcbfwfList()
            return;
        }
        // 根据类型分组

        let zygcList = new Array();
        let clList   = new Array();
        let sbList   = new Array();

        if(!ObjectUtils.isEmpty(ZCBFWF[0].ZBRFBGC[0].ZCBFWFZM)){
            for (let i = 0; i < ZCBFWF[0].ZBRFBGC[0].ZCBFWFZM.length; i++) {
                zygcList.push(ZCBFWF[0].ZBRFBGC[0].ZCBFWFZM[i].$);
            }
        }
        if(!ObjectUtils.isEmpty(ZCBFWF[0].ZBRGYCL[0].ZCBFWFZM)){
            for (let i = 0; i < ZCBFWF[0].ZBRGYCL[0].ZCBFWFZM.length; i++) {
                clList.push(ZCBFWF[0].ZBRGYCL[0].ZCBFWFZM[i].$);
            }
        }
        if(!ObjectUtils.isEmpty(ZCBFWF[0].ZBRGYSB[0].ZCBFWFZM)){
            for (let i = 0; i < ZCBFWF[0].ZBRGYSB[0].ZCBFWFZM.length; i++) {
                sbList.push(ZCBFWF[0].ZBRGYSB[0].ZCBFWFZM[i].$);
            }
        }

        unitProject.otherProjectServiceCosts = new Array();
        await this.setServiceCosts(unitProject, zygcList, '1', '招标人另行发包专业工程');
        await this.setServiceCosts(unitProject, clList, '2', '招标人供应材料');
        await this.setServiceCosts(unitProject, sbList, '3', '招标人供应设备');

        let ts = unitProject.otherProjectServiceCosts.filter(item =>item.dispNo.indexOf('.')!== -1);
        otherProject.total = Number(ts.reduce((accumulator, item) => {
            return NumberUtil.add(accumulator,item.fwje)  ;
        }, 0).toFixed(2))
        this.qbExtraTableArray.push(otherProject);
    }

    /**
     * 总承包服务费
     * @param unitProject
     * @param list
     * @param dispNo
     * @param name
     * @returns {Promise<void>}
     */
    async  setServiceCosts(unitProject, list, dispNo, name) {
        let serviceCostsArray = new Array();
        let parentsUuid = Snowflake.nextId();
        let serviceCostsParents = new OtherProjectServiceCost();
        serviceCostsParents.sequenceNbr = parentsUuid;
        serviceCostsParents.dispNo = dispNo;
        serviceCostsParents.fxName = name;
        serviceCostsParents.sortNo = 0;
        serviceCostsParents.constructId = unitProject.constructId;
        serviceCostsParents.spId = unitProject.spId;
        serviceCostsParents.unitId = unitProject.sequenceNbr;
        serviceCostsParents.dataType = 1;

        serviceCostsArray.push(serviceCostsParents);

        if (!ObjectUtils.isEmpty(list)) {
            for (let i = 0; i < list.length; i++) {
                let model = list[i];
                let uuid = Snowflake.nextId();
                let otherProjectServiceCost = new OtherProjectServiceCost();
                otherProjectServiceCost.sequenceNbr  = uuid;
                otherProjectServiceCost.xmje  = NumberUtil.costPriceAmountFormat(model.JSJC);
                otherProjectServiceCost.dispNo  = dispNo+'.'+(i+1);
                otherProjectServiceCost.fxName  = model.XMMC;
                otherProjectServiceCost.fwje  = NumberUtil.costPriceAmountFormat(model.JE);
                otherProjectServiceCost.rate  = model.FL;
                otherProjectServiceCost.parentId  = parentsUuid;
                otherProjectServiceCost.constructId = unitProject.constructId;
                otherProjectServiceCost.spId = unitProject.spId;
                otherProjectServiceCost.unitId = unitProject.sequenceNbr;
                otherProjectServiceCost.sortNo  = i + 1;
                otherProjectServiceCost.amount  = 1;
                otherProjectServiceCost.dataType = 2;
                serviceCostsArray.push(otherProjectServiceCost);
            }
        }else {
            let myNumber = 0;
            let formattedNumber = NumberUtil.costPriceAmountFormat(myNumber);
            let otherProjectServiceCost = new OtherProjectServiceCost();
            otherProjectServiceCost.sequenceNbr = Snowflake.nextId();
            otherProjectServiceCost.dispNo= dispNo+'.1';
            otherProjectServiceCost.xmje = formattedNumber;
            otherProjectServiceCost.rate= formattedNumber;
            otherProjectServiceCost.fwje = formattedNumber;
            otherProjectServiceCost.dataType =2 ;
            otherProjectServiceCost.parentId = parentsUuid;
            serviceCostsArray.push(otherProjectServiceCost);
        }
        unitProject.otherProjectServiceCosts.push(...serviceCostsArray);

    }

    /**
     * 计日工
     * @param 计日工
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertDayWorks(JRG, unitProject) {
        if (ObjectUtils.isEmpty(JRG)) {
            return;
        }

        const model = JRG[0].$;

        const otherProject = new OtherProject();
        otherProject.sequenceNbr = Snowflake.nextId();
        otherProject.extraName = '计日工';
        otherProject.sortNo = 7;
        otherProject.constructId = unitProject.constructId;
        otherProject.spId = unitProject.spId;
        otherProject.unitId = unitProject.sequenceNbr;
        otherProject.type = OtherProjectCalculationBaseConstant.jrg;
        otherProject.markSj = 1;
        otherProject.markSafa = 1;
        otherProject.amount = 1;
        if(!ObjectUtils.isEmpty(model)){
            otherProject.unit = model.JLDW;
            otherProject.dispNo = model.XH;
            otherProject.total = NumberUtil.costPriceAmountFormat(model.JE);
            otherProject.description = model.BZ;
        }else {
            otherProject.dispNo = '4';
        }

        let qtJxTotal = 0;
        let qtCsTotal = 0;
        const dayWorksArray = [];
        let sort = 0;

        const types = ['RG', 'CL', 'JX'];

        for (let type of types) {
            const arr = JRG[0][type]?.[0];
            if (!arr) continue;

            let otherProjectDayWork = new OtherProjectDayWork();
            let id = Snowflake.nextId();

            otherProjectDayWork.sequenceNbr = id;
            otherProjectDayWork.worksName = arr.$.MC;
            otherProjectDayWork.total = NumberUtil.costPriceAmountFormat(arr.$.JE);
            otherProjectDayWork.dispNo = types.indexOf(type) + 1;
            otherProjectDayWork.sortNo = sort++;
            otherProjectDayWork.constructId = unitProject.constructId;
            otherProjectDayWork.spId = unitProject.spId;
            otherProjectDayWork.unitId = unitProject.sequenceNbr;
            otherProjectDayWork.dataType = 1;

            dayWorksArray.push(otherProjectDayWork);

            const ZM = arr[`${type}ZM`];
            if (!ZM) continue;

            for (let i = 0; i < ZM.length; i++) {
                const element = ZM[i].$;

                otherProjectDayWork = new OtherProjectDayWork();
                otherProjectDayWork.sequenceNbr = Snowflake.nextId();
                otherProjectDayWork.worksName = element.MC;
                otherProjectDayWork.specification = element.GGXH;
                otherProjectDayWork.unit = element.JLDW;
                otherProjectDayWork.tentativeQuantity = NumberUtil.qtxmAmountFormat(element.SL);
                otherProjectDayWork.price = NumberUtil.costPriceAmountFormat(element.SCJ);
                otherProjectDayWork.total = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.tentativeQuantity, otherProjectDayWork.price));
                otherProjectDayWork.taxRemoval = 0;
                if(type === 'CL'){
                    otherProjectDayWork.taxRemoval =  11.28;
                }
                if(type === 'JX'){
                    otherProjectDayWork.taxRemoval =  8.66;
                }
                otherProjectDayWork.jxTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.total,otherProjectDayWork.taxRemoval/100));
                otherProjectDayWork.csPrice = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.price,(100-otherProjectDayWork.taxRemoval)/100)) ;
                otherProjectDayWork.csTotal = NumberUtil.costPriceAmountFormat(NumberUtil.multiply(otherProjectDayWork.total,(100-otherProjectDayWork.taxRemoval)/100)) ;
                otherProjectDayWork.dispNo = `${types.indexOf(type) + 1}.${i + 1}`;
                otherProjectDayWork.sortNo = sort++;
                otherProjectDayWork.parentId = id;
                otherProjectDayWork.constructId = unitProject.constructId;
                otherProjectDayWork.spId = unitProject.spId;
                otherProjectDayWork.unitId = unitProject.sequenceNbr;
                otherProjectDayWork.dataType = 2;

                qtJxTotal =  NumberUtil.add(qtJxTotal,otherProjectDayWork.jxTotal);
                qtCsTotal = NumberUtil.add(qtCsTotal,otherProjectDayWork.csTotal);

                dayWorksArray.push(otherProjectDayWork);
            }
        }
        otherProject.jxTotal = qtJxTotal;
        otherProject.csTotal = qtCsTotal;
        this.qbExtraTableArray.push(otherProject);
        unitProject.otherProjectDayWorks = dayWorksArray;
    }

    async convertZgj(ZGJ, unitProject){


        if (ObjectUtils.isEmpty(ZGJ) || ObjectUtils.isEmpty(unitProject)){
            return ;
        }
        let zgjRcjList = [];
        let model1;
        let model2;
        let model;
        if (ObjectUtils.isNotEmpty(ZGJ[0].CLZGJ)){
            model1 = ZGJ[0].CLZGJ[0].ZGCLZM;
            model = model1;
        }
        if (ObjectUtils.isNotEmpty(ZGJ[0].SBZGJ)){
            model2 = ZGJ[0].SBZGJ[0].ZGCLZM;
            if (model){
                model = model1.concat(model2);
            }else {
                model = model2;
            }
        }

        if (ObjectUtils.isEmpty(model)){
            return;
        }

        for (let i = 0; i<model.length; i++){
            if (ObjectUtils.isEmpty(model[i])){
                continue;
            }

            if (!ObjectUtils.isEmpty(model[i].$.MC) && model[i].$.MC.includes("名称")){
                continue;
            }
            let zgjRcj = {};
            //主键
            //zgjRcj.sequenceNbr = Snowflake.nextId();

            //编码
            zgjRcj.materialCode = model[i].$.BM;
            //名称
            zgjRcj.materialName = model[i].$.MC;
            //类型
            zgjRcj.kind = model[i].$.LX;
            if (ObjectUtils.isEmpty(model[i].$.LX)){
                zgjRcj.kind = -1;
            }else {
                if (model[i].$.LX.includes("材")){
                    zgjRcj.kind = 2;
                }else if (model[i].$.LX.includes("设")){
                    zgjRcj.kind = 4;
                }
            }

            //规格型号
            zgjRcj.specification = model[i].$.GGXH;
            //单位
            zgjRcj.unit = model[i].$.JLDW;
            //来源
            // zgjRcj.source = model[i].$.编码;
            // //产地
            // zgjRcj.producer =model[i].$.产地;
            // //厂家
            // zgjRcj.manufactor = model[i].$.厂家;
            //备注
            zgjRcj.remark = model[i].$.BZ;
            //关联材料编码
            zgjRcj.relevancyMaterialCodeList = null;
            //锁定
            zgjRcj.lock = 0;
            //市场价
            zgjRcj.marketPrice = NumberUtil.costPriceAmountFormat(model[i].$.SCJ);
            if (ObjectUtils.isEmpty(zgjRcj.marketPrice)){
                zgjRcj.marketPrice = 0;
            }
            zgjRcj.totalNumber = NumberUtil.costPriceAmountFormat(model[i].$.SL);
            await this.service.rcjProcess.addZgjList(unitProject,zgjRcj,"文件导入");
            //zgjRcjList.push(zgjRcj);
        }
        //unitProject.zgjRcjList = zgjRcjList;

    }

    async convertCbr(cbr,unitProject){
        if (ObjectUtils.isEmpty(cbr) || ObjectUtils.isEmpty(unitProject)){
            return ;
        }

        for (let zyclsbMx of cbr) {
            if (ObjectUtils.isEmpty(zyclsbMx)){
                continue;
            }

            let cbrRcj = {};
            //编码
            cbrRcj.materialCode = zyclsbMx.$.BM;
            //名称
            cbrRcj.materialName = zyclsbMx.$.MC;
            //规格型号
            //cbrRcj.specification = zyclsbMx.$.规格型号;
            //单位
            cbrRcj.unit = zyclsbMx.$.JLDW;
            //来源
            cbrRcj.source = "文件导入";
            //合计数量
            cbrRcj.totalNumber = zyclsbMx.$.SL;
            //单价
            //cbrRcj.marketPrice = zyclsbMx.$.投标单价;
            //备注
            //cbrRcj.remark = zyclsbMx.$.备注;
            //基础单价
            //cbrRcj.benchmarkUnitPrice = zyclsbMx.$.基准单价;
            //风险系数
            //cbrRcj.riskCoefficient = zyclsbMx.$.风险系数;
            //产地
            //cbrRcj.producer = zyclsbMx.$.产地;
            //厂家
            //cbrRcj.manufactor = zyclsbMx.$.厂家;

            await this.service.rcjProcess.addCbrList(unitProject,cbrRcj,"文件导入");
        }

    }



    // async convertItemBill(分部分项工程, unitProject) {
    //     let itemBillProjectArray = new Array();
    //     //构建 单独的单位工程一行数据
    //     let itemBillProject = new ItemBillProject();
    //
    //     let topId = Snowflake.nextId();
    //     itemBillProject.sequenceNbr = topId;
    //     itemBillProject.name = '单位工程';
    //     itemBillProject.kind = BranchProjectLevelConstant.top;
    //     itemBillProject.constructId = unitProject.constructId;
    //     itemBillProject.spId = unitProject.spId;
    //     itemBillProject.unitId = unitProject.sequenceNbr;
    //     itemBillProjectArray.push(itemBillProject)
    //
    //     let 分部子目 = 分部分项工程[0].分部子目;
    //
    //     if(ObjectUtils.isEmpty(分部子目)){
    //         unitProject.itemBillProjects = itemBillProjectArray;
    //         return;
    //     }
    //
    //     let sortNo =0;
    //     for (let i = 0; i < 分部子目.length; i++) {
    //         let 分部子目Element = 分部子目[i];
    //         let $ = 分部子目Element.$;
    //         //添加分布
    //         itemBillProject = new ItemBillProject();
    //         let fbId = Snowflake.nextId();
    //         itemBillProject.sequenceNbr = fbId;
    //         itemBillProject.name =$.MC;
    //         itemBillProject.bdCode =$.编码;
    //         itemBillProject.total =$.JE;
    //         itemBillProject.kind = BranchProjectLevelConstant.fb;
    //         itemBillProject.parentId = topId;
    //         itemBillProject.constructId = unitProject.constructId;
    //         itemBillProject.spId = unitProject.spId;
    //         itemBillProject.unitId = unitProject.sequenceNbr;
    //         itemBillProjectArray.push(itemBillProject);
    //         //此处判断下级是分布还是清单 二
    //         if(ObjectUtils.isEmpty(分部子目Element.分部分项工程量清单)){
    //             //子分部
    //             let 子分部 = 分部子目Element.分部子目;
    //             for (let j = 0; j < 子分部.length; j++) {
    //                 let 子分部Element = 子分部[j];
    //                 $ = 子分部Element.$;
    //                 let zfbId = Snowflake.nextId();
    //                 itemBillProject.sequenceNbr = zfbId;
    //                 itemBillProject.name =$.MC;
    //                 itemBillProject.bdCode =$.编码;
    //                 itemBillProject.total =$.JE;
    //                 itemBillProject.kind = BranchProjectLevelConstant.zfb;
    //                 itemBillProject.parentId = fbId;
    //                 itemBillProject.constructId = unitProject.constructId;
    //                 itemBillProject.spId = unitProject.spId;
    //                 itemBillProject.unitId = unitProject.sequenceNbr;
    //                 itemBillProjectArray.push(itemBillProject);
    //
    //                 //此处判断下级是分布还是清单 三
    //                 if(ObjectUtils.isEmpty(子分部Element.分部分项工程量清单)){
    //                     //子分部
    //                     let 子子分部 = 子分部Element.分部子目;
    //                     for (let k = 0; k < 子子分部.length; k++) {
    //                         let 子子分部Element = 子子分部[k];
    //                         $ = 子子分部Element.$;
    //                         let zzfbId = Snowflake.nextId();
    //                         itemBillProject.sequenceNbr = zzfbId;
    //                         itemBillProject.name =$.MC;
    //                         itemBillProject.bdCode =$.编码;
    //                         itemBillProject.total =$.JE;
    //                         itemBillProject.kind = BranchProjectLevelConstant.zfb;
    //                         itemBillProject.parentId = zfbId;
    //                         itemBillProject.constructId = unitProject.constructId;
    //                         itemBillProject.spId = unitProject.spId;
    //                         itemBillProject.unitId = unitProject.sequenceNbr;
    //                         itemBillProjectArray.push(itemBillProject);
    //                         //此处判断下级是分布还是清单 四
    //                         if(ObjectUtils.isEmpty(子子分部Element.分部分项工程量清单)){
    //                             //子分部
    //                             let 子子子分部 = 子子分部Element.分部子目;
    //                             for (let q = 0; q < 子子分部.length; q++) {
    //                                 let 子子子分部Element = 子子子分部[q];
    //                                 $ = 子子子分部Element.$;
    //                                 let zzzfbId = Snowflake.nextId();
    //                                 itemBillProject.sequenceNbr = zzzfbId;
    //                                 itemBillProject.name =$.MC;
    //                                 itemBillProject.bdCode =$.编码;
    //                                 itemBillProject.total =$.JE;
    //                                 itemBillProject.kind = BranchProjectLevelConstant.zfb;
    //                                 itemBillProject.parentId = zzfbId;
    //                                 itemBillProject.constructId = unitProject.constructId;
    //                                 itemBillProject.spId = unitProject.spId;
    //                                 itemBillProject.unitId = unitProject.sequenceNbr;
    //                                 itemBillProjectArray.push(itemBillProject);
    //
    //                                 //清单
    //                                 let 分部分项工程量清单 = 子子分部Element.分部分项工程量清单;
    //                                 for (let w = 0; w < 分部分项工程量清单.length; w++) {
    //                                     $ = 分部分项工程量清单[w].$;
    //                                     itemBillProject = new ItemBillProject();
    //                                     itemBillProject.sequenceNbr = Snowflake.nextId();
    //                                     itemBillProject.name =$.项目名称;
    //                                     itemBillProject.bdCode =$.项目编码;
    //                                     itemBillProject.unit =$.JLDW;
    //                                     itemBillProject.quantity =$.工程量;
    //                                     itemBillProject.rfee =$.人工费单价;
    //                                     itemBillProject.cfee =$.材料费单价;
    //                                     itemBillProject.jfee =$.机械费单价;
    //                                     itemBillProject.managerFee =$.管理费单价;
    //                                     itemBillProject.profitFee =$.利润单价;
    //                                     itemBillProject.price =$.综合单价;
    //                                     itemBillProject.total =$.综合合价;
    //                                     itemBillProject.projectAttr =$.项目特征;
    //                                     itemBillProject.kind = BranchProjectLevelConstant.qd;
    //                                     itemBillProject.sortNo =sortNo++;
    //                                     itemBillProject.parentId = zzzfbId;
    //                                     itemBillProject.constructId = unitProject.constructId;
    //                                     itemBillProject.spId = unitProject.spId;
    //                                     itemBillProject.unitId = unitProject.sequenceNbr;
    //                                     itemBillProjectArray.push(itemBillProject);
    //
    //                                 }
    //                             }
    //                         }else {
    //                             //清单
    //                             let 分部分项工程量清单 = 子子分部Element.分部分项工程量清单;
    //                             for (let e = 0; e < 分部分项工程量清单.length;e++) {
    //                                 $ = 分部分项工程量清单[e].$;
    //                                 itemBillProject = new ItemBillProject();
    //                                 itemBillProject.sequenceNbr = Snowflake.nextId();
    //                                 itemBillProject.name =$.项目名称;
    //                                 itemBillProject.bdCode =$.项目编码;
    //                                 itemBillProject.unit =$.JLDW;
    //                                 itemBillProject.quantity =$.工程量;
    //                                 itemBillProject.rfee =$.人工费单价;
    //                                 itemBillProject.cfee =$.材料费单价;
    //                                 itemBillProject.jfee =$.机械费单价;
    //                                 itemBillProject.managerFee =$.管理费单价;
    //                                 itemBillProject.profitFee =$.利润单价;
    //                                 itemBillProject.price =$.综合单价;
    //                                 itemBillProject.total =$.综合合价;
    //                                 itemBillProject.projectAttr =$.项目特征;
    //                                 itemBillProject.kind = BranchProjectLevelConstant.qd;
    //                                 itemBillProject.sortNo =sortNo++;
    //                                 itemBillProject.parentId = zzfbId;
    //                                 itemBillProject.constructId = unitProject.constructId;
    //                                 itemBillProject.spId = unitProject.spId;
    //                                 itemBillProject.unitId = unitProject.sequenceNbr;
    //                                 itemBillProjectArray.push(itemBillProject);
    //                             }
    //                         }
    //                     }
    //                 }else {
    //                     //清单
    //                     let 分部分项工程量清单 = 子分部Element.分部分项工程量清单;
    //                     for (let r = 0; r < 分部分项工程量清单.length; r++) {
    //                         $ = 分部分项工程量清单[r].$;
    //                         itemBillProject = new ItemBillProject();
    //                         itemBillProject.sequenceNbr = Snowflake.nextId();
    //                         itemBillProject.name =$.项目名称;
    //                         itemBillProject.bdCode =$.项目编码;
    //                         itemBillProject.unit =$.JLDW;
    //                         itemBillProject.quantity =$.工程量;
    //                         itemBillProject.rfee =$.人工费单价;
    //                         itemBillProject.cfee =$.材料费单价;
    //                         itemBillProject.jfee =$.机械费单价;
    //                         itemBillProject.managerFee =$.管理费单价;
    //                         itemBillProject.profitFee =$.利润单价;
    //                         itemBillProject.price =$.综合单价;
    //                         itemBillProject.total =$.综合合价;
    //                         itemBillProject.projectAttr =$.项目特征;
    //                         itemBillProject.kind = BranchProjectLevelConstant.qd;
    //                         itemBillProject.sortNo =sortNo++;
    //                         itemBillProject.parentId = zfbId;
    //                         itemBillProject.constructId = unitProject.constructId;
    //                         itemBillProject.spId = unitProject.spId;
    //                         itemBillProject.unitId = unitProject.sequenceNbr;
    //                         itemBillProjectArray.push(itemBillProject);
    //
    //
    //                     }
    //                 }
    //
    //             }
    //         }else {
    //             //清单
    //             let 分部分项工程量清单 = 分部子目Element.分部分项工程量清单;
    //             for (let t = 0; t < 分部分项工程量清单.length; t++) {
    //                  $ = 分部分项工程量清单[t].$;
    //                 itemBillProject = new ItemBillProject();
    //                 itemBillProject.sequenceNbr = Snowflake.nextId();
    //                 itemBillProject.name =$.项目名称;
    //                 itemBillProject.bdCode =$.项目编码;
    //                 itemBillProject.unit =$.JLDW;
    //                 itemBillProject.quantity =$.工程量;
    //                 itemBillProject.rfee =$.人工费单价;
    //                 itemBillProject.cfee =$.材料费单价;
    //                 itemBillProject.jfee =$.机械费单价;
    //                 itemBillProject.managerFee =$.管理费单价;
    //                 itemBillProject.profitFee =$.利润单价;
    //                 itemBillProject.price =$.综合单价;
    //                 itemBillProject.total =$.综合合价;
    //                 itemBillProject.projectAttr =$.项目特征;
    //                 itemBillProject.kind = BranchProjectLevelConstant.qd;
    //                 itemBillProject.sortNo =sortNo++;
    //                 itemBillProject.parentId = fbId;
    //                 itemBillProject.constructId = unitProject.constructId;
    //                 itemBillProject.spId = unitProject.spId;
    //                 itemBillProject.unitId = unitProject.sequenceNbr;
    //                 itemBillProjectArray.push(itemBillProject);
    //             }
    //         }
    //     }
    //
    //     unitProject.itemBillProjects = itemBillProjectArray;
    // }


    /**
     * 分部分项
     * @param 分部分项工程
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertItemBill(FBFXGC, unitProject) {
        let itemBillProjectArray = [];
        let itemBillProject = new ItemBillProject();
        let topId = Snowflake.nextId();
        itemBillProject.sequenceNbr = topId;
        itemBillProject.name = '单位工程';
        itemBillProject.kind = BranchProjectLevelConstant.top;
        itemBillProject.constructId = unitProject.constructId;
        itemBillProject.spId = unitProject.spId;
        itemBillProject.unitId = unitProject.sequenceNbr;
        itemBillProject.displaySign = BranchProjectDisplayConstant.open;
        itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
        itemBillProjectArray.push(itemBillProject);
        let FBZM = FBFXGC[0].FBZM;
        if (ObjectUtils.isEmpty(FBZM)) {
            let FBFXGCLQD = FBFXGC[0].FBFXGCLQD;
            if(!ObjectUtils.isEmpty(FBFXGCLQD)){
                await this.convertItemBillQd(FBFXGCLQD, topId, unitProject, itemBillProjectArray);
            }
            unitProject.itemBillProjects = arrayToTree(itemBillProjectArray);
            return;
        }

        let kind = BranchProjectLevelConstant.fb;
        // 递归处理子项目
        const createSubProjects =async (subProjects, parentId,kind) => {
            for (let i = 0; i < subProjects.length; i++) {
                let subProjectElement = subProjects[i];
                let $ = subProjectElement.$;
                itemBillProject = new ItemBillProject();
                let id = Snowflake.nextId();
                itemBillProject.sequenceNbr = id;
                itemBillProject.name = $.MC;
                itemBillProject.bdCode = $.BM;
                itemBillProject.total = Number($.JE);
                itemBillProject.kind = kind;
                itemBillProject.parentId = parentId;
                itemBillProject.constructId = unitProject.constructId;
                itemBillProject.spId = unitProject.spId;
                itemBillProject.unitId = unitProject.sequenceNbr;
                itemBillProject.displaySign = BranchProjectDisplayConstant.open;
                itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
                itemBillProjectArray.push(itemBillProject);

                if (ObjectUtils.isEmpty(subProjectElement.FBFXGCLQD)) {
                    await createSubProjects(subProjectElement.FBZM, id,BranchProjectLevelConstant.zfb);
                } else {
                    let FBFXGCLQD = subProjectElement.FBFXGCLQD;
                    await this.convertItemBillQd(FBFXGCLQD, id, unitProject, itemBillProjectArray);
                }
            }
        };

        await createSubProjects(FBZM, topId,kind);
        unitProject.itemBillProjects = arrayToTree(itemBillProjectArray);
    }


    async convertItemBillQd(FBFXGCLQD, id, unitProject, itemBillProjectArray) {
        for (let t = 0; t < FBFXGCLQD.length; t++) {
            let $ = FBFXGCLQD[t].$;
            let itemBillProject = new ItemBillProject();
            itemBillProject.sequenceNbr = Snowflake.nextId();


            itemBillProject.name = $.XMMC;
            itemBillProject.name = $.XMMC;
            itemBillProject.bdCode = $.XMBM;
            itemBillProject.fxCode = $.XMBM;

            itemBillProject.unit = $.JLDW;
            // itemBillProject.quantity = $.GCL;
            itemBillProject.quantity = NumberUtil.numberScale($.GCL,getUnitFormatEnum(itemBillProject.unit).value) + "";
            //清单工程量=清单工程量表达式/单位符号前数值
            let 单位num = $.JLDW.replace(/[^0-9].*/ig, '') !== '' ? $.JLDW.replace(/[^0-9].*/ig, '') : 1;
            itemBillProject.quantityExpression = NumberUtil.multiplyToStringExEnd0(单位num, $.GCL, ConstantUtil.QD_DE_DECIMAL_POINT);
            itemBillProject.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, $.GCL, ConstantUtil.QD_DE_DECIMAL_POINT);
            itemBillProject.projectAttr = $.XMTZ;
            itemBillProject.kind = BranchProjectLevelConstant.qd;
            itemBillProject.dispNo = (this.dispNo++) + '';
            itemBillProject.parentId = id;
            itemBillProject.constructId = unitProject.constructId;
            itemBillProject.spId = unitProject.spId;
            itemBillProject.unitId = unitProject.sequenceNbr;
            itemBillProject.displaySign = BranchProjectDisplayConstant.noSign;
            itemBillProject.displayStatu = BranchProjectDisplayConstant.displayMax;
            if (ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode))) {
                //如果map中没有去查数据库
                let res = await this.service.baseListService.queryQdByCode(itemBillProject.fxCode);
                if (!ObjectUtils.isEmpty(res)) {
                    this.qdMap.set(itemBillProject.fxCode, res)
                }
            }
            itemBillProject.standardId = !ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode)) ? this.qdMap.get(itemBillProject.fxCode).sequenceNbr : '';
            itemBillProject.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(itemBillProject.fxCode)) ? 0 : 1;
            itemBillProjectArray.push(itemBillProject);
        }

    }

    /**
     * 单价措施项目（目前只处理单价措施清单）
     * @param 措施项目
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertMeasureTableDJ(DJCSXM, unitProject) {
        if(ObjectUtils.isEmpty(DJCSXM)){
            return ;
        }
        let CSBT = DJCSXM[0].CSBT;
        if(!ObjectUtils.isEmpty(CSBT)){
            let djMeasureProjectTableArray = new Array();
            for (let i = 0; i < CSBT.length; i++) {
                let CSBTElement = CSBT[i];
                let $ = CSBTElement.$;
                //存放标题
                // let measureProjectTableBt = new MeasureProjectTable();
                // let btId = Snowflake.nextId();
                // measureProjectTableBt.sequenceNbr = btId;
                // measureProjectTableBt.fxCode = $.BM;
                // measureProjectTableBt.name = $.MC;
                // measureProjectTableBt.total = $.JE;
                // measureProjectTableBt.constructId = unitProject.constructId;
                // measureProjectTableBt.spId = unitProject.spId;
                // measureProjectTableBt.unitId = unitProject.sequenceNbr;
                // measureProjectTableBt.kind = BranchProjectLevelConstant.zfb;
                // measureProjectTableBt.displaySign = BranchProjectDisplayConstant.open;
                // measureProjectTableBt.displayStatu = BranchProjectDisplayConstant.displayMax;
                // measureProjectTableBt.adjustmentCoefficient = 1;
                // djMeasureProjectTableArray.push(measureProjectTableBt)



                    let CSZMQD = CSBTElement.CSZMQD;
                    if(!ObjectUtils.isEmpty(CSZMQD)){
                        for (let k = 0; k < CSZMQD.length; k++) {
                            $ = CSZMQD[k].$ ;
                            let measureProjectTable = new MeasureProjectTable();
                            measureProjectTable.sequenceNbr =Snowflake.nextId();
                            measureProjectTable.dispNo = (k+1)+'';
                            measureProjectTable.name = $.MC;
                            measureProjectTable.name = $.MC;
                            measureProjectTable.bdCode = $.BM;
                            measureProjectTable.fxCode = $.BM;
                            measureProjectTable.projectAttr = $.XMTZ;
                            measureProjectTable.unit = $.JLDW;
                            // measureProjectTable.quantity = $.GCL;
                            measureProjectTable.quantity = NumberUtil.numberScale($.GCL,getUnitFormatEnum(measureProjectTable.unit).value);
                            let 单位num = $.GCL.replace(/[^0-9].*/ig,'')!==''?$.GCL.replace(/[^0-9].*/ig,''): 1;
                            measureProjectTable.quantityExpression =NumberUtil.multiplyToStringExEnd0(单位num, $.GCL,ConstantUtil.QD_DE_DECIMAL_POINT);
                            measureProjectTable.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, $.GCL,ConstantUtil.QD_DE_DECIMAL_POINT);

                            measureProjectTable.kind = BranchProjectLevelConstant.qd;
                            measureProjectTable.constructId = unitProject.constructId;
                            measureProjectTable.spId = unitProject.spId;
                            measureProjectTable.unitId = unitProject.sequenceNbr;
                            measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                            measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                            measureProjectTable.adjustmentCoefficient = 1;
                            if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                //如果map中没有去查数据库
                                let res =await this.service.baseListService.queryQdByCode(measureProjectTable.fxCode);
                                if(!ObjectUtils.isEmpty(res)){
                                    this.qdMap.set(measureProjectTable.fxCode,res)
                                }
                            }
                            if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                                    measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                                }
                            }
                            measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr:'';
                            measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                            djMeasureProjectTableArray.push(measureProjectTable);
                        }
                        unitProject.djMeasureProjectTableArray = djMeasureProjectTableArray;
                    }


            }
        }
    }

    /**
     *
     * @param 总价措施措施项目
     * @param unitProject
     * @returns {Promise<void>}
     */
    async convertMeasureTableZJ(ZJCSXM, unitProject) {
        if(ObjectUtils.isEmpty(ZJCSXM)){
            return ;
        }
        let CSBT = ZJCSXM[0].CSBT;
        if(!ObjectUtils.isEmpty(CSBT)){
            for (let i = 0; i < CSBT.length; i++) {
                let CSBTElement = CSBT[i];
                let $ = CSBTElement.$;
                if($.MC==='安全生产、文明施工费'){
                    let awfMeasureProjectTableArray = new Array();
                    let CSZMQD = CSBTElement.CSZMQD;
                    if(!ObjectUtils.isEmpty(CSZMQD)){
                        for (let k = 0; k < CSZMQD.length; k++) {
                            $ = CSZMQD[k].$ ;
                            let measureProjectTable = new MeasureProjectTable();
                            measureProjectTable.sequenceNbr =Snowflake.nextId();
                            measureProjectTable.dispNo = (k+1)+'';
                            measureProjectTable.name = $.MC;
                            measureProjectTable.name = $.MC;
                            measureProjectTable.bdCode = $.BM;
                            measureProjectTable.fxCode = $.BM;

                            measureProjectTable.projectAttr = $.XMTZ;
                            measureProjectTable.unit = '项';
                            // measureProjectTable.quantity = $.GCL;
                            measureProjectTable.quantity = NumberUtil.numberScale($.GCL,getUnitFormatEnum(measureProjectTable.unit).value);
                            let 单位num = $.GCL.replace(/[^0-9].*/ig,'')!==''?$.GCL.replace(/[^0-9].*/ig,''): 1;
                            measureProjectTable.quantityExpression =NumberUtil.multiplyToStringExEnd0(单位num, $.GCL,ConstantUtil.QD_DE_DECIMAL_POINT);
                            measureProjectTable.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, $.GCL,ConstantUtil.QD_DE_DECIMAL_POINT);

                            measureProjectTable.kind = BranchProjectLevelConstant.qd;
                            measureProjectTable.constructId = unitProject.constructId;
                            measureProjectTable.spId = unitProject.spId;
                            measureProjectTable.unitId = unitProject.sequenceNbr;
                            measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                            measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                            measureProjectTable.adjustmentCoefficient = 1;
                            if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                //如果map中没有去查数据库
                                let res =await this.service.baseListService.queryQdByCode(measureProjectTable.fxCode);
                                if(!ObjectUtils.isEmpty(res)){
                                    this.qdMap.set(measureProjectTable.fxCode,res)
                                }
                            }
                            if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                                    measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                                }
                            }
                            measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr:'';
                            measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                            awfMeasureProjectTableArray.push(measureProjectTable)
                        }
                        unitProject.awfMeasureProjectTableArray = awfMeasureProjectTableArray;
                    }
                }else if($.MC=== '其他总价措施项目'){
                    let zjMeasureProjectTableArray = new Array();
                    let CSZMQD = CSBTElement.CSZMQD;
                    if(!ObjectUtils.isEmpty(CSZMQD)){
                        for (let k = 0; k < CSZMQD.length; k++) {
                            $ = CSZMQD[k].$ ;
                            let measureProjectTable = new MeasureProjectTable();
                            measureProjectTable.sequenceNbr =Snowflake.nextId();
                            measureProjectTable.dispNo = (k+1)+'';

                            measureProjectTable.name = $.MC;
                            measureProjectTable.name = $.MC;
                            measureProjectTable.bdCode = $.BM;
                            measureProjectTable.fxCode = $.BM;

                            measureProjectTable.projectAttr = $.XMTZ;
                            measureProjectTable.unit = $.JLDW;
                            // measureProjectTable.quantity = $.GCL;
                            measureProjectTable.quantity = NumberUtil.numberScale($.GCL,getUnitFormatEnum(measureProjectTable.unit).value);
                            let 单位num = $.GCL.replace(/[^0-9].*/ig,'')!==''?$.GCL.replace(/[^0-9].*/ig,''): 1;
                            measureProjectTable.quantityExpression =NumberUtil.multiplyToStringExEnd0(单位num, $.GCL,ConstantUtil.QD_DE_DECIMAL_POINT);
                            measureProjectTable.quantityExpressionNbr = NumberUtil.multiplyToStringExEnd0(单位num, $.GCL,ConstantUtil.QD_DE_DECIMAL_POINT);
                            measureProjectTable.kind = BranchProjectLevelConstant.qd;
                            measureProjectTable.constructId = unitProject.constructId;
                            measureProjectTable.spId = unitProject.spId;
                            measureProjectTable.unitId = unitProject.sequenceNbr;
                            measureProjectTable.displaySign = BranchProjectDisplayConstant.noSign;
                            measureProjectTable.displayStatu = BranchProjectDisplayConstant.displayMax;
                            measureProjectTable.adjustmentCoefficient = 1;
                            if(ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                //如果map中没有去查数据库
                                let res =await this.service.baseListService.queryQdByCode(measureProjectTable.fxCode);
                                if(!ObjectUtils.isEmpty(res)){
                                    this.qdMap.set(measureProjectTable.fxCode,res)
                                }
                            }
                            if(!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))){
                                if (!ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode)){
                                    measureProjectTable.zjcsClassCode =  Number(this.qdMap.get(measureProjectTable.fxCode).zjcsClassCode);
                                }
                            }
                            measureProjectTable.standardId = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode))? this.qdMap.get(measureProjectTable.fxCode).sequenceNbr:'';
                            measureProjectTable.isSupplement = !ObjectUtils.isEmpty(this.qdMap.get(measureProjectTable.fxCode)) ? 0 : 1;
                            zjMeasureProjectTableArray.push(measureProjectTable);
                        }
                        unitProject.zjMeasureProjectTableArray = zjMeasureProjectTableArray;
                    }
                }

            }
        }
    }

}


AnalyzingXMLServiceYCG.toString = () => '[class AnalyzingXMLServiceYCG]';
module.exports = AnalyzingXMLServiceYCG;